import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DbaService } from '../../../../services/dba.service';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import * as XLSX from 'xlsx';
import { SpinnerWhiteComponent } from '../../../../shared/components/spinner-white/spinner-white.component';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-bloating',
  standalone: true,
  imports: [CommonModule, NgSelectModule, FormsModule, CommonModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerWhiteComponent],
  templateUrl: './bloating.component.html',
  styles: ``
})
export class BloatingComponent {
   pageName:string = ''
  constructor(private fb: FormBuilder, private dba: DbaService,private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }
  bloatingForm: any
  projectId: any
  ConsList: any
  spinner: any
  tableHide: boolean = false
  p2:any
  searchText1: string = '';
  selectedItems:any=[]
  
  

  ngOnInit(): void {
    this.GetConsList()
    this.bloatingForm = this.fb.group({
      connection: ['', [Validators.required]],
      db: ['', [Validators.required]],
      schemaname: ['', Validators.required],
    })
  }

  get getbloatingControl() {
    return this.bloatingForm.controls;
  }

  GetConsList() {
    this.dba.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }

  dblist: any
  connId: any
  Getdbname(conId: string) {
    this.dblist = []
    this.connId = conId
    this.dba.getdbnames(conId).subscribe((data: any) => {
      this.dblist = data
    });
  }

  selecteddbname: any
  selectDB(data: any) {
    this.selecteddbname = data.toLowerCase()
  }
  selectedTgtCon: any
  schemaList: any
  getSchemas() {
    let obj = {
      conid: this.connId,
      dbname: this.selecteddbname.toLowerCase()
    }
    this.dba.tgtSchemaSelectwithdb(obj).subscribe((data: any) => {
      this.schemaList = data
      this.schemaList.sort((a:any, b:any) => a.schemaname.localeCompare(b.schemaname));
      this.schemaList.filter((item: any) => {
        item.type = "ALL"
      })
    })
  }

  get f() {
    return this.bloatingForm.controls;

  }

 
  selectedschema: string = ""
  Getsearch() {
    this.getbloatdetails("0")
    this.getbloatdetails("1")

  }

  tabvalue:string = 'analyze'
  setTabValue(value:any){
    this.tabvalue = value;
  }
 
  GetbloatingDetails: any
  indexbloatdata: any = []
  tablebloatdata: any = []
  getbloatdetails(value: any) {
    this.tableHide=true
    let obj = {
      Conid: this.connId,
      dbname: this.selecteddbname,
      option: value,
      schemaname:this.selectedschemas
    }
    if(value=="0")
      {
        this.dba.GetbloatingDetails(obj).subscribe((data: any) => {
         
          this.indexbloatdata = data
        });
      }
      else{
        this.dba.GetbloatingDetails(obj).subscribe((data: any) => {
          this.tablebloatdata = data
        });
      }

    
  }
    fileName = 'indexbloating.xlsx';
  testing: any = []
  excelSpin: any = false;
  exportexcel(): void {
    this.testing = []
    this.excelSpin = true
    var test = this.indexbloatdata
    for (var el of test) {
      var newEle: any = {};
      newEle.schemaname = el.schemaname;
      newEle.tablename = el.tablename;
      newEle.indexname = el.indexname;
      newEle.bloatpercentage = el.bloatpercentage;
      this.testing.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);

    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName);

  }
  fileName1 = 'tablebloating.xlsx';
  testing1: any = []
  excelSpin1: any = false;
  exportexcel1(): void {
    this.testing1 = []
    this.excelSpin1 = true
    var test = this.tablebloatdata
    for (var el of test) {
      var newEle: any = {};
      newEle.schemaname = el.schemaname;
      newEle.tablename = el.tablename;
      newEle.bloatpercentage = el.bloatpercentage;
      this.testing1.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing1);

    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName1);

  }
  selectedschemas: string = ""
  selectSchema(value: any) {
    var temp: any = []
    if (value.toString() == "ALL") {
      this.selectedschemas = ""
      this.schemaList.filter((item: any) => {
        temp.push("'" + item.schemaname + "'")
      })
      this.selectedschemas = temp.toString()
    }
    else {
      value.filter((item: any) => {
        temp.push("'" + item + "'")
      })

      this.selectedschemas = temp.toString()
    }

  }
}
import { Component } from '@angular/core';
import { DbaService } from '../../../../services/dba.service';
import { FormBuilder,Validators,FormsModule,ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { CommonModule } from '@angular/common';
import * as XLSX from 'xlsx';
import { ActivatedRoute } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgSelectModule } from '@ng-select/ng-select';

declare let $:any
@Component({
  selector: 'app-vacuum',
  standalone: true,
  imports: [NgxPaginationModule, SearchFilterPipe, CommonModule,FormsModule,ReactiveFormsModule,SpinnerComponent,NgSelectModule],
  templateUrl: './vacuum.component.html',
  styles: ``
})
export class VacuumComponent {
  selectedItems:string=''

  vacuumForm: any
  pageName: string = ''
  p1:number=1 
  piA:number=10
  datachange1:any
  constructor(
    private fb: FormBuilder,
    private dba: DbaService, private route: ActivatedRoute,private toast: HotToastService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
    this.isCheckBoxSel=false
  }
  tableHide: boolean = false

  ngOnInit(): void {
    this.GetConsList(),
    this.getreqTableData() 
      this.vacuumForm = this.fb.group({
        connection: ['', [Validators.required]],
        Dbase: ['', [Validators.required]],
        schemaname:['']
        
      })
  }
  dbnameres: any
  p2: number = 1;
  p3: number = 1;
  searchText: string = '';
  ConsList: any = [];
  projectId: any;
  isDisableAllCheck: any;
  showCheckStatus: boolean = false
  isCheckBoxSel:boolean = false
  schemaList: any = [];

  tabValue:string = 'analyze'
  setTabValue(value:any){
    this.tabValue = value;
  }
  fetchDbNames(value: string) {
    this.connId = value;
    this.dbnameres = []
    this.dba.getdbnames(value).subscribe((data: any) => {
      this.dbnameres = data
      //console.log(this.dbnameres)
    })
  }

  selecteddbname: any
  connId: any
  GetConsList() {
    this.dba.getConList(this.projectId.toString()).subscribe((data: any) => {
      const condata = data['Table1'];
      this.ConsList = condata.filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }


  vacuumsearchList: any = []
  GetFetchVacuumData() {
    this.showCheckStatus = false
    this.tableHide=true
    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname,
      schemas:this.selectedschema
    }
    ////console.log(obj);
    this.dba.FetchVacuumData(obj).subscribe((data: any) => {
      if (data.length > 0) {
        this.vacuumsearchList = data
        var i=1;
        this.vacuumsearchList.forEach((item: any) => {
          item.sno=i
          item.isSelected = false
          i++
        })
        this.vacuumsearchList=this.vacuumsearchList.filter((item:any)=>{
          if(item.live_tup >0 && item.dead_tup>0 )
            {
              return item
            }
        })
      }
    });

  }
  
  analyzesearchlist: any = []
  GetFetchAnalyzeData() {
    this.showCheckStatus = false

    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname,
      schemas:this.selectedschema
    }
    this.dba.GetAnalyzeData(obj).subscribe((data: any) => {
      this.analyzesearchlist = data
      var i=1;
      this.analyzesearchlist.forEach((item: any) => {
        item.sno=i
        item.isSelected = false
        i++
      })
      this.analyzesearchlist=this.analyzesearchlist.filter((item:any)=>{
        if(item.live_tup >0  )
          {
            return item
          }
      })
    });

  }

  newselecteddbname(value: string) {
    this.selecteddbname = value

  }

  selectAll($event: any) {
    this.showCheckStatus = true
    //console.log($event.target.checked)
    if ($event.target.checked) {
      this.analyzesearchlist.forEach((item: any) => {
        item.isSelected = true
      }) 
      this.itemSelectedAnalyze = true
    }
    else {
      this.analyzesearchlist.forEach((item: any) => {
        item.isSelected = false
      }) 
      this.itemSelectedAnalyze = false
      this.showCheckStatus=false
    }
  }

  selectAllVacuum($event: any) {
    this.showCheckStatus = true
    //console.log($event.target.checked)
    if ($event.target.checked) {
      this.vacuumsearchList.forEach((item: any) => {
        item.isSelected = true
      })
      this.itemSelecte = true
    }
    else {
      this.vacuumsearchList.forEach((item: any) => {
        item.isSelected = false
      })
      this.itemSelecte = false
      this.showCheckStatus=false
    }
  }

  onitemSelect($event: any, value: any) {
    if ($event.target.checked) {
      this.vacuumsearchList.forEach((item: any) => {
        if (item.sno == value) {
          item.isSelected = true
        }
      })
    } else {
      this.vacuumsearchList.forEach((item: any) => {
        if (item.sno == value) {
          item.isSelected = false
        }
      })
    }

    var temp = this.vacuumsearchList.filter((item: any) => {
      return item.isSelected == true
    })
    if (temp.length == 0) {
      this.showCheckStatus = false
    }
  }

// my changes------------------->

  onitemSelectAnalyze($event: any, value: any) {
    if ($event.target.checked) {
      this.analyzesearchlist.forEach((item: any) => {
        if (item.sno == value) {
          item.isSelected = true
        }
      })
    } else {
      this.analyzesearchlist.forEach((item: any) => {
        if (item.sno == value) {
          item.isSelected = false
        }
      })
    }

    var temp = this.analyzesearchlist.filter((item: any) => {
      return item.isSelected == true
    })
    if (temp.length == 0) {
      this.showCheckStatus = false
    }
  }

// ---------------------------------------->
  itemSelecte: boolean = false
  vacuumHide($event: any) {
    if ($event.target.checked) {
      this.itemSelecte = true
    }
    else {
      var filter = this.vacuumsearchList.filter((item: any) => {
          return item.isSelected == true
      })
      //console.log(filter)
      if (filter.length== 0) {
        this.itemSelecte = false
      }
    }
  } 
// my changes
  itemSelectedAnalyze: boolean = false
  
  analyzeHide($event: any) {
    if ($event.target.checked) {
      this.itemSelectedAnalyze = true
    }
    else {
      var filter = this.analyzesearchlist.filter((item: any) => {
          return item.isSelected == true
      })
      //console.log(filter)
      if (filter.length== 0) {
        this.itemSelectedAnalyze = false
      }
    }
  }
 
  fileName = 'Vacuum.xlsx';
  testing:any=[]
  excelSpin:any=false;
  exportexcelVacuum(): void {
    this.testing = []
    this.excelSpin = true
    var test = this.vacuumsearchList
    for (var el of test) {
      var newEle: any = {};
      newEle.relname = el.relname;
      newEle.schemaname = el.schemaname;
      newEle.live_tup = el.live_tup;
      newEle.last_vacuum = el.last_vacuum;
      newEle.dead_tup = el.dead_tup;
      this.testing.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);
   
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName);

  }

  
  fileNamee = 'Analyze.xlsx';
  testingg:any=[]
  excelSpinn:any=false;
  exportexcelAnalyze(): void {
    this.testingg = []
    this.excelSpinn = true
    var test = this.analyzesearchlist
    for (var el of test) {
      var newEle: any = {};
      newEle.relname = el.relname;
      newEle.schemaname = el.schemaname;
      newEle.live_tup = el.live_tup;
      newEle.last_analyze = el.last_analyze;
      newEle.dead_tup = el.dead_tup;
      this.testingg.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testingg);
   
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileNamee);

  }

  get f() {
    return this.vacuumForm.controls;
   
  }
  get getvacuumControl() {
    return this.vacuumForm.controls;
  }

  dbago() {
    var tempp: any = []
    var option;
    if (this.tabValue == "analyze") {
      option = "1"
      this.analyzesearchlist.forEach((item: any) => {
        if (item.isSelected == true) {
          tempp.push(item.schemaname + "." + item.relname)
        }
      })
    } else {
      option = "0"
      this.vacuumsearchList.forEach((item: any) => {
        if (item.isSelected == true) {
          tempp.push(item.schemaname + "." + item.relname)
        }
      })
    }
    let obj = {
      projectId: this.projectId.toString(),
      TgtConId: this.connId,
      dbname: this.selecteddbname.toLowerCase(),
      schematable: tempp.toString(),
      option: option
    }
    this.dba.triggerVacuum(obj).subscribe((data:any)=>{
      this.toast.success(data.message);
      $('#VacuumDemo').offcanvas('hide');
      $('#AnalyzeDemo').offcanvas('hide');

      
    })
  }
  checkboxselect($event: any): void {
    if ($event.target.checked) {
      this.isCheckBoxSel = true
    } else {
      this.isCheckBoxSel = false
    }
  }
  tabledata:any=[]
  ref_spin:any
  assessmentService:any
  z:any
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: 'DBA',
    };
    this.ref_spin = true
    this.dba.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      this.ref_spin = false
      // if (this.tabledata == undefined) {
      //   this.tabledata = []
      // }
      // else {
      
      //   for (let k = 0; k < this.tabledata.length; k++) {
      //     if (this.tabledata[k].status == "C") {
      //       this.tabledata[k].statusfull = "Completed"
      //     }
      //     else if (this.tabledata[k].status == "I") {
      //       this.tabledata[k].statusfull = "Initialize"
      //     }
      //     else if (this.tabledata[k].status == "P") {
      //       this.tabledata[k].statusfull = "Pending"
      //     }
      //     else {
 
      //     }
         
      //     if (this.tabledata[k].objecttype == "ALL") {
      //       this.tabledata[k].objecttype = ""
      //     }
      //   }
      //   if (this.tabledata != undefined) {
      //     for (this.z = 0; this.z < this.tabledata.length; this.z++) {
      //       for (let i = 0; i < this.ConsList?.length; i++) {
      //         if (
      //           this.tabledata[this.z].connection_id ==
      //           this.ConsList[i].Connection_ID
      //         ) {
      //           this.tabledata[this.z].conname = this.ConsList[i].conname;
      //         }
      //       }
      //     }
      //   }
      //   else {
      //     this.tabledata = []
      //   }
      // }
    });
  }
  deleteResponse: any;
  deleteTableDatas(request_id: any) {
    const obj:any = {
      projectId: this.projectId,
      requestId: request_id,
    };
    this.dba.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData();
    });
  }
  page2:any
  p:any
  onKey() {
    this.page2 = 1;
    this.p = 1;
    this.p2 = 1;
  }
 // Database details
  
 dblist: any
 
 Getdbname(conId: string) {
   this.dblist = []
   this.connId = conId
   this.dba.getdbnames(conId).subscribe((data: any) => {
     this.dblist = data
   });
 }

 selectDB(data: any) {
   this.selecteddbname = data.toLowerCase()
 }
 
 selectedschema: string = ""
  selectschema(value: any) {
    var temp: any = []
    if (value.toString() == "ALL") {
      this.selectedschema = ""
      this.schemaList.filter((item: any) => {
        temp.push("'" + item.schemaname.toUpperCase() + "'")
      })
      this.selectedschema = temp.toString()
      //this.GetObjects()
    }
    else if (value.length > 0) {
      value.filter((item: any) => {
        temp.push("'" + item.toLowerCase() + "'")
      })
      this.selectedschema = temp.toString()
      //this.GetObjects()
    }
    

  }
 selectedTgtCon: any
 
 getSchemas() {
   let obj = {
     conid: this.connId,
     dbname: this.selecteddbname.toLowerCase()
   }
   this.dba.tgtSchemaSelectwithdb(obj).subscribe((data: any) => {
     this.schemaList = data
     this.schemaList.filter((item: any) => {
       item.type = "ALL"
     })
   })
 } 
}

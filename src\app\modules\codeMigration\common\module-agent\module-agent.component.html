<!--- Bread Crumb --->
<div class="v-pageName">{{pageName}} </div>

<div class="body-main">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="agentForm">
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Source Schema</label>
                            <select formControlName="schema" class="form-select" #ss (change)="getObjectTypes(ss.value)">
                                <option disabled value="">Select a Source Schema</option>
                                @for ( schema of schemaList; track schema) {
                                <option value="{{ schema }}"> {{schema }}</option>
                                }
                            </select>
                            @if(f.schema.touched && f.schema.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(f.schema.errors.required) {schema is required}
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Type</label>
                            <select formControlName="objectType" class="form-select" #ot (change)="getObjectNames(ss.value, ot.value)">
                                <option value="">Select a Object Type</option>
                                @for ( objects of objectTypeList; track objects) {
                                <option value="{{objects }}"> {{objects }}</option>
                                }
                            </select>
                            @if(f.objectType.touched && f.objectType.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(f.objectType.errors.required) {Object Type is required}
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Name </label>
                            <select class="form-select" formControlName="objectName" #on (change)="getAgentTargetStatments(ss.value, ot.value, on.value)">
                                <option disabled value="">Select a Object Name</option>
                                @for ( objectNames of objectNamesList; track objectNames) {
                                <option value="{{ objectNames }}"> {{objectNames }}</option>
                                }
                            </select>
                            @if(f.objectName.touched && f.objectName.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(f.objectName.errors.required) {Object Name is required}
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 mt-3 pt-2">
                        <button class="btn btn-upload w-100" [disabled]="agentForm.invalid" (click)="triggerAgent()">
                            <span class="mdi mdi-cog-play"></span>
                            Trigger Agent @if(triggerSpin){<app-spinner />}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Agent Status -->
    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="row">
                <div class="col-sm-6 col-md-4 col-xl-2">
                    <button class="btn btn-sync mt-2" (click)="getModuleStatus()">Reload
                        @if(reloadSpin){
                        <app-spinner />
                        }@else{
                        <span class="mdi mdi-refresh"></span>
                        }

                    </button>
                </div>
                <div class="col-sm-6 col-md-8 col-xl-10">
                    <div class="custom_search cs-r">
                        <span class="mdi mdi-magnify"></span>
                        <input type="text" placeholder="Search Module Agent Status" aria-controls="example"
                            class="form-select" [(ngModel)]="dataChange" (keyup)="onKey()" />
                    </div>
                </div>
            </div>
        </div>

        <!-- for download file -->
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                        <tr>
                            <th>Process Type</th>
                            <th>Schema Name</th>
                            <th>Object Type</th>
                            <th>Object Name</th>
                            <th>Created Date</th>
                            <th>Updated Date</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                </thead>
                <tbody>
                    @for(con of executionLogs |searchFilter:
                    dataChange|paginate:{
                    itemsPerPage: 10, currentPage: page, id:'First'};
                    track con){
                        <tr>
                            <td>{{con.process_type}}</td>
                            <td>{{ con.schema_name }}</td>
                            <td>{{ con.object_type }}</td>
                            <td>{{ con.object_name }}</td>
                            <td>{{con.created_dt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>{{con.updated_dt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>
                                @if(con.status =='Error'){
                                <span class="badge badge-cdanger">{{con.status}}</span>
                                }@else{
                                <span class="badge badge-csuccess">success</span>
                                }
                            </td>

                            <td>
                                <button class="btn btn-download" (click)="downloadFile()">
                                    <span class="mdi mdi-cloud-download-outline"></span>
                                </button>
                            </td>

                        </tr>
                    } @empty {
                    <tr>
                        <td colspan="9">
                            <p class="text-center m-0 w-100">Empty</p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>

        <!-- pagination -->
        <div class="custom_pagination">
            <pagination-controls (pageChange)="page = $event" id="First">
            </pagination-controls>
        </div>
    </div>

    <!--- Agent Review -->
        <div class="qmig-card mt-3">          
        <div class="row">
            <div class="col-md-6 col-xl-6">
                <h3 class="main_h px-3 pt-3">Agent Review @if(selectedAgentTGTID){ / Selected Target Statement - {{selectedAgentTGTID}} }</h3>
            </div>
            <div class="col-md-3 col-xl-3 offset-md-3 px-3 pt-1">
                <div class="row">
                    <div class="col-12 col-sm-9 col-md-9 col-lg-9 col-xl-9 px-1">
                        <select class="form-select" (change)="getAgentDeployFullData(atgt.value)" #atgt>
                            <option selected value="">Select target statement</option>
                            @for ( list of TargetStatmentList; track list) {
                            <option value="{{ list }}">Target Statement - #{{list }}</option>
                            }
                        </select>
                    </div>
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 px-1">
                        <button class="btn btn-sync mt-1" (click)="getAgentTargetStatments(ss.value, atgt.value, on.value )">
                            @if(getRunSpin){
                            <app-spinner />
                            }@else{
                                <span class="mdi mdi-refresh"></span>
                            }
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- @for (agent of agentLogs; track agent.attempt_result.attempt_id; let i = $index) {
        <p>Comparison Status: {{i+1}} {{ agent.attempt_result.ai_comparison_status }}</p>
        <p>Comparison Status: {{i+1}} {{ agent.modules.join.feature_name }}</p>
        } -->
        @if (agentRawLogs) {            
        <div class="row m-3">       
            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
                <label class="form-label">Original Source Statement</label>
                <textarea 
                    class="form-control" 
                    rows="7" 
                    readonly disabled>{{ agentRawLogs.original_source_statement }}</textarea>
            </div>        
            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
                <label class="form-label" >QMigrator Target Statement</label>
                <textarea 
                    class="form-control" 
                    rows="7" 
                    readonly disabled>{{ agentRawLogs.target_statement }}</textarea>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
                <label class="form-label">AI Stage1 output</label>
                <textarea 
                    class="form-control" 
                    rows="7" disabled>{{ agentRawLogs.ai_converted_statement }}</textarea>
            </div>
        </div>
        }
        <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">     
            @for(agent of agentLogs; track agent; let i = $index){   
             <div class="accordion-item">
                <h2 class="accordion-header" [id]="'flush-headingOne' + i">
                <button
                    class="accordion-button collapsed"
                    type="button"
                    data-bs-toggle="collapse"
                    [attr.data-bs-target]="'#flush-collapseOne' + i"
                    aria-expanded="false"
                    [attr.aria-controls]="'flush-collapseOne' + i">
                    Attempt {{ agent.attempt_result.attempt_number }}
                </button>
                </h2>
                <div
      [id]="'flush-collapseOne' + i"
      class="accordion-collapse collapse"
      [attr.aria-labelledby]="'flush-headingOne' + i"
      data-bs-parent="#accordionPanelsStayOpenExample">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form">                         
                            <div class="accordion accordion-flush qmig-accordion" id="moduleAccordion">   
                                @for (module of objectValues(agent.modules); track module ; let j = $index) {
                                <div class="accordion-item">
                                    <h2 class="accordion-header" [id]="'module-headingOne' + j">
                                    <button
                                        class="accordion-button collapsed p-0 my-2"
                                        type="button"
                                        data-bs-toggle="collapse"
                                        [attr.data-bs-target]="'#module-collapseOne' + j"
                                        aria-expanded="false"
                                        [attr.aria-controls]="'module-collapseOne' + j">
                                        Feature name: &nbsp; <strong> {{ module.feature_name }}</strong>
                                    </button>
                                    </h2>
                                    <div
                                    [id]="'module-collapseOne' + j"
                                    class="accordion-collapse collapse"
                                    [attr.aria-labelledby]="'module-headingOne' + j"
                                    data-bs-parent="#moduleAccordion">
                                        <div class="row my-3">                                            
                                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                                <label class="form-label" [for]="'error_' + i">Original Module Code</label>
                                                <textarea 
                                                    class="form-control" 
                                                    rows="7" 
                                                    readonly disabled
                                                    [id]="'error_' + i">{{ module.original_module }}</textarea>
                                            </div>
                                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                                <label class="form-label" [for]="'error_' + i">Updated Module Code</label>
                                                <textarea 
                                                    class="form-control" 
                                                    rows="7" 
                                                    [(ngModel)]="module.updated_module"
                                                    [id]="'error_' + i" name="error_{{ i }}" ></textarea>
                                            </div>
                                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                                <label class="form-label" [for]="'registry_' + i">Registry Module Code</label>
                                                <textarea 
                                                    class="form-control" 
                                                    rows="7" 
                                                    [(ngModel)]="module.registry_module_code"
                                                    [id]="'registry_' + i" name="registry_{{ i }}" ></textarea>
                                            </div>
                                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4 mt-3">
                                                <label class="form-label" [for]="'converted_' + i">AI Stage2 output</label>
                                                <textarea 
                                                    class="form-control" 
                                                    rows="7" 
                                                    [id]="'converted_' + i" disabled>{{agent.attempt_result.stage2_output_statement}}</textarea>
                                            </div>
                                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4 mt-3">
                                                <label class="form-label" [for]="'comments_' + i">Reviewer Comments</label>
                                                <textarea 
                                                    class="form-control" 
                                                    rows="7" [(ngModel)]="module.reviewer_comments"
                                                    [id]="'comments_' + i" name="comments_{{ i }}" ></textarea>
                                            </div>                              
                                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4 mt-3 pt-4">
                                                <div class="cagent_status">
                                                    <p>AI Comparison Result : @if(agent.attempt_result.ai_comparison_status =="Success"){ <span style="color: #13cd68;">Matched</span> } @else { <span style="color: #ff3b56;">Not Matched</span> }</p>
                                                    <p>Verified by Reviewer: @if(module.is_reviewed.toString() =="true"){ <span style="color: #13cd68;">Yes</span>} @else { <span style="color: #ff3b56;">No</span> }</p>
                                                     @if(module.is_reviewed.toString() =="false"){ 
                                                        <div class="form-check">
                                                            <label class="form-check-label">
                                                                <span style="color: #ff3b56;">Check as Reviewed</span> 
                                                                <input type="checkbox"  [(ngModel)]="module.is_reviewed" name="checkbox"  class="form-check-input" (click)="onCheckboxChange($event)">
                                                            </label>
                                                        </div>
                                                     }
                                                    <p>Available in Registry : @if(module.is_inregistry.toString() =="true"){ <span style="color: #13cd68;">Yes</span> } @else { <span style="color: #ff3b56;">No</span> }</p>
                                                    <p>Merge Status: @if(module.is_merged.toString() =="true"){ <span style="color: #13cd68;">Yes (Deployed in QBook)</span> } @else { <span style="color: #ff3b56;">Not yet Deployed in QBook</span> }</p>

                                                    <div style="position: relative;display: inline-flex; vertical-align: middle;">
                                                        <button class="btn btn-upload btn-small" 
                                                                (click)="saveAgentLogs(module.id,module.reviewer_comments,module.reviewer_name, module.updated_module )"> &nbsp; Save &nbsp; 
                                                        </button>
                                                        <button class="btn btn-upload btn-small mx-1" (click)="moveToRegistry(module.id, module.reviewer_name, module.reviewer_comments)"> &nbsp; Move to Registry &nbsp; </button>
                                                        <button class="btn btn-upload btn-small" (click)="mergeToQBook(module.id, module.reviewer_name, module.reviewer_comments)" disabled> &nbsp; Merge &nbsp; </button>                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                }  
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            }@empty {
            <p class="text-center py-3">No Logs Found</p>
            }
        </div>
    </div>
</div>

<app-confirm-dialog
  [confirmVal]="'Are You Sure want to Mark it as Verified by Reviewer?'"
  [message]="'Are You Sure want to Mark it as Verified by Reviewer?'"
  (confirmResponse)="handleConfirm($event)">
</app-confirm-dialog>
<div class="v-pageName">{{pageName}}</div>
<!--- Performance optimisation List --->
<div class="qmig-card">
    <h3 class="main_h px-3 pt-3"> Long Running Queries </h3>
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="persfForm">
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="">
                        <label class="form-label d-required" for="targtetConnection"> Connection
                            Name</label>
                        <select formControlName="tgtconnection" #Myselect2 (change)="
                        selTgtId(Myselect2.value);GetperfSchemas() " class="form-select">
                        <option value="" disabled selected>Select Connection Name</option>
                            @for(list of tgtList;track list; ){
                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                            }
                        </select>
                        <div class="alert">
                            @if(validate.tgtconnection.touched && validate.tgtconnection.invalid) {
                            <p class="text-start text-danger mt-1">Connection Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="">
                        <label class="form-label d-required" for="fromDate"> From Date </label>
                        <input type="datetime-local" class="form-control" formControlName="fromDate" id="selectedFromDate">
                        <div class="alert">
                            @if(validate.fromDate.touched && validate.fromDate.invalid) {
                            <p class="text-start text-danger mt-1">From Date required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="">
                        <label class="form-label d-required" for="toDate"> To Date </label>
                        <input type="datetime-local" class="form-control" formControlName="toDate" id="selectedToDate">
                        <div class="alert">
                            @if(validate.toDate.touched && validate.toDate.invalid) {
                            <p class="text-start text-danger mt-1">To Date required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="mt-3 pt-2">
                        <button class="btn btn-upload w-100" (click)="getLongRunningQueries(persfForm.value)" [disabled]="persfForm.invalid"> Fetch Data
                            @if(fetchSpinner){<app-spinner />}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div [hidden]="dataFetched">
        <hr class="dash-dotted" />
        <div class="qmig-card-body">
            <div class="ps-3 col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 offset-md-9">
                <button class="btn btn-sign w-100" (click)="exportexcelShowlogs()">
                    <span class="mdi mdi-arrow-up-thin"></span>Export   @if(excelSpinn){<app-spinner />}</button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>S.No</th>
                        <th>PID</th>
                        <!-- <th>Query</th> -->
                        <th>UserName</th>
                        <th>State</th>
                        <th>QueryExecutionTime</th>
                        <th>Blocking PID</th>

                    </tr>
                </thead>
                <tbody>
                    @for (documents of longQueries | searchFilter: searchText
                    | paginate: {
                    itemsPerPage: 10, currentPage: pageNumber } ; track documents; )
                    {
                    <tr>
                        <td>{{pageNumber*10+$index+1-10}}</td>
                        <td>{{documents.pid}}</td>
                        <!-- <td>{{documents.query}}</td> -->
                        <td>{{documents.usename}}</td>
                        <td>{{documents.state}}</td>
                        <td>{{documents.queryExecDate}}</td>
                        <td>{{documents.blockingpid}}</td>
                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="6">
                            <p class="text-center m-0 w-100">Empty list of Queries </p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
    </div>
</div>




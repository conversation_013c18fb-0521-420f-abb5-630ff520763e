<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-12">
                <dash-tabs></dash-tabs>            
        </div>
    </div>


    <!--- Main Content --->
    <div class="qmigTabs mt-3">        
        <div class="row">            
            <div class="col-md-2 pe-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100"  (change)="onConnectionChange($event)">
                        <option selected disabled>Select Source Connection</option>
                        @for(ConsList of ConsList;track ConsList;){
                            <option value="{{ConsList.Connection_ID}}">{{ConsList.conname}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" (change)="onDbNameChange($event);">
                        <option value="" selected>Select Database</option>
                        @for(db of dbnameList; track db;) {
                          <option [value]="db.dbname">{{ db.dbname }}</option>
                        }
                      </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" (change)="onSchemaChange($event)">
                        <option selected disabled>Select Schema Name</option>
                        @for(sc of SchList; track sc;){
                            <option value="{{sc.schema_name}}">{{sc.schema_name}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" (change)="onObjectTypeChange($event)">
                        <option value="" selected>Select Object Type</option>
                        @for(obj of ObjList; track obj;) {
                          <option [value]="obj.object_type">{{ obj.object_type }}</option>
                        }
                      </select>
                </div>
            </div>            
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" (change)="onIterationChange($event)">
                        <option value="" selected>Select Iteration</option>
                        @for(it of itList; track it;) {
                          <option [value]="it.iteration_id">{{ it.iteration_id }}</option>
                        }
                      </select>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3 dashCH">
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total Count</h5>
                    <h1><span class="mdi mdi-database"></span> {{ totalCountSum }}</h1>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Converted Count</h5>
                    <h1><span class="mdi mdi-database-sync"></span> {{ matchedCountSum }}</h1>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total Progress</h5>
                    <div style="display: block; width: 100%; overflow-x: auto;">
                        <canvas id="myDoughnutChart" baseChart width="100" height="120" #chart3="base-chart"
                        [type]="'doughnut'"
                        [data]="lineChartData"
                        [options]="lineChartOptions"
                        [legend]="lineChartLegend"
                        [plugins]="lineChartPlugins">
                        </canvas>
                    </div>
              </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Object Match vs Pending by Connection</h5>
                    @if(sourceVSChartLabels.length > 0){
                        <div style="display: block;">
                            <canvas width="400" height="200" baseChart [datasets]="sourceVSChartData"
                                [labels]="sourceVSChartLabels" [options]="sourceVSChartOptions" [legend]="sourceVSChartLegend">
                            </canvas>
                        </div>
                    }@else {
                        <p class="text-center m-0 w-100">No data available for the selected connection</p>
                        }
                </div>
            </div>
        </div>        
        <div class="col-md-6 mt-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>Object, Match and Missing Count by Schema</h5>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group" [hidden]="Schemadisable">
                                <select class="form-select form-small mt-0 me-0" (change)="onSchemaChartChange($event)">
                                    <option value="" selected>Select Schema</option>
                                    @for(schm of SchemaList; track schm;) {
                                    <option [value]="schm.schema_name">{{ schm.schema_name }}</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    @if(longRunningChartLabels.length > 0){
                        <!-- <div style="display: block; width: 100%; overflow-x: auto;">
                            <canvas width="400" height="200" baseChart #chart1="base-chart" [datasets]="longRunningChartData"
                                [labels]="longRunningChartLabels" [options]="longRunningChartOptions" [legend]="longRunningChartLegend">
                            </canvas>
                        </div> -->
                        <div style="position: relative; display: block; width: 100%; overflow-x: auto;">
                            <canvas style="max-width: 100%; height: auto;" baseChart #chart1="base-chart"
                                [datasets]="longRunningChartPaginatedData" [labels]="longRunningChartPaginatedLabels"
                                [options]="longRunningChartOptions" [legend]="longRunningChartLegend">
                            </canvas>
                            <div class="pagination-controls">
                                <button class="prev-button" (click)="schprevPage()"
                                    [disabled]="longRunningChartCurrentPage === 1">&lt;</button>
                                <button class="next-button" (click)="schnextPage()"
                                    [disabled]="longRunningChartCurrentPage === longRunningChartTotalPages">&gt;</button>
                            </div>
                        </div>
                    } @else {
                        <p class="text-center m-0 w-100">No data available for the selected connection</p>
                        }
                </div>
            </div>
        </div>        
        <div class="col-md-6 mt-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Object, Match and Missing Count by Object Type</h5>
                    @if(sObjectChartPaginatedLabels.length > 0){
                        <div style="position: relative; display: block; width: 100%; overflow-x: auto;">
                            <canvas style="max-width: 100%; height: auto;" baseChart #chart1="base-chart"
                                [datasets]="sObjectChartPaginatedData" [labels]="sObjectChartPaginatedLabels"
                                [options]="sObjectChartOptions" [legend]="sObjectChartLegend">
                            </canvas>
                            <div class="pagination-controls">
                                <button class="prev-button" (click)="prevPage()"
                                    [disabled]="sObjectChartCurrentPage === 1">&lt;</button>
                                <button class="next-button" (click)="nextPage()"
                                    [disabled]="sObjectChartCurrentPage === sObjectChartTotalPages">&gt;</button>
                            </div>
                        </div>
                        }@else {
                        <p class="text-center m-0 w-100">No data available for the selected connection</p>
                        }
                </div>
            </div>
        </div>
    </div>
</section>
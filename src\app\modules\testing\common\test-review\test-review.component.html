<div class="v-pageName">{{pageName}}</div>

    <div class="body-main mt-4">
        <div class="qmig-card">
            <div class="qmig-card-body">
                <form class="form qmig-Form">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label d-required" for="name">Run No</label>
                                <select class="form-select" #myIteration aria-label="Default select example" (change)="selectedIteration(myIteration.value)">
                                    <option selected value="">Select Run No</option>
                                    <!-- <option *ngFor="let list of runInfo" value={{list.iter_schema_operation}}>{{list.iter_schema_operation}}</option> -->
                                    @for(list of runInfo; track runInfo; ){
                                    <option value="{{list.dbschema}}">{{list.dbschema}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="d-block" for="name">Group</label>
                                <select class="form-control" (change)="SelectedFeatureId(Feature.value)" #Feature aria-label="Default select example">
                                    <option selected>Select Group</option>
                                    <!-- <option *ngFor="let Feature of  FeatureObjData" value="{{Feature.feature_name}}">{{Feature.feature_name}}</option> -->
                                    @for(Feature of FeatureObjData; track Feature; ){
                                    <option value="{{Feature.dbfeature}}">{{Feature.dbfeature}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    <hr class="py-1" />
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="d-block" for="name">Lead Name</label>
                                <select class="form-control" (change)="selectedGroupId(group.value)" #group aria-label="Default select example">
                                    <option selected>Select Lead Name</option>
                                    <!-- <option *ngFor="let User of  leadfilterdata" value="{{User.resource_nm}}">{{User.resource_nm}}</option> -->
                                    @for(User of leadfilterdata; track User; ){
                                    <option value="{{User.resource_nm}}">{{User.resource_nm}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="name">Assign To</label>
                                <select class="form-control" aria-label="Default select example">
                                    <option selected value="">Select Assign To</option>
                                    <!-- <option *ngFor="let list of AssignDropdownData" value={{list.resource_nm}}>{{list.resource_nm}}</option> -->
                                    @for(list of AssignDropdownData; track list; ){
                                    <option value="{{list.resource_nm}}">{{list.resource_nm}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="name">Group Actual Time</label>
                                <input type="text" value="{{Actualtime}}" placeholder="" class="form-control cus-count" disabled>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="name">Group Estimation Time</label>
                                <input type="text" value="{{estHours}}" placeholder="" class="form-control cus-count" disabled>
                            </div>
                        </div>
                    </div>
                    <hr class="py-1" />
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="d-block" for="name">Test Case</label>
                                <select class="form-control" (change)="selectCodeObject(codeObject.value)" #codeObject aria-label="Default select example">
                                    <option selected value="">Select TestCase</option>
                                    <!-- <option *ngFor="let codeObj of  PrjObjectfeaturesSelectData" value="{{codeObj.objectname}}">{{codeObj.objectname}}</option> -->
                                    @for(codeObj of PrjObjectfeaturesSelectData; track codeObj; ){
                                    <option value="{{codeObj.objectname}}">{{codeObj.objectname}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="name">Estimation Time</label>
                                <input class="form-control cus-count" value="{{TotalTime}}" placeholder="Time" type="text" disabled>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="name">Working Status</label>
                                <input value="" class="form-control cus-count" value="{{WorkStatus}}" type="text" id="status" disabled placeholder="Status">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
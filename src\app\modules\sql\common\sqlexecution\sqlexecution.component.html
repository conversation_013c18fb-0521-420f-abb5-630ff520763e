  <!--- Bread Crumb --->
  <div class="body-header">
    <div class="row">
      <div class="col-md-6 col-xl-6">
        <div class="pageName">
          <i class="mdi mdi-database-search"></i>
          <span>SQL Execution</span>
        </div>
      </div>
    </div>
  </div>
    <div class="body-main mt-4">
      <div class="qmig-card">
        <div class="qmig-card-body">
          <form class="form qmig-Form">
            <div class="row">
              <div class="col-md-3 col-xl-3">
                <div class="form-group">
                  <label class="form-label d-required">SQL Connection</label>
                  <select class="form-select" formControlName="ddlfunctionnames" [(ngModel)]="dbConnName"
                    (change)="ddlcheckedChnaged(Myselect.value)" #Myselect>
                    <option selected value="">Select SQL Connection</option>
                    @for(list of targetData;track list;){
                    <option value="{{ list.conname }}">{{ list.conname}}</option>
                    }
                  </select>
                </div>
              </div>
              
              <div class="col-md-3 col-xl-3">
                <div class="form-group">
                  <label class="form-label d-required" for="name">Category</label>
                  <select class="form-select" formControlName="ddlCategory" #Myselect1
                    (change)="ddlCategorycheckedChnaged(Myselect1.value)">
                    <option selected value="">All Categories</option>
                    @for(miglist of categoryData;track ConsList;){
                    <option value="{{ miglist.scriptcategory }}">{{ miglist.scriptcategory}}</option>
                    }
                  </select>
                </div>
              </div>
              <div class="col-md-2 col-xl-2 mt-4 " [hidden]="!valChecked" >
                <div class="body-header-button">
                  <button class="btn btn-upload w-100" (click)="prepareSQLQuerys()"> Execute @if(executed){<app-spinner />}
                  </button>
                </div>
              </div>
              <div class="col-md-6 col-xl-6 mt-3">
                <div class="custom_search cs-r">
                  <span class="mdi mdi-magnify"></span>
                  <input type="text" placeholder="Search Reports1" class="form-control" [(ngModel)]="datachanges1" (keyup)="checked(datachanges1)">
                </div>
              </div>
              <div class="row" [hidden]="!catSel">
                <div class="table-responsive">
                  <table class="table table-hover qmig-table" id="example" style="width:100%">
                    <thead>
                      <tr>
                        <th>
                          <div class="form-check">
                              <input type="checkbox" id="inlineCheckbox1"  value="m1" (change)="checkUncheckAll($event)"
                              class="form-check-input">
                          </div>
                        </th>
                        <th>Category</th>
                        <th>Function Name</th>
                      </tr>
                    </thead>
                    
                    <tbody>
                      @for(list of FunctionalTableData |searchFilter: datachanges1 |paginate:{
                        itemsPerPage: 10, currentPage: pageNumber, id:'third'};
                        track list;){
                      <tr>
                        <td>                                                 
                          <div class="form-check">
                            <input  type="checkbox" [checked]="list.isSelected" name="list_name" value="{{list.id}}"
                            (click)="onChange(list,list.id, $event)" #checkValue class="form-check-input">
                        </div>
                        </td>
                        <td>{{list.scriptcategory}}</td>
                        <td>{{list.scriptfunction}}</td>
                      </tr>
                      } @empty {
                      <tr>
                        <td colspan="4">
                          <p class="text-center m-0 w-100">Empty list of Reports</p>
                        </td>
                      </tr>
                      }

                    </tbody>
                  </table>
                </div>
                <div class="custom_pagination">
                  <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                </div>
              </div>
            </div>
          </form>
          </div>
      </div>
    </div>


    <div class="body-main mt-4">
      <div class="qmig-card">
        <div class="qmig-card-body">
          <h3 class="main_h ">SQL Reports</h3>
          <form class="form" [formGroup]="sql">
            <div class="row">
              <div class="col-md-3 col-xl-3">
                <div class="form-group">
                  <label class="form-label d-required" >SQL Category</label>
                  <select class="form-select" formControlName="ddlCategorys" (change)="ddlcheckedChnages(Myselectss.value)" #Myselectss>
                    <option selected value="">Select SQL Connection</option>
                                  @for(list of categoryDatas;track list;){
                                  <option value="{{ list.scriptcategory }}">{{ list.scriptcategory}}</option>
                                  }
                  </select>
                </div>
              </div>
              <div class="col-md-3 col-xl-3">
                <div class="form-group">
                  <label class="form-label d-required" for="name">iteration</label>
                  <select class="form-select" formControlName="ddliterations" #Myselect1
                  (change)="ddlIterationcheckedChnaged(Myselect.value)">
                      <option selected value="">iteration</option>
                      @for(ConsList of iterationData;track ConsList;){
                        <option value="{{ ConsList.iteration }}">{{ ConsList.iteration}}</option>
                        }
                    </select>
                </div>
              </div>
              <div class="col-md-2 col-xl-2 mt-4 ">
                  <div class="body-header-button">
                      <button class="btn btn-upload w-100" [disabled]="sql.invalid" (click)="downloadFile()"> Get Report @if(spin_dwld){<app-spinner />}
                          </button>
                  </div>
              </div>
              
            </div>
          </form>
            </div>
      </div>
  </div>
import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { UpdateConnection, addPrjConnection, deleteFile, documentsList, executeBtn, fileStatus, migdetailData, projectMigdetails, redisCommand, reqObj, updatePrjConnection } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ResizeTableDirective } from '../../../../shared/directives/resize-table.directive';

declare let $: any;
@Component({
  selector: 'app-project-databases',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, ResizeTableDirective],
  templateUrl: './project-databases.component.html',
  styles: ``
})
export class ProjectDatabasesComponent {

  /*-- Global variables--*/
  projectId: string;

  //Project Database
  uploadForm: FormGroup = new FormGroup({});

  /*-- Add Database Button--*/

  DBConnectionForm: FormGroup = new FormGroup({});
  addupdateDetails: boolean = true;
  textCheck: boolean = true;
  Addspin: boolean = false;// Add button
  updateSpin: boolean = false;//update button
  recordSpin: boolean = false;
  buttondisable: boolean = true;
  deployVariables: boolean = true;

  /*--Create Database--*/

  prjSrcTgtD: any = {};
  dbquery: any;

  //Radio button

  hidedeployobjs: boolean = false;
  migDetailSelectData: any = [];
  DBName: any;
  dbForm: FormGroup = new FormGroup({});

  // for file name
  blobResponse: any = [];

  //deploy button
  commanDbResponse: any = {};
  deployForm: FormGroup = new FormGroup({});
  Deployspin: boolean = false;

  //upload file
  conName: any;
  uploadResponse: any;

  //upload button
  uploadSpin: boolean = false;

  //Download file 
  downloadfileData: any = [];
  downloadFiles: any;

  //execute button
  executeResponse: any = {};
  Executespin: boolean = false;
  fileName: string = '';
  fileResult: any;

  //blob files
  blobFiles: any;
  blobpaginationFilter: any;
  pageNumber: any;

  pageName:string = ''

  constructor(private titleService: Title, private toast: HotToastService, private assessmentService: AssessmentService, public formBuilder: FormBuilder,private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.deployForm = this.formBuilder.group({
      fileName: ['',],
      file: ['',]
    });
    this.dbForm = this.formBuilder.group({
      db_query: ['', [Validators.required]],
    });
    this.DBConnectionForm = this.formBuilder.group({
      dbconname: ['', [Validators.required]],
      dbname: ['', [Validators.required]],
      dbuserid: ['', [Validators.required]],
      dbhost: ['', [Validators.required]],
      dbpassword: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      server: [''],
      port: ['', [Validators.required]],
      serviceName: [''],
      id: ['']
    });
    this.projectmigdetailselect();
  }

  /*-- Form Validation --*/
  get f(): any {
    return this.deployForm.controls;
  }
  get getcontrol(): any {
    return this.dbForm.controls;
  }

  get formvalidator(): any {
    return this.DBConnectionForm.controls;
  }

  getProjectSrctgtconfSelectD() {
    this.prjSrcTgtD.projectId = this.projectId.toString();
    this.prjSrcTgtD.migsrcType = 'F';
    this.prjSrcTgtD.connectionName = 'null';
  }
  projectmigdetailselect() {
    const MigdetailData: migdetailData = {
      projectId: this.projectId.toString(),
      migsrcType: 'D',
    };
    this.assessmentService
      .projectMigDetailSelect(MigdetailData)
      .subscribe((data: projectMigdetails) => {
        this.migDetailSelectData = data['jsonResponseData']['Table1'];
      });
  }


  /*-- Execute Button --*/
  executeBtn(executeData: any) {
    const db = (<HTMLInputElement>document.getElementById("db_query")).value
    this.dbquery = db
    const obj: executeBtn = {
      dbconname: this.conName,
      projectId: this.projectId.toString(),
      queryTobeExecuted: "CREATE DATABASE " + this.dbquery + ";",
    };
    this.Executespin = true;
    this.assessmentService.ExecuteDbQuery(obj).subscribe((data: any) => {
      this.executeResponse = data;
      this.Executespin = false;

      const prjId = "\"qmigdb" + this.projectId + "\"";
      const error = "Unable to process your request42P04: database " + prjId + " already exists";
      if (data.value == error) {
        $('#demo').offcanvas('hide');
        this.toast.error(" DB Already Exists");
      }
      else {
        if (data['Table 1'][0].Status == "CREATE DATABASE") {
          $('#demo').offcanvas('hide');
          this.toast.success("Created Database SucccessFully.");
        }
      }
      this.Executespin = false;
    }, () => {
      this.toast.error('Something went wrong!');
      $('#demo').offcanvas('hide');
      this.Executespin = false;
    })
  }

  /*--File Upload Methods --*/
  uploadCommonBinaryFileToBlob() {
    const obj = {
      fileName: this.fileName,
      content: this.fileResult.split(',')[1],
    };
    this.uploadSpin = true;
    this.assessmentService.UpLoadCommonBinaryFileToBlob(obj).subscribe(
      (data: any) => {
        this.uploadResponse = data;
        this.uploadSpin = false;
        if (data) {
          this.toast.success('Data uploaded Successfully');
        }
      },
      () => {
        this.uploadSpin = false;
        this.toast.error('Something happened');
      }
    );
  }
  UploadBinaryFileToShare() {
    const obj = {
      projectID: this.projectId.toString(),
      containerName: 'qmigratorfiles' + this.projectId,
      folderName: this.projectId + '_DumpFile',
      fileName: this.fileName,
      content: this.fileResult.split(',')[1],
    };
    this.uploadSpin = true;
    this.assessmentService.UploadBinaryFileToShare(obj).subscribe(
      (data: any) => {
        this.uploadSpin = false;
        if (data) {
          this.toast.success('Data uploaded Successfully');
        }
      },
      () => {
        this.uploadSpin = false;
        this.toast.error('Something happened');
      }
    );
  }
  /*-- Upload Button --*/
  uploadBtn() {
    const value = 'PRJDB';
    if (this.conName.search(value) != -1) {
      this.uploadCommonBinaryFileToBlob();
    } else {
      this.UploadBinaryFileToShare();
    }
  }
  /*-- Add Button --*/
  addPrjConnection(formData: any) {
    const obj: addPrjConnection = {
      project_id: parseInt(this.projectId),
      migsrctype: "D",
      migsrctgt: "T",
      migenv: "DEV",
      fileName: "null",
      dbconname: formData.dbconname,
      dbhost: formData.dbhost,
      dbname: formData.dbname,
      schema: formData.schema,
      port: formData.port,
      dbuserid: formData.dbuserid,
      dbpassword: formData.dbpassword
    };
    this.Addspin = true;
    this.assessmentService.addPrjConnection(obj).subscribe((data: UpdateConnection) => {
      if (data) {
        this.Addspin = false;
        this.toast.success('Data Sent Successfully');
        $('#databaseDetails').offcanvas('hide');
        this.projectmigdetailselect();
      }
    },
      () => {
        this.Addspin = false;
        $('#databaseDetails').offcanvas('hide');
        this.toast.error('Something happened');
      }
    )
  }

  sendEye() {
    this.textCheck = !this.textCheck;
  }

  /*-- Update Button in Table  --*/
  updateRecord(data: any) {
    this.addupdateDetails = false;
    this.DBConnectionForm.patchValue({
      dbconname: data.dbconname,
      dbname: data.dbname,
      dbhost: data.dbhost,
      schema: data.dbschema,
      port: data.dbport,
      serviceName: data.serviceName,
      id: data.id
    });
  }
  /*-- Update Button --*/
  updatePrjConnection(formData: any) {
    const obj: updatePrjConnection = {
      project_id: parseInt(this.projectId),
      id: parseInt(formData.id),
      migsrctype: "D",
      migsrctgt: "T",
      migenv: "DEV",
      fileName: "null",
      dbconname: formData.dbconname,
      dbhost: formData.dbhost,
      dbname: formData.dbname,
      schema: formData.schema,
      port: formData.port,
      dbuserid: formData.dbuserid,
      dbpassword: formData.dbpassword
    };
    this.updateSpin = true;
    this.assessmentService.updatePrjConnection(obj).subscribe(() => {
      this.updateSpin = false;
      this.toast.success('Updated Successfully');
      $('#databaseDetails').offcanvas('hide');
      this.projectmigdetailselect();
    },
      () => {
        this.updateSpin = false;
        $('#databaseDetails').offcanvas('hide');
        this.toast.error('Something happened');
      }
    );
  }

  /*-- For Radio button Validation --*/
  sendValue($event: any): void {
    this.readThis($event.target);
  }

  readThis(inputValue: any): void {
    const file: File = inputValue.files[0];
    this.fileName = inputValue.files[0].name;
    const myReader: FileReader = new FileReader();
    myReader.onloadend = () => {
      this.fileResult = myReader.result;
    };
    myReader.readAsDataURL(file);
  }

  currentlyCheked(checkvalue: any) {
    this.DBName = checkvalue.dbname;
    this.conName = checkvalue.dbconname;
    this.dbquery = this.DBName;
    if (this.conName == "DRDB" + this.projectId) {
      this.hidedeployobjs = true;
    }
    else {
      this.hidedeployobjs = false;
    }
    this.buttondisable = false;
    const value = 'PRJDB';
    const value1 = 'DRDB';
    if (this.conName.search(value) != -1) {
      this.blobFiles = false;
      this.downloadFiles = true;
    } else if (this.conName.search(value1) != -1) {
      this.downloadFiles = false;
      this.blobFiles = true;
    }
  }
  /*-- deploy button --*/
  deployObject() {
    this.deployVariables = false;
    this.buttondisable = true;
  }

  closeDeploy() {
    this.deployVariables = true;
    this.buttondisable = false;
  }
  openPopup() {
    this.DBConnectionForm.reset();
    this.addupdateDetails = true;
  }

  /*-- Delete button --*/
  deleteConnection(id: string) {
    this.assessmentService.DeleteprjDbConnection(id).subscribe((data: fileStatus) => {
      if (data.message.includes("Connection Deleted")) {
        this.projectmigdetailselect();
        this.toast.success(data.message)
      }
      else {
        this.toast.error(data.message)
      }
    })
  }
}

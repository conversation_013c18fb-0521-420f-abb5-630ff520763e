load_module "modules/ngx_http_brotli_filter_module.so";
load_module "modules/ngx_http_brotli_static_module.so";

pid /tmp/nginx.pid;

worker_processes  auto;

events {
    worker_connections  512;
}

http {
    client_body_temp_path /tmp/client_temp;
    proxy_temp_path       /tmp/proxy_temp_path;
    fastcgi_temp_path     /tmp/fastcgi_temp;
    uwsgi_temp_path       /tmp/uwsgi_temp;
    scgi_temp_path        /tmp/scgi_temp;

    include       mime.types;
    keepalive_timeout  65;

    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_buffers 32 16k;
    gzip_proxied any;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;

    brotli on;
    brotli_buffers 32 8k;
    brotli_min_length 100;
    brotli_static on;
    brotli_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;

    # access_log /var/log/nginx/access.log;
    # error_log /var/log/nginx/error.log;

    access_log off;

    server {
        listen 4200;
        server_name  0.0.0.0;

        root   /usr/share/nginx/html;
        index  index.html index.htm;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location /healthz {
            add_header Content-Type "application/json" always;
            return 200 '{"status": "200"}';
        }

        location ~* \.(css|js|jpg|jpeg|png|gif|ico)$ {
            expires 30d;
            add_header Cache-Control "public, no-transform";
        }
    }
}
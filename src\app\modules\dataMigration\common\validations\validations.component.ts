import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GetFilesFromExpath, GetRequestData, SchemaSelect, Table, airflowValidationCommand, conList, deleteFile, documentsList, fileStatus, listSchema, operation, projectConRunTblInsert, redisCommand, setRedisCache, updateconnection } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

declare let $: any;
@Component({
  selector: 'app-validations',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './validations.component.html',
  styles: ``
})
export class ValidationsComponent {
  //validation form
  validationForm: any;
  value: any;
  airflowResponse: any

  //Source Connection
  selectedConname: any;
  ConsList: Table[] = [];
  conId: any;
  conName: any;
  prjdata: any;
  ref_spin: boolean = false;
  //Target Connection
  tgtlist: Table[] = [];
  tgtConId: any;
  tgtCon: any;
  objectType: any = [];

  //Schema Name
  schemaList: any;
  selectedItems = [];
  schemaName = [];
  selectedschemas: string = '';
  sourceId: any
  i: any;

  // Create Validation button
  createvalidationSpin: boolean = false;

  //hours
  hours: any = [{
    option: "ALL", value: "0"
  },
  {
    option: "1 hrs", value: "1"
  }, {
    option: "5 hrs", value: "5"
  }, {
    option: "10 hrs", value: "10"
  }, {
    option: "1 Day", value: "24"
  },
  ]
  hrs: any

  //reports pagination
  assessmentFiles: any;
  reportspageNumber: number = 1;
  pageNumberitems: number = 10;

  //download
  fileResponse: any;
  downloadSpin: boolean = false;
  loadingSpinners: { [key: string]: boolean } = {};

  //redis command
  redisResp: any;

  //delete 
  deleteResponse: any;


  //global variables
  projectId: any;

  //Save & Execute later
  runinfospin: boolean = false;


  //page refresh
  refSpin: boolean = false

  //operation
  executeForm = this.formBuilder.group({
    fileNameExecute: ['', [Validators.required]],
    operation: ['', [Validators.required]],
  });
  OpName: any;
  operation: operation[] = [];
  tabledata: any;

  //serch
  searchText: string = '';
  searchText1: string = '';


  //filename
  configFiles: any;
  selectedFile: any;

  //Execute
  dagres: any;
  executeSpin: boolean = false;
  runinfospins: boolean = false;
  migtypeid: string = ""
  pageName: string = ''
  schemalable: string = ""
  constructor(private titleService: Title, private toast: HotToastService, private datamigrationservice: DataMigrationService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }
  }


  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList();
    this.fetchAssessmentFiles()
    this.getOperationList();
    this.getreqTableData()
    // this.fetchValidationFiles()
    this.fetchAirflowFiles()
    this.refSpin = false
    this.validationForm = this.formBuilder.group({
      connectionType: ['', [Validators.required]],
      targetConnection: ['', [Validators.required]],
      fileName: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      tgtSchema: [''],
      operation: ['', [Validators.required]],
    });


  }
  /*For Source Connection*/
  tableHide: boolean = false
  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;
  }


  /*For SchemasList*/
  tgtschemaList: any = []
  getSchemasList(ConnectionId: any, value: string) {
    if (value == "S") {
      this.sourceId = ConnectionId
    }

    if (this.migtypeid == "31") {
      if (value == "S") {
        this.schemaList = []
        this.ConsList.filter((item: any) => {
          if (item.Connection_ID == ConnectionId) {
            this.schemaList.push({ schema_name: item.dbname });
          }
        })
      }
      else if (value == "T") {
        this.tgtschemaList = []
        this.tgtlist.filter((item: any) => {
          if (item.Connection_ID == ConnectionId) {
            this.tgtschemaList.push({ schema_name: item.dbname });
          }
        })
      }
    }
    else {
      const obj = {
        projectid: this.projectId,
        connectioId: ConnectionId,
      };
      this.datamigrationservice.SchemaListSelect(obj).subscribe((data: SchemaSelect) => {
        if (value == "S") {
          this.schemaList = data['Table1'];
        }
        else {
          this.tgtschemaList = data['Table1'];
          this.tgtschemaList = this.tgtschemaList.filter((item: any) => {
            return item.schemaname != "ALL"
          });
        }
      });
      this.ReadLocalJson()
      this.ReadLocalJson1()
    }
  }
  /*For SchemasList*/
  projectConRunTblInserts(data: any, value: any) {
    if (value == true) {
      data.status = 'P';
    } else {
      data.status = 'I';
    }
    const tableinsesrt = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(data.operation),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: '',
      objectname: '',
      objecttype: '',
    };
    if (this.tabledata.length != 0) {
      const res = this.tabledata.filter((item: any) => {
        return item.connection_id == this.conId && item.schemaname == this.schemaName.toString() && item.status == "I" && item.operation_name == "Code_Objects"
      })
      if (res.length >= 1) {
        this.runinfospins = false;
        $('#updateOrgModal').modal('hide');
        this.toast.error("Request already Added")
      }
      if (res.length == 0) {

        this.RedisCommand()
        this.getreqTableData();

      }
    }
    if (this.tabledata.length == 0) {
      this.RedisCommand()
      this.getreqTableData();

    }
  }
  tgtschemaname: string = ""
  selectSchema(item: any, value: string) {
    //console.log(item);
    this.tableHide = false
    if (value == "S") {
      if (item.length <= 1) {
        this.schemaName = item;
        this.tableHide = false;
        this.tgtschemaname = ""
      } else {
        this.tableHide = true;
        this.tgtschemaname = ""
      }
    } else {
      this.tgtschemaname = item
    }
  }

  //for target
  selectTgtId(value: any) {
    this.tgtConId = value
    //console.log(this.tgtConId)
    //this.validationForm.controls.schema.setValidators([Validators.required])
    this.validationForm.controls.schema.updateValueAndValidity()
  }

  //Create Validation
  triggerValidationCommand() {
    this.createvalidationSpin = true

    let obj: airflowValidationCommand = {
      projectId: this.projectId.toString(),
      srcId: this.sourceId,
      tgtId: this.tgtConId,
      task: "Validation",
      fileName: (<HTMLInputElement>document.getElementById("fname")).value,
      schema: this.schemaName.toString(),
      tgtschema: this.migtypeid == "40" ? this.schemaName.toString() : this.tgtschemaname
    }
    if (this.migtypeid == "35") {
      obj["tgtschema"] = "public";
      obj["schema"] = "public";
    }
    //console.log(obj)
    this.datamigrationservice.DataMigrationCommand(obj).subscribe((data: any) => {
      this.airflowResponse = data;
      this.createvalidationSpin = false
      this.toast.success(data.message)
      this.validationForm.reset();
    })
  }
  /*-Redis command-*/
  RedisCommand() {
    const obj: setRedisCache = {
      projectId: this.projectId.toString(),
      option: 2,
      schema: this.schemaName.toString(),
      connection: this.conName,
      Object: this.OpName,
      srcConId: this.conId,
      tgtConId: this.tgtConId,
      jobName: "qmig-convs"

    }
    this.datamigrationservice.setRedisCache(obj).subscribe((data: any) => {
      this.redisResp = data;
      this.runinfospins = false;
      $('#updateOrgModal').modal('hide');
      this.toast.success("Executed Successfully")
    })
  }

  /*-Operation-*/
  selectOperation(id: any) {
    this.OpName = id
  }
  /*-File Name-*/
  selectFname(value: any) {
    this.selectedFile = value.replace(".xlsx", "")
  }
  /*-Execute Dags-*/
  executeDags() {
    this.executeSpin = true
    // let obj = {
    //   operation: this.OpName,
    //   fileName: this.selectedFile
    // }
    const obj = {
      dagList: this.allIPs,
      // scn: this.scnValue,
      // type: this.initType
    }
    this.datamigrationservice.TriggerMultiDagsWithCDC(obj).subscribe((data: fileStatus) => {
      this.dagres = data;
      this.executeSpin = false
      this.toast.success(data.message)
    })
  }
  /*-Get File Names-*/
  fetchAssessmentFiles() {
    const path = "PRJ" + this.projectId + "SRC/Reports/validation_Reports/"
    this.datamigrationservice.GetCommonFilesFromDirectory(path).subscribe((data: any) => {
      this.assessmentFiles = data
    })
  }

  /*-Get Operation List-*/
  getOperationList() {
    if (this.migtypeid == "35") {
      this.operation = [{
        option: "Pre Validation", value: "Pre_Validation"
      },
      {
        option: "Post Validation", value: "Post_Validation"
      },
      ]
    }
    else if (this.migtypeid == "40") {
      this.operation = [{
        option: "Pre Validation", value: "Pre_Validation"
      },
      {
        option: "Post Validation", value: "Post_Validation"
      },
      ]
    }
    else {
      this.operation = [{
        option: "Pre Validation", value: "Pre_Validation"
      },
      {
        option: "Post Validation", value: "Post_Validation"
      },
      {
        option: "Complete Validation", value: "Complete_Validation"
      },
      ]
    }
  }

  /*-Table Data-*/
  z: any;
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: 'Assessment',
    };
    this.refSpin = true
    this.datamigrationservice.GetReqData(obj).subscribe((data: GetRequestData) => {
      this.tabledata = data['Table1'];
      if (this.tabledata != undefined) {
        for (this.z = 0; this.z < this.tabledata.length; this.z++) {
          for (let i = 0; i < this.ConsList?.length; i++) {
            if (
              this.tabledata[this.z].connection_id ==
              this.ConsList[i].Connection_ID
            ) {
              this.tabledata[this.z].conname = this.ConsList[i].conname;
            }
          }
        }
        //console.log(this.tabledata)
      }
      else {
        this.tabledata = []
      }
    });
  }

  fetchValidationFiles() {
    this.configFiles = []
    const path = "Config_Files/Data_Validation";// "AIRFLOW_FILES/Data_Migration_Validation_Reports/"
    this.refSpin = true;
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: GetFilesFromExpath) => {
      this.configFiles = data
      this.refSpin = false;
    })
  }
  fileSrc: any
  fetchSourceConfigFiles(conn: any) {
    this.configFiles = []
    this.refSpin = true
    this.fileSrc = conn
    const path = conn + "/Config_Files/Validation";
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      this.configFiles = data
      this.refSpin = false
    })
  }
  tgtcon: any
  fetchTargetConfigFiles(conn: any) {
    this.configFiles = []
    this.tgtcon = conn
    const path = this.fileSrc + "/" + this.tgtcon + "/Config_Files/Validation";
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      this.configFiles = data
      this.refSpin = false
    })
  }
  fileSrcExe: any
  configFilesExe: any = []
  fetchSourceConfigFiles1(conn: any) {
    this.configFilesExe = []
    this.refSpin = true
    this.fileSrcExe = conn
    const path = conn + "/Config_Files/Validation";
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      this.configFilesExe = data
      this.refSpin = false
    })
  }
  getreqfilename() {
    this.ref_spin = false;
    this.fetchSourceConfigFiles1(this.fileSrcExe);
    this.fetchTargetConfigFiles1(this.fileTgtExe);
  }
  fileTgtExe: any
  fetchTargetConfigFiles1(conn: any) {
    this.configFilesExe = []
    this.fileTgtExe = conn
    const path = this.fileSrcExe + "/" + this.fileTgtExe + "/Config_Files/Validation";
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      this.configFilesExe = data
      this.refSpin = false
    })
  }
  /*-get source and target connections-*/
  GetConsList() {
    this.datamigrationservice.getConList(this.projectId?.toString()).subscribe((data: conList) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      //console.log(this.ConsList)
      this.tgtlist = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      //console.log(this.tgtlist)
    });
  }
  /*-Form Validation-*/
  get f() {
    return this.validationForm.controls;

  }

  /*-Schema list-*/
  prjSchemaList() {
    const obj = {
      projectId: this.projectId.toString(),
      ConId: this.conId,
    };
    this.datamigrationservice.PrjSchemasListSelectData(obj).subscribe((data: any) => {
      this.schemaList = data['Table1'];
    });
  }
  openPopup() {
    this.validationForm.reset();
  }
  /*-Target connection-*/
  getTgtSchemasList(ConnectionId: any) {
    this.datamigrationservice.tgtSchemaSelect(ConnectionId).subscribe((data: any) => {
      this.schemaList = data;
    });
  }

  /*- reports and logs-*/
  projectConRunTblInsert(data: any, value: any) {
    if (value == true) {
      data.status = 'P';
    } else {
      data.status = 'I';
    }
    const tableinsesrt: projectConRunTblInsert = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(data.operation),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: '',
      objectname: '',
      objecttype: '',
    };
    this.runinfospin = true;
    this.datamigrationservice.projectConRunTblInsert(tableinsesrt).subscribe(
      (data: any) => {
        this.prjdata = data['jsonResponseData']['Table1'];
        if (data.message == 'Success') {
          this.runinfospin = false;
          $('#updateOrgModal').modal('hide');
          this.toast.success('Successfully Inserted');
          this.getreqTableData();
        }
      },
      () => {
        $('#updateOrgModal').modal('hide');
        this.toast.error('Something happened');
      }
    );
  }
  //download in reports
  downloadFile(fileInfo: any) {
    this.datamigrationservice.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(blob);
      document.body.appendChild(downloadLink);
      downloadLink.download = fileInfo.fileName;
      downloadLink.click();
      this.downloadSpin = false
    })
  }

  //delete button in reports
  deleteAirflowFiles(value: string) {
    this.datamigrationservice.deleteValidationFiles(value).subscribe((data: any) => {
      this.fetchValidationFiles()
      this.toast.success(data.message)
    })
  }
  onKey() {
    this.reportspageNumber = 1
    this.pageNumber1 = 1;
  }


  /*--- Airflow Reports Pagination   ---*/
  pageNumber: number = 1;
  pageNumber1: number = 1;

  pi: number = 10;
  pi1: number = 10;

  /*--- Airflow Reports   ---*/
  airflowFiles: any = [];
  fileStatus: any;

  /*--- Airflow Reports Download  ---*/
  spinDownload: boolean = false;
  /*--- Get Reports   ---*/
  airflowFiles1: any = []
  fetchAirflowFiles() {
    const path = "Validation_Reports";// 
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      this.airflowFiles1 = data
      this.fetchAirflowFiles1()
    })
  }
  fetchAirflowFiles1() {
    var tempfiles: any = []
    const path = "Summary_Validation_Reports";// "Summary_validation/"
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      tempfiles = data;
      if (tempfiles.length > 0) {
        tempfiles.forEach((itemk: any) => {
          this.airflowFiles1.push(itemk)
        })
      }
      this.airflowFiles1.sort((a: any, b: any) => {
        const dateA = new Date(a.created_dt).getTime();
        const dateB = new Date(b.created_dt).getTime();
        return dateB - dateA; // For descending order
      });
    })
  }


  /*--- Delete Project Reports   ---*/

  deleteFiles(path: string) {
    this.datamigrationservice.deleteFile(path).subscribe({
      next: (data: deleteFile) => {
        //console.log(data)
        this.fileStatus = data.message
        this.fetchAirflowFiles();
        this.toast.success(this.fileStatus)
      },
      error: (error) => {
        this.toast.error(error.message)
      },
    });
  }

  /*--- Apply Filter   ---*/

  applyFilter(filterValue: string) {
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
    this.airflowFiles1 = this.airflowFiles1.filter((el: any) => { return el.fileName.includes(filterValue) })
  }
  dagSelected: boolean = false
  selectDag(value: any) {
    this.dagSelected = true
    // this.selectedDag = value
  }
  reff_spin: boolean = false
  allIPs: any
  dagsInfo: any
  p: number = 1
  isDisableAllCheck: boolean = false
  fileName: any
  refreshdags() {
    if (this.selectedFile == undefined) {
      this.toast.error("Please select Config File")
    } else {
      this.dagsInfo = []
      this.allIPs = []
      this.p = 1
      this.reff_spin = true
      this.datamigrationservice.getValidationDags(this.selectedFile).subscribe((data: any) => {
        this.dagsInfo = data
        this.reff_spin = false
        this.dagsInfo.filter((item: any) => {
          item.isSelected = false
          return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
        })
      })

    }
  }
  masterSelected: boolean = false;
  getIPfromCheck: any;
  isCheckBoxSel: boolean = false;
  showCheckStatus: boolean = false
  checkboxselect($event: any, value: any): void {
    //console.log(value)
    this.getIPfromCheck = value;
    if ($event.target.checked) {
      this.isCheckBoxSel = true
      this.allIPs.length === null ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
      //console.log(this.allIPs.length)
      this.showCheckStatus = false
      const abc = this.dagsInfo.filter((item: any) => {
        return item.dag_id == value.toString()
      })
      //console.log(abc)

      var def = this.allIPs.filter((itemz: any) => {
        return itemz.dag_id == value.toString()
      })
      if (def.length == 0) {
        if (abc[0].isSelected == false) {
          abc[0].isSelected = true
        }
        this.allIPs.push(abc[0])
      }
      else {
        this.allIPs.filter((itema: any) => {
          if (itema.dag_id == value.toString()) {
            itema.isSelected = true
          }
        })
      }
      var check = this.allIPs.filter((itemz: any) => {
        return itemz.isSelected == true
      })
      if (check.length == this.dagsInfo.length) {
        this.showCheckStatus = true
      }
      //console.log(this.allIPs)
    } else {
      //let dag_index = this.dagsInfo.indexOf(value);
      // let i = 0
      this.dagsInfo.filter((item: any) => {
        if (item.dag_id == value) {
          item.isSelected = false
        }
        // i++
      })
      // let index = this.allIPs.indexOf(value);
      // this.dagsInfo[dag_index].isSelected = false
      // let y=0
      this.allIPs.filter((item1: any) => {
        if (item1.dag_id == value) {
          item1.isSelected = false
        }

        // y++
      })
      var check1 = this.allIPs.filter((itemz: any) => {
        return itemz.isSelected == true
      })
      if (check1.length == this.dagsInfo.length) {
        this.showCheckStatus = true
      }
      //this.allIPs[index].isSelected = false
      //const rem = this.allIPs.splice(index, 1);
      //console.log(rem)
      //console.log(this.allIPs)
      this.showCheckStatus = false
      this.allIPs.length === 0 ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
    }
  }
  selectAll($event: any) {
    this.showCheckStatus = $event.target.checked
    if ($event.target.checked) {
      this.allIPs = []
      this.dagsInfo.filter((item: any) => {
        if (item.isDagAvailable !== false) {
          item.isSelected = true
          this.allIPs.push(item)
        }
      })
      this.isCheckBoxSel = true
    } else {
      this.allIPs = []
      for (const element of this.dagsInfo) {
        element.isSelected = false
      }
      this.isCheckBoxSel = false
    }
    //console.log(this.allIPs)
  }
  DataMigrationNewCommand(fd: any) {
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnectionId: fd.connectionType,
      targetConnectionId: fd.targetConnection,
      schema: fd.schema.toString(),
      targetSchema: fd.tgtSchema,
      task: fd.operation,
      fileName: fd.fileName,
      jobName: "qmig-migrt",
      codeoption:true
    }
    this.datamigrationservice.DataMigrationNewCommand(obj).subscribe((data: any) => {
      this.toast.success(data.message);
    })
  }
  srcConId: any
  tgtConId1: any
  selectSrc(value: any) {
    this.srcConId = value
  }
  tgtPath: any
  foldersList: any = []
  selectTgt(value: any) {
    this.NewValidationFiles = []
    this.tgtConId1 = value
    this.tgtPath = this.srcConId + "/" + this.tgtConId1 + "/Validation_Reports"
    this.datamigrationservice.GetFilesFromExpath(this.tgtPath).subscribe((data: any) => {
      this.NewValidationFiles = data
    })
    // this.datamigrationservice.GetFolders(this.tgtPath).subscribe((data: any) => {
    //   data.forEach((item: any) => {
    //     var obj = {
    //       folderpath: item,
    //       folderName: item.split(/[/\\]/).pop()
    //     }
    //     this.foldersList.push(obj);
    //     this.foldersList = [...this.foldersList].reverse();
    //   })
    // })
  }
  fetchFolders(path: any) {
    this.datamigrationservice.GetFolders(path).subscribe((data: any) => {
      return data;
    })
  }
  NewValidationFiles: any;
  fetchNewValidationFiles(path: any) {
    this.NewValidationFiles = []
    // this.tgtPath = this.tgtPath + "/" + path
    path = path.replace("/mnt/extra", "")
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      this.NewValidationFiles = data
    })
  }
  getRunSpin11: boolean = false;
  fetchNewValidationFiles1() {
    this.getRunSpin11 = false;
    this.selectTgt(this.tgtConId1);
    this.selectSrc(this.srcConId)
  }
  getRunSpin: boolean = false;
  getUpdatedRunNumber() {
    this.fetchSourceConfigFiles(this.fileSrc);
    this.fetchTargetConfigFiles(this.tgtcon);
  }
  uploadSpin: boolean = false;
  readDataString: any = []
  selectedSourceConnection: string = '';
  selectedTargetConnection: string = '';
  onEditConfigClick() {
    this.openModal();
  }
  openModal() {
    $('#demo').offcanvas('show');
  }
  onSourceChange(value: any) {
    this.selectedSourceConnection = value;
  }
  onTargetChange(value: string) {
    this.selectedTargetConnection = value;
  }
  readData: any
  ReadLocalJson() {
    const path = "/mnt/extra/" + this.conId + "/Config_Files/chunk_configuration.json"
    this.updatePath = path;
    this.datamigrationservice.ReadLocalJson(path).subscribe((data: any) => {
      this.readData = data;
      this.readDataString = JSON.stringify(data, null, 0);
    })
  }
  ReadLocalJson1() {
    const path = "/mnt/extra/" + this.conId + "/" + this.tgtConId + "/Config_Files/chunk_configuration.json"
    this.updatePath = path;
    this.datamigrationservice.ReadLocalJson(path).subscribe((data: any) => {
      this.readData = data;
      this.readDataString = JSON.stringify(data, null, 0);
    })
  }
  //EditLocalJson
  editData: any
  updatePath: string = '';
  Update() {
    const parsedData = this.readDataString.replace(/[\r\n\/]/g, '')// JSON.stringify(this.readDataString);
    this.uploadSpin = true;
    let obj = {
      path: this.updatePath,
      jsonData: parsedData//.replace(/[\r\n\/]/g, '')
    }
    this.datamigrationservice.EditLocalJson(obj).subscribe({
      next: () => {
        this.uploadSpin = false;
        this.toast.success("Updated Successfully");
        $('#demo').offcanvas('hide');
      },
      error: (err) => {
        console.error('API Error:', err);
        this.uploadSpin = false;
        this.toast.error('Update failed. Please try again.');
      }
    });
  }
}

<div class="v-pageName">{{pageName}}</div>

<div class="body-main">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form" [formGroup]="sqlscriptForm">
                <div class="row">
                    <div class="col-md-4 ">
                        <div class="form-group">
                            <label class="form-label d-required">Function Name</label>
                            <input type="text" placeholder="Function Name" class="form-control" id="txtfunction"
                                name="txtfunction" [(ngModel)]="defaultfuncValue"
                                formControlName="scriptfunctionControl">
                            @if ( f.scriptfunctionControl.touched && f.scriptfunctionControl.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (f.scriptfunctionControl.errors?.['required']) { Function Name is required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label d-required">SQL Connection</label>
                            <select formControlName="ddlfunctionnames" [(ngModel)]="dbConnName" class="form-control"
                                #Myselect (change)="ddlcheckedChnaged(Myselect.value)"
                                aria-label="Default select example">
                                <option selected value="">Select Connection Type</option>
                                @for(list of targetData;track list;){
                                <option value="{{ list.Connection_ID }}">{{ list.conname}}</option>
                                }
                            </select>
                            @if ( f.ddlfunctionnames.touched && f.ddlfunctionnames.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (f.ddlfunctionnames.errors?.['required']) { SQL Connection is required }
                            </p>
                            }
                        </div>
                    </div>
                    <!-- <div class="col-md-4">
                                    <div class="form-group">
                                        <h6>Categoty</h6>
                                        <input type="text" placeholder="Category Name" class="form-control" id="txtcategory"
                                            name="txtcategory" [(ngModel)]="defaultCateValue"
                                            formControlName="scriptCategoryControl">
                                    </div>
                                </div> -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label d-required">Category</label>
                            <select class="form-control" #Myselect1 formControlName="scriptCategoryControl"
                                (change)="selectCategory(Myselect1.value)">
                                <option selected value="">All Categories</option>
                                @for(miglist of categoryData;track ConsList;){
                                <option value="{{ miglist.scriptcategory }}">{{ miglist.scriptcategory}}
                                </option>
                                }
                            </select>
                            @if ( f.scriptCategoryControl.touched && f.scriptCategoryControl.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (f.scriptCategoryControl.errors?.['required']) { Category is required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="form-label d-required">Script</label>
                            <textarea class="form-control" id="txtscript" name="txtscript" [(ngModel)]="defaultValue"
                                formControlName="scriptControl" placeholder="Script Function" rows="15"
                                cols="30"></textarea>
                            @if ( f.scriptControl.touched && f.scriptControl.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (f.scriptControl.errors?.['required']) { Script is required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-md-3 offset-md-9">
                        <button class="btn btn-sign w-100" data-bs-toggle="offcanvas"
                            (click)="InsertSqlScript(sqlscriptForm.value)" [disabled]="sqlscriptForm.invalid">
                            &nbsp;Save @if(add_spin){<app-spinner />}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
import { Component, KeyValueDiffers, signal } from '@angular/core';
import { HotToastService } from '@ngxpert/hot-toast';
import { PerofmanceTestingService } from '../../../../services/performanceTesting.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { interval } from 'rxjs';
import * as XLSX from 'xlsx';

@Component({
  selector: 'app-query-insights',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './query-insights.component.html',
  styles: ``
})


export class QueryInsightsComponent {
  pageName: string = '';
  projectId: string;
  selectedValues: string = '';
  perfForm: any;
  perfFormd: any;
  contype: string = "";
  tgtList: any;
  ConsList: any;
  ref_spin: boolean = false;
  ref_spin1: boolean = false;
  pageNumber: number = 1;
  tgtId: any;
  tgtValue: string = '';

  showall = false;
  userid = false;
  selectAll: boolean = false;
  queryid = false;
  calls = false;
  total_exec_time = false;
  min_exec_time = false;
  max_exec_time = false;
  shared_blks_read = false;
  shared_blks_hit = false;
  shared_blks_written = false;
  temp_blks_read = false;
  temp_blks_written = false;
  datachanges: any;
  sa = {
    query: false
  };
  selectedColumns: string = '';
  fromDate: any;
  toDate: any;
  selecteddate: string = 'null';
  selectedtodate: string = 'null';
  hideMultipleSelectionIndicator = signal(false)
  constructor(private toast: HotToastService, private performanceservices: PerofmanceTestingService, public formBuilder: FormBuilder, private route: ActivatedRoute, private router: Router,) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
  }
  ngOnInit(): void {
    this.pageName = this.route.snapshot.data['name'];
    this.perfForm = this.formBuilder.group({
      tgtconnection: ['', Validators.required],
      fromDate: [''],
      toDate: [''],
    });
    this.GetConsList();
  }
  checkboxes = [
    { id: 1, label: '*', value: 'ALL', checked: false },
    { id: 2, label: 'userid', value: 'UserId', checked: false },
    { id: 3, label: 'queryid', value: 'QueryId', checked: false },
    { id: 4, label: 'calls', value: 'Calls', checked: false },
    // { id: 5, label: 'rows',value:'Rows',  checked: false },
    { id: 6, label: 'total_exec_time', value: 'Total Exe Time', checked: false },
    { id: 7, label: 'min_exec_time', value: 'Min Exe Time', checked: false },
    { id: 8, label: 'max_exec_time', value: 'Max Exe Time', checked: false },
    // { id: 9, label: 'mean_exec_time', value:'Mean Exe Time', checked: false },
    { id: 10, label: 'shared_blks_read', value: 'Shared Bulks Read', checked: false },
    { id: 11, label: 'shared_blks_hit', value: 'Shared Bulks Hit', checked: false },
    { id: 12, label: 'shared_blks_written', value: 'Shared Bulks Written', checked: false },
    { id: 13, label: 'temp_blks_read', value: 'Temp Bulks Read', checked: false },
    { id: 14, label: 'temp_blks_written', value: 'Temp Bulks Written', checked: false },
    // { id: 15, label: 'blk_read_time', value:'Bulks Read Time', checked: false },
    // { id: 16, label: 'blk_write_time', value:'Bulks Write Time', checked: false },
    { id: 17, label: 'query', value: 'Query', checked: false },

  ];
  columns: any = [{ option: "UserId", value: "userid", checked: false },
  { option: "QueryId", value: "queryid", checked: false },
  { option: "Calls", value: "calls", checked: false },
  { option: "Total Exe Time", value: "total_exec_time", checked: false },
  { option: "Min Exe Time", value: "min_exec_time", checked: false },
  { option: "Max Exe Time", value: "max_exec_time", checked: false },
  { option: "Shared Blk Read", value: "shared_blks_read", checked: false },
  { option: "Shared Blk Hit", value: "shared_blks_hit", checked: false },
  { option: "Shared Blk Written", value: "shared_blks_written", checked: false },
  { option: "Temp Blk Read", value: "temp_blks_read", checked: false },
  { option: "Temp Blk Written", value: "temp_blks_written", checked: false },
  { option: "Query", value: "query", checked: false },
  ];

  get validate() {
    return this.perfForm.controls;
  }

  selectContype(value: string) {
    this.contype = value;
  }
  updateSelectedValues() {
    const checkedItems = this.checkboxes
      .filter(checkbox => checkbox.checked)
      .map(checkbox => checkbox.label);

    this.selectedValues = checkedItems.join(', ');
  }

  GetConsList() {
    this.performanceservices.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.ref_spin = false
    })
  }
  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
  }

  TableData: boolean = false;
  QueryColumn: any = []
  tableColumns: any = []
  NoData: boolean = false
  getapi() {
    const obj = {
      Conid: this.tgtId,
      column: this.selectedColumns,
      fromdate: this.selecteddate,
      todate: this.selectedtodate,
    }
    this.TableData = false;
    this.ref_spin1 = true;
    this.QueryColumn = [];
    this.tableColumns = []
    this.performanceservices.QueryInsightCoulumnByDate(obj).subscribe((data: any) => {
      this.QueryColumn = data;
      if (data.length != 0) {
        this.tableColumns = Object.keys(data[0]).filter(key =>
          this.columns.some((column: any) => column.value.replace(/_/g, '').toLowerCase() === key.toLowerCase() && column.checked));
        var dt = this.sortArrayByReference(this.QueryColumn, this.tableColumns);
        this.NoData = false
      }
      else {
        this.NoData = true
      }
      this.TableData = true;
      this.ref_spin1 = false;
    })
  }
  sortArrayByReference(data: any[][], sortOrder: number[]): any[][] {
    const orderMap = new Map(sortOrder.map((key, index) => [key, index]));
    return data.sort((a, b) => (orderMap.get(a[0]) ?? Infinity) - (orderMap.get(b[0]) ?? Infinity));
  }
  tempcolumns: any = []
  selectBoxesData(columnName: string, event: any) {
    if (columnName == "All") {
      this.tempcolumns = []
      if (event.target.checked) {
        this.columns.filter((element: any) => {
          this.tempcolumns.push(element.value);
        })
      }
      else {
        this.tempcolumns = [];
      }
    }
    else {
      this.columns.filter((element: any) => {
        if (element.value === columnName) {
          if (event.target.checked) {
            if (this.tempcolumns.length == 0) {
              this.tempcolumns.push(columnName);
            }
            else if (!this.tempcolumns.includes(element.value)) {
              this.tempcolumns.push(columnName);
            }
          }
          else {
            if (this.tempcolumns.includes(element.value)) {
              this.tempcolumns = this.tempcolumns.filter((item: any) => !element.value.includes(item));
            }
            //element.checked = false;
          }
        }
      });
    }
  }
  columnupdate() {
    // this.columns=this.tempcolumns;

    this.columns = this.columns.map((item: any) => ({
      ...item,
      checked: this.tempcolumns.some((s: any) => s === item.value)
    }));

    // this.columns.forEach((item:any)=>{
    //   this.tempcolumns.forEach((element:any)=>{
    //     if(item.value===element){
    //       item.checked=true;
    //     }
    //   })
    // })
    this.columns.forEach((element: any) => {
      if (element.checked) {
        this.updateSelectedColumns(element.value, element.checked);
      }
    })
    this.getapi();
  }
  columnSelected: boolean = false

  // Method to update the selected columns string when a checkbox changes
  updateSelectedColumns(columnName: string, isChecked: boolean): void {
    this.columns.forEach((element: any) => {
      if (element.value === columnName) {
        if (isChecked) {
          element.checked = true;
        }
        else {
          element.checked = false;
        }
      }
    });

    this.columnSelected = true
    // Ensure selectedColumns is initialized as a string if it's undefined
    if (!this.selectedColumns) {
      this.selectedColumns = '';
    }

    // If "All" is selected, set selectedColumns to '*' and disable further modifications
    if (columnName === 'All') {
      if (isChecked) {
        this.selectedColumns = '*';  // Set to '*' when "All" is checked
      } else {
        this.selectedColumns = '';
      }
      return;
    }
    if (isChecked) {
      if (!this.selectedColumns.includes(columnName)) {
        this.selectedColumns += (this.selectedColumns ? ',' : '') + columnName;
      }
    } else {
      const columnsArray = this.selectedColumns.split(',').filter(col => col !== columnName);
      this.selectedColumns = columnsArray.join(',');
    }
    if (this.selectedColumns && this.selectedColumns !== '*' && this.selectedColumns !== '') {
      this.selectedColumns = this.selectedColumns.replace(/,All/g, '');  // Remove "All" if it's in the list
    }
    // this.getapi();
  }

  onKey() {
    this.pageNumber = 1;
  }

  anyCheckboxSelected(): boolean {
    var selected: any;
    this.columns.forEach((element: any) => {
      if (element.checked) {
        selected = true
      }
    })
    return selected || this.selectAll;
    // return this.userid || this.queryid || this.calls || this.total_exec_time ||
    //   this.min_exec_time || this.max_exec_time || this.shared_blks_read ||
    //   this.shared_blks_hit || this.shared_blks_written || this.temp_blks_read ||
    //   this.temp_blks_written || this.sa.query || this.selectAll;
  }

  selectfromDate(fdate: any) {
    this.fromDate = fdate;
    this.selecteddate = this.fromDate;
  }

  selecttoDate(tdate: any) {
    this.toDate = tdate;
    this.selectedtodate = this.toDate;
  }
  toggleSelectAll() {
    // If "All" is checked, set all individual checkboxes to true
    if (this.selectAll) {
      this.columns.forEach((element: any) => {
        element.checked = true;
      });
      // this.userid = true;
      // this.queryid = true;
      // this.calls = true;
      // this.total_exec_time = true;
      // this.min_exec_time = true;
      // this.max_exec_time = true;
      // this.shared_blks_read = true;
      // this.shared_blks_hit = true;
      // this.shared_blks_written = true;
      // this.temp_blks_read = true;
      // this.temp_blks_written = true;
      // this.sa.query = true;
    } else {
      // If "All" is unchecked, set all individual checkboxes to false

      this.columns.forEach((element: any) => {
        element.checked = false;
      });
      // this.userid = false;
      // this.queryid = false;
      // this.calls = false;
      // this.total_exec_time = false;
      // this.min_exec_time = false;
      // this.max_exec_time = false;
      // this.shared_blks_read = false;
      // this.shared_blks_hit = false;
      // this.shared_blks_written = false;
      // this.temp_blks_read = false;
      // this.temp_blks_written = false;
      // this.sa.query = false;
    }
  }

  fileName3 = 'QueryInsightsExcel.csv';
  QueryColumnExcel: any = [];
  excelSpinn: any = false;

  exportexcelIndexTuningExcel(): void {
    this.QueryColumnExcel = []
    this.excelSpinn = true
    var test = this.QueryColumn;
    for (var el of test) {
      var newEle: any = {};
      newEle.userid = el.userid;
      newEle.queryid = el.queryid;
      newEle.calls = el.calls;
      newEle.rows = el.rows;
      newEle.totalexectime = el.totalexectime;
      newEle.minexectime = el.minexectime;
      newEle.maxexectime = el.maxexectime;
      newEle.meanexectime = el.meanexectime;
      newEle.sharedblksread = el.sharedblksread;
      newEle.sharedblkshit = el.sharedblkshit;
      newEle.sharedblkswritten = el.sharedblkswritten;
      newEle.tempblksread = el.tempblksread;
      newEle.tempblkswritten = el.tempblkswritten;
      newEle.blkreadtime = el.blkreadtime;
      newEle.blkwritetime = el.blkwritetime;
      newEle.query = el.query;
      this.QueryColumnExcel.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.QueryColumnExcel);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName3);
  }

}

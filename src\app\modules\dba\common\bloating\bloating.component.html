<div class="v-pageName">{{pageName}}</div>

<!--- connection details --->
<div class="body-main mt-4">

    <div class="accordion-body">
        <div class="qmig-card">
            <div class="qmig-card-body">
                <form class="form qmig-Form" [formGroup]="bloatingForm">
                    <div class="row">
                        <div class="col-md-3 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Connection
                                    Name</label>
                                <select class="form-select" formControlName="connection" #ser
                                    (change)="Getdbname(ser.value)" formControlName="connection">
                                    <option selected value="">Select a connection name</option>
                                    @for(ConsList1 of ConsList;track ConsList1; ){
                                    <option value="{{ ConsList1.Connection_ID }}"> {{ ConsList1.conname }}
                                    </option>
                                    }
                                </select>
                                @if ( f.connection.touched && f.connection.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.connection.errors.required) {connection name is Required }
                                </p>
                                }
                            </div>
                        </div>
                        <!-- Database details -->
                        <div class="col-md-3 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="Schema">Database</label>
                                <select class="form-select" formControlName="db" #db
                                    (change)="selectDB(db.value);getSchemas();" formControlName="db">
                                    <option selected value="">Select a database</option>
                                    @for(dbs of dblist;track dbs; ){
                                    <option value="{{ dbs.dbname }}"> {{ dbs.dbname }} </option>
                                    }
                                </select>
                                @if ( f.db.touched && f.db.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.db.errors.required) {Database is Required }
                                </p>
                                }
                            </div>
                        </div>

                        <!-- schema details -->
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label " for="Schema">Schema</label>
                                <!-- <select class="form-select">
                                                <option selected value=""></option>
                                                @for(sch of schemaList;track sch; ){
                                                <option value="{{ sch.schemaname }}"> {{ sch.schemaname }} </option>
                                                }
                                            </select> -->

                                <ng-select [items]="schemaList" [multiple]="true" bindLabel="schemaname" groupBy="type"
                                    [selectableGroup]="true" formControlName="schemaname"
                                    (change)="selectSchema(selectedItems)" [closeOnSelect]="false"
                                    bindValue="schemaname" [(ngModel)]="selectedItems">
                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                        <input id="item-{{index}}" type="checkbox" [ngModel]="item$.selected" [ngModelOptions]="{ standalone : true }"  />
                                        {{item.type | uppercase}}
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                            [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                    </ng-template>

                                </ng-select>
                                @if ( f.schemaname.touched && f.schemaname.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.schemaname.errors.required) {schemaname is Required }
                                </p>
                                }

                            </div>
                        </div>

                        <!-- search role button -->
                        <!-- <div class="col-md-4 col-xl-4"></div> -->
                        <div class="col-md-3 col-xl-3">
                            <div class="form-group mt-1">
                                <button class="btn btn-upload  mt-4" type="button" (click)="Getsearch()">
                                    <span class="mdi mdi-database-minus"></span>
                                    Fetch
                                    @if(spinner){<app-spinner-white />}
                                </button>
                            </div>
                        </div>

                        <div class="col-md-4 col-xl-4"></div>
                        <div class="col-md-10"></div>
                        <div class="col-md-2 text-right">
                            <!-- <button class="btn btn-upload w-100">
                                <i class="mdi mdi-download "></i> Download 
                                <i *ngIf="excelSpin"
                                    class="fa fa-spinner fa-spin"></i> 
                                </button> -->
                            @if(tabvalue=='analyze'){
                            <button class="btn btn-sign w-100" (click)="exportexcel1()">
                                <i class="mdi mdi-download "></i> Download <i *ngIf="excelSpin"
                                    class="fa fa-spinner fa-spin"></i> </button>
                            }@else{
                            <button class="btn btn-upload w-100" (click)="exportexcel()">
                                <i class="mdi mdi-download "></i> Download <i *ngIf="excelSpin"
                                    class="fa fa-spinner fa-spin"></i> </button>
                            }

                        </div>
                    </div>
                </form>

            </div>
            <div class="row" [hidden]="!tableHide">
                <div class="col-md-9">

                </div>
                <div class="container">
                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="analyze-tab" data-bs-toggle="tab" href="#analyze" role="tab"
                                aria-controls="analyze" aria-selected="false" (click)="setTabValue('analyze')">Table
                                Bloat </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="vacuum-tab" data-bs-toggle="tab" href="#vacuum" role="tab"
                                aria-controls="vacuum" aria-selected="true" (click)="setTabValue('vacuum')">Index
                                Bloat</a>
                        </li>
                    </ul>


                    <div class="container">
                        <div class="row">
                            <div class="col-md-9">
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade" id="vacuum" role="tabpanel" aria-labelledby="vacuum-tab">
                            <div class="container">
                                <div class="row" style="padding-top: 15px;">
                                    <div class="col-md-2">
                                        <!-- <button class="btn btn-upload w-100"  data-bs-toggle="offcanvas"
                                        data-bs-target="#VacuumDemo">Vacuum</button> -->
                                    </div>
                                    <div class="col-md-8"></div>


                                </div>
                            </div>
                            <div class="offcanvas offcanvas-end" tabindex="-1" id="VacuumDemo">
                                <div class="offcanvas-header">
                                    <!-- <h4 class="main_h">Vacuum</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button> -->
                                </div>

                            </div>

                            <table class="table">
                                <thead>
                                    <tr>
                                        <th style="width: 200px;">Schema Name</th>
                                        <th style="width: 200px;">Table Name</th>
                                        <th style="width: 200px;">Index Name</th>
                                        <th style="width: 200px;">Bloat percentage</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @for (documents of indexbloatdata | searchFilter: searchText1 |paginate:
                                    {itemsPerPage:50,currentPage: p2,id:'First'} ; track documents; let i =
                                    $index) {
                                    <tr>

                                        <!-- <td>{{ pi * (page1 - 1) + i + 1 }}</td> -->
                                        <td>{{documents.schemaname}}</td>
                                        <td>{{documents.tablename}}</td>
                                        <td>{{documents.indexname}}</td>
                                        <td>{{documents.bloatpercentage}}</td>


                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">No Results</p>
                                        </td>
                                    </tr>
                                    }


                                </tbody>
                            </table>

                        </div>
                        <div class="tab-pane fade show active" id="analyze" role="tabpanel"
                            aria-labelledby="analyze-tab">
                            <div class="container">
                                <div class="row" style="padding-top: 15px;">
                                    <div class="col-md-2">
                                        <!-- <button class="btn btn-upload w-100"
                                        data-bs-toggle="offcanvas" data-bs-target="#AnalyzeDemo">Analyze</button> -->
                                    </div>
                                    <div class="col-md-8"></div>

                                </div>
                            </div>

                            <!-- <div class="offcanvas offcanvas-end" tabindex="-1" id="AnalyzeDemo">
                            <div class="offcanvas-header">
                                <h4 class="main_h">Analyze</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
                            </div>                   
                        </div> -->

                            <table class="table">
                                <thead>
                                    <tr>
                                        <th style="width: 200px;">Schema Name</th>
                                        <th style="width: 200px;">Table Name</th>
                                        <th style="width: 200px;">bloat Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>


                                    @for (documents of tablebloatdata | searchFilter: searchText1 |paginate:
                                    {itemsPerPage:50,currentPage: p2,id:'First'} ; track documents; let i =
                                    $index) {
                                    <tr>

                                        <!-- <td>{{ pi * (page1 - 1) + i + 1 }}</td> -->
                                        <td>{{documents.schemaname}}</td>
                                        <td>{{documents.tablename}}</td>
                                        <td>{{documents.bloatpercentage}}</td>


                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">No Results</p>
                                        </td>
                                    </tr>
                                    }


                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>


            </div>
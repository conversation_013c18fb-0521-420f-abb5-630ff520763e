<div class="v-pageName">{{pageName}}</div>
<!-- TestCaseFile Upload -->
<div class="body-main">
    <div class="qmig-card">
        <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-heading">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                        Upload
                    </button>
                </h2>
                <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="TestCaseUploadForm" (ngSubmit)="uploadFile()">
                            <div class="row">
                                <div class="col-md-3 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Test Case
                                            Format(src /
                                            tgt)</label>
                                        <select class="form-select">
                                            <option selected value="">Select Test Case</option>
                                            @for(type of databases;track type; ){
                                            <option value="{{ type.value }}"> {{ type.option }} </option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label d-required">Upload File</label>
                                        <div class="custom-file-upload">
                                            <input class="form-control" type="file" id="formFile"
                                                (change)="onFileSelected($event)">
                                            <div class="file-upload-mask">
                                                @if (fileName == '') {
                                                <img src="assets/images/fileUpload.png" alt="img" />
                                                <p>Drag and drop deployment file here or click add deployment file </p>
                                                <button class="btn btn-upload"> Add File </button>
                                                }
                                                <div
                                                    class="d-flex justify-content-center align-items-center h-100 w-100">
                                                    <p> {{ fileName }} </p>
                                                </div>
                                            </div>
                                            @if ( f.file.touched && f.file.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.file.errors.required) { File is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 offset-md-6">
                                    <button class="btn btn-upload w-100" (click)="uploadBtn()"> <span
                                            class="mdi mdi-cloud-upload"></span> Upload</button>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-sign w-100"
                                        (click)="ValidateBtn(TestCaseUploadForm.value)"><span
                                            class="mdi mdi-file-check"></span> Validate</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="flush-headingOne">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                            Extraction Report
                        </button>
                    </h2>
                    <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                        data-bs-parent="#accordionFlushExample">
                        <div class="qmig-card">
                        <div class="qmig-card-body">
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>Sr.No</th>
                                        <th>FileName</th>
                                        <th>Upload Date/Time</th>
                                        <th>Upload Status</th>
                                        <th>Conversion Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (file of fileData | searchFilter: searchText | paginate: { itemsPerPage: 10,
                                    currentPage:
                                    pageNumber } ; track file; let i = $index) {
                                    <tr>
                                        <td>{{pageNumber*10+i+1-10}}</td>
                                        <td>{{file.filename|slice:0:20}}</td>
                                        <td>{{file.uploaddatetime}}</td>
                                        <td>{{file.uploadstatus}}</td>
                                        <td>{{file.conversionstatus}}</td>

                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <h4 class="text-center m-0 w-100">No Data Available</h4>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <!-- Pagination -->
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Update Button Click -->
            <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
                <div class="offcanvas-header">
                    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
                </div>
                <div class="offcanvas-body">
                    <form class="form qmig-Form" [formGroup]="uploadForm">
                        <div class="form-group">
                            <label for="formFile" class="form-label d-required">Upload File</label>
                            <div class="custom-file-upload">
                                <div class="file-upload-mask">
                                    @if (fileName == '') {
                                    <img src="assets/images/fileUpload.png" alt="img" />
                                    <p>Drag and drop deployment file here or click add deployment file </p>
                                    <button class="btn btn-upload w-100"> Add File </button>
                                    }
                                    <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                        <p> {{ fileName }} </p>
                                    </div>
                                </div>
                            </div>
                            @if ( f.file.touched && f.file.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (f.file.errors.required) { File is required }
                            </p>
                            }
                        </div>
                        <div class="form-group">
                            <button class="btn btn-upload w-100" (click)="uploadBtn()"> <span
                                    class="mdi mdi-file-plus"></span>
                                Upload File @if(uploadfileSpin){<app-spinner />}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RolesComponent } from './common/roles/roles.component';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { VacuumComponent } from './common/vacuum/vacuum.component';
import { UsersComponent } from './common/users/users.component';
import { PermissionsComponent } from './common/permissions/permissions.component';
import { BloatingComponent } from './common/bloating/bloating.component';
import { HealthcheckreportComponent } from './common/healthcheckreport/healthcheckreport.component';
import { ReportsComponent } from './common/reports/reports.component';
import { DbaMonitoringComponent } from './common/dba-monitoring/dba-monitoring.component';
import { AgentComponent } from './common/agent/agent.component';


const routes: Routes = [

  {path:'', component:LayoutComponent, children:[
    {path:'roles', component:RolesComponent, data: { name: 'roles'}},
    {path:'users', component:UsersComponent, data: { name: 'users'}},
    {path:'vacuum', component:VacuumComponent, data: {name: 'vacuumAnalyze'}},
    {path:'permissions', component:PermissionsComponent, data: {name: 'permissions'}},
    {path:'bloating', component:BloatingComponent, data: {name: 'bloating'}},
    {path:'Healthcheckreport', component:HealthcheckreportComponent, data: {name: 'Healthcheckreport'}},
    {path:'dba-monitoring', component:DbaMonitoringComponent, data: {name: 'DAB Monitor'}},
    {path:'reports', component:ReportsComponent, data: {name: 'Reports'}},
    {path:'agent', component: AgentComponent, data: { name: 'Agent' } }


  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DbaRoutingModule { }

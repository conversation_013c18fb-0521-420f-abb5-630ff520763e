import { Injectable } from '@angular/core';
import { environment } from '../environments/environment';
import { ApiService } from './api.service';
import { testingAPIConstant } from '../constant/testingAPIConstant';
import { ConnectionObjTable, FileInsertObj, GetDataByBatchIdTable, ProjectSrctgtconfSelect, RunInfoTable, SchemaTableList, TestcaseBatchidsTable, TestcycleReqObj, deletefiles, docList, downloadDocs, sourceConnectionName, test, uploadDocs, workLoadReqObj } from '../models/interfaces/types';
import { Observable } from 'rxjs';
import { AutomationAPIconstant } from '../constant/automationAPIConstant';

@Injectable({
  providedIn: 'root'
})
export class TestingService {

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl + 'Testing/'

  // uploadfileURL = this.apiURL + testingAPIConstant.UpLoadFilesToSubFolder
  // UpLoadFilesToSubFolder = (body: any): Observable<uploadDocs> => {
  //   return this.apiService.post(this.uploadfileURL, body);
  // }
  filesURL = this.apiURL + testingAPIConstant.Files
  Files = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.filesURL, body);
  }

  deletefilesURL = this.apiURL + testingAPIConstant.deleteFiles
  deleteFiles = (body: any): Observable<uploadDocs> => {
    return this.apiService.get(this.filesURL, body);
  }
  testcycleURL = this.apiURL + testingAPIConstant.selecttestcaseRange
  selecttestcaseRange = (body: any): Observable<any> => {
    return this.apiService.post(this.testcycleURL, body);
  }
  getFileURL = this.apiURL + testingAPIConstant.getFiles;
  getFiles = (body: string): Observable<any> => {
    return this.apiService.get(this.getFileURL + body)
  }
  docURL = this.apiURL + testingAPIConstant.uploadDocuments
  uploadDocuments = (body: any): Observable<any> => {
    return this.apiService.post(this.docURL, body);
  }
  FiledownloadURL = this.apiURL + testingAPIConstant.downloadFiles;
  downloadFiles = (body: string): Observable<downloadDocs> => {
    return this.apiService.get(this.FiledownloadURL + + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }

  deleteFilesURL = this.apiURL + testingAPIConstant.deleteFile;
  deleteFile = (body: string): Observable<deletefiles> => {
    return this.apiService.get(this.deleteFilesURL + body)
  }


  test_cycleURL = this.apiURL + testingAPIConstant.tgtCycltestcasehdrSelect
  tgtCycltestcasehdrSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.test_cycleURL, body);
  }
  ConlistURL = this.apiURL + testingAPIConstant.getConList
  getConList = (body: any): Observable<any> => {
    return this.apiService.get(this.ConlistURL + body);
  }
  //GetReqData
  GetReqDataUrl = this.apiURL + testingAPIConstant.GetReqData
  GetReqData = (body: any): Observable<any> => {
    return this.apiService.get(this.GetReqDataUrl + body.projectId + '&operationType=' + body.operationType);
  }

  DatabybatchidURL = this.apiURL + testingAPIConstant.GetDataByBatchId
  GetDataByBatchId = (body: any): Observable<GetDataByBatchIdTable> => {
    return this.apiService.post(this.DatabybatchidURL, body);
  }

  PrjSrcfeaturesselectURL = this.apiURL + testingAPIConstant.testPrjSrcfeaturesSelect
  testPrjSrcfeaturesSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjSrcfeaturesselectURL, body);
  }

  PrjfeaturesgroupleadselectURL = this.apiURL + testingAPIConstant.testPrjFeaturesGroupLeadSelect
  testPrjFeaturesGroupLeadSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjfeaturesgroupleadselectURL, body);
  }

  PrjobjectfeaturesselectURL = this.apiURL + testingAPIConstant.testPrjObjectsfeaturesSelect
  testPrjObjectsfeaturesSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjobjectfeaturesselectURL, body);
  }

  PrjSrcFeaturesEstTimeCountSelectURL = this.apiURL + testingAPIConstant.testPrjSrcFeaturesEstTimeCountSelect
  testPrjSrcFeaturesEstTimeCountSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjSrcFeaturesEstTimeCountSelectURL, body);
  }

  PrjSrcFeaturesassignURL = this.apiURL + testingAPIConstant.testPrjSrcfeaturesAssignSelect
  testPrjSrcfeaturesAssignSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjSrcFeaturesassignURL, body);
  }

  prjruninfoiterationURL = this.apiURL + testingAPIConstant.testPrjRuninfoIteration
  testPrjRuninfoIteration = (body: any): Observable<any> => {
    return this.apiService.get(this.prjruninfoiterationURL + body);
  }

  PrjFeatureGroupURL = this.apiURL + testingAPIConstant.testPrjFeatureGroupSelect
  testPrjFeatureGroupSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjFeatureGroupURL, body);
  }

  ExecutionURL = this.apiURL + testingAPIConstant.GetExecutionLog
  GetExecutionLog = (body: any): Observable<any> => {
    return this.apiService.post(this.ExecutionURL, body);
  }
  prjsrcobjectselectCDCURL = this.apiURL + testingAPIConstant.PrjsrcObjectsSelectCDC
  PrjsrcObjectsSelectCDC = (body: any): Observable<any> => {
    return this.apiService.post(this.prjsrcobjectselectCDCURL, body);
  }

  prjtgtobjectselectCDCURL = this.apiURL + testingAPIConstant.PrjtgtObjectsSelectCDC
  PrjtgtObjectsSelectCDC = (body: any): Observable<any> => {
    return this.apiService.post(this.prjtgtobjectselectCDCURL, body);
  }


  TestcasedetailupdateURL = this.apiURL + testingAPIConstant.TestCaseDetailUpdate
  TestCaseDetailUpdate = (body: any): Observable<any> => {
    return this.apiService.post(this.TestcasedetailupdateURL, body);
  }

  prjtgttestcasedtlURL = this.apiURL + testingAPIConstant.PrjTgttestcasedtlSelect
  PrjTgttestcasedtlSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.prjtgttestcasedtlURL, body);
  }

  TestCaseURL = this.apiURL + testingAPIConstant.getTestCaseBatches
  getTestCaseBatches = (body: any): Observable<TestcaseBatchidsTable> => {
    return this.apiService.get(this.TestCaseURL + body);
  }

  TestCaseUpdateURL = this.apiURL + testingAPIConstant.PrjTgtTestCaseUpdate
  PrjTgtTestCaseUpdate = (body: any): Observable<any> => {
    return this.apiService.post(this.TestCaseUpdateURL, body);
  }

  TestCasehdrURL = this.apiURL + testingAPIConstant.PrjTgttestcasehdrSelect
  PrjTgttestcasehdrSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.TestCasehdrURL, body);
  }

  getrunnumbersURL = this.apiURL + testingAPIConstant.getRunNumbers
  getRunNumbers = (body: any): Observable<any> => {
    return this.apiService.get(this.getrunnumbersURL + body);
  }


  schemalistselectURL = this.apiURL + testingAPIConstant.PrjSchemasListSelectData
  PrjSchemasListSelectData = (body: any): Observable<any> => {
    return this.apiService.get(this.schemalistselectURL + body);
  }



  srctgtconfselectURL = this.apiURL + testingAPIConstant.ProjectSrctgtconfSelect
  ProjectSrctgtconfSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.srctgtconfselectURL, body);
  }

  FilecontenttURL = this.apiURL + testingAPIConstant.GetFileContent
  GetfileContent = (body: any): Observable<any> => {
    return this.apiService.post(this.FilecontenttURL, body);
  }
  FilesfromdirURL = this.apiURL + testingAPIConstant.GetFilesFromDir
  GetFilesFromDir = (body: any): Observable<any> => {
    return this.apiService.get(this.FilesfromdirURL + body);
  }

  GetProjectsrctestcasehdrSelectURL = this.apiURL + testingAPIConstant.GetProjectsrctestcasehdrSelect
  GetProjectsrctestcasehdrSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.GetProjectsrctestcasehdrSelectURL, body);
  }

  testPrjSrcFeaturesAssignInsertURL = this.apiURL + testingAPIConstant.testPrjSrcFeaturesAssignInsert
  testPrjSrcFeaturesAssignInsert = (body: any): Observable<any> => {
    return this.apiService.post(this.testPrjSrcFeaturesAssignInsertURL, body);
  }


  testPrjSrcFeaturesInsertURL = this.apiURL + testingAPIConstant.testPrjSrcFeaturesInsert
  testPrjSrcFeaturesInsert = (body: any): Observable<any> => {
    return this.apiService.post(this.testPrjSrcFeaturesInsertURL, body);
  }
  testPrjObjectFeaturesInsertURL = this.apiURL + testingAPIConstant.testPrjObjectFeaturesInsert
  testPrjObjectFeaturesInsert = (body: any): Observable<any> => {
    return this.apiService.post(this.testPrjObjectFeaturesInsertURL, body);
  }
  testPrjObjectFeatureInsertNewURL = this.apiURL + testingAPIConstant.testPrjObjectFeatureInsertNew
  testPrjObjectFeatureInsertNew = (body: any): Observable<any> => {
    return this.apiService.post(this.testPrjObjectFeatureInsertNewURL, body);
  }

  GetProjectObjectFeaturesSelectCdcURL = this.apiURL + testingAPIConstant.GetProjectObjectFeaturesSelectCdc
  GetProjectObjectFeaturesSelectCdc = (body: any): Observable<any> => {
    return this.apiService.post(this.GetProjectObjectFeaturesSelectCdcURL, body);
  }

  GetProjectObjectFeaturesSelectCdcNewURL = this.apiURL + testingAPIConstant.GetProjectObjectFeaturesSelectCdcNew
  GetProjectObjectFeaturesSelectCdcNew = (body: any): Observable<any> => {
    return this.apiService.post(this.GetProjectObjectFeaturesSelectCdcNewURL, body);
  }

  ObjectFeaturesInserCdcURL = this.apiURL + testingAPIConstant.ObjectFeaturesInserCdc
  ObjectFeaturesInserCdc = (body: any): Observable<any> => {
    return this.apiService.post(this.ObjectFeaturesInserCdcURL, body);
  }
  ObjectFeaturesInserCdcNewURL = this.apiURL + testingAPIConstant.ObjectFeaturesInserCdcNew
  ObjectFeaturesInserCdcNew = (body: any): Observable<any> => {
    return this.apiService.post(this.ObjectFeaturesInserCdcURL, body);
  }
  CreateWorkItemURL = this.apiURL + testingAPIConstant.createWorkItem
  createWorkItem = (body: any): Observable<any> => {
    return this.apiService.post(this.CreateWorkItemURL, body);
  }

  testTgtCodeUpdateURL = this.apiURL + testingAPIConstant.testTgtCodeUpdateLocal
  testTgtCodeUpdate = (body: any): Observable<any> => {
    return this.apiService.post(this.testTgtCodeUpdateURL, body);
  }



  //Sukanya changes//

  /*Get API Calls start*/

  // getRunNumbersURL = this.apiURL + testingAPIConstant.getRunNumbers;
  // getRunNumbers = (body: any): Observable<any> => {
  //   return this.apiService.get(this.getRunNumbersURL + body);
  // };

  getFilesURL = this.apiURL + testingAPIConstant.TestCaseFileSelect;
  TestcaseFileSelect = (body: string): Observable<any> => {
    return this.apiService.get(this.getFilesURL + body)
  }

  getObjectTypesURL = this.apiURL + testingAPIConstant.getObjectTypes;
  getObjectTypes = (body: string): Observable<any> => {
    return this.apiService.get(this.getObjectTypesURL + body + '&objectgroupname=' + 'Storage_Objects')
  }
  getBatchIdsURL = this.apiURL + testingAPIConstant.getBatchIds;
  getBatchIds = (body: any): Observable<any> => {
    return this.apiService.get(this.getBatchIdsURL + body.projectId + '&schemaId=' + body.schemaId + '&moduleName=' + body.ModuleName)
  }

  getModulesURL = this.apiURL + testingAPIConstant.getModules;
  getModules = (body: string): Observable<any> => {
    return this.apiService.get(this.getModulesURL + body)
  }

  getTestcasesURL = this.apiURL + testingAPIConstant.getTestcases;
  getTestcases = (body: any): Observable<any> => {
    return this.apiService.get(this.getTestcasesURL + body)
  }

  secUserSelectByProjectURL = this.apiURL + testingAPIConstant.secUserSelectByProject
  secUserSelectByProject = (body: string): Observable<any> => {
    return this.apiService.get(this.secUserSelectByProjectURL + body);
  }

  getConnectionsListURL = this.apiURL + testingAPIConstant.GetConnectionList
  getConnectionsList = (body: string): Observable<ConnectionObjTable> => {
    return this.apiService.get(this.getConnectionsListURL + body);

  }

  // getSchemaListURL = this.apiURL + testingAPIConstant.getSchemaList
  // getSchemaList = (body: schemaListObj): Observable<SchemaTableList> => {
  //   return this.apiService.get(this.getSchemaListURL + body.projectId + '&ConId=' + body.connectionId);
  // }


  getSchemaListURL = this.apiURL + testingAPIConstant.getSchemaList
  getSchemaList = (body: string): Observable<SchemaTableList> => {
    return this.apiService.get(this.getSchemaListURL + body);
  }

  // getProjectSrctgtconfURL = this.apiURL + testingAPIConstant.getProjectSrctgtconfSelect;
  // getProjectSrctgtconfSelect = (body: any): Observable<sourceConnectionName> => {
  //   return this.apiService.get(this.getProjectSrctgtconfURL + body)
  // }
  getSecUserProjectMenuURL = this.apiURL + testingAPIConstant.getSecUserProjectMenu;
  getSecUserProjectMenu = (): Observable<sourceConnectionName> => {
    return this.apiService.get(this.getSecUserProjectMenuURL)
  }

  getInfraSelectURL = this.apiURL + testingAPIConstant.getInfraSelect;
  getInfraSelect = (body: any): Observable<sourceConnectionName> => {
    return this.apiService.get(this.getInfraSelectURL + body)
  }

  getTestRunInfoURL = this.apiURL + testingAPIConstant.getTestRunInfo;
  getTestRunInfo = (body: string): Observable<RunInfoTable> => {
    return this.apiService.get(this.getTestRunInfoURL + body)
  }

  getFilesFromDirectoryURL = this.apiURL + testingAPIConstant.getFilesFromDirectory;
  getFilesFromDirectory = (body: string): Observable<any> => {
    return this.apiService.get(this.getFilesFromDirectoryURL + body)
  }

  /* Get Api call end*/

  //  Post Api Call Start

  uploadCloudFilesURL = this.apiURL + testingAPIConstant.uploadCloudFiles;
  uploadCloudFiles = (body: FormData): Observable<string> => {
    return this.apiService.post(this.uploadCloudFilesURL, body);
  };


  getExecutionLogURL = this.apiURL + testingAPIConstant.getExecutionLog;
  getExecutionLog = (body: any): Observable<any> => {
    return this.apiService.post(this.getExecutionLogURL, body);
  };

  docusURL = this.apiURL + testingAPIConstant.prjSchemasListSelect
  prjSchemasListSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.docusURL, body);
  };

  TestcaseInsert = this.apiURL + testingAPIConstant.prjTestCaseFileInsert
  prjTestCaseFileInsert = (body: FileInsertObj): Observable<any> => {
    return this.apiService.post(this.TestcaseInsert, body);
  }
  testPrjSrcObjectsSelectURL = this.apiURL + testingAPIConstant.testPrjSrcObjectsSelect
  testPrjSrcObjectsSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.testPrjSrcObjectsSelectURL, body);
  }
  testCaseWrkLoadCmdURL = this.apiURL + testingAPIConstant.testCaseWrkLoadCmd
  testCaseWrkLoadCmd = (body: workLoadReqObj): Observable<string> => {
    return this.apiService.post(this.testCaseWrkLoadCmdURL, body);
  }

  testPrjTgtObjectsSelectURL = this.apiURL + testingAPIConstant.testPrjTgtObjectsSelect
  testPrjTgtObjectsSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.testPrjTgtObjectsSelectURL, body);
  }

  testPrjTgtDeploystatusSelectURL = this.apiURL + testingAPIConstant.testPrjTgtDeploystatusSelect
  testPrjTgtDeploystatusSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.testPrjTgtDeploystatusSelectURL, body);
  }


  prjsrcObjectsSelectCDCURL = this.apiURL + testingAPIConstant.testPrjSrcObjectsSelect
  prjsrcObjectsSelectCDC = (body: any): Observable<any> => {
    return this.apiService.post(this.prjsrcObjectsSelectCDCURL, body);
  }

  getTestcaseNamesURL = this.apiURL + testingAPIConstant.getTestcaseNames
  getTestcaseNames = (body: any): Observable<any> => {
    return this.apiService.post(this.getTestcaseNamesURL, body);
  }
  PrjtgtObjectsSelectCdcURL = this.apiURL + testingAPIConstant.prjtgtObjectsSelectCdc
  prjtgtObjectsSelectCdc = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjtgtObjectsSelectCdcURL, body);
  }

  //getRunno
  GetRunnoUrl = this.apiURL + testingAPIConstant.GetRunno
  GetRunno = (body: any): Observable<any> => {
    return this.apiService.get(this.GetRunnoUrl + body);
  };
  //GetIndividualLogs
  GetIndividualLogsUrl = this.apiURL + testingAPIConstant.GetIndividualLogs
  GetIndividualLogs = (body: any): Observable<any> => {
    return this.apiService.post(this.GetIndividualLogsUrl, body);
  };

  //downloadLargeFiles
  downloadLargeFilesUrl = this.apiURL + testingAPIConstant.downloadLargeFiles
  downloadLargeFiles = (body: any): Observable<any> => {
    return this.apiService.get(this.downloadLargeFilesUrl + encodeURIComponent(body), { responseType: 'blob' as 'json' });
  };
  /*--- Redis Command ---*/

  TestCycleCmdURL = this.apiURL + testingAPIConstant.TestCycleCmd
  TestCycleCmd = (body: TestcycleReqObj): Observable<string> => {
    return this.apiService.post(this.TestCycleCmdURL, body);
  }

  //deleteTableData
  deleteTableDataUrl = this.apiURL + testingAPIConstant.deleteTableData
  deleteTableData = (body: any): Observable<any> => {
    return this.apiService.get(this.deleteTableDataUrl + body.projectId + '&requestId=' + body.requestId);
  };

  webTestCommandURL = this.apiURL + testingAPIConstant.webTestCommand
  webTestCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.webTestCommandURL, body);
  }
  baselineCommandURL = this.apiURL + testingAPIConstant.baselineCommand
  baselineCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.baselineCommandURL, body);
  }
  SchemaListSelectURL = this.apiURL + testingAPIConstant.SchemaListSelect;
  SchemaListSelect = (body: any): Observable<any> => {
    return this.apiService.get(this.SchemaListSelectURL + body)
  }
  TablesSelectURL = this.apiURL + testingAPIConstant.TablesSelect;
  tablesSelect = (body: any): Observable<any> => {
    return this.apiService.get(this.TablesSelectURL + body.connectioId + "&schemaname=" + body.schemaname)
  }

  // testPrjSrcfeaturesSelectURL = this.apiURL + testingAPIConstant.testPrjSrcfeaturesSelect
  // testPrjSrcfeaturesSelect = (body: any): Observable<any> => {
  //   return this.apiService.post(this.testPrjSrcfeaturesSelectURL, body);
  // }

  // testPrjFeaturesGroupLeadSelectURL = this.apiURL + testingAPIConstant.testPrjSrcfeaturesSelect
  // testPrjFeaturesGroupLeadSelect = (body: any): Observable<any> => {
  //   return this.apiService.post(this.testPrjFeaturesGroupLeadSelectURL, body);
  // }

  //Post Api Call end



  /*--- Redis Command ---*/

  // insertSchemaURL = this.apiURL + testingAPIConstant.assessment.insertSchema

  // insertSchema = (body: insertSchema): Observable<insertSchema> => {
  //   return this.apiService.post(this.docusURL, body);
  // };
  TriggerbuttonURL = this.apiURL + AutomationAPIconstant.Triggerbutton;
  Triggerbutton = (body: any): Observable<any> => {
    return this.apiService.post(this.TriggerbuttonURL, body);
  }
  //reports
  reportsURL = this.apiURL + AutomationAPIconstant.reports;
  automationreports = (): Observable<any> => {
    return this.apiService.get(this.reportsURL )
  }
  getFilesFromDirectoryURL1 = this.apiURL + testingAPIConstant.getFilesFromDirectory1;
  getFilesFromDirectory1 = (body:any): Observable<any> => {
    return this.apiService.get(this.getFilesFromDirectoryURL1 + body.path)
  }
  UploadDagFilesURL = this.apiURL + testingAPIConstant.uploadCloudFiles1;
  UploadDagFiles = (body: any): Observable<any> => {
    return this.apiService.post(this.UploadDagFilesURL, body);
  }
}




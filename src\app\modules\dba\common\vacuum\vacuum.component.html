<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card mt-3">

    <div class="body-main mt-4">
        <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-heading">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                        Vacuum & Analyze
                    </button>
                </h2>
                <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="vacuumForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Connection
                                            Name</label>
                                        <select class="form-select" formControlName="connection" #con
                                            (change)=" Getdbname(con.value)">

                                            <option selected value="">Select a connection name</option>
                                            @for(ser of ConsList;track ser; ){
                                            <option value="{{ ser.Connection_ID }}">
                                                {{ ser.conname }}
                                            </option>
                                            }
                                        </select>
                                        @if ( f.connection.touched && f.connection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.connection.errors.required) {Connection name is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- select database -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Database</label>
                                        <select class="form-select" #db1 formControlName="database"
                                            (change)="selectDB(db1.value);getSchemas()">
                                            <option selected value="">Select a Database</option>
                                            @for(dbs of dblist;track dbs; ){
                                            <option value="{{ dbs.dbname }}"> {{ dbs.dbname }} </option>
                                            }
                                        </select>
                                        @if ( f.Dbase.touched && f.Dbase.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.Dbase.errors.required) {Database name is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- schema details -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Schema</label>

                                        <ng-select [items]="schemaList" [multiple]="true" bindLabel="schemaname"
                                            groupBy="type" [selectableGroup]="true" formControlName="schemaname"
                                            (change)="selectschema(selectedItems);" [(ngModel)]="selectedItems"
                                            [closeOnSelect]="false" bindValue="schemaname">
                                            <ng-template ng-optgroup-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [ngModel]="item$.selected" />
                                                {{item.type | uppercase}}
                                            </ng-template>
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" />
                                                {{item.schemaname}}
                                            </ng-template>

                                        </ng-select>
                                        @if ( f.schemaname.touched && f.schemaname.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.schemaname.errors.required) {Schemaname is required }
                                        </p>
                                        }

                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group mt-1">
                                        <button type="button" class="btn btn-upload w-100 mt-4"
                                            (click)="GetFetchVacuumData();GetFetchAnalyzeData()"> <span
                                                class="mdi mdi-database-minus"></span> Fetch </button>

                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="qmig-card mt-3" [hidden]="!tableHide">
                        <div class="qmig-card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="qmigTabs vacuum-tab mt-1">
                                        <ul class="nav nav-pills">
                                            <li class="nav-item">
                                                <a class="nav-link active" id="analyze-tab" data-bs-toggle="tab"
                                                    href="#analyze" role="tab" aria-controls="analyze"
                                                    aria-selected="false" (click)="setTabValue('analyze')">Analyze</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" id="vacuum-tab" data-bs-toggle="tab" href="#vacuum"
                                                    role="tab" aria-controls="vacuum" aria-selected="true"
                                                    (click)="setTabValue('vacuum')">Vacuum</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            @if(tabValue == 'analyze'){
                                            <button class="btn btn-upload w-100" [disabled]="!itemSelectedAnalyze"
                                                data-bs-toggle="offcanvas"
                                                data-bs-target="#AnalyzeDemo">Analyze</button>
                                            }@else{
                                            <button class="btn btn-upload w-100" [disabled]="!itemSelecte"
                                                data-bs-toggle="offcanvas" data-bs-target="#VacuumDemo">Vacuum</button>
                                            }
                                        </div>
                                        <div class="col-md-6">
                                            @if(tabValue == 'analyze'){
                                            <button class="btn btn-sign w-100" (click)="exportexcelAnalyze()">
                                                <i class="mdi mdi-download "></i> Download <i *ngIf="excelSpin"
                                                    class="fa fa-spinner fa-spin"></i> </button>
                                            }@else{
                                            <button class="btn btn-upload w-100" (click)="exportexcelVacuum()">
                                                <i class="mdi mdi-download "></i> Download <i *ngIf="excelSpin"
                                                    class="fa fa-spinner fa-spin"></i> </button>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-content mt-3" id="myTabContent">
                                <div class="tab-pane fade" id="vacuum" role="tabpanel" aria-labelledby="vacuum-tab">
                                    <div class="offcanvas offcanvas-end" tabindex="-1" id="VacuumDemo">
                                        <div class="offcanvas-header">
                                            <h4 class="main_h">Vacuum</h4>
                                            <button type="button" class="btn-close"
                                                data-bs-dismiss="offcanvas"></button>
                                        </div>
                                        <div class="offcanvas-body">
                                            <form class="form qmig-Form">
                                                <div class="form-group">
                                                    <label for="formFile"></label>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio"
                                                            name="exampleRadios" id="exampleRadios1" value="option1">
                                                        <label class="form-check-label" for="exampleRadios1">
                                                            Immediate Vacuum
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio"
                                                            (click)="checkboxselect($event)" name="exampleRadios"
                                                            id="exampleRadios2" value="option2">
                                                        <label class="form-check-label" for="exampleRadios2">
                                                            Schedule
                                                        </label>
                                                    </div>
                                                    <div>
                                                        @if(isCheckBoxSel) {
                                                        <input class="form-select" type="datetime-local">

                                                        }
                                                    </div>
                                                    <div class="text-center">
                                                        <button class="btn btn-upload w-100 mt-4" (click)="dbago()">
                                                            <span></span> GO </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>

                                    <table class="table table-hover qmig-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 50px;">
                                                    <div class="form-check m-0">
                                                        <label class="form-check-label" style="padding-left: 7px;">
                                                            <input type="checkbox" (click)="selectAllVacuum($event)"
                                                                class="form-check-input" [checked]="showCheckStatus"
                                                                [disabled]="isDisableAllCheck"> <i
                                                                class="input-helper"></i>
                                                        </label>
                                                    </div>
                                                </th>

                                                <th style="width: 200px;">Schema</th>
                                                <th style="width: 200px;">Table</th>
                                                <th style="width: 200px;">Row Count</th>
                                                <th style="width: 200px;">Dead Tuples</th>
                                                <th style="width: 200px;">Last Vaccum</th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                            @for (documents of vacuumsearchList | searchFilter: searchText| paginate:{
                                            itemsPerPage: 10,
                                            currentPage: p3,
                                            id:'Second' };track documents; let i = $index) {
                                            <tr>
                                                <td>
                                                    <label class="form-check-label">
                                                        <input type="checkbox" class="form-check-input"
                                                            (click)="onitemSelect($event,documents.sno);vacuumHide($event)"
                                                            [checked]='documents.isSelected'>
                                                        <i class="input-helper"></i>
                                                    </label>
                                                </td>
                                                <td>{{ documents.schemaname }}</td>
                                                <td>{{ documents.relname }}</td>
                                                <td>{{ documents.live_tup }}</td>
                                                <td>{{ documents.dead_tup }}</td>
                                                <td>{{ documents.last_vacuum }}</td>
                                            </tr>
                                            }
                                        </tbody>
                                    </table>
                                    <div class="custom_pagination">
                                        <pagination-controls (pageChange)="p3 = $event"
                                            id="Second"></pagination-controls>
                                    </div>
                                </div>
                                <div class="tab-pane fade show active" id="analyze" role="tabpanel"
                                    aria-labelledby="analyze-tab">
                                    <div class="offcanvas offcanvas-end" tabindex="-1" id="AnalyzeDemo">
                                        <div class="offcanvas-header">
                                            <h4 class="main_h">Analyze</h4>
                                            <button type="button" class="btn-close"
                                                data-bs-dismiss="offcanvas"></button>
                                        </div>
                                        <div class="offcanvas-body">
                                            <form class="form qmig-Form">
                                                <div class="form-group">
                                                    <label for="formFile"></label>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio"
                                                            name="exampleRadios" id="exampleRadios1" value="option1">
                                                        <label class="form-check-label" for="exampleRadios1">
                                                            Immediate Analyze
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio"
                                                            (click)="checkboxselect($event)" name="exampleRadios"
                                                            id="exampleRadios2" value="option2">
                                                        <label class="form-check-label" for="exampleRadios2">
                                                            Schedule
                                                        </label>
                                                    </div>
                                                    <div>
                                                        @if(isCheckBoxSel) {
                                                        <input class="form-select" type="datetime-local">
                                                        }
                                                    </div>
                                                    <div class="form-group mt-1">
                                                        <button class="btn btn-upload w-100 mt-4" (click)="dbago()">
                                                            <span></span> GO </button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>

                                    <table class="table table-hover qmig-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 50px;">
                                                    <div class="form-check m-0">
                                                        <label class="form-check-label" style="padding-left: 7px;">
                                                            <input type="checkbox" (click)="selectAll($event)"
                                                                class="form-check-input" [checked]="showCheckStatus"
                                                                [disabled]="isDisableAllCheck"> <i
                                                                class="input-helper"></i>
                                                        </label>
                                                    </div>
                                                </th>

                                                <th style="width: 200px;">Schema</th>
                                                <th style="width: 200px;">Table</th>
                                                <th style="width: 200px;">Row Count</th>
                                                <th style="width: 200px;">Dead Tuples</th>
                                                <th style="width: 200px;">Last Analyze</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for(analyze of analyzesearchlist | searchFilter: searchText| paginate:{
                                            itemsPerPage: 10,
                                            currentPage: p2,
                                            id:'First' }; track analyze; let i = $index) {
                                            <tr>
                                                <td>
                                                    <label class="form-check-label">
                                                        <input type="checkbox" class="form-check-input"
                                                            (click)="onitemSelectAnalyze($event,analyze.sno);analyzeHide($event)"
                                                            [checked]='analyze.isSelected'>
                                                        <i class="input-helper"></i>
                                                    </label>
                                                </td>
                                                <td>{{ analyze.schemaname }}</td>
                                                <td>{{ analyze.relname }}</td>
                                                <td>{{ analyze.live_tup }}</td>
                                                <td>{{ analyze.dead_tup }}</td>
                                                <td>{{ analyze.last_analyze }}</td>
                                            </tr>
                                            }
                                        </tbody>
                                    </table>

                                    <div class="custom_pagination">
                                        <pagination-controls (pageChange)="p2 = $event"
                                            id="First"></pagination-controls>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!tableHide">
                        <div class="custom_search cs-r my-3 me-3">
                            <span class="mdi mdi-magnify"></span>
                            <input type="text" placeholder="Search Status" class="form-control" [(ngModel)]="searchText"
                                (keyup)="onKey()">
                        </div>
                    </div>
                </div>

            </div>

            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingTest">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                        Vacuum / Analyze Status
                    </button>
                </h2>
                <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-6">
                                <div class="col-12 col-sm-6 col-md-6">
                                    <h3 class="main_h py-4 ps-3">vacuum analyze status
                                        <button class="btn btn-sync" (click)="getreqTableData()">
                                            @if(ref_spin){
                                            <app-spinner />
                                            }@else{
                                                <span class="mdi mdi-refresh"></span>
                                            }
                                        </button>
                                    </h3>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-6">
                                <div class="custom_search cs-r my-3 me-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Status" class="form-control"
                                        [(ngModel)]="datachange1" (keyup)="onKey()">
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>

                                        <th>Connection</th>
                                        <th>Schema </th>
                                        <th>Table </th>
                                        <th>Operation </th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Status</th>
                                        <th>Delete</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(con of tabledata| searchFilter: datachange1| paginate:{ itemsPerPage: piA,
                                    currentPage:
                                    p1,
                                    id:'second' };
                                    track con;) {
                                    <tr>
                                        <!-- <td>{{con.run_id}}</td> -->
                                        <td>{{ con.conname }}</td>
                                        <td>{{ con.schemaname }}</td>
                                        <td>{{con.objectname}}</td>
                                        <td>{{ con.remarks }}</td>
                                        <td>{{con.created_dt}}</td>
                                        <td>{{con.updated_dt}}</td>
                                        <td>
                                            @switch (con.status) {
                                            @case ('I') {
                                            <span>Initialize</span>
                                            }
                                            @case ('P') {
                                            <span>Pending</span>
                                            }
                                            @default {
                                            <span>Completed</span>
                                            }
                                            }
                                        </td>
                                        <td>
                                            <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                                <span class="mdi mdi-delete btn-icon-prepend"></span>
                                            </button>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p1 = $event" id="second"></pagination-controls>
                        </div>
                    </div>

                </div>

            </div>
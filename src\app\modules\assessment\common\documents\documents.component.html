<div class="v-pageName">{{pageName}}</div>
<!--- Documents List --->
<div class="body-main">
    <div class="row">
        <div class="col-12 col-md-6 col-xl-6">
            <div class="custom_search ms-0">
                <span class="mdi mdi-magnify"></span>
                <input type="text" placeholder="Search Document" class="form-control" [(ngModel)]="searchText"
                    (keyup)="onKey()">
            </div>
        </div>
        <div class="col-6 col-md-6 col-xl-6">
            <div class="body-header-button">
                <button class="btn btn-upload" data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                        class="mdi mdi-file-document-plus-outline"></span> Upload Document</button>
            </div>
        </div>
    </div>
    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="row">
                @for (documents of projectDocuments | searchFilter: searchText | paginate: { itemsPerPage: 10,
                currentPage: pageNumber } ; track documents; let i = $index) {
                <div class="col-12 col-sm-6 col-md-5 col-lg-5 col-xl-4 col-xxl-3 px-1">
                    <div class="documentCard" [ngClass]="documents.fileName | slice:-4">
                        <div class="docCicon">
                            @switch (documents.extension) {
                            @case ("xlsx") {
                            <span class="mdi mdi-file-excel-box"></span>
                            }
                            @case (".vtt") {
                            <span class="mdi mdi-text-box"></span>
                            }
                            @case ("docx") {
                            <span class="mdi mdi-text-box"></span>
                            }
                            @case (".csv") {
                            <span class="mdi mdi-file-excel-box"></span>
                            }
                            @case (".pdf") {
                            <span class="mdi mdi-file-pdf-box"></span>
                            }
                            @default {
                            <span class="mdi mdi-text-box"></span>
                            }
                            }
                        </div>
                        <div class="docCContent">
                            <h4 data-bs-toggle="tooltip" data-bs-placement="top" title={{documents.fileName}}>
                                {{documents.fileName | slice:0:14 }} @if(documents.fileName.length > 15){ ... }</h4>
                            <p>Project Docs</p>
                        </div>
                        <div class="docCAction">
                            <button class="btn btn-download" (click)="downloadFile(documents)">
                                <span class="mdi mdi-cloud-download-outline"></span>
                            </button>
                            <button class="btn btn-delete" (click)="deleteFiles(documents.filePath)">
                                <span class="mdi mdi-delete"></span>
                            </button>
                        </div>
                    </div>
                </div>
                } @empty {
                <p class="text-center m-0 w-100">Empty list of Documents</p>
                }
            </div>
        </div>
        @if(projectDocuments?.length > 0){
        <div class="custom_pagination mt-0">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
        }
    </div>
</div>


<!--- Upload Documnets  --->
<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
    <div class="offcanvas-header">
        <h4 class="main_h">Upload Document</h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="uploadForm" (ngSubmit)="uploadFile()">
            <div class="form-group">
                <label for="formFile" class="form-label d-required">Upload Deployment File </label>
                <div class="custom-file-upload">
                    <input class="form-control" type="file" id="formFile" formControlName="file"
                        (change)="onFileSelected($event)">
                    <div class="file-upload-mask">
                        @if (fileName == '') {
                        <img src="assets/images/fileUpload.png" alt="img" />
                        <p>Drag and drop deployment file here or click add deployment file </p>
                        <button class="btn btn-upload"> Add File </button>
                        }
                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                            <p> {{ fileName }} </p>
                        </div>
                    </div>
                </div>
                @if ( f.file.touched && f.file.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (f.file.errors.required) { File is required }
                </p>
                }
            </div>
            <div class="form-group">
                <button class="btn btn-upload w-100" [disabled]="uploadForm.invalid"> <span
                        class="mdi mdi-file-plus"></span> Upload File @if(uploadfileSpin){<app-spinner />}</button>
            </div>
        </form>
    </div>
</div>
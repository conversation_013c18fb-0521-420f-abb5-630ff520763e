<div class="v-pageName">{{pageName}}</div>

<!-- UserTestCase UI -->
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Test Cases
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="TestCaseUploadForm">
                        <div class="row">
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Module Name</label>
                                    <select class="form-select" formControlName="moduleName" #Modules
                                        (change)="getBatchIds(Modules.value) ">
                                        <option selected value="">Select Module Name</option>
                                        @for(module of moduleResponse;track module;){
                                        <option value="{{module.module_name}}"> {{module.module_name}} </option>
                                        }
                                    </select>
                                    @if ( f.moduleName.touched && f.moduleName.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.moduleName.errors.required) {Module Name is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Batch ID</label>
                                    <select class="form-select" formControlName="batchId" #batch
                                        (change)="getTestCaseIds(batch.value)">
                                        <option selected value="">Select Batch ID</option>
                                        @for(bid of batchIdResponse;track bid; ){
                                        <option value="{{bid.batch_id}}"> {{bid.batch_id}} </option>
                                        }
                                    </select>
                                    @if ( f.batchId.touched && f.batchId.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.batchId.errors.required) {Batch Id is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- TestCase Dropdown -->
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Testcase ID</label>
                                    <select class="form-select" formControlName="testcaseId" #tid
                                        (change)="SelectTid(tid.value)">
                                        <option selected value="">Select Testcase ID</option>
                                        @for(tcr of testCaseResponse;track tcr; ){
                                        <option value="{{tcr.user_testcase_id}}"> {{tcr.user_testcase_id}} </option>
                                        }
                                    </select>
                                    @if ( f.testcaseId.touched && f.testcaseId.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.testcaseId.errors.required) {Testcase Id is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-md-3 mt-4">
                                <div class="form-group">
                                    <button class="btn btn-upload w-100" (click)="getTestCaseNames()"
                                        [disabled]="TestCaseUploadForm.invalid"><span
                                            class="mdi mdi-file-plus"></span>Get Test Case</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    User Test Case Files
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Sr.No</th>
                                    <th>Code Object</th>
                                    <th>Object Signature </th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (tcnresponse of testCaseNamesResponse | searchFilter: searchText |
                                paginate: { itemsPerPage: 10, currentPage:
                                pageNumber } ; track tcnresponse; let i = $index) {
                                <tr>
                                    <td>{{pageNumber*10+i+1-10}}</td>
                                    <td>{{tcnresponse.code_object_name|slice:0:20}}</td>
                                    <td>{{tcnresponse.object_signature}}</td>

                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center">No Data Available</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                    </div>
                </div>
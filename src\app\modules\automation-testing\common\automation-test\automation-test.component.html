<div class="v-pageName">{{pageName}}</div>

<!---Reports & Logs---->
<div class="qmig-card">
    <!-- <h3 class="main_h px-3 pt-3">Extraction </h3> -->
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Upload Scripts
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <!---Code Extraction List --->
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="getForm" (ngSubmit)="uploadFile()">
                            <div class="form-group">
                                <label for="formFile" class="form-label d-required">Upload Script </label>
                                <div class="custom-file-upload">
                                    <input class="form-control" formControlName="file"
                                        (change)="onFileSelected1($event)" type="file" id="formFile" #fileInput>
                                    <div class="file-upload-mask">
                                        @if (fileName == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drag and drop Script file here or click and add Script file </p>
                                        <button class="btn btn-upload"> Add Script </button>
                                        }@else{
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ fileName }} </p>
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-9">

                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100"> <span class="mdi mdi-file-plus"></span>
                                            Upload@if(getSpin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingone">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseone" aria-expanded="false" aria-controls="flush-collapse">
                    Paramter files Upload
                </button>
            </h2>
            <div id="flush-collapseone" class="accordion-collapse collapse" aria-labelledby="flush-headingone"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="getForm" (ngSubmit)="uploadFile()">
                            <div class="form-group">
                                <label for="formFile" class="form-label d-required">Upload Parameters File </label>
                                <div class="custom-file-upload">
                                    <input class="form-control" formControlName="document"
                                        (change)="onFileSelected1($event)" type="file" id="formFile">
                                    <div class="file-upload-mask">
                                        @if (fileName == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drag and drop parameters files here or click and add parameters files </p>
                                        <button class="btn btn-upload"> Add Parameters files </button>
                                        }@else{
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ fileName }} </p>
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-9">

                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100"> <span class="mdi mdi-file-plus"></span>
                                            Upload@if(getSpin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div> -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Trigger Test Automation Suite
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <!--- codeexcration List --->
                <div class="qmig-card">
                    <div class="row">
                        <div class="col-md-3 mt-3">
                            <div class="form-group">
                                <!-- <label class="form-label d-required" for="targtetConnection">Operation</label> -->
                                <select class="form-select" [(ngModel)]="selectedParamFile" #MyOp>
                                    <option selected disabled>File Names</option>
                                    @for(filelist of operation;track filelist;){
                                    <option value="{{filelist.fileName }}">
                                        {{ filelist.fileName }}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 mt-3">
                            <div class="form-group">
                                <button class="btn btn-upload w-100" (click)="TriggerButton()"> <span
                                        class="mdi mdi-file-plus"></span>
                                    Trigger@if(getSpin){<app-spinner />}</button>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-6">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                    <h3 class="main_h px-3 pb-1 pt-3">List of Reports</h3>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.NO</th>
                                    <th>File Name</th>
                                    <th>Created Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (Reports of automationReports | searchFilter: searchText | paginate: {
                                itemsPerPage: pi,
                                currentPage: pageNumber } ; track Reports; let i = $index) {
                                <tr>
                                    <td>{{pageNumber*pi+i+1-pi}}</td>
                                    <td>{{Reports.fileName }}</td>
                                    <td>{{Reports.created_dt }}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(Reports)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                        <button (click)="deleteTableDatas(Reports.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
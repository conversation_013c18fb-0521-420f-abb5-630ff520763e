import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { environment } from '../environments/environment';
import { AutomationAPIconstant } from '../constant/automationAPIConstant';

@Injectable({
    providedIn: 'root'
})
export class AssessmentService {

    constructor(private readonly apiService: ApiService) { }

    apiURL = environment.serviceUrl + 'Testing/'

    //Triggerbutton
    TriggerbuttonURL = this.apiURL + AutomationAPIconstant.Triggerbutton;
    Triggerbutton = (body: any): Observable<any> => {
        return this.apiService.post(this.TriggerbuttonURL, body);
    }
}
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DocumentsComponent } from './common/documents/documents.component';
import { ConnectionsComponent } from './common/connections/connections.component';
import { ExtractSchemasComponent } from './common/extract-schemas/extract-schemas.component';
import { ProjectDatabasesComponent } from './common/project-databases/project-databases.component';
import { CodeExtractionComponent } from './common/code-extraction/code-extraction.component';
import { AssessmentComponent } from './common/assessment/assessment.component';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { ServerscanComponent } from './common/serverscan/serverscan.component';
import { DBScanComponent } from './common/dbscan/dbscan.component';
import { ReportsUploadComponent } from './common/reports-upload/reports-upload.component';
import { environment } from '../../environments/environment';
import { E2eMigrationComponent } from './common/e2e-migration/e2e-migration.component';
import { ServerParametersComponent } from './common/server-parameters/server-parameters.component';
import { HelpUsComponent } from './common/help-us/help-us.component';

const routes: Routes = [
  {path:'', component:LayoutComponent, children:[
    {path: '', redirectTo: 'codeExtraction', pathMatch: 'full' },
    {path:'documents', component:DocumentsComponent,  data: { name: 'Documents' }},
    {path:'connections', component:ConnectionsComponent,data: { name: 'Connections' }},
    {path:'extractSchemas', component:ExtractSchemasComponent,data: { name: 'Schema Management' }},
    {path:'projectDatabases', component:ProjectDatabasesComponent,data: { name: 'Project Databases' }},
    {path:'codeExtraction', component:CodeExtractionComponent, data: { name: 'Extraction' }},
    {path:'assessment', component:AssessmentComponent,  data: { name: 'Code Scan' }},
    {path:'serverScan', component:ServerscanComponent,  data: { name: 'Server Scan' }},
    {path:'DBScan', component:DBScanComponent,  data: { name: 'DB Scan' }},
    {path:'e2emigration', component:E2eMigrationComponent,  data: { name: 'E2E Migration' }},
    {path:'reportsUpload', component:ReportsUploadComponent,  data: { name: 'Reports' }},
    {path:'serverParameters', component:ServerParametersComponent,  data: { name: 'Server Parameters' }},
    {path:'help', component:HelpUsComponent,  data: { name: 'Help' }},
    ...(environment.migrationPath == 'ora2pg' ? [{ path: 'awrScan', loadComponent: () => import('./ora2pg/awr-scan/awr-scan.component').then(m => m.AwrScanComponent), data: { name: 'AWR Scan' } }]: []),
    ...(environment.migrationPath == 'maria2mysql' ? [{ path: 'e2e-migration', loadComponent: () => import('./maria2mysql/e2e-migration/e2e-migration.component').then(m => m.E2eMigrationComponent), data: { name: 'E2E Migration' } }]: [])
  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AssessmentRoutingModule { }

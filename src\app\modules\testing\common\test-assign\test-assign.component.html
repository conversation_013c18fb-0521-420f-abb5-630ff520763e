<div class="v-pageName">{{pageName}}</div>
 
    <!-- TestAssign  UI -->
    <div class="body-main">
        <div class="qmig-card">
            <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="flush-heading">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                            Connections
                        </button>
                    </h2>
                    <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                    data-bs-parent="#accordionFlushExample">
            <div class="qmig-card-body">
                <form class="form qmig-Form" [formGroup]="caAssignForm">
                    <div class="row">
                        <!-- RunNo DropDown -->
                        <div class="col-md-3 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Run No</label>
                                <select class="form-select" [(ngModel)]="IterationNo"
                                    [ngModelOptions]="{standalone: true}" #myIteration
                                    (change)="selectedIteration(myIteration.value)">
                                    <option selected value="">Select Run No</option>
                                    @for(list of runInfo;track list; ){
                                    <option value="{{list.dbschema}}"> {{ list.dbschema}} </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <!-- TestCase DroDown -->
                        <div class="col-md-3 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="Schema">Test Case</label>
                                <ng-select [items]="srcObjectsSelectData" [multiple]="true" bindLabel="objectname"
                                    placeholder='Select Code Object' [(ngModel)]="objectname" [selectableGroup]="true"
                                    [closeOnSelect]="false" bindValue="src_id" [ngModelOptions]="{standalone: true}"
                                    #slctCodeObj (change)="selectCodeObject($event)">
 
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                            [ngModelOptions]="{ standalone : true }" /> {{item.objectname}}
                                    </ng-template>
                                </ng-select>
                            </div>
                        </div>
                        <!-- MapppedCount Input -->
                        <div class="col-md-3 col-xs-3">
                            <div class="form-group">
                                <label for="name" class="form-label d-required">Mapped Count </label>
                                <input class="form-control" id="objTextBox" placeholder="Count" value="{{featureCount}}"
                                    disabled type="text">
                            </div>
                        </div>
                    </div>
 
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <div class="accordion accordion-flush qmig-accordion" id="accordionFlushExample">
                                <!-- GroupMap Section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="flush-headingOne">
                                        <button class="accordion-button collapsed main_h" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#flush-collapseOne"
                                            aria-expanded="false" aria-controls="flush-collapseOne">
                                            Group Map
                                        </button>
                                    </h2>
                                    <div id="flush-collapseOne" class="accordion-collapse collapse"
                                        aria-labelledby="flush-headingOne" data-bs-parent="#accordionFlushExample">
                                        <div class="row">
 
                                            <div class="col-md-3 col-xl-3">
                                                <div class="form-group">
                                                    <label class="form-label d-required"
                                                        for="targtetConnection">Group</label>
                                                    <select class="form-select" #featureSelect
                                                        (change)="featureSelection(featureSelect.value)">
                                                        <option selected value="">Select Group</option>
                                                        @for(flist of featureData;track flist; ){
                                                        <option value="{{ flist.feature_name }}"> {{ flist.feature_name
                                                            }} </option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                            <!-- <div class="col-md-3 col-xl-3" [hidden]="hideTextBox">
                                                <div class="form-group">
                                                    <label class="form-label d-required"
                                                        for="targtetConnection">Group</label>
                                                    <input class="form-control" id="ftrTextBox"
                                                        formControlName="featureTxtBox" type="text">
                                                </div>
                                            </div> -->
                                            <div class="col-md-3 col-xl-3" [hidden]="hideTextBox">
                                                <div class="form-group">
                                                    <label class="d-block" for="name">Feature</label>
                                                    <input class="form-control" id="ftrTextBox"
                                                        aria-label="Default select example" type="text">
                                                </div>
                                            </div>
 
                                            <div class="col-md-3 col-xs-3">
                                                <div class="form-group">
                                                    <label for="formFile" class="form-label d-required">Mapped Count
                                                    </label>
                                                    <input class="form-control" id="objTextBox" placeholder="Count"
                                                        disabled value="{{featureCount}}" type="text">
                                                </div>
                                            </div>
 
                                            <div class="col-md-3 col-xs-3">
                                                <div class="form-group">
                                                    <label for="formFile" class="form-label d-required">Estimation  Minutes </label>
                                                    <input class="form-control" id="estimtion" placeholder="Minutes"
                                                        disabled value="{{estHours}}" type="text">
                                                </div>
                                            </div>
 
                                            <div class="col-md-3 col-xl-3">
                                                <div class="body-header-button">
                                                    <button class="btn btn-upload" (click)="mapToFeatureBtn(caAssignForm.value)">Map To Group</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Assign Group Section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="flush-headingtwo">
                                        <button class="accordion-button collapsed main_h" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#flush-collapsetwo"
                                            aria-expanded="false" aria-controls="flush-collapsetwo">
                                            Assign Group
                                        </button>
                                    </h2>
                                    <div id="flush-collapsetwo" class="accordion-collapse collapse"
                                        aria-labelledby="flush-headingtwo" data-bs-parent="#accordionFlushExample1">
                                        <div class="row">
 
                                            <div class="col-md-3 col-xl-3">
                                                <div class="form-group">
                                                    <label class="form-label d-required" for="targtetConnection">Lead
                                                        Name</label>
                                                    <select class="form-select"  #leadName (change)="selectGroupId(leadName.value)">
                                                        <option selected value="">Select Lead Name</option>
                                                        @for(userList of groupleadData;track userList; ){
                                                        <option value="{{ userList.resource_nm }}"> {{
                                                            userList.resource_nm }} </option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-xl-3">
                                                <div class="form-group">
                                                    <label class="form-label d-required" for="targtetConnection">Assign
                                                        To</label>
                                                    <select class="form-select" #resourcenm (change)="selectrmName(resourcenm.value)">
                                                        <option selected value="">Select Assign To</option>
                                                        @for(list of userGroupData;track list; ){
                                                        <option value="{{ list.resource_nm }}"> {{ list.resource_nm }}
                                                        </option>
                                                        }
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-xl-3">
                                                <div class="body-header-button">
                                                    <button class="btn btn-upload" (click)="AssignToBtn(caAssignForm.value)">Assign To</button>
                                                </div>
                                            </div>
 
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        </div>
            </div>
        </div>
    
 
        <div>
 
 
            @if(ObjectSelected){
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="caAssignForm">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="col-md-12 pl-0 mb-3 pb-1">
                                    <div class="form-check form-check-flat form-check-primary">
                                        <label class="form-check-label">
                                            <input type="checkbox"  #chck
                                                (click)="checkboxselect($event)"
                                                class="form-check-input"> <i class="input-helper"></i><i
                                                class="input-helper"></i> Show statement number
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group" >
                                    <h6>Source Code</h6>
                                    <textarea class="form-control" id="w3review" name="w3review"
                                        placeholder="Source Code" value="{{sourceCode}}" rows="35"
                                        cols="30" disabled></textarea>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="ca_tabs">
                                    <button (click)="TgtCodeShows()" class="btn btn-upload"
                                        [ngClass]="TgtUpdateTgt?'':'active'">OriginalTargetCode
                                    </button>
                                    <button (click)="TgtCodeShow()" class="btn btn-upload"
                                        [ngClass]="!TgtUpdateTgt?'':'active'">UpdateTargetCode</button>
                                </div>
                                <div [hidden]="TgtUpdateTgt" class="form-group">
                                    <h6>Original TargetCode</h6>
                                    <textarea class="form-control" id="orginaltgtcode" name="w3review"
                                        placeholder="Original TargetCode" value="{{tgtobjCode}}"
                                        rows="35" cols="30" disabled></textarea>
                                </div>
                                <div [hidden]="!TgtUpdateTgt" class="form-group">
                                    <h6>Updated TargetCode</h6>
                                    <textarea class="form-control" id="w3review" name="w3review"
                                        placeholder="Updated TgtCode" value="{{tgtobjUpdatedCode}}"
                                        rows="35" cols="30"></textarea>
                                </div>
                            </div>
 
                            <div class="col-md-12 row justify-content-end">
                                <div class="">
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group pt-3">
                                        <div
                                            class="form-check form-check-flat form-check-primary mt-m10">
                                            <label class="form-check-label">
                                                <input type="checkbox"
                                                    (click)="overRideChkBox($event)"
                                                    class="form-check-input" #checkbox> <i
                                                    class="input-helper"></i><i
                                                    class="input-helper"></i> Over Ride
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-primary btn-icon-text w-100"
                                        (click)="saveBtn()"><i class="fas fa-save"></i>
                                        &nbsp;Save </button>
                                </div>
                            </div>
                            <hr>
                        </div>
                    </form>
                    </div>
                </div>
            }
            <div class="accordion accordion-flush qmig-accordion" id="accordionFlushExample3">
                <!-- Deployment Log Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="flush-headingthree">
                        <button class="accordion-button collapsed main_h" type="button"
                            data-bs-toggle="collapse" data-bs-target="#flush-collapsethree"
                            aria-expanded="false" aria-controls="flush-collapsethree">
                            Deployment Log
                        </button>
                    </h2>
                    <div id="flush-collapsethree" class="accordion-collapse collapse"
                        aria-labelledby="flush-headingthree" data-bs-parent="#accordionFlushExample3">
                        <div class="row">
 
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Deployment
                                        time
                                        stamp</label>
                                    <select class="form-select" (change)="selectTimestampp($event)">
                                        <option selected value="">Select Deploy time stamp</option>
                                        @for(stamp of statusSelectData;track stamp; ){
                                        <option value="{{ stamp.activitytime }}"> {{ stamp.activitytime }}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3 col-xs-3">
                                <div class="form-group">
                                    <label for="name" class="form-label d-required">Deployed Status </label>
                                    <input class="form-control" id="objTextBox" placeholder="Status"
                                        value="{{deploystatuss}}" disabled type="text">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-xl-3">
                                <h6>Status Message</h6>
                                <textarea class="form-control" id="w3review" name="w3review"
                                    placeholder="Status Message" value="{{statusMessages}}" rows="10"
                                    cols="40"></textarea>
                            </div>
                            <div class="col-md-3 col-xl-3">
                                <h6>Observation Text</h6>
                                <textarea class="form-control" id="w3review" name="w3review"
                                    placeholder="Observation Text" value="{{observationss}}" rows="10"
                                    cols="40"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="accordion accordion-flush qmig-accordion" id="accordionFlushExample4">
                <!-- Execution Log Section -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="flush-headingfour">
                        <button class="accordion-button collapsed main_h" type="button"
                            data-bs-toggle="collapse" data-bs-target="#flush-collapsefour"
                            aria-expanded="false" aria-controls="flush-collapsefour">
                            Execution Log
                        </button>
                    </h2>
                    <div id="flush-collapsefour" class="accordion-collapse collapse"
                        aria-labelledby="flush-headingfour" data-bs-parent="#accordionFlushExample4">
                        <div class="form-group">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>Time Stanp</th>
                                        <th>Object Sign</th>
                                        <th>Status Message</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (list of logDetails | searchFilter: searchText | paginate: {
                                    itemsPerPage: 10, currentPage: pageNumber } ; track list; let i =
                                    $index) {
                                    <tr>
                                        <td>{{list.activitytime}}
                                        </td>
                                        <td>{{list.object_signature}}</td>
                                        <td>{{list.statusmessage}}</td>
                                    </tr>
                                    }
 
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@media screen and (max-width: 480px) {
  .close-sidebar {
    display: none;
  }

  /*--- Login Page ----*/
  .login-logo {
    display: block;
    margin-bottom: 10px;

    img {
      max-width: 200px;
    }
  }

  .login-from-content {
    min-width: max-content;
    max-width: 20rem;
    width: 20rem;
  }

  .login-image {
    display: none;
  }

  section.login-page {
    padding: 0;
  }

  .login-from-content h2 {
    margin-bottom: 5px;
  }

  .login-from-content p {
    font-size: 0.9em;
  }

  .login-form {
    height: calc(100vh - 0rem);
  }

  .res-navLogo,
  .res-navHelp {
    display: block;
  }

  .sideNav-flex {
    height: auto;

    >.help_center {
      display: none;
    }
  }

  /*--- Main pages ---*/
  .main_body {
    padding-left: 1rem !important;
    padding-top: 85px;
  }

  header {
    display: none;
  }

  section.main-body {
    padding-top: 0rem;
  }

  .sideNav {
    position: fixed;
    height: auto;
    background: rgba(231, 231, 232, 0.45);
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
    margin: 10px;
    max-width: 96%;
    width: 100%;
    z-index: 9999999;
  }

  .res-nav {
    display: flex;
  }

  .hCard {
    padding: 5px;
  }

  .hCard:not(:nth-child(3)) {
    display: none;
  }

  .hCard span.mdi {
    padding: 0px 5px;
    font-size: 16px;
    line-height: 28px;
  }

  .custom_search input {
    min-width: 300px;
  }

  .custom_search {
    margin-top: 10px;
    width: 100%;
  }

  .navbar-vertical-content {
    background: none;
    margin-top: 20px;
  }

  .help_center {
    position: relative;
    max-width: 100%;
    height: 220px;
    margin: auto;
    overflow: hidden;
    border-radius: 1rem;
    h3 {
      color: #fff;
      text-align: center;
      padding: 30px 0px;
    }  
    &:before {
      content: '';
      background: #8d48eb;
      width: 100%;
      position: absolute;
      height: 100%;
      top: 0;
      z-index: -1;
    }
    &:after {
      content: '';
      position: absolute;
      width: 140px;
      height: 140px;
      background: #9f64ff;
      right: -30px;
      z-index: -1;
      bottom: -30px;
      border-radius: 5rem;
    }
  }

  .help_cd {
    left: 0;
    width: 100%;
    text-align: center;
  }

  .logo {
    max-width: 150px;
  }

  /*--- Table ---*/
  .qmig-table td:last-child {
    min-width: 90px;
  }

  .pageName i {
    font-size: 1.5em;
  }

  .pageName {
    padding: 12px 20px;
    font-size: 0.75em;
  }

  .btn-upload {
    font-size: 0.75em;
    padding: 12px 20px;
  }

  .rs-or2 {
    order: 2
  }

  .offcanvas.offcanvas-end {
    margin-top: 75px;
  }
  .res-d-none{
    display: none;
  }
  .res-d-block{
    display: block;
  }
  .mt-r-3{    
    margin-top: 15px;
  }
  .login-from-content .custom-file-upload {
    max-width: 320px;
  }
}

@media (min-width:481px) and (max-width:768px) {
  .close-sidebar {
    display: none;
  }

  /*--- Login Page ----*/
  .login-logo {
    display: block;
    margin-bottom: 10px;

    img {
      max-width: 200px;
    }
  }

  .login-from-content {
    min-width: max-content;
    max-width: 20rem;
    width: 20rem;
  }

  .login-image {
    display: none;
  }

  section.login-page {
    padding: 0;
  }

  .login-from-content h2 {
    margin-bottom: 5px;
  }

  .login-from-content p {
    font-size: 0.9em;
  }

  .login-form {
    height: calc(100vh - 0rem);
  }

  .res-navLogo,
  .res-navHelp {
    display: block;
  }

  .sideNav-flex {
    height: auto;

    >.help_center {
      display: none;
    }
  }

  /*--- Main pages ---*/
  .main_body {
    padding-left: 1rem !important;
    padding-top: 85px;
  }

  header {
    display: none;
  }

  section.main-body {
    padding-top: 0rem;
  }

  .sideNav {
    position: fixed;
    height: auto;
    background: rgba(231, 231, 232, 0.45);
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
    margin: 10px;
    max-width: 96%;
    width: 100%;
    z-index: 9999999;
  }

  .res-nav {
    display: flex;
  }

  .hCard {
    padding: 5px;
  }

  .hCard:not(:nth-child(3)) {
    display: none;
  }

  .hCard span.mdi {
    padding: 0px 5px;
    font-size: 16px;
    line-height: 28px;
  }

  .custom_search input {
    min-width: 300px;
  }

  .custom_search {
    margin-top: 10px;
    width: 100%;
  }

  .navbar-vertical-content {
    background: none;
    margin-top: 20px;
  }

  .help_center {
    position: relative;
    max-width: 100%;
    height: 220px;
    margin: auto;
    overflow: hidden;
    border-radius: 1rem;
    h3 {
      color: #fff;
      text-align: center;
      padding: 30px 0px;
    }  
    &:before {
      content: '';
      background: #8d48eb;
      width: 100%;
      position: absolute;
      height: 100%;
      top: 0;
      z-index: -1;
    }
    &:after {
      content: '';
      position: absolute;
      width: 140px;
      height: 140px;
      background: #9f64ff;
      right: -30px;
      z-index: -1;
      bottom: -30px;
      border-radius: 5rem;
    }
  }
  .help_cd {
    left: 0;
    width: 100%;
    text-align: center;
  }
  .logo {
    max-width: 150px;
  }

  /*--- Documents ---*/  
  .rs-or2 {
    order: 2
  }

  .offcanvas.offcanvas-end {
    margin-top: 75px;
  }
  .docCicon {
    font-size: 1.25em;
    max-width: 40px;
    height: 40px;
    line-height: 19px;
}
.docCAction {
  width: 27%;
}
.res-d-none{
  display: none;
}
.res-d-block{
  display: block;
}
.mt-r-3{    
  margin-top: 15px;
}
}

@media (min-width:769px) and (max-width:990px) {
  .close-sidebar {
    display: none;
  }

  /*--- Login Page ----*/
  .login-logo {
    display: block;
    margin-bottom: 10px;

    img {
      max-width: 200px;
    }
  }

  .login-from-content {
    min-width: max-content;
    max-width: 20rem;
    width: 20rem;
  }

  .login-image {
    display: none;
  }

  section.login-page {
    padding: 0;
  }

  .login-from-content h2 {
    margin-bottom: 5px;
  }

  .login-from-content p {
    font-size: 0.9em;
  }

  .login-form {
    height: calc(100vh - 0rem);
  }

  .res-navLogo,
  .res-navHelp {
    display: block;
    max-width: 480px;
    margin: auto;
  }

  .sideNav-flex {
    height: auto;

    >.help_center {
      display: none;
    }
  }

  /*--- Main pages ---*/
  .main_body {
    padding-left: 1rem !important;
    padding-top: 85px;
  }

  header {
    display: none;
  }

  section.main-body {
    padding-top: 0rem;
  }

  .sideNav {
    position: fixed;
    height: auto;
    background: rgba(231, 231, 232, 0.45);
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
    margin: 10px;
    max-width: 96%;
    width: 100%;
    z-index: 9999999;
  }

  .res-nav {
    display: flex;
  }

  .hCard {
    padding: 5px;
  }

  .hCard span.mdi {
    padding: 0px 5px;
    font-size: 16px;
    line-height: 28px;
  }

  .custom_search input {
    min-width: 300px;
  }

  .custom_search {
    width: 100%;
  }

  .navbar-vertical-content {
    background: none;
    margin-top: 20px;
  }

  .help_center {
    position: relative;
    max-width: 100%;
    height: 220px;
    margin: auto;
    overflow: hidden;
    border-radius: 1rem;
    h3 {
      color: #fff;
      text-align: center;
      padding: 30px 0px;
    }  
    &:before {
      content: '';
      background: #8d48eb;
      width: 100%;
      position: absolute;
      height: 100%;
      top: 0;
      z-index: -1;
    }
    &:after {
      content: '';
      position: absolute;
      width: 140px;
      height: 140px;
      background: #9f64ff;
      right: -30px;
      z-index: -1;
      bottom: -30px;
      border-radius: 5rem;
    }
  }
  .help_cd {
    left: 0;
    width: 100%;
    text-align: center;
  }
  .logo {
    max-width: 150px;
  }

  .login-from-content {
    min-width: max-content;
    max-width: 20rem;
    width: 20rem;
  }

  .login-content p {
    font-size: 12px;
  }

  /*--- documents --*/
  .btn-upload, .pageName {
    font-size: 0.75em;
    padding: 12px 20px;
}
.pageName i {
  font-size: 18px;
}
.offcanvas.offcanvas-end {
  margin-top: 75px;
}
.mt-cmd-3 {
  margin-top: 1rem !important;
}
.res-d-none{
  display: none;
}
.res-d-block{
  display: block;
}
}

@media (min-width:991px) and (max-width:1200px) {
  .custom_search input {
    min-width: 300px;
  }
    /*--- documents --*/
    .btn-upload, .pageName {
      font-size: 0.75em;
      padding: 12px 20px;
  }
  .pageName i {
    font-size: 18px;
  }
  .mt-cmd-3 {
    margin-top: 1rem !important;
  }
  .res-d-none{
    display: none;
  }
  .res-d-block{
    display: block;
  }
}

@media (min-width:1201px) and (max-width:1310px) {
.form-label {
    font-size: 0.8em;
}
.form-select, .form-control {
    font-size: 0.8em;
    min-height: 40px;
    padding: 10px;
}
.main_h {
    font-size: 16px;
}


}

@media (min-width:1201px) and (max-width:1440px) {
  .btn-small {
    padding: 7px 10px;
}
  /* .main_body {
    padding-left: calc(25vw - 4.5rem) !important;
  } */
  .pageName {
    padding: 10px 20px;
    font-size: 0.9em;
  }
  .qmigTabs .nav .nav-link {
    padding: 7px 11px;
    font-size: 12.5px;
}
button.btn.btn-upload.font-s {
  font-size: 0.75em;
}
.dash-cItem:not(:last-child) {
  border: none;
  margin-left: 0px;
}
.dashCH h5 {
    font-size: 15px;
}
.dashCH h2 {
    font-size: 0.97rem;
}


}


@media (min-width: 1200px) {
  .col-cm1 {
    width: 20%;
  }
}
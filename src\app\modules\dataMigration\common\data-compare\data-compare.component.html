<div class="v-pageName">{{pageName}}</div>

<!---Code Compare List --->
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Create Configuration
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-md-6 col-xl-6 mt-4"></div>
                            <div class="col-12 col-md-3 col-xl-3 mt-4"></div>
                            <div class="col-12 col-md-3 col-xl-3 mt-4">
                                <button class="btn btn-upload w-100 " (click)="onEditConfigClick()"
                                    data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                                        class="mdi mdi-checkbox-marked-circle-outline" aria-hidden="true"></span>Edit
                                    Configuration</button>
                            </div>
                        </div>
                        <form class="form qmig-Form" [formGroup]="datacompareForm">
                            <div class="row">
                                <!-- Src connection -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source Connection
                                            Name</label>
                                        <select class="form-select" formControlName="connectionName" #Myselect1
                                            (change)="
                            getSchemasList(Myselect1.value,'S');
                            selectedfiletype(Myselect1.value)
                            " aria-label="Default select example">
                                            <option> Select Source Connection</option>
                                            @for(list of ConsList; track list; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        @if ( getControl.connectionName.touched && getControl.connectionName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (getControl.connectionName.errors?.['required']) { Source Connection is
                                            required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <!-- src schema -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="targtetConnection">Source {{schemalable}}</label>
                                        <!-- <select class="form-select" formControlName="schema" #obj
                                            (change)="selectObj(obj.value,'S')">
                                            <option>Select Schema</option>
                                            @for(list of schemaList; track list;){
                                            <option value={{list.schema_name}}>
                                                {{list.schema_name}}</option>
                                            }
                                        </select> -->
                                        <ng-select (change)="selectObj(selectedSchemas,'S')" [selectableGroup]="true"
                                            [placeholder]="'Select schema Name'" [items]="schemaList" [multiple]="true"
                                            bindLabel="schema_name" [closeOnSelect]="false" bindValue="schema_name"
                                            [(ngModel)]="selectedSchemas" formControlName="schema">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schema_name}}
                                            </ng-template>
                                        </ng-select>
                                        <!-- <ng-select [placeholder]="'Select Schema Name'" formControlName="schemas"
                                            groupBy="type" [selectableGroup]="true" [items]="schemaList"
                                            (change)="selectObj('value','S')" [multiple]="true" bindLabel="schema_name"
                                            [closeOnSelect]="false" bindValue="schema_name" clearAllText="Clear"
                                            [clearSearchOnAdd]="true" [(ngModel)]="selectedSchemas">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }"
                                                    value={{item.schema_name}} />
                                                {{item.schema_name}}
                                            </ng-template>
                                            <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.schema_name}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template>
                                        </ng-select> -->
                                        @if ( getControl.schema.touched && getControl.schema.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (getControl.schema.errors?.['required']) { Source {{schemalable}} is
                                            required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- TableName -->
                                @if(!tableHide){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="targtetConnection">Table Name</label>
                                        <!-- <select class="form-select" formControlName="table" #tbl
                                            (change)="changeFn1(tbl.value)">
                                            <option>Select Table</option>
                                            @for(table of tablesresponse; track table;){
                                            <option value={{table.table_name}}>
                                                {{table.table_name}}</option>
                                            }
                                        </select> -->
                                        <ng-select (change)="changeFn1(selectedItems)"
                                            [placeholder]="'Select Table Name'" [items]="tablesresponse"
                                            [multiple]="true" bindLabel="table_name" [closeOnSelect]="false"
                                            bindValue="table_name" [(ngModel)]="selectedItems" formControlName="table">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.table_name}}
                                            </ng-template>
                                        </ng-select>
                                        <!-- @if ( getControl.table.touched && getControl.table.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (getControl.table.errors?.['required']) { Table Name is required
                                            }
                                        </p>
                                        } -->
                                    </div>
                                </div>
                                }

                                <!-- Tgt connection -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Target Connection
                                            Name</label>
                                        <select class="form-select" formControlName="targetConnection" #Myselect2
                                            (change)="
                                      selTgtId(Myselect2.value);getSchemasList(Myselect2.value,'T');
                                     " aria-label="Default select example">
                                            <option>Select Target Connection</option>
                                            @for(list of tgtList; track list;) {
                                            <option value="{{ list.Connection_ID }}">
                                                {{ list.conname }}</option>
                                            }
                                        </select>
                                        @if ( getControl.targetConnection.touched &&
                                        getControl.targetConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (getControl.targetConnection.errors?.['required']) { Target Connection
                                            is required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- Tgt schema  -->
                                @if(!tableHide){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="targtetConnection">Target
                                            {{schemalable}}</label>
                                        <select class="form-select" formControlName="tgtschema" #tgtobj
                                            (change)="selectObj(tgtobj.value,'T')">
                                            <option>Select Schema</option>
                                            @for(tgtlist of tgtschemaList; track tgtlist;){
                                            <option value={{tgtlist.schema_name}}>
                                                {{tgtlist.schema_name}}</option>
                                            }
                                        </select>
                                        <!-- @if ( getControl.tgtschema.touched && getControl.tgtschema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (getControl.tgtschema.errors?.['required']) { Target {{schemalable}} is required
                                                }
                                            </p>
                                            } -->
                                    </div>
                                </div>
                                }
                                <!-- config Name -->

                                <!-- compare type -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Compare
                                            Type
                                            <span class="qmig-tooltip"><i>Type of comparsion to run</i></span>
                                        </label>
                                        <select class="form-select" formControlName="method" #method
                                            (change)="methodSelect(method.value)">
                                            <option>Select Method </option>
                                            <option selected value="Detailed_Compare">Detailed Compare </option>
                                            <option selected value="Sample_Compare">Sample Compare
                                            </option>
                                        </select>
                                        @if ( getControl.method.touched && getControl.method.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (getControl.method.errors?.['required']) { Compare Method is required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Configuration
                                            Name</label>
                                        <input type="text" class="form-control" formControlName="confiName"
                                            placeholder="CONFIG_FILE_DATE" maxlength="15" />
                                        @if ( getControl.confiName.touched && getControl.confiName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (getControl.confiName.errors?.['required']) { Configuration Name is
                                            required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>


                                <!-- @if(datacompareForm.value.method == 'Sample_Data'){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                } -->



                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group mt-1">
                                        <div class="text-right">
                                            <button class="btn btn-upload w-100 mt-4"
                                                [disabled]="datacompareForm.invalid"
                                                (click)="DataMigrationNewCommand(datacompareForm.value)">
                                                <span class="mdi mdi-file-cog-outline" aria-hidden="true"></span>
                                                Create Config <i *ngIf="spin" class="fa fa-spinner fa-spin"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapsea" aria-expanded="true" aria-controls="flush-collapse">
                    Execution
                </button>
            </h2>
            <div id="flush-collapsea" class="accordion-collapse collapse " aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="ExecutionForm"
                            (ngSubmit)="getDagFormValue(ExecutionForm.value)">
                            <div class="row">
                                <div class="col-12 col-sm-4 col-md-4 col-lg-4 ">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source
                                            Connection Name</label>
                                        <select class="form-select" #srcFile1
                                            (change)="fetchSourceConfigFiles(srcFile1.value,'1')">
                                            <option>Select a Source Connection</option>
                                            @for ( src of ConsList; track src) {
                                            <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-4 col-md-4 col-lg-4 ">
                                    <div class="form-group">
                                        <label class="form-label" for="targtetConnection">Target Connection Name</label>
                                        <select class="form-select" #tgtFile1
                                            (change)="fetchTargetConfigFiles(tgtFile1.value,'1')">
                                            <option>Select Target Connection</option>
                                            @for(tgt of tgtList;track tgt; ){
                                            <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <!-- config details -->
                                <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Select Configuration
                                            File</label>
                                        <select class="form-select" #config
                                            (change)="selectDag(config.value);getConfigDags(config.value)"
                                            formControlName="config">
                                            <option>Select a Configuration File</option>
                                            @for(Config of configFilesExe ;track Config; ){
                                            <option value="{{ Config.filePath }}"> {{ Config.fileName }} </option>
                                            }
                                        </select>

                                        <!-- Validators -->
                                        @if ( forms.config.touched && forms.config.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (forms.config.errors.required) {config is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 d-flex pt-2">
                                    <button class="btn btn-sync" (click)="fetchConfigFiles()">
                                        @if(files_spin){
                                        <app-spinner />
                                        }@else{
                                        <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </div>
                                @if(dagSelected)
                                {

                                <div class="col-6 col-md-3 col-xl-3 mt-4">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100" data-bs-toggle="offcanvas"
                                            data-bs-target="#updateOrgModal" (click)="openPopup();popup(1)"
                                            [disabled]="!isCheckBoxSel">
                                            <span class="mdi mdi-timer-play"></span>Schedule Dag
                                        </button>
                                    </div>
                                </div>
                                <!-- Trigger details-->
                                <div class="col-6 col-md-3 col-xl-3 mt-4">
                                    <div class="body-header-button">
                                        <button class="btn btn-sign w-100" (click)="getSCN();popup(0)"
                                            [disabled]="!isCheckBoxSel">
                                            <span class="mdi mdi-cog-play"></span>
                                            Trigger Dag @if(trigger_spin){<app-spinner />}</button>
                                    </div>
                                </div>
                                }
                            </div>
                        </form>
                    </div>
                </div>
                @if(dagSelected)
                {
                <div class="body-main mt-4">
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <div class="row">
                                <div class="col-12 col-md-7 offset-5 d-flex">
                                    <div class="custom_search cs-r me-3 my-3">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Dags" class="form-control"
                                            [(ngModel)]="searchText1" (keyup)="onKey()">
                                    </div>
                                    <button class="btn btn-sync" (click)="refreshdags()">
                                        @if(reff_spin){
                                        <app-spinner />
                                        }@else{
                                        <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- Table details -->
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table" id="example" style="width:100%">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <div class="form-check m-0">
                                                <label class="form-check-label">
                                                    <input type="checkbox" (click)="selectAll($event)"
                                                        class="form-check-input" [checked]="showCheckStatus"
                                                        [disabled]="isDisableAllCheck"> <i class="input-helper"></i>
                                                </label>
                                            </div>
                                        </th>

                                        <th>Dag</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- For pagination -->
                                    @for(dag of dagsInfo | searchFilter: searchText1 |paginate:
                                    {itemsPerPage:10,currentPage:
                                    p,id:'First'};track dag){
                                    <tr>
                                        <td>
                                            <div class="form-check mt-33">
                                                <label class="form-check-label">
                                                    <input type="checkbox" class="form-check-input" #chck
                                                        (click)="checkboxselect($event, chck.value)"
                                                        value="{{dag.dag_id}}" [checked]='dag.isSelected'
                                                        [disabled]="dag.isDagAvailable==false">
                                                    <i class="input-helper"></i>
                                                </label>
                                            </div>
                                        </td>
                                        <td>{{dag.dag_id}}</td>
                                        @if(dag.isDagAvailable==true)
                                        {<td><i class="mdi mdi-check green"></i>
                                        </td>
                                        }
                                        @if(dag.isDagAvailable==false)
                                        {<td><i class="mdi mdi-close red"></i>
                                        </td>
                                        }
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p = $event" id="First"></pagination-controls>
                        </div>
                    </div>
                </div>
                }
            </div>
        </div>
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Configuration Files
                </button>
            </h3>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Source
                                    Connection Name</label>
                                <select class="form-select" #srcFile
                                    (change)="fetchSourceConfigFiles(srcFile.value,'0')">
                                    <option>Select a Source Connection</option>
                                    @for ( src of ConsList; track src) {
                                    <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                            <div class="form-group">
                                <label class="form-label" for="targtetConnection">Target Connection Name</label>
                                <select class="form-select" #tgtFile
                                    (change)="fetchTargetConfigFiles(tgtFile.value,'0')">
                                    <option>Select Target Connection</option>
                                    @for(tgt of tgtList;track tgt; ){
                                    <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-6 col-lg-6 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Config Files" class="form-control"
                                    [(ngModel)]="datachange2" (keyup)="onkey()">
                            </div>
                            <button class="btn btn-sync" (click)="getUpdatedRunNumber1()">
                                @if(getRunSpin1){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(validrepo1 of configFiles |searchFilter: searchText |paginate:{
                            itemsPerPage: 10, currentPage: pageNumber, id:'fourth'};
                            track validrepo1; let i=$index){
                            <tr>
                                <td>{{i + 1}}</td>
                                <td>{{validrepo1.fileName }}</td>
                                <td>{{validrepo1.created_dt}}</td>
                                <TD>
                                    <button class="btn btn-download" (click)="downloadFile(validrepo1)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                    <button class="btn btn-delete" (click)="deleteFiles(validrepo1.filePath)">
                                        <span class="mdi mdi-delete"></span>
                                    </button>
                                </TD>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty list of Reports</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="pageNumber = $event" id="fourth"></pagination-controls>
                </div>
            </div>

        </div>
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    Reports
                </button>
            </h3>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Source
                                    Connection Name</label>
                                <select class="form-select" #srcRep (change)="selectSrcForReports(srcRep.value)">
                                    <option>Select a Source Connection</option>
                                    @for ( src of ConsList; track src) {
                                    <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                            <div class="form-group">
                                <label class="form-label" for="targtetConnection">Target Connection Name</label>
                                <select class="form-select" #tgtRep (change)="GetDataCompareReports(tgtRep.value)">
                                    <option>Select Target Connection</option>
                                    @for(tgt of tgtList;track tgt; ){
                                    <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <!-- <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                            <div class="form-group">
                                <label class="form-label" for="targtetConnection">Dag Folders</label>
                                <select class="form-select" #fol (change)="GetDataCompareReports(fol.value)">
                                    <option>Select Folder</option>
                                    @for(list of DataCompareFolders; track list; ){
                                    <option value="{{list.folderpath}}">{{list.folderName}}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div> -->
                        <!-- <div class="col-12 col-sm-4 col-md-4 col-lg-4 ">
                            <div class="form-group">
                                <label class="form-label" for="targtetConnection">Dag Files</label>
                                <select class="form-select" #fil (change)="GetDataCompareReports(fil.value)">
                                    <option>Select Folder</option>
                                    @for(list of datacompareDagfolders; track list; ){
                                    <option value="{{list}}">{{list}}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div> -->
                        <div class="col-12 col-sm-6 col-md-6 col-lg-6 d-flex">
                            <div class="custom_search cs-r me-3 my-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Data Compare Report" class="form-control"
                                    [(ngModel)]="datachange2" (keyup)="onkey()">
                            </div>
                            <button class="btn btn-sync" (click)="getUpdatedRunNumber()">
                                @if(getRunSpin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>

                    <!-- for download file -->
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>File Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(docs of DataCompareReports |searchFilter: datachange2
                                |paginate:{
                                itemsPerPage: 10, currentPage: pageNumber1, id:'third'};
                                track docs){
                                <tr>
                                    <td>{{ pageNumber*10+$index+1-10 }}</td>
                                    <td>{{docs.fileName }}</td>
                                    <td>{{docs.created_dt}}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(docs)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                        <button (click)="deleteFiles1(docs.filePath)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber1 = $event" id="third"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="offcanvas offcanvas-end" tabindex="-1" id="updateOrgModal">
        <div class="offcanvas-header">
            <h4 class="card-title">
                <i class="fas fa-chalkboard-teacher"></i> Dag Execution
            </h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <form class="form qmig-Form">
                <div class="form-group">
                    @if(scheduleSelect){
                    <div>
                        <span>Selected Dags:{{count.length}}</span>
                        <div>

                            @if(initialselected){
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Type</label>
                                <select class="form-select" #init (change)="selecttype(init.value)">>
                                    <option>Select a Types</option>
                                    @for(Types of initTypes ;track Types; ){
                                    <option value="{{ Types.value }}"> {{ Types.option }} </option>
                                    }
                                </select>
                            </div>
                            }
                        </div>
                    </div>
                    <button class="btn btn-upload w-100" (click)="getSCN()">
                        Execute @if(executeSpin){<app-spinner />}</button>
                    }
                    @if(!scheduleSelect){
                    <div>
                        <span>Selected Dags:{{count.length}}</span>
                        <div>
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Schedule Date</label>
                                <input type="datetime-local" class="form-control" id="schDate">
                            </div>
                            <button class="btn btn-upload w-100" (click)="scheduleDags()"> <span
                                    class="mdi btn-icon-prepend"></span>
                                Schedule</button>
                        </div>
                    </div>
                    }

                    <!-- Execute button -->

                </div>
            </form>
        </div>
    </div>
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Json Details</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <!--- Connection Name --->
        <div class="offcanvas-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="form-label d-required" for="name">Json Data </label>
                    <textarea class="form-control" type="text" id="read" placeholder="data" rows="15" cols="30"
                        [(ngModel)]="readDataString"></textarea>
                </div>
            </div>
            <div class="body-header-button mt-1">
                <button (click)="Update()" class="btn btn-upload w-100 "> Save
                    @if(uploadSpin){<app-spinner />}</button>
            </div>
        </div>
    </div>
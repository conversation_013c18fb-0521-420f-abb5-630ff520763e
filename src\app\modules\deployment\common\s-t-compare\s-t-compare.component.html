
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card mt-3">
    <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingOne">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                    aria-expanded="true" aria-controls="collapseOne">
                    S & T Compare
                </button>
            </h2>
            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                data-bs-parent="#accordionExample">
                <div class="qmig-card-body">
                    <!-- <div class="row">
                        <div class="col-md-6">
                            <p><b> Target Delta :</b></p>
                        </div>
                        <div class="col-md-6">
                            <p><b>Target BaseLine :</b> {{Targetfilename}}</p>
                        </div>
                    </div> -->
                    <!-- <hr class="mt-0" /> -->
                    <form class="form qmig-Form" [formGroup]="targetcomapareform">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Run No</label>
                                    <select formControlName="runnumber" class="form-select" #run
                                        (change)="selectIteration(run.value)">
                                        <option selected disabled>Select Run Number</option>
                                        @for(list of runNumbers;track list;){
                                        <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                        }
                                    </select>
                                    <div class="alert">
                                        @if(validate.runnumber.touched && validate.runnumber.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validate.runnumber.errors?.['required']) { Run Number is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>

                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Connection Type</label>
                                    <select class="form-select" formControlName="contype" #drpdwn
                                        (change)="selectedDropdown(drpdwn.value)">
                                        <option selected> Select Connection Type</option>
                                        <option value="1">Database</option>
                                    </select>
                                    <div class="alert">
                                        @if(validate.contype.touched && validate.contype.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validate.contype.errors?.['required']) { Connection Type is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label for="name" class="form-label d-required">Source Connection</label>
                                    <select class="form-select" formControlName="sourcecon"
                                         aria-label="Default select example">
                                        <option selected disabled>Select Source Connection</option>
                                        @for(list of SourceCo;track list;){
                                        <option value="{{ list.Connection_ID }}">{{ list.conname}}</option>
                                        }
                                    </select>
                                    <div class="alert">
                                        @if(validate.sourcecon.touched && validate.sourcecon.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validate.sourcecon.errors?.['required']) {Source Connection is required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label for="name" class="form-label d-required">Target Connection</label>
                                    <select class="form-select" formControlName="tgtcon" #src
                                        (change)="prjSchemaLists(src.value)" aria-label="Default select example">
                                        <option selected disabled>Select Target Connection</option>
                                        @for(list of TargetConn;track list;){
                                        <option value="{{ list.Connection_ID }}">{{ list.conname}}</option>
                                        }
                                    </select>
                                    <div class="alert">
                                        @if(validate.tgtcon.touched && validate.tgtcon.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validate.tgtcon.errors?.['required']) { Reference Connection is required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label for="name" class="form-label d-required">Schema Name</label>
                                    <ng-select [placeholder]="'Select Schema Type'" [items]="schemaList"
                                        [multiple]="true" bindLabel="schemaname" groupBy="gender"
                                        [selectableGroup]="true" [closeOnSelect]="false" bindValue="schemaname"
                                        [(ngModel)]="selectedItems" formControlName="scheman"
                                        (change)="selectschema(selectedItems)">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index"
                                            clearAllText="Clear" [clearSearchOnAdd]="true">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                        </ng-template>
                                        <ng-template ng-multi-label-tmp let-items="items">
                                            <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                {{item.schemaname}}
                                            </div>
                                            <div class="ng-value" *ngIf="items.length > 1">
                                                <span class="ng-value-label">{{items.length - 1}} more...</span>
                                            </div>
                                        </ng-template>
                                    </ng-select>
                                    <div class="alert">
                                        @if(validate.scheman.touched && validate.scheman.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validate.scheman.errors?.['required']) { Schema Name is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label for="name" class="form-label d-required">Object Category</label>
                                    <select class="form-select" aria-label="Default select example"
                                        aria-placeholder="Select Object List" #object
                                        (click)="SelectObjectTypes(object.value)" formControlName="objectt">
                                        <option value="All">All</option>
                                        <option value="Storage_Objects">Storage_Objects</option>
                                        <option value="Code_Objects">Code_Objects</option>
                                    </select>
                                    <div class="alert">
                                        @if(validate.objectt.touched && validate.objectt.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validate.objectt.errors?.['required']) { Object Category is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label for="name" class="form-label d-required">Object Type</label>
                                    <select class="form-select" aria-label="Default select example"
                                        aria-placeholder="Select Object List" #object
                                         formControlName="objectttype">
                                        <option selected value="" disabled>Select Type</option>    
                                        @for(obj of objectNames;track obj; ){
                                          <option value="{{obj.objectname}}"> {{obj.objectname}} </option>
                                          }          
                                    </select>
                                    <div class="alert">
                                        @if(validate.objectttype.touched && validate.objectttype.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validate.objectttype.errors?.['required']) { Object Type is required }
                                        </p>
                                        }
                                    </div>
                            </div>
                            </div>   
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                  <label for="" class="form-label d-required">Comparision Type</label>
                                  <select class="form-select" formControlName="viewType">
                                    <option selected value="" disabled>Select Comparision Type</option>
                                    @for(list of viewData;track list; ){
                                      <option  value={{list.value}}>{{list.view}}</option>
                                      } 
                                  </select>                 
                                  @if ( validate.viewType.touched && validate.viewType.invalid) {
                                      <p class="text-start text-danger mt-1">
                                          @if (validate.viewType.errors.required) { View Type is required }
                                      </p>                                    
                                  }
                                </div>
                              </div>       
                              <div class="form-group"></div>
                              <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2 text-end">
                                        <button type="button" class="btn btn-upload w-100 font-s"
                                            (click)="DeltaCommand(targetcomapareform.value)"
                                            [disabled]="targetcomapareform.invalid">Execute</button>
                            </div>
                            
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                    Delta changes
                </button>
            </h2>
            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo"
                data-bs-parent="#accordionExample">
                <div class="qmig-card-body">
                    <h3 class="main_h pb-1">Delta changes </h3>
                    <form class="form qmig-Form" [formGroup]="targetdeltaform">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Run No</label>
                                    <select formControlName="scrun" class="form-select" #scrun
                                        (change)="selectRunnumber(scrun.value)">
                                        <option selected disabled>Select SQL Connection</option>
                                @for(list of runNumbers;track list;){
                                <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                }
                                    </select>
                                    <div class="alert">
                                        @if(validates.scrun.touched && validates.scrun.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validates.scrun.errors?.['required']) { Run Number is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label for="name" class="form-label d-required">Schema Name</label>
                                    <select class="form-select" aria-label="Default select example"
                                        aria-placeholder="Select Schema Name" #scschemaa
                                        (change)="sourceSel(scschemaa.value)" formControlName="scschema">
                                        <option selected>Select Schema</option>
                                        @for(list of schemaNamess;track list;){
                                                <option value="{{ list.schemaname }}">{{ list.schemaname}}</option>
                                                }
                                    </select>
                                    <div class="alert">
                                        @if(validates.scschema.touched && validates.scschema.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if(validates.scschema.errors?.['required']) { Schema Name is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- <div class="col-md-3 offset-3 mt-4" [hidden]="!sourceSelected">
                                <button class="btn btn-upload w-100" [disabled]="targetdeltaform.invalid">
                                    <span class="mdi mdi-cloud-download-outline"></span> Download Delta Objects</button>
                            </div> -->

                        </div>
                    </form>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>Sno</th>
                                <th>Schema/Object Type </th>
                                <th>File Name </th>
                                <th>BaseLine Code</th>
                                <th>Delta Code</th>
                                <th>Code Differences</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (con of GetSourceFilePathData |searchFilter: datachange1| paginate: { itemsPerPage: 10,
                            currentPage: pages ,id:'Third'} ;track con) {
                            <tr>
                                <td>{{p*10+$index+1-10}}</td>
                                <td>{{ con.schemaname }}/{{con.objecttype_name}}</td>
                                <td>{{con.basefilename}}</td>
                                <td>
                                    @if(con.baseline_filepath!='' && con.basefilename!=undefined){
                                    <a data-placement="bottom"
                                        (click)="downloadFiles(con.baseline_filepath,con.baselineFile)">
                                        <span class="mdi mdi-file-arrow-up-down"></span></a>
                                    }
                                </td>
                                <td>
                                    @if(con.current_filepath!='' && con.basefilename!=undefined){
                                    <a data-placement="bottom"
                                        (click)="downloadFiles(con.current_filepath,con.currentFile)">
                                        <span class="mdi mdi-file-arrow-up-down"></span></a>
                                    }
                                </td>
                                <td>
                                    @if(con.delta_filepath!='' && con.basefilename!=undefined){
                                    <a data-placement="bottom"
                                    (click)="downloadFiles(con.delta_filepath,con.htmlFile)">
                                    <span class="mdi mdi-file-arrow-up-down"></span></a>
                                    }
                                </td>
                                <td>

                            </tr>
                            } @empty {
                            <!-- <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty list of Documents</p>
                                </td>
                            </tr> -->
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="pages = $event" id="Third">
                    </pagination-controls>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                    S & T Compare Status
                </button>
            </h2>
            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree"
                data-bs-parent="#accordionExample">
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-6">
                        <h3 class="main_h py-4 ps-3">
                            S & T Compare Current Status
                            <button class="btn btn-sync " (click)="refresh()">
                                @if(ref_spin){
                                    <app-spinner />
                                    }@else{
                                        <span class="mdi mdi-refresh"></span>
                                    }
                            </button>
                        </h3>
                    </div>
                    <!-- search status of storage obj migration -->
                    <div class="col-12 col-sm-6 col-md-6">
                        <div class="custom_search cs-r my-3 me-3">
                            <span class="mdi mdi-magnify"></span>
                            <input type="text" placeholder="Search Status" class="form-control" [(ngModel)]="datachanges"
                                (keyup)="onKey()">
                        </div>
                    </div>
                </div>
                <div class="table-responsive mt-2">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>Run No</th>
                                <th>Connection</th>
                                <th>Operation</th>
                                <th>Schema Name</th>
                                <th>Object Type</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (con of RequestTableData |searchFilter: datachanges| paginate: { itemsPerPage: 10,
                            currentPage: p ,id:'second'} ;track con) {
                            <tr>
                                <td>{{p*10+$index+1-10}}</td>
                                <td>{{con.run_id}}</td>
                                <td>{{ con.conname }}</td>
                                <td>{{ con.operation_name }}</td>
                                <td>{{ con.schemaname }}</td>
                                <td>{{ con.objecttype }}</td>
                                <td>{{con.created_dt}}</td>
                                <td>{{con.updated_dt}}</td>
                                <td>
                                    @switch (con.status) {
                                    @case ('I') {
                                    <span>Initialize</span>
                                    }
                                    @case ('P') {
                                    <span>Pending</span>
                                    }
                                    @default {
                                    <span>Completed</span>
                                    }
                                    }
                                </td>
                                <td>
                                    <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                        <span class="mdi mdi-delete btn-icon-prepend"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty list of Documents</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
            <!-- <div class="accordion-item">
                <h3 class="accordion-header" id="flush-headingFour">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseFour" aria-expanded="false" aria-controls="flush-collapseFour">
                        Page-Activity Log
                    </button>
                </h3>
                <div id="flush-collapseFour" class="accordion-collapse collapse" aria-labelledby="flush-headingFour"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <div class="row">
                                <div class="col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Run No</label>
                                        <select formControlName="runnumber" class="form-select"
                                            (change)="getPrjExeLogSelectTask(runnum.value)" #runnum>
                                            <option selected disabled>Select Run Number</option>
                                            @for(list of runNumbersData;track list;){
                                            <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-hover qmig-table">
                                        <thead>
                                            <tr>
                                                <th>S.No</th>
                                                <th>Run No</th>
                                                <th>Date and Time</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for (con of prjLogData |searchFilter: datachanges1| paginate: {
                                            itemsPerPage:
                                            10,currentPage: page , id: 'first'} ;track con; ) {
                                            <tr>
                                                <td>{{p*10+$index+1-10}}</td>
                                                <td>{{ con.iteration }}</td>
                                                <td>{{ con.activity_date }}</td>
                                                <td>{{ con.migtask }}</td>
                                            </tr>
                                            } @empty {
                                            <tr>
                                                    <td colspan="4">
                                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="custom_pagination">
                                    <pagination-controls (pageChange)="page = $event" id="first">
                                    </pagination-controls>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
            <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
                <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="flush-headingFive">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                data-bs-target="#flush-collapseFive" aria-expanded="false"
                                aria-controls="flush-collapseFive">
                                Execution Logs
                            </button>
                        </h3>
                        <div id="flush-collapseFive" class="accordion-collapse collapse"
                            aria-labelledby="flush-headingFive" data-bs-parent="#accordionFlushExample">
                            <div class="qmig-card">
                                <div class="qmig-card-body">
                                    <div class="row">
                                        <div class="col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="Schema">Run No</label>
                                                <select class="form-select" (change)="selectIterForLogs(runnumb.value)"
                                                    #runnumb>
                                                    <option selected disabled>Select Run Number</option>
                                                    @for(list of runNumbersData;track list;){
                                                    <option value="{{ list.iteration }}">{{ list.iteration}}
                                                    </option>
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-hover qmig-table">
                                                <thead>
                                                    <tr>
                                                        <th>S.No</th>
                                                        <th>File Name</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @for (con of LogsData |searchFilter: datachanges2| paginate:{
                                                    itemsPerPage:10,currentPage: p2 ,id:'second'} ;track con) {
                                                    <tr>
                                                        <td>{{ page3*10+$index+1-10 }}</td>
                                                        <td>{{con.fileName }}</td>
                                                        <td>
                                                            <button class="btn btn-download"
                                                                (click)="downloadFile(con)">
                                                                <span class="mdi mdi-cloud-download-outline"></span>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    } @empty {
                                                    <tr>
                                                        <td colspan="4">
                                                            <p class="text-center m-0 w-100">Empty</p>
                                                        </td>
                                                    </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="custom_pagination">
                                            <pagination-controls (pageChange)="p2 = $event" id="second">
                                            </pagination-controls>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

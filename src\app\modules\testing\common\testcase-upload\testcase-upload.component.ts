import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { TestingService } from '../../../../services/testing.service';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { FileInsertObj } from '../../../../models/interfaces/types';
import { ActivatedRoute } from '@angular/router';


declare let $: any;

@Component({
  selector: 'app-test-case-upload',
  templateUrl: './testcase-upload.component.html',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  styles: ``
})
export class TestCaseUploadComponent {

  TestCaseUploadForm: any;
  // Page Details
  pageTitle: string = "Test Case Upload"
  pageIcon: string = "assets/images/fileUpload.png"

  // Basic Project Details
  project_name: string = '';
  getRole: string;
  projectId: string;

  // DataBase Dropdown Options
  databases: any = [
    { value: 'S', option: 'Source' },
    { value: 'T', option: 'Target' },
  ];

  //Upload file Variables
  fileName: string = '';
  fileAdd: boolean = false;
  assessmentFiles: any
  isDataAvailable: boolean = false
  projectDocs: any
  selectFile: any;
  fileData: any;
  testCaseInsertData: any;
  Spin: boolean = false;
  uploadfileSpin: boolean = false;

  searchText: string = '';

  //Pagination Variable
  pageNumber: number = 1;

  //Validated Button Variables
  selectedInfra: any;
  infraSelectData: any;
  Add_Spin: boolean = false;

  ExecututionFiles: any;

  pageName: string = ''

  // Constructor
  constructor(private toast: HotToastService, public formBuilder: FormBuilder, public testingService: TestingService, private route: ActivatedRoute) {
    this.project_name = JSON.parse(localStorage.getItem('project_name') || '{}');
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') || 'null');
    this.pageName = this.route.snapshot.data['name'];

  }

  /*-- uploadForm--*/
  uploadForm = this.formBuilder.group({
    file: ['', [Validators.required]],
  })

  ngOnInit(): void {
    this.TestCaseUploadForm = this.formBuilder.group({
      fileName: ['', [Validators.required]],
      SourceType: ['', [Validators.required]],
    });
    this.testcaseFileSelect();
    this.getinfrastructureDetail();
    this.filterExecutionReports();

  }

  /* UploadButton related Methods Start*/
  uploadBtn() {
    this.uploadFile();
    this.testcaseFileInsert();
  }

  testcaseFileInsert() {
    const FileInsert: FileInsertObj = {
      projectID: this.projectId.toString(),
      fileName: this.fileName,
      uploadDateTime: "NULL",
      uploadStatus: "NULL",
      conversionStatus: "NULL",
      acl: " "
    }
    this.testingService.prjTestCaseFileInsert(FileInsert).subscribe((data) => {
      this.testCaseInsertData = data;
      this.testcaseFileSelect();
    })
    //this.fetchAssessmentFiles();//need to call this method depending on the response of above api once we get the proper api.     
  }

  testcaseFileSelect() {
    this.testingService.TestcaseFileSelect(this.projectId).subscribe((data: any) => {
      this.fileData = data['Table1'];
    })
  }

  uploadFile() {
    //console.log("uploaded")
    this.uploadfileSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', "projectdoc");
    this.testingService.uploadDocuments(formData).subscribe(
      (response: any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        //this.getDocuments();
        this.toast.success(response.message)
        $('#demo').offcanvas('hide');
      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.toast.error('Something went wrong')
        //this.openPopup()
        $('#demo').offcanvas('hide');
      }
    )
  }

  // uploadFile() {    
  //   this.Spin = true
  //   const formData: FormData = new FormData();
  //   formData.append('file', this.selectFile, this.fileName);
  //   formData.append('path', "PRJ"+this.projectId+"SRC/Testing/TestCases/");//path needs to added once python code is ready

  //   //console.log(formData)

  //   this.testingService.uploadCloudFiles(formData).subscribe(
  //     (response:any) => {
  //       this.Spin = false
  //       this.fileAdd = false
  //       this.testcaseFileInsert();
  //       //this.fetchAssessmentFiles();//will remove once we get clarity on the new api.
  //       this.toast.success(response.message)
  //       //console.log('File uploaded successfully', response);
  //     },
  //     error => {
  //       this.Spin = false
  //       this.fileAdd = false
  //       console.error('Error uploading file', error);
  //     }
  //   );
  // }

  // fetchAssessmentFiles() {
  //   const path = "projectdoc/"
  //   this.testingService.getFilesFromDirectory(path).subscribe((data: any) => {
  //     this.projectDocs = data
  //     //console.log(data.length)
  //     data.length === 0 ? this.isDataAvailable = true : this.isDataAvailable = false;
  //   })
  // }

  filterExecutionReports() {
    var path = 'PRJ' + this.projectId + 'SRC'
    // /mnt/eng/PRJ1167SRC/Schemas.csv
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
      //console.log(this.ExecututionFiles)
    })
  }



  /* UploadButton Methods End*/

  // ValidateButtonClickEvent

  getinfrastructureDetail() {
    this.testingService.getInfraSelect(this.projectId).subscribe((data) => {
      //this.infraSelectData = data['jsonResponseData']['Table1'];
      this.infraSelectData = data;
    });
  }


  ValidateBtn(Data: any) {
    this.selectedInfra = this.infraSelectData.filter((item: any) => {
      return item.active === "True";
    })
    let ValidateBtnObj = {
      projectID: this.projectId.toString(),
      tenantID: this.selectedInfra[0].tenant,
      subscriptionID: this.selectedInfra[0].subscription,
      resourceGroup: this.selectedInfra[0].resourcegroup,
      vmName: this.selectedInfra[0].vmname,
      location: this.selectedInfra[0].infralocation,
      command: "fun_uptest.sh",
      objectType: Data.SourceType,
      id: this.projectId.toString(),
      fileName: this.fileName,

    };
    this.Add_Spin = true;
    //Need to add the api call once it is implemented from python end    
    this.testcaseFileSelect();
  }

  //  Close Button Event
  openPopup() {
    this.uploadForm.reset();
    this.fileName = ''
  }

  // Fetching testCaseUpload Control details from html
  get f(): any {
    return this.uploadForm.controls;
  }

  // testCaseFile Selected Event
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
    //console.log("fileselected",this.fileName);
  }

}

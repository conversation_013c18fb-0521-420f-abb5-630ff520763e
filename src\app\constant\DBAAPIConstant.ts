export const dbaAPIConstant = {
    getConnections: 'GetConnectionList?projectId=',
    getdbnames: 'GetDbnames?ConId=',
    getroles: 'GetRoles?ConId=',
    getusers: 'GetUsers?ConId=',
    fetchRoleInfo: 'fetchRoleInfo?ConId=',
    GetUsersInfo: 'GetUsersInfo?ConId=',
    FetchVacuumData: 'FetchVacuumData?ConId=',
    GetAnalyzeData: 'FetchAnalyzeData?ConId=',
    etchVacuumData: 'FetchVacuumData?ConId=',
    Vacuumschema: 'Vacuumschema?schema=',
    Analyzeschema: 'Analyzeschema?schema=',
    tgtSchemaSelectwithdb: 'GetPgSchemaswithDb?ConId=',
    getobjecttype: 'GetObjectTypes',
    getexecutedata: 'GetExecutedata?ConId=',
    getobjectnames: 'GetObjects',
    getpermissions: 'GetPermissions',
    GetbloatingDetails: 'GetbloatingDetails',
    grantpermissions: 'GrantPermissions',
    triggerVacuum: 'Redis/DBACommand',
    GetReqData: 'GetRequestTableData?projectId=',
    deleteTableData: 'DeleteRequestTableData?projectId=',
    GetRunno:'GetRunNumbers?projectId=',
    DeleteIterationByRange:'DeleteIterationByRange',
    downloadLargeFiles: 'DownloadLargeFile?filePath=',
    uploadLargeFiles: 'UploadCloudFiles',
    deleteFile: 'DeleteFiles?path=',
    GetFilesFromDir: 'GetFilesFromDirectory?path=',
    DBAScriptInsertion:'DBAScriptInsertion',
    DBATaskList:'DBATaskList?projectId=',
    DBACategoryName:'DBACategoryName?category=',
    GetServerParamsData:'GetServerParamsData?conid=',
    QueryExecution:'QueryExecution',
    GetDBARunids:'GetDBAScriptRunids',
    GetDBAExeResults:'GetDBAScriptResultsByRunid?runid=',
}
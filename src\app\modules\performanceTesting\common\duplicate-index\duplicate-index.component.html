<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="row">
        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
            <h3 class="main_h px-3 pt-3"> Index Insights </h3>
        </div>
        <div class="col-12 col-sm-6 col-md-9 col-lg-9 col-xl-9 text-end mt-3 pe-4" [hidden]="!DuplicateCount">
            <p class="m-0">Duplicate Count : <b>{{DuplicateCount }}</b> | Original Count : <b>{{OriginalCount}}</b> | Suggested Date : <b>{{CurrentTime}}</b></p>
        </div>
    </div>
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="perfForm">
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="">
                        <label class="form-label d-required" for="targtetConnection"> Connection
                            Name</label>
                        <select formControlName="tgtconnection" #Myselect2 (change)="selTgtId(Myselect2.value)" class="form-select">
                            <option value="" disabled selected>Select Connection</option>
                            <option *ngFor="let list of tgtList" [value]="list.Connection_ID">{{ list.conname }}
                            </option>
                        </select>
                        <div class="alert">
                            @if(validate.tgtconnection.touched && validate.tgtconnection.invalid) {
                            <p class="text-start text-danger mt-1">Connection Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                    <div class="">
                        <label class="form-label d-required" for="index"> Schema Name</label>
                        <ng-select [placeholder]="'Select Schema'" formControlName="schemaname"
                                            [items]="perSchema" [multiple]="true"
                                            (change)="selectSchema(selectedObjItems1)" bindLabel="schemaname"
                                            groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false"
                                            bindValue="schemaname" [(ngModel)]="selectedObjItems1">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                        </ng-select>
                        <div class="alert">
                            @if(validate.schemaname.touched && validate.schemaname.invalid) {
                            <p class="text-start text-danger mt-1">Schema Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 mt-4 pt-2">
                    <button class="btn btn-upload w-100" (click)="GetDuplicateIndexData(perfForm.value)" [disabled]="perfForm.invalid">Fetch Data
                        @if(Exespinner){<app-spinner />}
                    </button>
                </div>
            </div>
        </form>
    </div>
    <div [hidden]="!TableData">
        <hr class="dash-dotted" />
        
        <div class="row">
            <!-- Export Button -->
            <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3  mb-3" [hidden]="duplicateIndexData?.length <= 0" [ngClass]="updateBtn ? 'offset-md-3': 'offset-md-6'">
                <button class="btn btn-upload w-100" (click)="exportexcelAnalyze()">
                    <span class="mdi mdi-arrow-up-thin"></span>Export
                    @if(excelSpinn){<app-spinner />}
                </button>
            </div>
              <!-- DownLoad Button -->
            <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3" [hidden]="duplicateIndexData?.length <= 0">
                <button class="btn btn-sign w-100" (click)="FailedDuplicateIndexExport()">
                    <span class="mdi mdi-arrow-up-thin"></span>Log
                </button>
            </div>
            <!-- Update Button -->
            <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3" [hidden]="!updateBtn">
                <button class="btn btn-danger w-100" (click)="PerfDuplicateindexUpdate()">
                    <span class="mdi mdi-database-minus"> Drop </span> 
                    @if(Updatespinner){<app-spinner />}
                </button>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>S.No</th>
                        <th>SchemaName</th>
                        <th>TableName</th>
                        <th>IndexName</th>
                        <th>IndexType</th>
                        <th>IndexColumns</th>
                        <th>
                            <div class="form-check">
                                <input type="checkbox" [(ngModel)]="selectAll" class="form-check-input"
                                (change)="toggleAllSelection();currentlyCheked($event)" />
                            </div>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @for (documents of duplicateIndexData | paginate: { itemsPerPage: 10,
                    currentPage: pageNumber } ; track documents) {
                    <tr>
                        <td>{{pageNumber*10+$index+1-10}}</td>
                        <td>{{documents.schemaname}}</td>
                        <td>{{documents.tablename}}</td>
                        <td>{{documents.indexname}}</td>
                        <td>{{documents.indexType}}</td>
                        <td>{{documents.indexColumns}}</td>
                        <td>
                            <div class="form-check">
                                <!-- Row-level checkbox -->
                                <input *ngIf="documents.indexType === 'Duplicate'"  type="checkbox" [(ngModel)]="documents.isSelected" class="form-check-input"
                                    (change)="onCheckboxChange(documents);currentlyCheked($event)"
                                    [value]="documents.dropScripts" />
                            </div>
                        </td>

                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="7">
                            <p class="text-center m-0 w-100">No Results Found</p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
    </div>
</div>
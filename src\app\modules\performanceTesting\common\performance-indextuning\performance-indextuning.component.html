<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingOne">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                    aria-expanded="true" aria-controls="collapseOne">
                    Index Tuning
                </button>
            </h2>
            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                data-bs-parent="#accordionExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="perfForm">
                        <div class="row">
                            <!--- Category  --->

                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection"> Connection
                                        Name</label>
                                    <select formControlName="tgtconnection" #Myselect2 (change)="
                                    selTgtId(Myselect2.value);GetperfSchemas()" class="form-select">
                                        <option value="" disabled selected>Select Connection Name</option>
                                        @for(list of tgtList;track list; ){
                                        <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                        }
                                    </select>
                                    <div class="alert">
                                        @if(validate.tgtconnection.touched && validate.tgtconnection.invalid) {
                                        <p class="text-start text-danger mt-1">Connection Name required
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-md-3 col-xl-">
                                <div class="form-group">
                                    <label class="form-label q-tooltip mt-3"
                                        for="recommendedIndexCount">Recommendation</label>
                                    <span class="qmig-tooltip">
                                        <i>Recommended Index count based on Target connections.</i>
                                    </span>
                                    <b>
                                        <div>
                                            <a data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                                                aria-expanded="false" aria-controls="collapseTwo"> View Recommended
                                                Index </a>
                                        </div>
                                    </b> 
                                </div>
                            </div> -->
                            <div class="col-md-3 col-xl-3 offset-md-6 mt-4 pt-2 text-end" [hidden]="!IndexCount">
                                <div class="form-group">
                                    <p>Create Index : <b>{{IndexCount}} </b> | Drop Index : <b>{{DroppedIndexData}}</b>
                                    </p>

                                </div>
                            </div>
                            <div>
                                <!-- <div class="form-group">
                                    <label class="form-label q-tooltip mt-3" for="recommendedIndex">Recomoneded Index Timestamp</label>
                                    <b>
                                        <div>{{IndexTimeStamp}} </div>
                                    </b> Dynamically Display the Index Count 
                                </div> -->
                            </div>
                        </div>
                    </form>
                </div>
                <!--- Table Partition --->
                <div class="body-main" [hidden]="IndexTuningTableData?.length <= 0">
                    <hr class="dash-dotted m-0" />
                    <div class="qmig-card-body">
        <div class="row">
            
            <!-- Export Button -->
            <div class="col-12 col-sm-6 col-md-3">
                <h3 class="main_h pb-3 ps-3">
                    Recommended Indexes
                </h3>
            </div>
            <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3  mb-3" [ngClass]="updateBtn ?'': 'offset-md-3'">
                <button class="btn btn-upload w-100" (click)="exportexcelIndexTuning()">
                    <span class="mdi mdi-arrow-up-thin"></span>Export
                    @if(excelspind){<app-spinner />}
                </button>
            </div>
              <!-- DownLoad Button -->
            <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3">
                <button class="btn btn-sign w-100" (click)="IndexTuningFiledlog()" >
                    <span class="mdi mdi-arrow-up-thin"></span>Log
                </button>
            </div>
            <!-- Update Button -->
            <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3" [hidden]="!updateBtn">
                <button class="btn btn-danger w-100" (click)="PerfIndexUpdate();PerfIndexExecution()">
                    <span class="mdi mdi-database-minus"> Execute </span> 
                    @if(indexupda){<app-spinner />}
                </button>
            </div>
        </div>
                    </div>
                    <div [hidden]="false">
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>S.No</th>
                                        <th>SchemaName</th>
                                        <th>TableName</th>
                                        <th>Column Name</th>
                                        <th>Index Suggested</th>
                                        <th>Created Date</th>
                                        <th>Status</th>
                                        <th>
                                            <div class="form-check">
                                                <input type="checkbox" [(ngModel)]="selectAll" class="form-check-input"
                                                    (change)="toggleAllSelection();currentlyCheked($event)" />
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (con of IndexTuningTableData | searchFilter: searchText
                                    | paginate: {
                                    itemsPerPage: 10, currentPage: pageNumber2 } ; track con; )
                                    {
                                    <tr>
                                        <td>{{p1*10+$index+1-10}}</td>
                                        <td>{{con.schemaName}}</td>
                                        <td>{{con.tableName}}</td>
                                        <td>{{con.columnName}}</td>
                                        <td>{{con.indexSugg}}</td>
                                        <td>{{con.date}}</td>
                                        <td>{{con.status}}</td>
                                        <td>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input"
                                                    [(ngModel)]="con.isSelected"
                                                    (change)="onCheckboxChange(con);currentlyCheked($event)"
                                                    [value]="con.indexSugg" />
                                            </div>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="8">
                                            <p class="text-center m-0 w-100">Empty list </p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pageNumber2 = $event"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="headingFive">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                    Used Indexes
                </button>
            </h2>
            <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive"
                data-bs-parent="#accordionExample">
                <div class="body-main">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="UsedIndexForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                                    <div class="form-group">
                                        <select formControlName="ustgtconn" #Myselect5 (change)="
                                        selTgtused(Myselect5.value);GetperfSchemasu()" class="form-select">
                                            <option value="" disabled selected>Select Connection Name</option>
                                            @for(list of tgtList;track list; ){
                                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(usvaldate.ustgtconn.touched && usvaldate.ustgtconn.invalid) {
                                            <p class="text-start text-danger mt-1">Connection Name required
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                                    <div class="form-group">
                                        <ng-select [placeholder]="'Select Schema'" formControlName="schemanames"
                                            [items]="perSchema" [multiple]="true"
                                            (change)="selectSchema1(selectedObjItems2)" bindLabel="schemaname"
                                            groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false"
                                            bindValue="schemaname" [(ngModel)]="selectedObjItems2">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if(usvaldate.schemanames.touched && usvaldate.schemanames.invalid) {
                                            <p class="text-start text-danger mt-1">Connection Name required
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                                    <input type="text" class="form-control" formControlName="number1" placeholder="Days"
                                        id="number">
                                    <div class="alert">
                                        @if(usvaldate.number1.touched && usvaldate.number1.invalid) {
                                        <p class="text-start text-danger mt-1"> Days required
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-3 col-md-4 col-lg-4 col-xl-2 ">
                                    <button class="btn btn-upload w-100" (click)="GetUsedIndexes(UsedIndexForm.value)"
                                        [disabled]="UsedIndexForm.invalid">
                                        Fetch @if(ref_spin1){<app-spinner />}</button>
                                </div>
                                <div class="col-12 col-sm-3 col-md-4 col-lg-4 col-xl-2"
                                    [hidden]="UsedIndexesData?.length <= 0">
                                    <button class="btn btn-upload w-100" (click)="exportexcelusedIndex()"
                                        [disabled]="UsedIndexForm.invalid">
                                        <span class="mdi mdi-arrow-up-thin"></span>Export
                                        @if(excelSpinn1){<app-spinner />}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div [hidden]="false" [hidden]="UsedIndexesData?.length <= 0">
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>Sno</th>
                                        <th>Schema Name</th>
                                        <th>Table Name</th>
                                        <th>Index Name</th>
                                        <th>Totals Scan's</th>
                                        <th>Total Fetch</th>
                                        <th>Index Size</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (con of UsedIndexesData | searchFilter: searchText
                                    | paginate: {
                                    itemsPerPage: 10, currentPage: pageNumber } ; track con; )
                                    {
                                    <tr>
                                        <td>{{p1*10+$index+1-10}}</td>
                                        <td>{{con.schemaname}}</td>
                                        <td>{{con.tablename}}</td>
                                        <td>{{con.indexname}}</td>
                                        <td>{{con.totalscans}}</td>
                                        <td>{{con.totalfetch}}</td>
                                        <td>{{con.index_size}}</td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="8">
                                            <p class="text-center m-0 w-100">Empty list </p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="headingSix">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseSix" aria-expanded="false" aria-controls="collapseSix">
                    UnUsed Indexes
                </button>
            </h2>
            <div id="collapseSix" class="accordion-collapse collapse" aria-labelledby="headingSix"
                data-bs-parent="#accordionExample">
                <div class="body-main">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="unUsedIndexForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                                    <div class="form-group">
                                        <select formControlName="untgtconn" #Myselect4 (change)="
                                        selTgtunused(Myselect4.value);GetperfSchemasun()" class="form-select">
                                            <option value="" disabled selected>Select Connection Name</option>
                                            @for(list of tgtList;track list; ){
                                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(unvaldate.untgtconn.touched && unvaldate.untgtconn.invalid) {
                                            <p class="text-start text-danger mt-1">Connection Name required
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                                    <div class="form-group">
                                        <ng-select [placeholder]="'Select Schema'" formControlName="schemanamess"
                                            [items]="perSchema" [multiple]="true"
                                            (change)="selectSchema2(selectedObjItems3)" bindLabel="schemaname"
                                            groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false"
                                            bindValue="schemaname" [(ngModel)]="selectedObjItems3">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if(unvaldate.schemanamess.touched && unvaldate.schemanamess.invalid) {
                                            <p class="text-start text-danger mt-1"> Schema required
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                                    <input type="text" class="form-control" formControlName="number2" placeholder="Days"
                                        id="number">
                                    <div class="alert">
                                        @if(unvaldate.number2.touched && unvaldate.number2.invalid) {
                                        <p class="text-start text-danger mt-1">Days required
                                        </p>
                                        }
                                    </div>

                                </div>

                                <div class="col-12 col-sm-3 col-md-4 col-lg-4 col-xl-2 ">
                                    <button class="btn btn-upload w-100"
                                        (click)="GetUnUsedIndexes(unUsedIndexForm.value)"
                                        [disabled]="unUsedIndexForm.invalid">
                                        Fetch @if(ref_spin2){<app-spinner />}</button>
                                </div>
                                <div class="col-12 col-sm-3 col-md-4 col-lg-4 col-xl-2"
                                    [hidden]="UnUsedIndexesData?.length <= 0">
                                    <button class="btn btn-upload w-100" (click)="exportexcelUnUsedIndex()"
                                        [disabled]="unUsedIndexForm.invalid">
                                        <span class="mdi mdi-arrow-up-thin"></span>Export
                                        @if(excelSpinn2){<app-spinner />}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div [hidden]="false" [hidden]="UnUsedIndexesData?.length <= 0">
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>Sno</th>
                                        <th>Schema Name</th>
                                        <th>Table Name</th>
                                        <th>Index Name</th>
                                        <th>Totals Scan's</th>
                                        <th>Total Fetch</th>
                                        <th>Index Size</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (con of UnUsedIndexesData | searchFilter: searchText
                                    | paginate: {
                                    itemsPerPage: 10, currentPage: pageNumber1 } ; track con; )
                                    {
                                    <tr>
                                        <td>{{p*10+$index+1-10}}</td>
                                        <td>{{con.schemaname}}</td>
                                        <td>{{con.tablename}}</td>
                                        <td>{{con.indexname}}</td>
                                        <td>{{con.totalscans}}</td>
                                        <td>{{con.totalfetch}}</td>
                                        <td>{{con.index_size}}</td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="8">
                                            <p class="text-center m-0 w-100">Empty list </p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pageNumber1 = $event"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="headingSeven">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseSeven" aria-expanded="false" aria-controls="collapseSeven">
                    Bloated Indexes
                </button>
            </h2>
            <div id="collapseSeven" class="accordion-collapse collapse" aria-labelledby="headingSeven"
                data-bs-parent="#accordionExample">
                <div class="body-main">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="BloatedForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                                    <div class="form-group">
                                        <select formControlName="bltgtconn" #Myselect7 (change)="
                                        selTgtbloat(Myselect7.value);GetperfSchemaBloat()" class="form-select">
                                            <option value="" disabled selected>Select Connection Name</option>
                                            @for(list of tgtList;track list; ){
                                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(blvaldate.bltgtconn.touched && blvaldate.bltgtconn.invalid) {
                                            <p class="text-start text-danger mt-1">Connection Name required
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                                    <div class="form-group">
                                        <ng-select [placeholder]="'Select Schema'" formControlName="schemanamebl"
                                            [items]="perSchema" [multiple]="true"
                                            (change)="selectSchema1(selectedObjItems4)" bindLabel="schemaname"
                                            groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false"
                                            bindValue="schemaname" [(ngModel)]="selectedObjItems4">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if(blvaldate.schemanamebl.touched && blvaldate.schemanamebl.invalid) {
                                            <p class="text-start text-danger mt-1">Connection Name required
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-3 col-md-4 col-lg-4 col-xl-2 ">
                                    <button class="btn btn-upload w-100"
                                        (click)="GetBloatedIndexes(BloatedForm.value);GetBloatedIndexExcel(BloatedForm.value)"
                                        [disabled]="BloatedForm.invalid">
                                        Fetch @if(ref_spin1){<app-spinner />}</button>
                                </div>
                                <div class="col-12 col-sm-3 col-md-4 col-lg-4 col-xl-2"
                                    [hidden]="BloatIndexesData?.length <= 0">
                                    <button class="btn btn-upload w-100" (click)="exportexcelShowlogsBloat()"
                                        [disabled]="BloatedForm.invalid">
                                        <span class="mdi mdi-arrow-up-thin"></span>Export
                                        @if(excelSpinn1){<app-spinner />}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div [hidden]="false" [hidden]="BloatIndexesData?.length <= 0">
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>Sno</th>
                                        <th>Schema Name</th>
                                        <th>Table Name</th>
                                        <th>Index Name</th>
                                        <th>Index Size</th>
                                        <th>IndexBloat</th>
                                        <th>Vacum Table</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (con of BloatIndexesData | searchFilter: searchText
                                    | paginate: {
                                    itemsPerPage: 10, currentPage: pageNumber } ; track con; )
                                    {
                                    <tr>
                                        <td>{{p1*10+$index+1-10}}</td>
                                        <td>{{con.schemaname}}</td>
                                        <td>{{con.tablename}}</td>
                                        <td>{{con.indexname}}</td>
                                        <td>{{con.indexsize}}</td>
                                        <td>{{con.indexBloat}}</td>
                                        <td>{{con.vacumtable}}</td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="8">
                                            <p class="text-center m-0 w-100">Empty list </p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="headingFour">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                    Sequence Scan Export
                </button>
            </h2>
            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour"
                data-bs-parent="#accordionExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="ExcelForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection"> Connection
                                        Name</label>
                                    <select formControlName="tgtconnect" #Myselect7 (change)="
                                    selTgt(Myselect7.value)" class="form-select">
                                        <option value="" disabled selected>Select Connection Name</option>
                                        @for(list of tgtList;track list; ){
                                        <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                        }
                                    </select>
                                    <div class="alert">
                                        @if(valdate.tgtconnect.touched && valdate.tgtconnect.invalid) {
                                        <p class="text-start text-danger mt-1">Connection Name required
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="targtetConnection">From Date</label>
                                    <input type="date" formControlName="fromDate" class="form-control" id="fromDate">
                                </div>
                                <div class="alert">
                                    @if(valdate.fromDate.touched && valdate.fromDate.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(valdate.fromDate.errors?.['required']) { From Date is required }
                                    </p>
                                    }
                                </div>
                            </div>

                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="targtetConnection">To Date</label>
                                    <input type="date" formControlName="toDate" class="form-control" id="toDate">
                                </div>
                                <div class="alert">
                                    @if(valdate.toDate.touched && valdate.toDate.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(valdate.toDate.errors?.['required']) { To Date is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-4 mt-4 pt-2 col-md-4 col-lg-4 col-xl-3 ">
                                <button class="btn btn-upload w-100" (click)=" IndexeExcelDownLoad(ExcelForm.value);"
                                    [disabled]="ExcelForm.invalid">
                                    <span class="mdi mdi-arrow-up-thin"></span>Export
                                    @if(excelspind){<app-spinner />}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                    File Upload & Insertion
                </button>
            </h2>
            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree"
                data-bs-parent="#accordionExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                            <div class="form-group" [formGroup]="uploadForm">
                                <label class="form-label d-required">Upload File</label>
                                <div class="custom-file-upload">
                                    <input class="form-control" type="file" id="formFile" formControlName="file1"
                                        (change)="onFileSelected($event)">
                                    <div class="file-upload-mask">
                                        @if (FileNameUpload == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drop the file here or click add file </p>
                                        <button class="btn btn-upload"> Add File </button>
                                        }
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ FileNameUpload }} </p>
                                        </div>
                                    </div>
                                    @if ( validates.file1.touched && validates.file1.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (validates.file1.errors.required) { File is required }
                                    </p>
                                    }

                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 offset-md-9">
                            <button class="btn btn-upload w-100" (click)="uploadFile()"
                                [disabled]="uploadForm.invalid"><span class="mdi mdi-file-plus"></span>Upload
                                File
                                @if(uploadfileSpin){<app-spinner />}</button>
                        </div>
                    </div>
                    <hr class="dash-dotted" />
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="Executionform">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">

                                    <div class="">
                                        <label class="form-label d-required" for="targtetConnection">Upload
                                            Files</label>
                                        <select formControlName="filenamee" #ex (click)="selectFilenames(ex.value)"
                                            class="form-select">
                                            <option selected disabled>Select Fiels</option>
                                            @for(list of uploadedData;track list;){
                                            <option value="{{ list.fileName }}">{{ list.fileName}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validatee.filenamee.touched && validatee.filenamee.invalid) {
                                            <p class="text-start text-danger mt-1">Connection Name required
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                                    <div class="">
                                        <label class="form-label d-required" for="targtetConnection"> Connection
                                            Name</label>
                                        <select formControlName="tgtconn" #Myselect3 (change)="
                                    selTgtIds(Myselect3.value)" class="form-select">
                                            <option value="" disabled selected>Select Connection Name</option>
                                            @for(list of tgtList;track list; ){
                                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validatee.tgtconn.touched && validatee.tgtconn.invalid) {
                                            <p class="text-start text-danger mt-1">Connection Name required
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 mt-4 pt-2">
                                    <button type="button" #inert class="btn btn-upload w-100"
                                        (click)="TablePartitionInsert(Executionform.value)"
                                        [disabled]="Executionform.invalid"><span class="mdi mdi-cloud-sync"></span>
                                        insertion @if(tablespin){<app-spinner />}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
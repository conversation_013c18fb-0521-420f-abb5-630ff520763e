<!-- <header -->
<div class="v-pageName">{{pageName}}</div>
<div class="row">
    <div class="col-12 col-md-6 col-xl-6">
        <h3 class="main_h px-3 pt-3"> Baseline DataBase Deployment </h3>
    </div>
    <div class="col-6 col-md-6 col-xl-6">
        <div class="body-header-button">
            <button class="btn btn-upload" data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                    class="mdi mdi-file-document-plus-outline"></span> Upload File</button>
        </div>
    </div>
</div>
<div class="qmig-card mt-3">
    <div class="qmig-card-body">
        <form class="form add_form" [formGroup]="TestCaseForm">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="operation">Operation</label>
                        <select class="form-select ml-0" formControlName="operation" #op
                            (change)="GetOperation(op.value)">
                            <option selected value="">Source operation </option>
                            <option value="Source_Restore">Source_Restore</option>
                            <option value="Target_Backup">Target_Backup</option>
                            <option value="Target_Restore">Target_Restore</option>
                        </select>
                        @if ( f.operation.touched && f.operation.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.operation.errors.required) {Operation is Required }
                        </p>
                        }
                    </div>
                </div>
                @if(TestCaseForm.value.operation == 'Source_Restore'){
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="name">Source Connection</label>
                        <select class="form-select ml-0" formControlName="sourceConnection">
                            <option selected value="">Source Connection </option>
                            @for(list of sourceData; track sourceData; ){
                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                            }
                        </select>
                        @if ( f.sourceConnection.touched && f.sourceConnection.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.sourceConnection.errors.required) {Source Connection is Required }
                        </p>
                        }
                    </div>
                </div>
                }
                <!-- Target Connection Dropdown -->
                @if(TestCaseForm.value.operation == 'Target_Backup' || TestCaseForm.value.operation ==
                'Target_Restore'){
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="name">Target Connection</label>
                        <select class="form-select ml-0" formControlName="targetConnection" #tgt
                            (change)="GetSchemaList(tgt.value)">
                            <option selected value="">Target Connection</option>
                            @for(list of targetData; track targetData; ){
                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                            }
                        </select>
                        @if ( f.targetConnection.touched && f.targetConnection.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.targetConnection.errors.required) {Target Connection is Required }
                        </p>
                        }
                    </div>
                </div>
                }
                <!-- schema -->
                <!-- <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="targtetConnection"> Schema Name </label>
                        <ng-select  formControlName= "schema"  (change)="selectSchema()"
                            [items]="schemaList" [multiple]="true" bindLabel="schemaname" [closeOnSelect]="false"
                            bindValue="schemaname" [(ngModel)]="selectedItems">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                            </ng-template>
                        </ng-select>
                        @if ( f.schema.touched && f.schema.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (f.schema.errors.required) {Schema Name is Required }
                            </p>
                            }
                    </div>
                </div> -->
                @if(TestCaseForm.value.operation == 'Target_Backup'){
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Schema Name</label>
                        <select class="form-select ml-0" formControlName="schema" #schm
                            (change)="selectSchema(schm.value)">
                            <option selected value="">Schema Name </option>
                            @for(sch of schemaList; track sch; ){
                            <option value="{{sch.schemaname}}">{{sch.schemaname}}</option>
                            }
                        </select>
                        @if ( f.schema.touched && f.schema.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.schema.errors.required) {Schemaname is required }
                        </p>
                        }

                    </div>
                </div>
                <!-- Tables -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label " for="targtetConnection"> Table Name </label>
                        <ng-select formControlName="tables" [items]="tablesList" [multiple]="true" bindLabel="tablename" formControlName="tables"
                            [closeOnSelect]="false" [selectableGroup]="true" bindValue="tablename" groupBy="type" [(ngModel)]="selectedtabItems">
                            <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                <input id="item-{{index}}" type="checkbox" [ngModel]="item$.selected" />
                                {{item.type | uppercase}}
                            </ng-template>
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                    [ngModelOptions]="{ standalone : true }" /> {{item.tablename}}
                            </ng-template>
                        </ng-select>
                        @if ( f.tables.touched && f.tables.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.tables.errors.required) {Table Name is Required }
                        </p>
                        }
                    </div>
                </div>
                }

                @if(TestCaseForm.value.operation == 'Source_Restore'){
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="name">Source Backup File</label>
                        <select class="form-select ml-0" formControlName="sourceFile">
                            <option selected value="">Source Backup Files</option>
                            @for(src of SourceBackupData; track src; ){
                            <option value="{{src.filePath}}">{{src.fileName}}</option>
                            }
                        </select>
                        @if ( f.sourceFile.touched && f.sourceFile.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.sourceFile.errors.required) {Target Backup File is Required }
                        </p>
                        }
                    </div>
                </div>
                }
                <!-- Threads label -->
                @if(TestCaseForm.value.operation == 'Target_Restore'){
                <div class="col-md-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="name">Target Backup File</label>
                        <select class="form-select ml-0" formControlName="targetFile">
                            <option selected value="">Target Backup Files</option>
                            @for(tgt of TargetBackupData; track tgt; ){
                            <option value="{{tgt.filePath}}">{{tgt.fileName}}</option>
                            }
                        </select>
                        @if ( f.targetFile.touched && f.targetFile.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.targetFile.errors.required) {Target Backup File is Required }
                        </p>
                        }
                    </div>
                </div>
                }
                <div class="col-md-3"
                    [ngClass]="{'mt-4 pt-1': TestCaseForm.value.operation == 'Source_Restore' || TestCaseForm.value.operation == 'Target_Restore', 'offset-md-9': TestCaseForm.value.operation == 'Target_Backup', 'offset-md-6 mt-4 pt-1': TestCaseForm.value.operation == ''}">
                    <div class="form-group">
                        <button class="btn btn-upload w-100" [disabled]="TestCaseForm.invalid"
                            (click)="triggerBaselineComand()"><span class="mdi mdi-cog-play"></span>Execute
                            @if(formSpinner){<app-spinner-white />}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!--- Upload Documnets  --->
<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
    <div class="offcanvas-header">
        <h4 class="main_h">Upload Document</h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="uploadForm" (ngSubmit)="uploadFile()">
            <!-- <div class="form-group">
                <label class="form-label d-required" for="name">Choose File Type</label>                            
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="fileType" id="srcBack" formControlName="fileType">
                    <label class="form-check-label form-label" for="srcBack">
                      Source Backup File
                    </label>
                </div>                        
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="fileType" id="tgtBack" formControlName="fileType">
                    <label class="form-check-label form-label" for="tgtBack">
                      Target Backup File
                    </label>
                </div>
                @if ( u.fileType.touched && u.fileType.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (u.fileType.errors.required) {File Type is Required }
                </p>
                }
            </div> -->
            <div class="form-group">
                <label for="formFile" class="form-label d-required">Upload Source Backup File </label>
                <div class="custom-file-upload">
                    <input class="form-control" type="file" id="formFile" formControlName="file"
                        (change)="onFileSelected($event)">
                    <div class="file-upload-mask">
                        @if (fileName == '') {
                        <img src="assets/images/fileUpload.png" alt="img" />
                        <p>Drag and drop Source Backup file here or click add Source Backup file </p>
                        <button class="btn btn-upload"> Add File </button>
                        }
                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                            <p> {{ fileName }} </p>
                        </div>
                    </div>
                </div>
                @if ( u.file.touched && u.file.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (u.file.errors.required) { File is required }
                </p>
                }
            </div>
            <div class="form-group">
                <button class="btn btn-upload w-100" [disabled]="uploadForm.invalid"> <span
                        class="mdi mdi-file-plus"></span> Upload File @if(fileUploadSpin){<app-spinner />}</button>
            </div>
        </form>
    </div>
</div>
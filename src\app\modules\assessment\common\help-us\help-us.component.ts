import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-help-us',
  standalone: true,
  imports: [],
  templateUrl: './help-us.component.html',
  styles: `
     
  .word-doc {
    background: white;
    color: black;
    font-size: 12pt;
    line-height: 1.6;
    padding: 72px; /* 1 inch margin (96px = 1 inch) */
    margin: auto;
    box-shadow: 0 0 5px rgba(0,0,0,0.1);
  }
  
  .word-doc h1 {
    font-size: 20pt;
    text-align: center;
    margin-bottom: 30px;
  }
  
  .word-doc h2 {
    font-size: 16pt;
    margin-top: 30px;
    margin-bottom: 10px;
    color: #000080;
  }
  
  .word-doc p {
    margin: 8px 0;
  }
  
  .image-placeholder {
    width: 100%;
    height: 180px;
    background-color: #f0f0f0;
    color: #888;
    text-align: center;
    line-height: 180px;
    margin: 10px 0 20px;
    border: 1px solid #ccc;
  }
  
  .image-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin: 10px 0 20px;
  }
  
  .image-row .image-placeholder {
    flex: 1;
    height: 150px;
  }
  `
})
export class HelpUsComponent {

  pageName:string = ''
  projectId:string = ''
  spinDownload: boolean = false;
  
  constructor(
    private readonly titleService: Title,
    private readonly route: ActivatedRoute){
    this.pageName = this.route.snapshot.data['name'];
  }
  
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
  }

}

<!--- Bread Crumb --->
<div class="v-pageName">{{pageName}} @if(migtypeid == '35'){Code Objects }</div>
@if(migtypeid == '35'){
    <div class="row mb-4">
        <div class="col-md-8">
            <h3 class="main_h mt-2">Manual Conversion @if(migtypeid == '35'){Code Objects}</h3>
        </div>
        <div class="col-md-4">
            <div class="qmigTabs">
                <ul class="nav nav-pills">
                    <li class="nav-item">
                        <a class="nav-link" routerLinkActive="active" routerLink="../manualConversion">Storage Object's</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" routerLinkActive="active" routerLink="../../codeMigration/manualConversionCO">Code Object's</a>
                    </li>
                </ul>
            </div>  
        </div>
    </div>
    }
<!--- Manual conversion form  --->
<div class="body-main res-d-none">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="manualForm">
                <div class="row">
                    <div class="col-md-3 col-xl-2 col-cm1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Run Number</label>
                            <select formControlName="runNumber" placeholder="Name" class="form-select" #run
                                (change)="getSchema(run.value)">
                                <option disabled>Select a Run Number</option>
                                @for ( list of runNumbersList; track list) {
                                <option value="{{ list.run_id }}">{{list.run_id }}</option>
                                }
                            </select>
                            @if(ff.runNumber.touched && ff.runNumber.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.runNumber.errors.required) {Run No is required}
                            </p>
                            }
                        </div>
                    </div>
                    @if(migtypeid=="30" || migtypeid=="20"){
                    <div class="col-md-2 col-xl-2 col-cm1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Source {{schemalable}}</label>
                            <select formControlName="schema" class="form-select" #sche
                                (change)="selectSrcSchema(sche.value)">
                                <option disabled>Select a Source Schema</option>
                                @for ( schema1 of srcSchemaList; track schema1) {
                                <option value="{{ schema1.schema_id }}"> {{schema1.schemaname }}</option>
                                }
                            </select>
                            @if(ff.schema.touched && ff.schema.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.schema.errors.required) {{{schemalable}} is required}
                            </p>
                            }
                        </div>
                    </div>
                    }
                    @if(migtypeid !="35"){
                    <div class="col-md-2 col-xl-2 col-cm1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Target {{schemalable}}</label>
                            <select formControlName="targetSchema" class="form-select" [(ngModel)]="schemaName" #schema
                                (change)="getObjectType(schema.value)">
                                <option disabled>Select a Target Schema</option>
                                @for ( schema of tgtSchemaList1; track schema) {
                                <option value="{{ schema.schema_id }}"> {{schema.schemaname }}</option>
                                }
                            </select>
                            @if(ff.targetSchema.touched && ff.targetSchema.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.targetSchema.errors.required) {Target {{schemalable}} is required}
                            </p>
                            }
                        </div>
                    </div>
                    }
                    <div class="col-md-2 col-xl-2 col-cm1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Type</label>
                            <select formControlName="objectType" class="form-select"
                                (change)="getobjtypeid(object.value)" #object>
                                <option disabled>Select a Object Type</option>
                                @if(migtypeid=="35"){
                                <option selected value="">Program</option>
                                }
                                @else{
                                @for ( objects of objectTypeList; track objects) {
                                <option value="{{objects.objecttype_id }}"> {{objects.objecttype_name }}</option>
                                }
                                }
                            </select>
                            @if(ff.objectType.touched && ff.objectType.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.objectType.errors.required) {Object Type is required}
                            </p>
                            }
                        </div>
                    </div>
                    @if(migtypeid != "35"){
                    <div class="col-md-2 col-xl-2 col-cm1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Status</label>
                            <select formControlName="objectStatus" class="form-select" #status
                                (change)="getObjectName(status.value)">
                                <option disabled>Select a Object Status</option>
                                <option value="true">Deployed</option>
                                <option value="false">Non Deployed</option>
                            </select>
                            @if(ff.objectStatus.touched && ff.objectStatus.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.objectStatus.errors.required) {Object Status is required}
                            </p>
                            }
                        </div>
                    </div>
                    }
                    @if(this.migtypeid== "35")
                    { <div class="col-md-3 col-xl-2 col-cm1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Name </label>
                            <select formControlName="objectName" class="form-select"
                                (change)="getObjectStatus(obj.value);getObjectCode()" formControlName="objectName" #obj>
                                <option disabled>Select a Object Name</option>
                                @for ( objects of objectNames; track objects) {
                                <option value="{{ objects.object_name }}"> {{objects.object_name }}</option>
                                }
                            </select>
                            @if(ff.objectName.touched && ff.objectName.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.objectName.errors.required) {Object Name is required}
                            </p>
                            }
                        </div>
                    </div>}
                    @else{
                    <div class="col-md-3 col-xl-2 col-cm1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Name @if(hidedata){( <small>{{
                                    ff.objectStatus.value == 'true' ? 'Deployed :' : 'Non Deployed : ' }}
                                    @if(objectNames){{{objectNames.length}}}</small>)}</label>
                            <select formControlName="objectName" class="form-select"
                                (change)="getObjectStatus(obj.value);getObjectCode()" formControlName="objectName" #obj>
                                <option disabled>Select a Object Name</option>
                                @for ( objects of objectNames; track objects) {
                                <option value="{{ objects.object_name }}"> {{objects.object_name }}</option>
                                }
                            </select>
                            @if(ff.objectName.touched && ff.objectName.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.objectName.errors.required) {Object Name is required}
                            </p>
                            }
                        </div>
                    </div>
                    }



                </div>
            </form>
            <!--- source and target code details --->
            @if(objectSelected){
            <div class="row">
                <div class="col-md-6">
                    <p><b>Source Code </b> </p>
                </div>
                <div class="col-md-6">
                    <p> <b>Target Code </b></p>
                </div>
                <div class="stmt_list">
                    <form>
                        @for (statement of statementList; track statement) {
                        <div class="row mx-0">
                            <div class="col-md-6 border_right pl-0">
                                <span class="stmt_show ">
                                    <hr />
                                </span>
                                <div class="stmt_card">
                                    <div class="stmt_block">
                                        @if(!statementShow && statement.tgt_id == currentStmt){
                                        <p>
                                            <a type="button" class="btn btn-stmt"
                                                (click)="convertCode(statement.tgt_id)">
                                                Conversion Statement
                                            </a>
                                        </p>
                                        }
                                        <textarea #div1 (keyup)="autoGrowTextZone($event)" id="div1"
                                            class="form-control gb_text" placeholder="Statement" disabled
                                            value="{{statement.src_code}}"> {{statement.src_code}}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 border_left">
                                <div class="stmt_card">
                                    <span class="stmt_show ">
                                        <hr />
                                        <div class="row">
                                            <div class="col-md-2">
                                                <span class="stmt_show_btn">
                                                    <i (click)="showStatement(statement.tgt_id)"
                                                        class="mdi mdi-chevron-down"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </span>
                                    <div class="stmt_block">
                                        <textarea #div2 id="div2"
                                            (change)="textareaChanged(statement.tgt_id, div2.value)"
                                            (keyup)="autoGrowTextZone($event)" class="form-control gb_text"
                                            placeholder="Statement"
                                            value="{{statement.updated_code}}">{{statement.updated_code}}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        }

                    </form>
                </div>
            </div>
            }
            @if(objectSelected){
            <div class="row">
                <form class="form qmig-Form" [formGroup]="deploymentForm">
                    <div class="row">
                        <div class="col-md-3 col-xl-3" [hidden]="!displayTargetConnection">
                            <div class="form-group">
                                <label class="form-label d-required" for="name">Target Connection Name</label>
                                <select placeholder="Target Connection" formControlName="conname" class="form-select"
                                    #Myselect2 (change)=" selTgtId(Myselect2.value)">
                                    <option disabled>Select a Target Connection</option>
                                    @for ( list of targetList; track list) {
                                    <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                    }
                                </select>
                                @if ( f.conname.touched && f.conname.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.conname.errors.required) { File is required }
                                </p>
                                }
                            </div>
                        </div>
                        <!-- <div class="col-md-3 col-xl-3" [hidden]="!displayTargetConnection">
                            <div class="form-group">
                                <label class="form-label d-required" for="name">Target Schema Name</label>
                                <select class="form-select" #tgtsc (change)="selectTgtSchema(tgtsc.value)">
                                <option selected value="">Select Target Schema</option>
                                @for(tgtsch of tgtSchemaList;track tgtsch; ){
                                <option value="{{tgtsch.schemaname}}">{{tgtsch.schemaname}}</option>
                                }
                            </select>
                            </div>
                        </div> -->
                        <div class="col-md-3 col-xl-3" [hidden]="!displayTargetConnection">
                            <div class="form-group">
                                <label class="form-label d-required" for="name">Time Taken To Fix</label>
                                <input type="text" id="timetaken" formControlName="time_taken_min" placeholder="Time"
                                    class="form-control" />
                                @if ( f.time_taken_min.touched && f.time_taken_min.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.time_taken_min.errors.required) { File is required }
                                </p>
                                }
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-6" [hidden]="!displayTargetConnection">
                            <div class="form-group">
                                <label class="form-label d-required" for="name">Deployment Observation Text</label>
                                <textarea id="w3review" name="w3review" formControlName="observations"
                                    class="form-control" item id="deployeeText" placeholder="Observation Text" rows="5"
                                    cols="20"></textarea>
                                @if ( f.observations.touched && f.observations.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.observations.errors.required) { File is required }
                                </p>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-2 col-xl-2 offset-md-6">
                                <button class="btn btn-upload w-100" [disabled]="buttonDisable" (click)="saveData()">
                                    <span class="mdi mdi-content-save-edit btn-icon-prepend"></span>
                                    Save@if(saveSpin){<app-spinner />}</button>
                            </div>
                            <div class="col-md-2 col-xl-2">
                                <button class="btn btn-upload w-100" [disabled]="deploymentForm.invalid"
                                    (click)="deployData()">
                                    <span class="mdi mdi-cog-play btn-icon-prepend"></span>
                                    Deploy@if(deploySpin){<app-spinner />}</button>
                            </div>
                            <div class="col-md-2 col-xl-2">
                                <button class="btn btn-upload w-100" [disabled]="buttonDisable"
                                    [hidden]="displayTargetConnection" (click)="Reset()">
                                    <span class="mdi mdi-content-save-off-outline btn-icon-prepend"></span>
                                    Reset</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            }

        </div>
    </div>
</div>

<div class="body-main mt-4 res-d-block">
    <div class="qmig-card">
        <p class="p-3 text-center">This page is only compatible with desktop. </p>
    </div>
</div>
<!--- Deployment log details --->
<div class="body-main mt-4">
    <div class="qmig-card" [hidden]="!objectSelected">
        <h3 class="main_h px-3 pt-3">Reports & Logs</h3>
        <div class="accordion accordion-flush qmig-accordion" id="accordionFlushExample">
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                        Deployment Log
                    </button>
                </h2>
                <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <div class="body-main mt-4">
                        <div class="qmig-card">
                            <div class="qmig-card-body">
                                <form class="form qmig-Form" [formGroup]="deploymentLog">
                                    <div class="row">
                                        <div class="col-md-3 col-xl-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="name">Deployed time
                                                    stamp</label> @if(!deploystatusSpin){
                                                <button class="btn" (click)="GetDeployStatus()">
                                                    <span class="mdi mdi-sync"></span>
                                                </button>
                                                }
                                                <select class="form-select" #Time
                                                    (change)="filterDeployData(Time.value)" formControlName="timeStamp">
                                                    <option selected value="">Select Deploy time stamp</option>
                                                    @for ( stamp of TgtDeployData; track stamp) {
                                                    <option value="{{stamp.activitytime}}"> {{stamp.activitytime }}
                                                    </option>
                                                    }
                                                </select>

                                            </div>
                                        </div>
                                        <div class="col-md-3 col-xl-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="name">Deployed
                                                    Status</label>
                                                <input type="text" class="form-control" value="{{deploystatuss}}"
                                                    placeholder="Status" formControlName="status" />
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-xl-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="name">Deploy Time </label>
                                                <input type="text" class="form-control" value="{{deployTime}}"
                                                    placeholder="Time" formControlName="time" />
                                            </div>
                                        </div>
                                        <div class="col-md-3 col-xl-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="name">Deploy Cumulative
                                                    Time </label>
                                                <input type="text" class="form-control" value="{{totaldeploy}}"
                                                    placeholder="cumulative Time" formControlName="cumulativeTime" />
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-xl-6">
                                            <div class="form-group">
                                                <h6>Status Message</h6>
                                                <textarea id="w3review" name="w3review" class="form-control"
                                                    placeholder="Status Message" value="{{statusMessages}}"
                                                    formControlName="statusMessage" rows="10" cols="50"></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-xl-6">
                                            <div class="form-group">
                                                <h6>Observation Text</h6>
                                                <textarea id="w3review" name="w3review" class="form-control"
                                                    placeholder="Status Message" value="{{observations}}"
                                                    formControlName="observationTime" rows="10" cols="50">
                                            </textarea>
                                            </div>
                                        </div>
                                    </div>

                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
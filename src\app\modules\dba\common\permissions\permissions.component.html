<div class="v-pageName">{{pageName}}</div>
<!--- Validation--->

<div class="qmig-card mt-3">
    <div class="body-main mt-4">
        <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-heading">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                        View Permission Deployment
                    </button>
                </h2>
                <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <form class="form qmig-Form" [formGroup]="exeForm">
                                <div class="row">
                                    <div class="col-md-3 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="targtetConnection">Connection
                                                Name</label>
                                            <select class="form-select" formControlName="tgtCon" #con
                                                (change)="Getdbname(con.value)">
                                                <option selected value="">Select a connection Name</option>
                                                @for(ConsList1 of ConsList ;track ConsList1; ){
                                                <option value="{{ ConsList1.Connection_ID  }}"> {{ ConsList1.conname }}
                                                </option>
                                                }
                                            </select>
                                            @if ( getControl.tgtCon.touched && getControl.tgtCon.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (getControl.tgtCon.errors.required) {Connection name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <!-- Database details -->
                                    <div class="col-md-3 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="Schema">Database</label>
                                            <select class="form-select" formControlName="db" #db
                                                (change)="selectDB(db.value);getSchemas();">
                                                <option selected value="">Select a Database</option>
                                                @for(dbs of dblist;track dbs; ){
                                                <option value="{{ dbs.dbname }}"> {{ dbs.dbname }} </option>
                                                }
                                            </select>
                                            @if ( getControl.db.touched && getControl.db.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (getControl.db.errors.required) {Database name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-xl-3 mt-4 pt-1">
                                        <div class="form-group">

                                            <button class="btn btn-upload " type="button" (click)="GetExecutedata()"
                                                [disabled]="exeForm.invalid"> <span class=""></span>
                                                Execute @if(spinner_exe){<app-spinner />} </button>

                                        </div>
                                    </div>
                                </div>
                            </form>
                            <!-- <hr class="mt-4" />  -->
                            <!-- connection Name details - -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="accordion-item">
                <h2 class="accordion-header" id="headingtwo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapsetwo" aria-expanded="false" aria-controls="collapsetwo">
                        Permissions
                    </button>
                </h2>
                <div id="collapsetwo" class="accordion-collapse collapse" aria-labelledby="headingtwo"
                    data-bs-parent="#accordionExample">
                    <div class="body-main mt-4">
                        <div class="qmig-card">
                            <div class="qmig-card-body">
                                <form class="form qmig-Form" [formGroup]="permissionForm">
                                    <div class="row">
                                        <div class="col-md-4 col-xl-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="targtetConnection">Connection
                                                    Name</label>
                                                <select class="form-select" formControlName="connection" #con1
                                                    (change)="Getdbname(con1.value)">
                                                    <option selected value="">Select a connection Name</option>
                                                    @for(Cons of ConsListForPermissions ;track Cons; ){
                                                    <option value="{{ Cons.Connection_ID  }}"> {{
                                                        Cons.conname }} </option>
                                                    }
                                                </select>
                                                @if ( f.connection.touched && f.connection.invalid) {
                                                <p class="text-start text-danger mt-1">
                                                    @if (f.connection.errors.required) {Connectionname is required }
                                                </p>
                                                }
                                            </div>
                                        </div>
                                        <!-- Database details -->
                                        <div class="col-md-4 col-xl-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="Schema">Database</label>
                                                <select class="form-select" #db1 formControlName="database"
                                                    (change)="selectDB(db1.value);GetUsers()">
                                                    <option selected value="">Select a Database</option>
                                                    @for(dbs of dblist;track dbs; ){
                                                    <option value="{{ dbs.dbname }}"> {{ dbs.dbname }} </option>
                                                    }
                                                </select>
                                                @if ( f.database.touched && f.database.invalid) {
                                                <p class="text-start text-danger mt-1">
                                                    @if (f.database.errors.required) {Database is required }
                                                </p>
                                                }
                                            </div>
                                        </div>
                                        <!-- Category details  -->
                                        <!-- <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="Schema">Category</label>
                                            <select class="form-select" #Cate formControlName="category"
                                                (change)="SelectUserorRole(Cate.value)">
                                                <option selected value="">Select a Category</option>
                                                <option value="0">User</option>
                                                <option value="1">Role</option>
                                            </select>
                                            @if ( f.category.touched && f.category.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.category.errors.required) {category is Required }
                                            </p>
                                            }
                                        </div>
                                    </div> -->
                                  
                                        <!-- Role/User details -->
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="usrrole">Users</label>
                                                <select class="form-select" #usrrole formControlName="usr_rol"
                                                    (change)="selectusrrole(usrrole.value);getSchemas()">
                                                    <option selected value="">Select a User</option>
                                                    @for(user of Userlist;track user; ){
                                                    <option value="{{ user.username}}"> {{ user.username }}
                                                    </option>
                                                    }
                                                    <!-- @if (roleSelected){ @for(role of rolelist;track role; ){
                                                <option value="{{ role.rolename}}"> {{ role.rolename }}
                                                </option>
                                                }} -->
                                                </select>
                                                @if ( f.usr_rol.touched && f.usr_rol.invalid) {
                                                <p class="text-start text-danger mt-1">
                                                    @if (f.usr_rol.errors.required) {User is required }
                                                </p>
                                                }
                                            </div>
                                        </div>
                                        <!-- schema details -->
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="Schema">Schema</label>
                                                <!-- <ng-select [items]="schemaList" [multiple]="true" bindLabel="schemaname"
                                                    groupBy="type" [selectableGroup]="true" formControlName="schemaname"
                                                    (change)="selectschema(selectedItems);GetObjectTypes()"
                                                    [(ngModel)]="selectedItems" [closeOnSelect]="false"
                                                    bindValue="schemaname">
                                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <input id="item-{{index}}" type="checkbox"
                                                            [ngModel]="item$.selected" />
                                                        {{item.type | uppercase}}
                                                    </ng-template>
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <input id="item-{{index}}" type="checkbox"
                                                            [(ngModel)]="item$.selected"
                                                            [ngModelOptions]="{ standalone : true }" />
                                                        {{item.schemaname}}
                                                    </ng-template>

                                                </ng-select> -->
                                                <select class="form-select" #usrrole formControlName="schemaname"
                                                #sc  (change)="selectschema(sc.value);GetObjectTypes()"  >
                                                    <option selected value="">Select a Schema</option>
                                                    @for(sch of schemaList;track sch; ){
                                                    <option value="{{ sch.schemaname}}"> {{ sch.schemaname }}
                                                    </option>
                                                    }
                                                  
                                                </select>
                                                @if ( f.schemaname.touched && f.schemaname.invalid) {
                                                <p class="text-start text-danger mt-1">
                                                    @if (f.schemaname.errors.required) {Schemaname is required }
                                                </p>
                                                }

                                            </div>
                                        </div>
                                        <!-- object type details -->
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="obj">Object Type</label>
                                                <select class="form-select" formControlName="objType" #objty
                                                    (change)="selectobjtype(objty.value);GetObjects()">
                                                    <option selected value="">Select a object type</option>
                                                    @for(objitem of objecttypelist;track objitem; ){
                                                    <option value="{{ objitem.object_Type}}"> {{
                                                        objitem.object_Type}} </option>
                                                    }
                                                </select>
                                                @if ( f.objType.touched && f.objType.invalid) {
                                                <p class="text-start text-danger mt-1">
                                                    @if (f.objType.errors.required) {Objecttype is required }
                                                </p>
                                                }
                                            </div>
                                        </div>

                                        <!-- object name details -->
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label" for="Schema">Object Name</label>
                                                <ng-select [items]="objectlist" [multiple]="true"
                                                    bindLabel="object_Name" groupBy="type" [selectableGroup]="true"
                                                    formControlName="objName"
                                                    (change)="selectobjectname(selectedItems1)"
                                                    [(ngModel)]="selectedItems1" [closeOnSelect]="false"
                                                    bindValue="object_Name">
                                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <input id="item-{{index}}" type="checkbox"
                                                            [ngModel]="item$.selected" />
                                                        {{item.type | uppercase}}
                                                    </ng-template>
                                                    <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                        let-index="index">
                                                        <input id="item-{{index}}" type="checkbox"
                                                            [(ngModel)]="item$.selected"
                                                            [ngModelOptions]="{ standalone : true }" />
                                                        {{item.object_Name}}
                                                    </ng-template>

                                                </ng-select>
                                                @if ( f.objName.touched && f.objName.invalid) {
                                                <p class="text-start text-danger mt-1">
                                                    @if (f.objName.errors.required) {Objectname is required }
                                                </p>
                                                }
                                            </div>
                                        </div>
                                        <!-- <div class="row"> -->
                                        <!-- <div class="col-md-3"></div> -->
                                        <div class="col-md-3 col-xl-3 pt-1">
                                            <button class="btn btn-upload  mt-4" type="button"
                                                (click)="GetPermissions(0)" [disabled]="permissionForm.invalid">
                                                <span class=""></span>
                                                Fetch @if(spinner){<app-spinner />}
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="body-main mt-4" [hidden]="!hideTable">

                        <div class="qmig-card">
                            <div class="row">
                                <div class="col-md-10 "></div>
                                <div class="col-md-2 pb-3">
                                    <button class="btn btn-upload  mt-4" type="button" (click)="editcheckbox()" [disabled]="disableEdit">
                                        Edit
                                    </button>&nbsp;
                                    <button class="btn btn-upload  mt-4" type="button" (click)="submitGrants()" [disabled]="disableEdit">
                                        Save @if(spin_save){<app-spinner />}
                                    </button>
                                </div>
                            </div>
                            <!-- <h3 class="main_h px-3 pb-1 pt-3">Roles</h3> -->
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>S.NO</th>
                                        <th>Owner</th>
                                        <th>Schema</th>
                                        <th>Object Type</th>
                                        <th>Object Name</th>
                                        @if(TableSelect ||SequenceSelect ){<th>Select</th>}
                                        @if(TableSelect){<th>Insert</th>}
                                        @if(TableSelect ||SequenceSelect) {<th>Update</th>}
                                        @if(TableSelect){<th>Delete</th>
                                        <th>Truncate</th>
                                        <th>References</th>
                                        <th>Trigger</th>}
                                        @if(FunctionSelect){ <th>Execute</th>}
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (documents of searchresult | searchFilter: searchText1
                                    |paginate:
                                    {itemsPerPage:50,currentPage: p2,id:'First'} ; track
                                    documents; let i = $index) {
                                    <tr>

                                        <td>{{ pi * (page1 - 1) + i + 1 }}</td>
                                        <td>{{documents.object_Owner}}</td>
                                        <td>{{documents.schema}}</td>
                                        <td>{{documents.object_Type }}</td>
                                        <td>{{documents.object_Name}}</td>
                                        @if(TableSelect ||SequenceSelect ){
                                        <td>
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    [disabled]="disablecheckbox"
                                                    (click)="getMethod($event,documents.object_Name,'select')"
                                                    [checked]='documents.grants.toLowerCase().includes("select")'>
                                                <i class="input-helper"></i>
                                            </label>
                                        </td>
                                        }
                                        @if(TableSelect){
                                        <td>
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    [disabled]="disablecheckbox"
                                                    (click)="getMethod($event,documents.object_Name,'insert')"
                                                    [checked]='documents.grants.toLowerCase().includes("insert")'>
                                                <i class="input-helper"></i>
                                            </label>
                                        </td>
                                        }
                                        @if(TableSelect ||SequenceSelect){
                                        <td>
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    [disabled]="disablecheckbox"
                                                    (click)="getMethod($event,documents.object_Name,'update')"
                                                    [checked]='documents.grants.toLowerCase().includes("update")'>
                                                <i class="input-helper"></i>
                                            </label>
                                        </td>
                                        }
                                        @if(TableSelect){
                                        <td>
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    [disabled]="disablecheckbox"
                                                    (click)="getMethod($event,documents.object_Name,'delete')"
                                                    [checked]='documents.grants.toLowerCase().includes("delete")'>
                                                <i class="input-helper"></i>
                                            </label>
                                        </td>
                                        <td>
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    [disabled]="disablecheckbox"
                                                    (click)="getMethod($event,documents.object_Name,'truncate')"
                                                    [checked]='documents.grants.toLowerCase().includes("truncate")'>
                                                <i class="input-helper"></i>
                                            </label>
                                        </td>
                                        <td>
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    [disabled]="disablecheckbox"
                                                    (click)="getMethod($event,documents.object_Name,'references')"
                                                    [checked]='documents.grants.toLowerCase().includes("references")'>
                                                <i class="input-helper"></i>
                                            </label>
                                        </td>
                                        <td>
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    [disabled]="disablecheckbox"
                                                    (click)="getMethod($event,documents.object_Name,'trigger')"
                                                    [checked]='documents.grants.toLowerCase().includes("trigger")'>
                                                <i class="input-helper"></i>
                                            </label>
                                        </td>
                                        }
                                        @if(FunctionSelect){
                                        <td>
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    [disabled]="disablecheckbox"
                                                    (click)="getMethod($event,documents.object_Name,'execute')"
                                                    [checked]='documents.grants.toLowerCase().includes("execute")'>
                                                <i class="input-helper"></i>
                                            </label>
                                        </td>
                                        }

                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">No Results</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                            <div class="custom_pagination">
                                <pagination-controls (pageChange)="p2 = $event" id="First"></pagination-controls>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Validation
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="execProjectForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Run No</label>
                                    <select class="form-select" #run (change)="getRunNumber(run.value)"
                                        formControlName="runNo">
                                        <option selected value="">Select Run No</option>
                                        @for( list of runnoForReports;track list;){
                                        <option value="{{ list.iteration}}">{{list.iteration}}</option>
                                        }
                                    </select>
                                    @if ( f.runNo.touched && f.runNo.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.runNo.errors.required) {Run No is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Source Connection Name</label>
                                    <select class="form-select" #Myselect1 (change)="
                                selectedfiletype(Myselect1.value);
                            " formControlName="connectionType">
                                        <option selected value="">Select Connection </option>
                                        @for(list of ConsList ;track list; ){
                                        <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                        }
                                    </select>
                                    @if ( f.connectionType.touched && f.connectionType.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.connectionType.errors.required) {connectionType is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Source {{schemalable}} Name</label>
                                    <select class="form-select" #sch (change)="selectschema(sch.value)"
                                        formControlName="schema">
                                        <option selected value="">Select Source {{schemalable}}</option>
                                        @for(sc of schemaList;track sc; ){
                                        <option value="{{sc.schema_name}}">{{sc.schema_name}}</option>
                                        }
                                    </select>
                                    @if ( f.schema.touched && f.schema.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.schema.errors.required) {schema is Required }
                                    </p>
                                    }
                                    <!-- <ng-select [placeholder]="'Select Schema Name'" [items]="schemaList"
                                                (change)="selectschema(selectedItems)" [multiple]="true" bindLabel="schemaname"
                                                groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false"
                                                bindValue="schemaname" [(ngModel)]="selectedItems"
                                                [ngModelOptions]="{standalone: true}">
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                                    <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                        [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                                </ng-template>
                                            </ng-select> -->
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="name">Target Connection </label>
                                    <select class="form-select" (change)="
                                                selectTgtId(tgtConnection.value);getSchemasList(tgtConnection.value)"
                                        #tgtConnection formControlName="targetConnection">
                                        <option selected value="">Select Target Connection</option>
                                        @for(list1 of tgtlist;track list1; ){
                                        <option value="{{list1.Connection_ID}}">{{list1.conname}}</option>
                                        }
                                    </select>
                                    @if ( f.targetConnection.touched && f.targetConnection.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.targetConnection.errors.required) {TargetConnection is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="name">Target {{schemalable}} Name</label>
                                    <select class="form-select" #tgtsc (change)="selectTgtSchema(tgtsc.value)">
                                        <option selected value="">Select Target {{schemalable}}</option>
                                        @for(tgtsch of tgtSchemaList;track tgtsch; ){
                                        <option value="{{tgtsch.schema_name}}">{{tgtsch.schema_name}}</option>
                                        }
                                    </select>
                                    <!-- <ng-select [placeholder]="'Select Schema Name'" [items]="tgtSchemaList"
                                                (change)="selectTgtSchema(selectedItems)" [multiple]="true" bindLabel="schemaname"
                                                groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false"
                                                bindValue="schemaname" [(ngModel)]="selectedItemstgt"
                                                [ngModelOptions]="{standalone: true}">
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                                    <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                        [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                                </ng-template>
                                            </ng-select> -->
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Operation</label>
                                    <select class="form-select" #MyOp (change)="selectOperation(MyOp.value)"
                                        formControlName="operation">
                                        <option selected value="">Select operation</option>
                                        @for(list of operation;track list; ){
                                        <option value="{{list.operation_category }}">{{list.operation_category}}
                                        </option>
                                        }
                                    </select>
                                    @if ( f.operation.touched && f.operation.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.operation.errors.required) {Operation is Required }
                                    </p>
                                    }
                                </div>
                            </div>

                            <!-- <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="Schema">Pattern(optional)</label>
                                                <input class="form-control" type="text" id="pattern" placeholder="Pattern" />
                                            </div>
                                        </div> -->

                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="body-header-button">
                                    <button (click)="openModal(this.execProjectForm.value)"
                                        [disabled]="execProjectForm.invalid" class="btn btn-upload w-100 mt-4"
                                        data-bs-toggle="offcanvas" data-bs-target="#demo">
                                        <span class="mdi mdi-checkbox-marked-circle-outline" aria-hidden="true"></span>
                                        Check Details</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Execution Status
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-7 offset-5 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" aria-controls="example"
                                    class="form-control" [(ngModel)]="searchText" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="getreqTableData()">
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table" id="example" style="width: 100%">
                            <thead>
                                <tr>
                                    <th>RunNo</th>
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>Operation Category</th>
                                    <th>{{schemalable}} Name</th>
                                    <th>Start Date </th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (con of tabledata |searchFilter: searchText| paginate: { itemsPerPage: 10,
                                currentPage: page2 ,id:'second'} ;track con) {

                                <tr>
                                    <td>{{con.iteration_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_name }}</td>
                                    <td>{{con.operation_category}}</td>
                                    <td>{{ con.schema_name }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        {{con.status}}
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="page2 = $event" id="second"></pagination-controls>
                    </div>

                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Reports
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-6 col-sm-3 col-md-3 col-lg-3 col-xl-3 mt-3">
                            <div class="form-group">
                                <select class="form-select ml-0" #iter (change)="selectIteration(iter.value)">
                                    <option selected value="">
                                        Select Run Number
                                    </option>

                                    @for(role of runnoForReports ;track role; ){
                                    <option value="{{ role.iteration }}"> {{ role.iteration }}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-6 col-sm-3 col-md-3 col-lg-3 col-xl-3 mt-3"></div>
                        <div class="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-6 d-flex">
                            <div class="custom_search cs-r me-3 my-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Validation Reports" aria-controls="example" />
                            </div>
                            <button class="btn btn-sync" (click)="getUpdatedRunNumber()">
                                @if(getRunSpin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(docs of assessmentFiles |searchFilter: datachange|paginate:{
                            itemsPerPage: 10, currentPage: p, id:'third'};
                            track docs){
                            <tr>
                                <td>{{ p*10+$index+1-10 }}</td>
                                <td>{{docs.fileName }}</td>
                                <td>{{docs.created_dt }}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(docs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                </div>
            </div>
        </div>
        <!-- <div class="accordion-item">
                    <h2 class="accordion-header" id="flush-headingTwo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                            Page Activity Log
                        </button>
                    </h2>
                    <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                        data-bs-parent="#accordionFlushExample">
                        <div class="qmig-card-body">
                            <div class="row">
                                <div class="col-sm-6 col-md-4 col-xl-2">
                                    <div class="form-group">
                                        <select class="form-select ml-0" #assessment
                                            (change)="GetIndividualLogs(assessment.value)">
                                            <option selected value="">
                                                Select Run Number
                                            </option>
                                            @for(role of runNoData ;track role; ){
                                            <option value="{{ role.iteration }}"> {{ role.dbschema }}
                                            </option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-md-8 col-xl-10">
                                    <div class="custom_search cs-r">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Activity Logs" aria-controls="example"
                                            [(ngModel)]="datachange2" (keyup)="onKey()" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table" id="example" style="width: 100%">
                                <thead>
                                    <tr>
                                        <th>SNo</th>
                                        <th>Run No</th>
                                        <th>Date and Time</th>
                                        <th>Task</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (documents of logdata |searchFilter: datachange2| paginate: { itemsPerPage: 10,
                                    currentPage: page , id: 'first'} ;track documents;){
                                    <tr>
                                        <td>{{page*10+$index+1-10}}</td>
                                        <td>{{ documents.iteration }}</td>
                                        <td>{{ documents.activity_date }}</td>
                                        <td>{{ documents.migtask }}</td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="page = $event" id="first">
                            </pagination-controls>
                        </div>
                    </div>
                </div> -->
    </div>
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Validation</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <div class="offcanvas-body">
            <form class="form qmig-Form">
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Source Connection</label>
                    : &nbsp;{{ conName }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Source Schema Name</label>
                    : &nbsp;{{ selectedschemas }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name">Target Connection</label>
                    : &nbsp;{{tgtCon}}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Target Schema Name</label>
                    : &nbsp;{{ selectedtgtScema }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name">Operation</label>
                    : &nbsp;{{OpName}}
                </div>

                <div class="form-group">
                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 me-1" (click)="
                        ConversionCommand()">
                            <span></span> Execute</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
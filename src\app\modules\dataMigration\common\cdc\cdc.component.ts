import { Component } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { CommonModule } from '@angular/common';
import { AssessmentService } from '../../../../services/assessment.service';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { SpinnerComponent } from "../../../../shared/components/spinner/spinner.component";
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { GetConfigData, deleteFile } from '../../../../models/interfaces/types';
import { NgSelectModule } from '@ng-select/ng-select';
import { elementAt } from 'rxjs';
declare let $: any;

@Component({
  selector: 'app-cdc',
  standalone: true,
  imports: [NgxPaginationModule, SearchFilterPipe, CommonModule, FormsModule, ReactiveFormsModule, SpinnerComponent, NgSelectModule],
  templateUrl: './cdc.component.html',
  styles: ``
})
export class CdcComponent {
  ExecutionForm: any
  userData: any = [];
  p: number = 1;
  datachange: any;
  pi: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;

  p1: number = 1;
  p2: number = 1;
  pi1: number = 10;
  piB: number = 10;
  page1: number = 1;
  page2: number = 1;
  datachange1: any;
  not_grid1: boolean = true;
  grid_active1: boolean = true;

  pi2: number = 10;
  page3: number = 1;
  datachange2: any;
  not_grid2: boolean = true;
  grid_active2: boolean = true;


  dagForm: FormGroup = this.formBuilder.group({
    Dag: ['', [Validators.required]],
    operations: ['', [Validators.required]],
    type: ['']
  })


  cdcForm: any;


  getSpin: boolean = false;
  projectId: any;

  getDagSpin: boolean = false;
  serverResponse: any

  dagTriggered: boolean = true;
  scheduleSelect: boolean = false
  operations: any = []

  initTypes: any = [{ option: "Normal", value: "default" },
  { option: "High Volume", value: "high_volume" }]
  migtypeid: any
  searchText1: string = ''
  searchText: string = ''
  cdcLoadData: any = [
    { name: "Database", value: "Database" },
    { name: "File", value: "File" },
  ]

  logFiles: any
  pageName: string = ''
  airflowFiles: any;
  getForm: any;
  cdcLoadForm: any
  schemalable: string = ""
  selectedSchemas: any = []
  constructor(private titleService: Title, private route: ActivatedRoute, private toast: HotToastService, private datamigration: DataMigrationService, private assessment: AssessmentService, private codemigration: CodeMigrationService, private formBuilder: FormBuilder, private project: DataMigrationService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }
    if (this.migtypeid == "41") {
      var tables = [{
        table_name: 'ahcpatient'
      },
      {
        table_name: 'emp_data'
      }]
      this.tablesList = tables
    }
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getConfigMenu()
    this.fetchConfigFiles()
    this.getConfigData()
    this.fetchCloudFiles()
    if (this.migtypeid == "31" || this.migtypeid == "40") {
      this.operations = [
        { option: "CDC", value: "CDC" },
      ]
    }
    else if (this.migtypeid == "41") {
      this.operations = [
        { option: "Reverse CDC", value: "Reverse_CDC" },
      ]
    } else {
      this.operations = [
        { option: "CDC", value: "CDC" },
        { option: "Load Files", value: "Load_Files" },
        { option: "Reverse CDC", value: "Reverse_CDC" },
      ]
    }

    this.ExecutionForm = this.formBuilder.group({
      // operation: ['', [Validators.required]],
      config: ['', [Validators.required]],

    })
    this.cdcForm = this.formBuilder.group({
      configFile: ['', [Validators.required]],
      fromDate: ['', [Validators.required]],
      toDate: ['', [Validators.required]],
    })
    this.getForm = this.formBuilder.group({
      operation: ['', [Validators.required]],
      confiName: ['', [Validators.required, Validators.maxLength(15)]],
      sourceConnection: ['', [Validators.required]],
      targetConnection: [''],
      tables: [''],
      schemas: [''],
      tgtschemas: [''],
      cdcLoadType: ['', [Validators.required]],
      replicate: [''],
    })
    if (this.migtypeid == "41") {
      this.cdcLoadForm = this.formBuilder.group({
        targetConnection: ['', [Validators.required]],
        confiName: ['', [Validators.required, Validators.maxLength(15)]],
        schemas: ['', [Validators.required]],
        tables: ['', [Validators.required]],
      });
    }
    else if (this.migtypeid == "31") {
      this.cdcLoadForm = this.formBuilder.group({
        operation: ['', [Validators.required]],
        confiName: ['', [Validators.required, Validators.maxLength(15)]],
        refFile: ['', [Validators.required]],
      });
    }
    this.GetConsList()
    //this.getDags()
    this.dagTriggered = false
    this.GetDagRes("refresh")
    // this.getDags();
  }
  get f() {
    return this.getForm.controls;
  }
  /*-Form Validation-*/
  get fs() {
    return this.cdcForm.controls;

  }
  get forms(): any {
    return this.ExecutionForm.controls;
  }
  get e() {
    return this.cdcLoadForm.controls;
  }

  selectedOperation: any
  initialselected: boolean = false
  initType: any
  selecttype(value: any) {
    this.initType = value
  }

  onKey() {
    var sercachbar = (<HTMLInputElement>document.getElementById("searchdag")).value
    this.p1 = 1;
    this.page2 = 1;
    this.p2 = 1;
    this.page3 = 1;
    this.p = 1;
    //console.log(sercachbar, "bar")
    if (sercachbar.length > 0) {
      this.isDisableAllCheck = true
    }
    else {
      this.isDisableAllCheck = false
    }
  }

  configFiles: any = []
  fetchConfigFiles() {
    var path = "Config_Files/Data_Migration"
    this.datamigration.getFilesfromExPath(path).subscribe((data: any) => {
      this.configFiles = data
    })
  }

  filteredConfigFiles: any = []
  filterConfigFiles(value: any) {
    this.filteredConfigFiles = []
    if (value == "E2E_Data_Load") {
      this.filteredConfigFiles = this.configFiles.filter((item: any) => {
        return item.fileName.startsWith("e2e_")
      })
    } else if (value == "Index_Data_Load") {
      this.filteredConfigFiles = this.filteredConfigFiles = this.configFiles.filter((item: any) => {
        return item.fileName.startsWith("index_")
      })
    }
    else if (value == "CDC_Data_Load") {
      this.filteredConfigFiles = this.filteredConfigFiles = this.configFiles.filter((item: any) => {
        return item.fileName.startsWith("cdc_")
      })
    } else if (value == "CDC") {
      this.filteredConfigFiles = this.cdcfiles
    }

  }

  srcId: any
  replicationList: any;
  getDb(value: any) {
    this.srcId = value
  }

  getPGTables(value: any) {
    const pgObj = {
      conid: this.tgtId,
      schema: value
    }
    if (this, this.migtypeid != "41") {
      this.datamigration.GetPGTables(pgObj).subscribe((data: any) => {
        this.tablesList = data
      })
    }
  }

  dagsInfo: any
  ref_spin: boolean = false
  isDisableAllCheck: boolean = false
  getDags() {
    this.datamigration.getDags().subscribe((data: any) => {
      this.dagsInfo = data
      this.dagsInfo.filter((item: any) => { return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true })
    })
  }
  getDagFormValue(value: any) {
    //console.log(value)
  }

  dagSelected: boolean = false
  selectedDag: any
  selectedDagName: string = ''
  cdcSelected: boolean = false
  selectDag(value: any) {
    this.dagSelected = true
    this.selectedDag = value
    this.selectedDagName = value.split('/').pop()
    this.selectedDagName == 'CDC_AF_DEMO_504_PM.xlsx' ? this.cdcSelected = true : this.cdcSelected = false;
    //console.log(value.split('/').pop())
  }
  srcCon: any
  SelSrcConn(value: any) {
    this.srcCon = value
  }
  trigger_spin: boolean = false;
  scnValue: any

  executeSpin: boolean = false
  getSCN() {
    this.executeSpin = true
    if (this.migtypeid == "20") {
      this.triggerDag()
    }
    else {
      if (this.srcCon == "" || this.selectedOperation == "Reverse_CDC") {
        this.allIPs = this.allIPs.filter((itemy: any) => {
          return itemy.isSelected == true
        })
        this.triggerDag()
      } else {
        this.datamigration.getScnNumber(this.srcCon).subscribe((data: any) => {
          this.executeSpin = false
          if (data != undefined) {
            this.scnValue = data.scn
            if (this.scnValue != "") {
              this.allIPs = this.allIPs.filter((itemk: any) => {
                return itemk.isSelected == true
              })
              //console.log(this.allIPs)
              $('#updateOrgModal').offcanvas('hide');
              this.triggerDag()
            }
          }
          else {
            $('#updateOrgModal').offcanvas('hide');
            this.toast.error("Failed TO get SCN Number")
          }
        })
      }
    }
  }
  dagRes: any
  dagId: any;
  triggerDag() {
    this.trigger_spin = true
    const obj = {
      dagList: this.allIPs,
      scn: this.scnValue,
      type: this.initType
    }
    this.datamigration.TriggerMultiDagsWithCDC(obj).subscribe((data: any) => {
      this.dagRes = data;
      this.trigger_spin = false
      this.allIPs = []
      this.showCheckStatus = false
      this.dagsInfo.filter((item: any) => {
        if (item.isSelected == true) {
          item.isSelected = false
        }
      })
      $('#updateOrgModal').offcanvas('hide');
      this.toast.success(data.message)
    },
      error => {
        this.trigger_spin = false
        $('#updateOrgModal').offcanvas('hide');
        this.toast.error('Dag Trigger Failed ');
      })
  }

  CdcTriggerDag() {
    const TriggerDagIPS = this.allIPs.map((data: any) => {
      const smObj = {
        'dag_id': data,
        'isCDC': true
      }
      return smObj
    });
    let obj = {
      dagList: TriggerDagIPS,
      scn: ''
    }
    this.datamigration.TriggerMultiDagsWithCDC(obj).subscribe((data: any) => {
      this.dagRes = data;
      this.toast.success(data.message)
    })
    //console.log(obj)
  }

  dagStatus: any
  GetDagRes(value: any) {
    this.dagTriggered = (localStorage.getItem("DagExecuted") != "false")
    this.ref_spin = true
    this.dagId = localStorage.getItem("Dag_Id")
    const runId = localStorage.getItem("Run_Id")
    if (this.dagId != null && runId != null) {
      const obj = {
        dag_id: this.dagId,
        dag_run_id: runId
      }
      this.datamigration.getDagStatus(obj).subscribe((data: any) => {
        this.dagStatus = data.state;
        this.ref_spin = false
      })
    }
    else {
      this.ref_spin = false
      if (value != "refresh") {
        this.toast.error("Plaese Execute a Dag");
      }
    }
  }

  masterSelected: boolean = false;
  getIPfromCheck: any;
  isCheckBoxSel: boolean = false;
  showCheckStatus: boolean = false
  allIPs: any = []
  checkboxselect($event: any, value: any): void {
    //console.log(value)
    this.getIPfromCheck = value;
    if ($event.target.checked) {
      this.isCheckBoxSel = true
      this.allIPs.length === null ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
      //console.log(this.allIPs.length)
      this.showCheckStatus = false
      const abc = this.dagsInfo.filter((item: any) => {
        return item.dag_id == value.toString()
      })
      //console.log(abc)

      var def = this.allIPs.filter((itemz: any) => {
        return itemz.dag_id == value.toString()
      })
      if (def.length == 0) {
        if (abc[0].isSelected == false) {
          abc[0].isSelected = true
        }
        this.allIPs.push(abc[0])
      }
      else {
        this.allIPs.filter((itema: any) => {
          if (itema.dag_id == value.toString()) {
            itema.isSelected = true
          }
        })
      }
      var check = this.allIPs.filter((itemz: any) => {
        return itemz.isSelected == true
      })
      if (check.length == this.dagsInfo.length) {
        this.showCheckStatus = true
      }
      //console.log(this.allIPs)
    } else {
      //let dag_index = this.dagsInfo.indexOf(value);
      // let i = 0
      this.dagsInfo.filter((item: any) => {
        if (item.dag_id == value) {
          item.isSelected = false
        }
        // i++
      })
      // let index = this.allIPs.indexOf(value);
      // this.dagsInfo[dag_index].isSelected = false
      // let y=0
      this.allIPs.filter((item1: any) => {
        if (item1.dag_id == value) {
          item1.isSelected = false
        }

        // y++
      })
      var check1 = this.allIPs.filter((itemz: any) => {
        return itemz.isSelected == true
      })
      if (check1.length == this.dagsInfo.length) {
        this.showCheckStatus = true
      }
      //this.allIPs[index].isSelected = false
      //const rem = this.allIPs.splice(index, 1);
      //console.log(rem)
      //console.log(this.allIPs)
      this.showCheckStatus = false
      this.allIPs.length === 0 ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
    }
  }

  selectAll($event: any) {
    this.showCheckStatus = $event.target.checked
    if ($event.target.checked) {
      this.allIPs = []
      this.dagsInfo.filter((item: any) => {
        if (item.isDagAvailable !== false) {
          item.isSelected = true
          this.allIPs.push(item)
        }
      })
      this.isCheckBoxSel = true
    } else {
      this.allIPs = []
      for (const element of this.dagsInfo) {
        element.isSelected = false
      }
      this.isCheckBoxSel = false
    }
    //console.log(this.allIPs)
  }

  ConsList: any = [];
  tgtList: any
  GetConsList() {
    this.datamigration.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }
  dagData: any
  selectedPath: any
  getConfigDags(path: any) {
    this.showCheckStatus = false
    this.dagsInfo = []
    this.allIPs = []
    this.selectedPath = path
    // if(path.includes("/mnt/extra")){
    //   path = path.replace("/mnt/extra/", "")
    // }
    //  path="C:\\Users\\<USER>\\Downloads\\catchup_config.xlsx"
    this.p = 1
    this.datamigration.getExcelDagsNew(path).subscribe((data: any) => {
      this.dagsInfo = data.dagList
      this.srcCon = data.sourceConnectionId
      this.dagsInfo.filter((item: any) => {
        item.isSelected = false
        return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
      })
    })
    // }
  }
  reff_spin: boolean = false
  refreshdags() {
    if (this.selectedPath == undefined) {
      this.toast.error("Please select Config File")
    } else {
      this.dagsInfo = []
      this.allIPs = []
      this.p = 1
      this.reff_spin = true
      if (this.selectedOperation == 'Partition_Initial_Data_Load' || this.selectedOperation == 'GG_Initial_Data_Load' || this.selectedOperation == "Initial_Data_Load") {
        this.datamigration.getggpdDags(this.selectedPath).subscribe((data: any) => {
          this.dagsInfo = data.dagList
          this.reff_spin = false
          this.srcCon = data.sourceConnectionId
          this.dagsInfo.filter((item: any) => {
            item.isSelected = false
            return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
          })
        })
      } else {
        this.datamigration.getExcelDags(this.selectedPath).subscribe((data: any) => {
          this.reff_spin = false
          this.dagsInfo = data.dagList
          this.srcCon = data.sourceConnectionId
          this.dagsInfo.filter((item: any) => {
            item.isSelected = false
            return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
          })
        })
      }
    }
  }
  count: any = []
  openPopup() {
    this.count = this.allIPs.filter((item: any) => {
      return item.isSelected == true
    })
  }
  scheduleResponse: any
  schedule_spin: boolean = false
  scheduleDags() {
    this.schedule_spin = true
    var datestring = (<HTMLInputElement>document.getElementById("schDate")).value
    var dags: any = []
    let obj1: any = {}
    this.allIPs.forEach((item: any) => {
      obj1 = {}
      if (item.isSelected == true) {
        obj1['dag_id'] = item.dag_id
        dags.push(obj1);
      }
    })
    var date = new Date(datestring)
    var utc = date.toUTCString()
    var utcdate = new Date(utc);
    const year = utcdate.getUTCFullYear();
    const month = this.padZero(date.getUTCMonth() + 1); // Months are zero-based
    const day = this.padZero(date.getUTCDate());
    const hours = this.padZero(date.getUTCHours());
    const minutes = this.padZero(date.getUTCMinutes());
    const seconds = this.padZero(date.getUTCSeconds());

    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
    let obj = {
      dagsListreqs: dags,
      scheduleDate: formattedDate
    }
    //console.log(obj);
    this.datamigration.ScheduleDags(obj).subscribe((data: any) => {
      this.schedule_spin = false
      this.scheduleResponse = data.message
      if (data.message.includes("Dags scheduled")) {
        this.toast.success(this.scheduleResponse)
      } else {
        this.toast.error(this.scheduleResponse)
      }
      (<HTMLInputElement>document.getElementById("schDate")).value = "";
      $('#updateOrgModal').offcanvas('hide');
    })
  }
  padZero(num: number): string {
    return num < 10 ? '0' + num : num.toString();
  }
  popup(value: any) {
    if (value == 0) {
      this.scheduleSelect = true
    }
    else {
      this.scheduleSelect = false
    }
  }
  configData: any = []
  getConfigMenu() {
    this.datamigration.GetConfigMenu(this.migtypeid).subscribe((data: any) => {
      this.configData = data['Table1']
      this.configData = this.configData.filter((item: any) => {
        if (this.migtypeid == "31") {
          return item.configvalue == "E2E_Data_Load"
        } else {
          return item.configvalue == "E2E_Data_Load" || item.configvalue == "Index_Data_Load" || item.configvalue == "CDC_Data_Load"
        }

      })
      var cdcv = { configtype: "CDC", configvalue: "CDC" }
      this.configData.push(cdcv)

    })
  }

  fileResponse: any
  spinDownload: boolean = false;
  /*--- Download file   ---*/
  downloadFile(fileInfo: any) {
    this.datamigration.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spinDownload = false

    })
  }

  /*--- Delete Project Reports   ---*/

  deleteFiles(path: string) {
    this.datamigration.deleteFile(path).subscribe({
      next: (data: deleteFile) => {
        this.fetchCloudFiles()
        this.toast.success("Deleted Successfully")
      },
      error: (error) => {
        this.toast.error(error.message)
      },
    });
  }

  /*--- Apply Filter   ---*/

  applyFilter(filterValue: string) {
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
    this.airflowFiles = this.airflowFiles.filter((el: any) => { return el.fileName.includes(filterValue) })
  }
  cdcConfigFiles: any = []
  getConfigData() {
    this.datamigration.GetConfigData().subscribe((data: GetConfigData) => {
      this.cdcConfigFiles = data['Table1']
      this.cdcConfigFiles = this.cdcConfigFiles.filter((item: any) => {
        return item.file_name.startsWith("cdc_")
      })
    })
  }
  cdcResponse: any = []
  summarySpinner: boolean = false
  reportSpinner: boolean = false

  FetchCdcData(formData: any) {
    this.summarySpinner = true
    let obj = {
      configId: formData.configFile,
      toDate: formData.toDate.replace("T", " ") + ".000",
      fromDate: formData.fromDate.replace("T", " ") + ".000"
    }
    this.datamigration.GetCdcData(obj).subscribe((data: any) => {
      this.cdcResponse = data['Table1']
      this.summarySpinner = false
    })
  }
  TriggerCDC(formData: any) {
    this.reportSpinner = true
    var file = this.cdcConfigFiles.filter((item: any) => {
      return item.config_id == formData.configFile
    })
    let obj = {
      projectId: this.projectId.toString(),
      fileName: file[0].config_file_name,
      toDate: '"' + formData.toDate.replace("T", " ") + ".000" + '"',
      fromDate: '"' + formData.fromDate.replace("T", " ") + ".000" + '"'
    }
    this.datamigration.TriggerCDC(obj).subscribe((data: any) => {
      this.cdcResponse = data
      this.reportSpinner = false
      this.toast.success("Command Triggered");
    })
  }
  cdcfiles: any = []
  refCon_spin: boolean = false
  fetchCloudFiles() {
    const path = 'Config_Files/CDC'
    this.datamigration.getFilesfromExPath(path).subscribe((data: any) => {
      this.cdcfiles = data
      this.refCon_spin = false
    })
  }
  fileSrc: any
  fetchSourceConfigFiles(conn: any) {
    this.ref_spin = true
    this.fileSrc = conn
    const path = conn + "/Config_Files/CDC";
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.cdcfiles = data
      this.ref_spin = false
    })
  }
  filetgt:any
  fetchTargetConfigFiles(conn: any) {
    this.ref_spin = true
    this.filetgt=conn
    const path = this.fileSrc + "/" + this.filetgt + "/Config_Files/CDC";
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.cdcfiles = data
      this.ref_spin = false
    })
  }
  fileSrcexe: any
  cdcFileExe: any = []
  fetchSourceConfigFiles1(conn: any) {
    this.ref_spin = true
    this.fileSrcexe = conn
    const path = conn + "/Config_Files/CDC";
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.cdcFileExe = data
      this.ref_spin = false
    })
  }
  fetchTargetConfigFiles1(conn: any) {
    this.ref_spin = true
    const path = this.fileSrcexe + "/" + conn + "/Config_Files/CDC";
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.cdcFileExe = data
      this.ref_spin = false
    })
  }

  refreshConfigFiles() {
    this.refCon_spin = true
    this.fetchCloudFiles()
  }

  schemaList: any = []
  schemaListFiles: any = []
  tgtSchemaList: any = []
  selectedItemstgt: any
  getSchemasList(ConnectionId: any, value: string) {
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId,
    };
    if (this.migtypeid == "31" && value == "S") {
      this.schemaList = []
      var local = this.ConsList.filter((item: any) => {
        return item.Connection_ID == ConnectionId
      })
      let obj = {
        schema_name: local[0].dbschema
      }
      this.schemaList.push(obj)
    }
    else {
      this.datamigration.SchemaListSelect(obj).subscribe((data: any) => {
        if (value == "S") {
          this.schemaList = data['Table1'];
        }
        else {
          this.tgtSchemaList = []
          this.schemaListFiles = []
          if (this.migtypeid == "41") {
            this.tgtList.filter((item: any) => {
              if (item.Connection_ID == ConnectionId) {
                this.tgtSchemaList.push({ schemaname: item.dbname })
                this.schemaListFiles.push({ schemaname: item.dbname })
              }
            })
            this.FetchMongoTables(ConnectionId)
          }
          else {
            this.tgtSchemaList = data['Table1'];
            this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => {
              return item.schemaname != "ALL"
            });
          }
        }
        if (this.migtypeid == "35" || this.migtypeid == "34") {
          this.fetchDynamoTables(ConnectionId);
        }
      });
      this.ReadLocalJson()
      this.ReadLocalJson1()
    }
  }
  tablesList: any = []
  fetchDynamoTables(conId: any) {
    this.datamigration.fetchDynamoTables(conId).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  FetchMongoTables(conId: any) {
    this.datamigration.FetchMongoTables(conId).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.table_name = item.dbname
      })
      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  schemaID: any
  schemaName: any = []
  selectedsrcschema: string = ""
  showschemaa: boolean = false
  onItemSelect(item: any) {
    //console.log(item)
    this.selectedsrcschema = item
    // if (item.length == 1) {
    if (item != undefined) {
      this.showschemaa = false;
      const schemaId = this.schemaList.filter((el: any) => el.schemaname == item.toString())
      this.schemaName.push(item);
      //console.log(schemaId)
      let obj = {
        schema: item,
        srcId: this.srcId
      }
      if (this.migtypeid == "35" || this.migtypeid == "34") {

      }
      else {
        this.datamigration.GetTablesByschemaGG(obj).subscribe((data: any) => {
          this.tablesList = data['Table1']
          this.tablesList.forEach((item: any) => {
            item.type = "ALL"
          })
          //console.log("Tables", this.tablesList)

        })
      }

      this.schemaID.push(schemaId[0].schema_id)
      // this.schemasCheck()
    }
    else {
      this.showschemaa = true;
    }
  }
  tableHide: boolean = false
  OnSchemasSelect() {
    if (this.selectedSchemas.length == 1) {
      this.tableHide = false
      let obj = {
        schema: this.selectedSchemas.toString(),
        srcId: this.srcId
      }
      if (this.migtypeid == "35" || this.migtypeid == "34") {

      }
      else {
        this.getOracleTables(this.selectedSchemas.toString())
        // this.datamigration.GetTablesByschemaGG(obj).subscribe((data: any) => {
        //   this.tablesList = data['Table1']
        //   this.tablesList.forEach((item: any) => {
        //     item.type = "ALL"
        //   })
        //   //console.log("Tables", this.tablesList)

        // })
      }
    }
    else {
      this.tableHide = true
    }
  }
  tgtId: any
  getTgtId(value: any) {
    this.tgtId = value
    this.datamigration.getRelication(value).subscribe((data: any) => {
      this.replicationList = data
    })
  }
  tgtschemaname: string = ""
  selecttgtschema(value: string) {
    this.tgtschemaname = value
  }
  executed: boolean = false
  DataMigrationCommand(formData: any, value: any) {
    let obj: any = []
    if (value == 1) {
      obj = {
        task: this.selectedTask,
        projectId: this.projectId.toString(),
        srcId: formData.sourceConnection,
        tgtId: formData.targetConnection,
        schema: this.migtypeid == "41" ? formData.tgtschemas : formData.schemas,
        tgtschema: this.migtypeid == "40" ? formData.schemas : formData.tgtschemas,
        tableList: this.selectedTable == undefined ? "" : this.selectedTable.toString(),
        batchsize: formData.batchsize,
        cdcType: formData.cdcLoadType,
        fileName: formData.confiName
      }
      if (this.selectedTask == "Reverse_CDC") {
        obj['replicationSlotName'] = formData.replicate
        obj['schema'] = formData.tgtschemas
      }
    }
    else if (value == 2) {

      if (this.migtypeid == "41") {
        obj = {
          fileName: formData.confiName,
          projectId: this.projectId.toString(),
          task: "Load_CDC_Files",
          tgtId: formData.targetConnection,
          schema: formData.schemas,
          tablename: this.selectedTable.toString()
        }
      }
      else {
        obj = {
          fileName: formData.confiName,
          task: formData.operation,
          reffilename: formData.refFile,
        }
      }
    } else {
      var filename = this.cdcConfigFiles.filter((item: any) => {
        return item.config_id == formData.configFile
      })
      obj = {
        task: "CDC_Report",
        projectId: this.projectId.toString(),
        starttime: "'" + formData.fromDate.replace("T", " ") + ".000'",
        endtime: "'" + formData.toDate.replace("T", " ") + ".000'",
        fileName: filename[0].file_name
      }
    }

    this.datamigration.DataMigrationCommand(obj).subscribe((data: any) => {
      this.getForm.reset();
      this.toast.success(data.message);
    })
  }
  selectedTable: any = []
  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }
  tablesSelect(value: any) {
    if (value == "ALL") {
      this.tablesList.forEach((item: any) => {
        this.selectedTable.push(item.table_name)
      })
    }
  }
  selectedTask: any
  getOP(value: any) {
    this.selectedTask = value
    if (value == "Reverse_CDC") {
      if (this.migtypeid == "41") {
        this.clearValidators(['replicate'])
      } else {
        this.setRequiredValidators(['replicate']);
      }
    }
    else if (value == "CDC") {
      this.clearValidators(['replicate'])
    }
    else if (value == "Load_Files") {
      this.setRequiredValidators(['sourceConnection', 'targetConnection', 'confiName']);
      this.clearValidators(['replicate', 'schemas', 'tables', 'tgtschemas', 'cdcLoadType'])
      this.updateAllControls()
    }
  }
  setRequiredValidators(controls: string[]) {
    controls.forEach(control => {
      (this.getForm.controls[control as keyof typeof this.getForm.controls] as FormControl).setValidators([Validators.required]);
    });
  }
  clearValidators(controls: string[]) {
    controls.forEach(control => {
      (this.getForm.controls[control as keyof typeof this.getForm.controls] as FormControl).clearValidators();
    });
  }
  updateAllControls() {
    Object.keys(this.getForm.controls).forEach(control => {
      (this.getForm.controls[control as keyof typeof this.getForm.controls] as FormControl).updateValueAndValidity();
    });
  }
  cdcvalue: any
  selectCDCLevel(value: any) {
    this.cdcvalue = value;
    if (value == 'Table') {
      if (this.migtypeid == "40") {
        this.setRequiredValidators(['schemas', 'tables']);
      }
      else {
        this.setRequiredValidators(['schemas', 'tables', 'tgtschemas']);
      }
      // this.updateAllControls()
    } else if (value == 'Database') {
      this.clearValidators(['schemas', 'tables', 'tgtschemas'])
      // this.updateAllControls()
    }
  }

  DataMigrationNewCommand(fd: any) {

    let obj = {
      projectId: this.projectId.toString(),
      sourceConnectionId: fd.sourceConnection,
      targetConnectionId: fd.targetConnection,
      schema: this.selectedSchemas.toString(),
      targetSchema: fd.tgtschemas,
      task: fd.operation,
      tablename: this.selectedTable.toString(),
      fileName: fd.confiName,
      dataLoadType: fd.data_LoadType,
      cdcType: fd.cdcLoadType,
      jobName: "qmig-migrt",
      codeoption:true
    }
    this.datamigration.DataMigrationNewCommand(obj).subscribe((data: any) => {
      this.toast.success(data.message);
    })
  }
  getOracleTables(value: any) {
    let obj = {
      conid: this.srcId,
      schemaname: value
    }
    this.datamigration.getOracleTables(obj).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.table_name = item.tablename
      })

      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  getRunSpin11: boolean = false;
  fetchNewValidationFiles1() {
    this.getRunSpin11 = false;
    this.fetchSourceConfigFiles(this.fileSrc)
    this.fetchTargetConfigFiles(this.filetgt)
  }
  uploadSpin: boolean = false;
  readDataString: any = []
  selectedSourceConnection: string = '';
  selectedTargetConnection: string = '';
  onEditConfigClick() {
    this.openModal();
  }
  openModal() {
  $('#demo').offcanvas('show');
  }
  onSourceChange(value: any) {
    this.selectedSourceConnection = value;
  }
  onTargetChange(value: string) {
    this.selectedTargetConnection = value;
  }
  readData: any
  ReadLocalJson() {
    const path = "/mnt/extra/" + this.srcId + "/Config_Files/chunk_configuration.json"
    this.updatePath = path;
    this.datamigration.ReadLocalJson(path).subscribe((data: any) => {
      this.readData = data;
      this.readDataString = JSON.stringify(data, null, 0);
    })
  }
  ReadLocalJson1() {
    const path = "/mnt/extra/" + this.srcId + "/" + this.tgtId + "/Config_Files/chunk_configuration.json"
    this.updatePath = path;
    this.datamigration.ReadLocalJson(path).subscribe((data: any) => {
      this.readData = data;
      this.readDataString = JSON.stringify(data, null, 0);
    })
  }
  //EditLocalJson
  editData: any
  updatePath: string = '';
  Update() {
    const parsedData = this.readDataString.replace(/[\r\n\/]/g, '')// JSON.stringify(this.readDataString);
    this.uploadSpin = true;
    let obj = {
      path: this.updatePath,
      jsonData: parsedData//.replace(/[\r\n\/]/g, '')
    }
    this.datamigration.EditLocalJson(obj).subscribe({
      next: () => {
        this.uploadSpin = false;
        this.toast.success("Updated Successfully");
        $('#demo').offcanvas('hide');
      },
      error: (err) => {
        console.error('API Error:', err);
        this.uploadSpin = false;
        this.toast.error('Update failed. Please try again.');
      }
    });
  }
}


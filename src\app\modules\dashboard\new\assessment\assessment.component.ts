import { Component } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartOptions, ChartType } from 'chart.js';

@Component({
  selector: 'app-assessment',
  standalone: true,
  imports: [NgChartsModule,TabsComponent],
  templateUrl: './assessment.component.html',
  styles: ``
})


export class NewAssessmentComponent {

  /*--- Code Object Chart Data ---*/

  public cObjectChartLabels = ['Procedure', 'Function'];
  public cObjectChartType = 'bar';
  public cObjectChartLegend = true;
  public cObjectChartData = [
    {
      data: [160,4],
      label: ' Manual',
      borderColor:'#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [171,1],
      label: ' QMigrator',
      borderColor:'#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public cObjectChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    indexAxis: 'y',
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          usePointStyle: true,
          boxWidth: 6
        }
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

  /*--- Storage Object Chart Data ---*/

  public sObjectChartLabels = ['Table', 'Primary_key', 'Sequence', 'Function', 'Index', 'View', 'Package'];
  public sObjectChartType = 'bar';
  public sObjectChartLegend = true;
  public sObjectChartData = [
    {
      data: [91, 87, 80, 78, 73,65, 59, 50],
      label: ' Manual',
      borderColor:'#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [82,79,72,68, 64, 60, 55, 47],
      label: ' QMigrator',
      borderColor:'#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public sObjectChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    indexAxis: 'y',
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          usePointStyle: true,
          boxWidth: 6
        }
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };


  /*--- Storage Object Chart Data ---*/

  public MigrationChartLabels = ['Procedure', 'Table', 'Primary_key', 'Sequence', 'Function', 'Index', 'View', 'Package'];
  public MigrationChartType = 'bar';
  public MigrationChartLegend = true;
  public MigrationChartData = [
    {
      data: [91, 87, 80, 78, 73,65, 59, 50],
      label: ' Manual',
      borderColor:'#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [82,79,72,68, 64, 60, 55, 47],
      label: ' QMigrator',
      borderColor:'#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public MigrationChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    indexAxis: 'y',
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          usePointStyle: true,
          boxWidth: 6
        }
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

  /*---- totalMax Charts ----*/
  // Custom plugin for displaying percentage in the center
  centerTextPlugin = {
    id: 'centerTextPlugin',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'totalMaxCPUChart') {
      const { ctx, chartArea: { width, height } } = chart;

      ctx.save();

      // Assuming you are showing the percentage of the first dataset
      const total = chart.config.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
      const value = chart.config.data.datasets[0].data[0];
      const percentage = ((value / total) * 100).toFixed(1) + '%';

      // Define style for the text
      ctx.font = 'bold 25px Geist';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Set position to center of the chart
      const centerX = width / 2;
      const centerY = height / 2 + 20;

      // Draw the text in the center
      ctx.fillText(percentage, centerX, centerY);
      ctx.restore();
    }
  }
  };

  public totalMaxChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'January',
      'February',
    ],
    datasets: [
      {
        data: [95, 5],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
            '#8b36ff',
            '#f0f0f0'
        ],        
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,
        
      }
    ]
  };
  public totalMaxChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This totalMax disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }

  };
  public totalMaxChartLegend = false;
  public totalMaxChartPlugins = [this.centerTextPlugin];

  /*---- totalMax Charts ----*/
  // Custom plugin for displaying percentage in the center
  centerTextPlugin1 = {
    id: 'centerTextPlugin1',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'totalAvgCPUChart') {
      const { ctx, chartArea: { width, height } } = chart;

      ctx.save();

      // Assuming you are showing the percentage of the first dataset
      const total = chart.config.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
      const value = chart.config.data.datasets[0].data[0];
      const percentage = ((value / total) * 100).toFixed(1) + '%';

      // Define style for the text
      ctx.font = 'bold 25px Geist';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Set position to center of the chart
      const centerX = width / 2;
      const centerY = height / 2 + 20;

      // Draw the text in the center
      ctx.fillText(percentage, centerX, centerY);
      ctx.restore();
    }
  }
  };

  public totalAvgChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'January',
      'February',
    ],
    datasets: [
      {
        data: [95, 5],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
            '#8b36ff',
            '#f0f0f0'
        ],        
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,
        
      }
    ]
  };
  public totalAvgChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This totalMax disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }

  };
  public totalAvgChartLegend = false;
  public totalAvgChartPlugins = [this.centerTextPlugin1];

  /*---- totalMax Charts ----*/
  // Custom plugin for displaying percentage in the center
  centerTextPlugin2 = {
    id: 'centerTextPlugin2',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'totalMemCPUChart') {
      const { ctx, chartArea: { width, height } } = chart;

      ctx.save();

      // Assuming you are showing the percentage of the first dataset
      const total = chart.config.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
      const value = chart.config.data.datasets[0].data[0];
      const percentage = ((value / total) * 100).toFixed(1) + '%';

      // Define style for the text
      ctx.font = 'bold 25px Geist';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Set position to center of the chart
      const centerX = width / 2;
      const centerY = height / 2 + 20;

      // Draw the text in the center
      ctx.fillText(percentage, centerX, centerY);
      ctx.restore();
    }
  }
  };

  public totalMemChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'January',
      'February',
    ],
    datasets: [
      {
        data: [95, 5],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
            '#8b36ff',
            '#f0f0f0'
        ],        
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,
        
      }
    ]
  };
  public totalMemChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This totalMax disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }

  };
  public totalMemChartLegend = false;
  public totalMemChartPlugins = [this.centerTextPlugin2];

  /*---- totalMax Charts ----*/
  // Custom plugin for displaying percentage in the center
  centerTextPlugin3 = {
    id: 'centerTextPlugin3',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'totalMAvgCPUChart') {
      const { ctx, chartArea: { width, height } } = chart;

      ctx.save();

      // Assuming you are showing the percentage of the first dataset
      const total = chart.config.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
      const value = chart.config.data.datasets[0].data[0];
      const percentage = ((value / total) * 100).toFixed(1) + '%';

      // Define style for the text
      ctx.font = 'bold 25px Geist';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Set position to center of the chart
      const centerX = width / 2;
      const centerY = height / 2 + 20;

      // Draw the text in the center
      ctx.fillText(percentage, centerX, centerY);
      ctx.restore();
    }
  }
  };

  public totalMAvgChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'January',
      'February',
    ],
    datasets: [
      {
        data: [95, 5],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
            '#8b36ff',
            '#f0f0f0'
        ],        
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,
        
      }
    ]
  };
  public totalMAvgChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This totalMax disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }

  };
  public totalMAvgChartLegend = false;
  public totalMAvgChartPlugins = [this.centerTextPlugin3];

  
  /*--- source vs extraction % ---*/
  public sourceVSChartLabels = ['Azure Datebase for PostgreSQl', 'Azure Kubernetes Services','IP Addresses', 'Load Balancer', 'Storage Accounts', 'Virtual Machine'];
  public sourceVSChartType = 'bar';
  public sourceVSChartLegend = false;
  public sourceVSChartData = [
    {
      data: [57000, 39034, 613.75, 1954.26, 3624.73, 39034.34],
      label: ' Migration Estimation',
      borderColor:'#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      barThickness: 30, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public sourceVSChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          autoSkip: false,
          precision: 2,
          source: 'auto',
        }
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
         display: false
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

    /*--- Long Running Queries Chart Data ---*/

    public longRunningChartLabels = ['Production', 'Dev', 'UAT'];
    public longRunningChartType = 'bar';
    public longRunningChartLegend = true;
    public longRunningChartData = [
      {
        data: [86035.63, 43017.81, 43017.81],
        label: ' 1 Year Reserved Instance',
        borderColor:'#a25eff',
        backgroundColor: '#a25eff',
        hoverBackgroundColor: '#8b36ff',
        barThickness: 30, // Set exact bar thickness (smaller number for thinner bars)
        maxBarThickness: 70, // Set max bar thickness if auto-calculating
      },
      {
        data: [72042.03, 36021.09, 36021.90],
        label: ' 3 Years Reserved Instance',
        borderColor:'#4c00b5',
        backgroundColor: '#4c00b5',
        hoverBackgroundColor: '#4c00b5',
        barThickness: 30, // Set exact bar thickness (smaller number for thinner bars)
        maxBarThickness: 70, // Set max bar thickness if auto-calculating
      },
      {
        data: [113777.02, 56888.51,56888.51],
        label: ' Pay As you go',
        borderColor:'#cf30fc',
        backgroundColor: '#f75989',
        hoverBackgroundColor: '#f5346e',
        barThickness: 30, // Set exact bar thickness (smaller number for thinner bars)
        maxBarThickness: 70, // Set max bar thickness if auto-calculating
      }
    ];
    public longRunningChartOptions: ChartOptions<'bar'> = {
      responsive: true,
      scales: {
        x: {
          beginAtZero: true,
        },
        y: {
          beginAtZero: true,
        },
      },
      interaction: {
        mode: 'index'
      },
      plugins: {
        legend: {
          display: true,
          labels: {
            usePointStyle: true,
            boxWidth: 6
          }
        },
        tooltip: {
          enabled: true,
          usePointStyle: true,
          titleSpacing: 5,
          backgroundColor: '#ffffff', // White background
          titleColor: '#333333', // Dark title color
          bodyColor: '#666666', // Lighter body text color
          borderColor: '#cccccc', // Light gray border
          borderWidth: 1, // Thin border
          cornerRadius: 8, // Rounded corners
          padding: 10, // Padding inside the tooltip
        }
      }
    };

  /*--- source vs extraction % ---*/
  public migrationTimelineChartLabels = ['Data Migration', 'Storage Objects', 'Code Object', 'Workload Changes', 'Integration Testing', 'Performance Validation'];
  public migrationTimelineChartType = 'horizontalBar';
  public migrationTimelineChartLegend = true;
  public migrationTimelineChartData = [
    {
      data: [
        [20, 40],
        [20, 40],
        [20, 50],
        undefined,
        [60, 75],
      ],
      label: ' Tasks owned by Kotak',
      borderColor:'#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [
        undefined,
        undefined,
        undefined,
        undefined,
        [45, 65],
        [50, 85],
      ],
      label: ' Tasks owned by Kotak and Quadrant',
      borderColor:'#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [
        undefined,
        undefined,
        undefined,
        [45, 75],
        [70, 95],
      ],
      label: ' Tasks owned by Quadrant',
      borderColor:'#cf30fc',
      backgroundColor: '#f75989',
      hoverBackgroundColor: '#f5346e',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public migrationTimelineChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio:false,
    indexAxis:'y',
    scales: {
      x: {
        beginAtZero: false,
      },
      y: {
        beginAtZero: false,
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
         display: true,
         labels: {
           usePointStyle: true,
           boxWidth: 6
         }
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };


  
}


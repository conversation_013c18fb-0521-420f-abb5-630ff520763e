import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { TabsComponent } from '../tabs/tabs.component';
import { DeploymentService } from '../../../../services/deployment.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-target-compare',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, TabsComponent],
  templateUrl: './target-compare.component.html',
  styles: ``
})
export class TargetCompareComponent {
  pi: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;
  page1: number = 1;
  pages: number = 1;
  fileData: any;
  datachange1: any;
  datachange2: any;
  TgtUpdateTgt: any;
  logfile: any;
  project_name: any
  projectId: any
  getRole: any;
  Sourcefilename: any;
  SourceDeltafilename: any;
  NoDataHide: boolean = false;
  logdata: any = [];
  dropdownList = [];
  selectedItems = [];
  schemaName: any = [];
  targetcompareform: any;
  targetdeltaform: any;
  runNumbers: any;
  schemaNames: any;
  uploadedData: any = [];
  deltaFileData: any;
  prjSrcTgtData: any = [];
  blobfile: any;
  datachanges: any;
  // @ViewChild(PlyrComponent)
  // plyr: PlyrComponent | undefined;

  // // or get it from plyrInit event
  // player: Plyr | undefined;
  poster = 'assets/videos/qmig_ui.jpg';
  iteratSel: boolean = true;
  UserInfo: any;
  pageName: string = '';


  constructor(public fb: FormBuilder,
    public deployment: DeploymentService,
    private toast: HotToastService,
    private route: ActivatedRoute,
  ) {
    this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    this.Sourcefilename = localStorage.getItem('Sourcefilename');
    this.SourceDeltafilename = localStorage.getItem('SourceDeltaFile');
    let userJson = localStorage.getItem('userData') as string;
    this.UserInfo = JSON.parse(userJson);
    this.pageName = this.route.snapshot.data['name'];

  }
  ngOnInit(): void {
    this.targetcompareform = this.fb.group({
      runnumber: ['', [Validators.required]],
      sourceext: ['', [Validators.required]],
      sourcecon: ['', [Validators.required]],
      scheman: ['', [Validators.required]],
      objectt: ['', [Validators.required]],
    });
    this.targetdeltaform = this.fb.group({
      scrun: ['', [Validators.required]],
      scschema: ['', [Validators.required]],
    });
    this.getInfraSelect()
    // this.getuploadedFiles()
    this.getFiles()
    // this.getSchemaNames()

    this.getBlobfileData();
    this.getRunNumber();
    this.getRunNumbers();
    this.getPrjExeLogSelectTask("null");
    this.GetRequestTableData();

  }
  get validate() {
    return this.targetcompareform.controls;
  }

  get validates() {
    return this.targetdeltaform.controls;
  }
 
  deltaschema:any;
  sourceSelected: boolean = false
  sourceSel(value: string) {
    value != '' ? this.sourceSelected = true : this.sourceSelected = false;
    this.deltaschema=value;
    this.GetSourceFilePath();
  }

  getRunNumber() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbers = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }

  runNumbersData: any;
  getRunNumbers() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbersData = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }

  // select schema data
  selectschema(data: any) {
    this.schemaName = data;

    //console.log(data)
  }

  getBlobfileData() {
    // this.project.GetBlobfileByFileName("source_compare.mp4").subscribe((data: any) => {
    //   this.blobfile = data.fileUrl;
    // })
  }
  get getControl() {
    return this.targetcompareform.controls;
  }
  TgtCodeShow() {
    this.TgtUpdateTgt = true;
  }
  TgtCodeShows() {
    this.TgtUpdateTgt = false;
  }

  onKey() {
    this.pageNumber = 1;
    this.page2=1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3=1;
  }
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all') {
      this.pi = this.reportsData.length;
      this.p = 1;

    }
    else if (value == '20') {
      this.p = 1;
    }
    else if (value == '50') {
      this.p = 1;
    }
    else if (value == '100') {
      this.p = 1;
    }
  }
  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }

  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }
  SourceCo:any;
  GetDBConnections() {
    const projectId = this.projectId.toString();
    this.deployment.getConList(projectId).subscribe((data) => {
      this.prjSrcTgtData = data['Table1'];
      this.SourceCo = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "S" && item.migsrctype == "D";
      })
    })
  }

  GetDeltaFiles() {
    // this.project.GetDeltaFiles(this.projectId).subscribe((data) => {
    //   this.deltaFileData = data['Table1'];
    // });
  }
  fileSchemaData: any;
  GetDeltaFileSchemas(fileName: any) {
    let obj = {
      projectId: this.projectId,
      fileName: fileName
    }
    // this.project.GetDeltFileSchemas(obj).subscribe((data) => {
    //   this.fileSchemaData = data['Table1'];
    // });
  }

  getuploadedFiles(schema: any) {
    let requestObj: any = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "Reports",
      subFolderName1: "Oracle",
      subfoldername2: schema
    };
    // this.project.GetFilesBySchema(requestObj).subscribe((data: any) => {
    //   if (data.value != "" && data.value != null) {

    //   }
    //   else {
    //     this.uploadedData = data;
    //     this.NoDataHide = true;
    //   }
    // });
  }
  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }
  reportsData: any
  getFiles() {
    let requestObj: any = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "Reports",
      subFolderName1: "Source_Compare_ExcelReports"
    };
    // this.project.Files(requestObj).subscribe((data: any) => {
    //   if (data.value != "" && data.value != null) {

    //   }
    //   else {
    //     this.reportsData = data;

    //   }
    // });
  }
  spin_process: any
  infraData: any
  filtered: any
  getInfraSelect() {
    let id = this.projectId;
    // this.project.InfraSelect(id).subscribe((data) => {
    //   this.infraData = data['jsonResponseData']['Table1'];
    //   this.filtered = this.infraData.filter((element: any) => {
    //     return element.active == 'True';
    //   });
    // });
    // excelFileName: any = "No Data Found"
    // selectIteration(iter: any) {
    //   this.iteration = iter
    //   let obj = {
    //     projectID: this.projectId.toString(),
    //     containerName: 'qmigratorfiles' + this.projectId,
    //     path: "Code/Delta_process/" + iter + "/Source/EXCELREPORT/"
    //  }
    // this.project.getFileLogs(obj).subscribe((data: any) => {
    //   //console.log(data)
    //   if (data) {
    //     this.iteratSel = false
    //     this.excelFileName = data['0'].name
    //   } else {
    //     this.excelFileName = "No Data Found"
    //   }
    //   //console.log(this.excelFileName)
    // })
  }

  iteration: any
  selectIteration(iter: any) {
    this.iteration = iter
  }
  spin_submit: boolean = false;
  ExecuteCommandOnVM() {
    this.spin_submit = true
    let obj = {
      projectID: this.projectId.toString(),
      command: 'delta_source_compare.sh',
      ObjectType: this.UserInfo.email,
      id: this.projectId.toString(),
      schema: "'" + this.schemaName.toString() + "'",
      ConnectionName: this.iteration,//iteration or run no.
      Connection: this.srcConn,
      isQbook: true
    }

    // this.project.ExecuteCommandOnVM(obj).subscribe(
    //   (data: any) => {
    //     this.spin_submit = false;
    //     if ((data.message == 'Command Executed Successfully')) {
    //       this.alertService.success(data.message);
    //     }
    //   },
    //   (error) => {
    //     this.spin_submit = false;
    //     this.alertService.danger(
    //       'Something Went Wrong please try Again Later!'
    //     );
    //   }
    // );
  }
  schemaData: any
  getFileSchemas(iteration: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: iteration,
      migType: "source"
    }
    // this.project.GetFileSchemas(obj).subscribe((data: any) => {
    //   this.schemaData = data['Table1'];
    //   for (const element of this.schemaData) {
    //     element.schemaname = element.schemaname.toUpperCase();
    //   }
    // })
  }
  //schemaList:any
  GetSchema(runno: any) {
    let obj = {
      projectId: this.projectId,
      runno: runno
    }
    this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
      this.schemaNames = data['Table1'];
    });
  }

  sas: any
  download_path: any
  tempval1: any
  tempval2: any
  tempval3: any


  isLoading1: any = [];
  isLoading2: any = [];
  isLoading3: any = [];

  isload1(value: any) {
    this.tempval1 = value
    this.isLoading1[value] = true
    this.isLoading2[this.tempval2] = false
    this.isLoading3[this.tempval3] = false

  }
  isload2(value: any) {
    this.tempval2 = value
    this.isLoading1[this.tempval1] = false
    this.isLoading2[value] = true
    this.isLoading3[this.tempval3] = false

  }
  isload3(value: any) {
    this.tempval3 = value
    this.isLoading1[this.tempval1] = false
    this.isLoading2[this.tempval2] = false
    this.isLoading3[value] = true

  }




  spin_downl: boolean = false
  sass: any
  downloadExcelFile(filename: any) {
    if (this.iteration != "") {
      this.spin_downl = true;
      let obj = {
        projectID: this.projectId.toString(),
        containerName: "qmigratorfiles" + this.projectId,
        filename: filename,
        path: "Code/Delta_process/" + this.iteration + "/Source/EXCELREPORT/Oracle_New_Delta_Objects.xlsx"
      }
      // this.project.GetFileContentfrompath(obj).subscribe((data: any) => {
      //   this.sass = data.message
      //   const downloadLink = document.createElement('a');
      //   const fileName = filename;
      //   downloadLink.href = ' data:application/octet-stream;base64,' + this.sass
      //   downloadLink.download = fileName;
      //   downloadLink.click();
      //   this.spin_downl = false
      // })
    }
    //   else {
    //     this.alertService.danger("File Path Empty");
    //   }
    // }
  }

  // based on connection getting the Reference Connection

  sourceext: any;
  sourcecon: any;
  scheman: any;
  date: any;
  sourcefile: any;
  fileupload: any;
  datachanges1: any;
  datachanges2: any;
  datachanges3: any;
  pageNumber: number = 1;
  p: number = 1;
  page: number = 1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  selectedDropdown(data: any) {
    if (data == "Database") {
      this.GetDBConnections();
    }
    else if (data == "") {

    }
  }
  // schema list

  conid: any;
  SourceConn: any;
  srcConn: any
  schemaList: any = []
  prjSchemaLists(conid: any) {
    this.SourceConn = conid;
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnection: conid.toString()
    }
    this.scheman = '';
    this.deployment.getschemaList(obj).subscribe((data) => {
      this.schemaList = data['Table1'];
    });
  }

  // 
  onItemSelect(item: any) {
    this.schemaName.push(item.schemaname);
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.schemaname);
    this.schemaName.splice(inde, 1);
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.schemaname);
    });
  }
  onDeSelectAll(items: any) {
    this.schemaName = [];
  }

  // sc schemalist
  schemaNamess:any;
  GetSCSchemaList(runno: any) {
    this.schemaNamess = []
    let obj = {
      projectId: this.projectId,
      runno: runno
    }
    this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
      this.schemaNamess = data['Table1'];
    });
  }

  // table print 

  sourcePaths: any
  GetSourcePaths(schema: any) {
    // this.iteratSel = true
    let obj = {
      projectId: this.projectId.toString(),
      schemaName: schema,
      objectName: "null",
      iteration: this.iteration
    }
    this.deployment.GetSourceFilePath(obj).subscribe((data: any) => {
      this.sourcePaths = data['Table1'];
      this.NoDataHide = true;
      for (const element of this.sourcePaths) {
        let schemaobjtype = element.baseline_filepath.split('/')[6] + "/" + element.baseline_filepath.split('/')[7]
        element.baselineFile = element.objectname + "_baseline.sql"
        element.currentFile = element.objectname + "_current.sql"
        element.htmlFile = element.objectname + ".html"
        element.schemaobj = schemaobjtype
        element.lastmodified = ""
        element.baseFile=element.objectname
      }
    })
  }

  Deltabaseline: any;
  DeltaCommand(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.sourceext,
      srcCon: value.sourcecon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      extraction_Category: "TARGET_COMPARISION",
      task: "TARGET_COMPARISION",
      fileName: "",
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      debugger;
      this.Deltabaseline = data;
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data.message);
      }
    })
  }
  getconsoledatadelta(value: any) {
    let obj = {
      "runNumbers": value.scrun,
      "scschema": value.scschema,
    }
  }
  ExeLog: any = {};
  prjLogData: any;
  selectedrun: any;
  getPrjExeLogSelectTask(value: any) {
    this.selectedrun = value;
    if (value == "") {
      value = "null";
    }
    this.prjLogData = [];
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operationType = "Target_Compare";
    this.ExeLog.action = "null";
    this.ExeLog.row_id = "null";
    this.ExeLog.runno = value;
    this.ExeLog.operationName = "null";
    this.deployment.PrjExelogSelectTask(this.ExeLog).subscribe((data: any) => {
      this.prjLogData = data['Table1'];
      this.NoDataHide = true;
    })
  }
  refresh()
  {
    this.GetRequestTableData();
  }
  ref_spin: boolean = false;
  RequestTableData: any;
  GetRequestTableData() {
    let obj = {
      projectId: this.projectId,
      operationType: 'Target_Compare'
    }
    this.ref_spin=true;
    this.deployment.GetRequestTableData(obj).subscribe((data: any) => {
      this.RequestTableData = data['Table1'];
      this.ref_spin=false;
    });
  }
  deleteResponse: any;
  // delete request table data
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.deployment.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      if (data['Table1'][0].v_status) {
        this.toast.success(data['Table1'][0].v_status);
      }
      else {
        this.toast.error(data);
      }
	  this.GetRequestTableData();
    })
  }
  LogsData: any;
  GetFilesExecutionLogsFromDirectory() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iterationForLogs + "/Execution_Logs/Deployment/Target_Compare"
    };
    this.LogsData=[];
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.LogsData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }

  iterationForLogs: any;
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value;
    this.GetFilesExecutionLogsFromDirectory();
  }

  fileResponse: any;
  spin_dwld: any;
  //download files
  downloadFile(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  downloadFiles(path:string,filename:string) {
    var pth=path.split("/mnt/pypod/")[1]
    pth="/mnt/eng/"+pth
    this.deployment.downloadLargeFiles(pth).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = filename;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  run:any;
  selectRunnumber(itera: any) {
    this.run = itera;
    this.GetSCSchemaList(itera);
  }
  GetSourceFilePathData:any;
  GetSourceFilePath()
  {
    const obj = {
      iteration:this.run,
      projectId: this.projectId.toString(),
      objectName: null,
      type:"T",
      schemaName:this.deltaschema.toString(),
    }
    this.deployment.GetSourceFilePath(obj).subscribe((data: any) => {
      this.GetSourceFilePathData = data['Table1'];
      for (const element of this.GetSourceFilePathData) {
        let schemaobjtype = element.baseline_filepath.split('/')[6] + "/" + element.baseline_filepath.split('/')[7]
        element.baselineFile = element.objectname + "_baseline.sql"
        element.currentFile = element.objectname + "_current.sql"
        element.htmlFile = element.objectname + ".html"
        element.schemaobj = schemaobjtype
        element.lastmodified = ""
        element.basefilename = element.objectname
      }
    });
  }

}


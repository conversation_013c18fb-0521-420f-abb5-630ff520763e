import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { Title } from '@angular/platform-browser';
import { GetIndividual, GetIndividualLogs, GetOperations, GetReqData, GetRunno, Operation, PrjSchemasListSelectData, SchemaListSelect, SchemaSelect, assessmentsetRedisCache, conList, deleteTableData, deleteTabledata, fileStatus, projectConRunTblInsert, reqData } from '../../../../models/interfaces/types';

declare let $: any;


@Component({
  selector: 'app-assessment',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, RouterOutlet, NgSelectModule],
  templateUrl: './assessment.component.html',
  styles: ``
})

export class AssessmentComponent {
  // All Variables
  i: string = '';
  z: number = 0;
  projectId: string = '';
  enableFileName: boolean = false;
  //searchFilter
  datachanges: any;
  datachange1: any;
  datachange2: any;
  datachange: any;
  piA: number = 10;
  piB: number = 10;
  //Page Navigation Variables
  pageNumber: number = 1;
  p: number = 1;
  p1: number = 1;
  p2: number = 1;
  page: number = 1;
  page1: number = 1;
  page2: number = 1;
  pi: number = 10;
  //DB Details Variables
  resourceGroup: string = '';
  location: string = '';
  vmName: string = '';
  dbName: string = '';
  AssessmentForm: any;
  //Execute Command Variables
  runinfospin: boolean = false;
  runinfospins: boolean = false;
  getRunSpin: boolean = false;
  ref_spin1: boolean = false;
  //Download File Variables
  dropdownList: string = '';
  selectedItems: string = '';
  schemaName: any = [];
  schemaname: any = [];
  fileResponse: string = '';
  spin_dwld: boolean = false;
  disabledprevious: boolean = true;
  grid_active: boolean = true;
  not_grid: boolean = true;
  //Schema Variables
  selectedschemas: string = '';
  awrSchemaName: string = '';
  showSchema: boolean = false;
  showSchema1: boolean = true;
  value: any;
  spin: boolean = false;
  //Ref-Spin Variables
  ref_spin: boolean = false
  iterationselected: string = '';
  //GetConList variables
  ConsList: any = []
  connectionType: any = [
    { values: 'F', option: 'File' },
    { values: 'D', option: 'Database' },
  ];

  //Get PrjExeLogSelect variables
  userData: any = [];
  reqD: any = [];
  //SchemaList Variables
  schemaList: any = [];
  operation: any = [];
  ExeLog: any = {};
  runNoData: any = [];
  prjdata: any = [];
  //selected filter variables
  selectedConname: string = '';
  conId: string = '';
  conName: string = '';
  tabledata: any;
  //Logs Variable
  r_id: string = '';
  logdata: any;
  //Run Number Variables
  runnoExtraction: any = [];
  runnoForReports: any = [];
  currentRunNumber: string = '';
  //Operation Variable
  opeList: any = [];
  OpName: string = '';

  ExecututionFiles: any
  datachange4: any
  plog: any

  pageName: string = ''
  schemalable: any;
  migtypeid: any
  //Constructor
  constructor(private toast: HotToastService, private assessmentService: AssessmentService, public formBuilder: FormBuilder, private route: ActivatedRoute, private titleService: Title) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }
  }

  // NgOn Init
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.AssessmentForm = this.formBuilder.group({
      runNumber: ['', [Validators.required]],
      connectionType: [''],
      connectionName: ['', [Validators.required]],
      fileName: [''],
      operation: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      refreshFlag: ['']
    });
    this.p1 = 1;
    this.page2 = 1;
    this.p = 1;
    this.p2 = 1;
    this.page = 1;
    this.page1 = 1;
    this.fetchAssessmentFiles()
    //this.GetConsList();
    this.getreqTableData();
    this.ref_spin = false
    this.getOperationList();
    // this.GetIndividualLogs("null");
    this.getPrjRuninfoSelectAll();



  }
  // Get Method
  get f() {
    return this.AssessmentForm.controls;
  }
  openPopup() {
    this.schemaName = []
    this.selectedschemas = ''
    this.AssessmentForm.reset();
  }
  //schema 

  changeFn(value: any) {
    // this.schemaName = value
    if (value == "ALL") {
      this.schemaList.forEach((item: any) => {
        this.schemaName.push(item.schema_name)
      })
    }
    else{
    this.schemaName = value
    }
  }

  //selectedfiletype
  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;
  }
  //Get Schemas List
  getSchemasList(ConnectionId: any) {
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId,
    };
    this.assessmentService.SchemaListSelect(obj).subscribe((data: any) => {
      this.schemaList = data['Table1'];
      this.schemaList = this.schemaList.filter((item: any) => item.schema_name !== 'ALL');
      this.schemaList.forEach((item: any) => {
        item.type = "ALL"
      })
    });
  }

  //Get PrjExeLogSelect
  getPrjExeLogSelect() {
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operation = 'TASK';
    this.ExeLog.pageNo = '1';
    this.assessmentService.PrjExeLogSelect(this.ExeLog).subscribe((data) => {
      this.userData = data['Table1'];
    });
  }
  //Getreq TableData
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: 'Assessment',
    };
    this.ref_spin = true
    this.assessmentService.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        for (let k = 0; k < this.tabledata.length; k++) {
          if (this.tabledata[k].status == "C") {
            this.tabledata[k].statusfull = "Completed"
          }
          else if (this.tabledata[k].status == "I") {
            this.tabledata[k].statusfull = "Initialize"
          }
          else if (this.tabledata[k].status == "P") {
            this.tabledata[k].statusfull = "Pending"
          }
          else {

          }

          if (this.tabledata[k].objecttype == "ALL") {
            this.tabledata[k].objecttype = ""
          }
        }
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList?.length; i++) {
              if (
                this.tabledata[this.z].connection_id ==
                this.ConsList[i].Connection_ID
              ) {
                this.tabledata[this.z].conname = this.ConsList[i].conname;
              }
            }
          }
        }
        else {
          this.tabledata = []
        }
      }
    });
  }

  //Get Individual Logs
  GetIndividualLogs(action: any) {
    this.p2 = 1
    if (action == "null") {
      this.r_id = "null"
    }
    if (action == "previous") {
      this.r_id = this.logdata[0].exelog_id;
    }
    if (action == "next") {
      this.r_id = this.logdata[this.logdata.length - 1].exelog_id;
      this.disabledprevious = false;
    }
    if (action == '') {
      action = 'null'
    }
    const logobj = {
      projectId: this.projectId.toString(),
      operationType: "Assessment",
      operationName: 'null',
      action: 'null',
      row_id: this.r_id,
      runno: action
    }
    this.assessmentService.GetIndividualLogs(logobj).subscribe((data) => {
      this.logdata = data['Table1'];
    });
  }
  totalRunnumbers: any = []
  //Get PrjRuninfo SelectAll
  getPrjRuninfoSelectAll() {
    this.assessmentService.GetRunno(this.projectId).subscribe((data: any) => {
      this.getRunSpin = false
      this.totalRunnumbers = data['Table1']
      this.runNoData = data['Table1'];
      this.runnoForReports = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoForReports = this.runnoForReports.filter((data1: any) => { return (data1.iteration != '') })
      // this.runNoData = this.runNoData.filter((item: any) => {

      //   return (item.operation_type == "Assessment") || item.iteration == ""
      // })
      this.runnoExtraction = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoExtraction = this.runnoExtraction.filter((item1: any) => {
        // if (item1.dbschema == "") {
        //   item1.dbschema = "New",
        //     item1.iteration = "New"
        // }
        return item1.iteration != ""
      })
      this.runnoForReports = this.filterList(this.runnoForReports)
      this.runNoData = this.filterList(this.runNoData);
      this.runnoExtraction = this.filterList(this.runnoExtraction)
    });
  }

  //Filter List
  filterList(listData: any) {
    let uniqueNames: any = []
    for (let k = 0; k < listData.length; k++) {
      if (uniqueNames.length == 0) {
        uniqueNames.push(listData[k])
      }
      else {
        var abc = uniqueNames.filter((item: any) => {
          return item.iteration === listData[k].iteration
        })
        if (abc.length == 0) {
          uniqueNames.push(listData[k])
        }
      }
    }
    return uniqueNames;
  }
  //Get Updated RunNumber

  getUpdatedRunNumber() {
    this.getRunSpin = true
    this.getPrjRuninfoSelectAll()
  }

  //Get PrjSchemasList SelectData

  openModal(value: any) {
    for (let i = 0; i < this.schemaName.length; i++) {
      this.selectedschemas = this.selectedschemas + this.schemaName[i];
      if (i < this.schemaName.length - 1) {
        this.selectedschemas = this.selectedschemas + ',';
      }
    }
    $('#demo').offcanvas('show');
  }
  //Schema List
  prjSchemaList() {
    const obj = {
      projectId: this.projectId.toString(),
      ConId: this.conId,
    };
    this.assessmentService.PrjSchemasListSelectData(obj).subscribe((data) => {
      this.schemaList = data['Table1'];

    });
  }
  //Select Operation

  selectOperation(id: any) {
    // const op = this.operation.filter((item: any) => {
    //   return item.operation_id == id;
    // });
    this.OpName = id;
    this.AssessmentForm.controls.schema.setValidators([Validators.required])
    this.AssessmentForm.controls.schema.updateValueAndValidity()
  }
  //Get ConsList
  GetConsList() {
    this.assessmentService.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
    });
  }
  //Get OperationList

  getOperationList() {
    const obj = {
      projectId: this.projectId,
      OperationType: 'Assessment',
    };
    this.assessmentService.GetOperations(obj).subscribe((data: any) => {
      this, (this.operation = data['Table1']);
    });
  }
  //project ConRunTblInsert

  projectConRunTblInsert(data: any, value: any) {
    if (value == true) {
      data.status = 'P';
    } else {
      data.status = 'I';
    }
    const tableinsesrt = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(data.operation),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: '',
      objectname: '',
      objecttype: '',
    };
    this.runinfospin = true;
    this.assessmentService.projectConRunTblInsert(tableinsesrt).subscribe(
      (data: any) => {
        this.prjdata = data['jsonResponseData']['Table1'];
        if (data.message == 'Success') {
          this.runinfospin = false;
          $('#demo').offcanvas('hide');
          this.toast.success('Successfully Inserted');
          this.getreqTableData();
        }
      },
      () => {
        $('#demo').offcanvas('hide');
        this.spin = false;
        this.toast.error('Something happened');
      }
    );
  }
  //Project ConRunTblInserts
  projectConRunTblInserts(data: any, value: any) {
    this.runinfospins = true;
    if (value) {
      data.status = 'P';
    } else {
      data.status = 'I';
    }
    const tableinsesrt = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(data.operation),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: '',
      objectname: '',
      objecttype: '',
    };
    this.selectedItems = ''
    if (this.tabledata.length != 0) {
      const res = this.tabledata.filter((item: any) => {
        return item.connection_id == this.conId && item.schemaname == this.schemaName.toString() && item.status == "I" && item.operation_name == this.OpName
      })
      if (res.length >= 1) {
        this.runinfospins = false;
        $('#demo').offcanvas('hide');
        this.toast.error("Request already Added")
      }
      if (res.length == 0) {
        this.RedisCommand()
        this.getreqTableData();
        // $('#updateOrgModal').modal('hide');
      }
    }
    if (this.tabledata.length == 0) {
      this.RedisCommand()
      this.getreqTableData();
      //// this.runinfospins = false;
      // $('#updateOrgModal').modal('hide');

    }
  }
  // Get Run Number

  getRunNumber(data: any) {
    this.schemaList = []
    this.ConsList = []
    this.currentRunNumber = data;
    this.totalRunnumbers.filter((item: any) => {
      if (item.iteration == data) {
        this.schemaList.push({ schema_name: item.schemaname })
        this.schemaList = this.schemaList.filter((item: any) => item.schema_name !== 'ALL');
        this.schemaList.forEach((item: any) => {
          item.type = "ALL"
        })
        if (this.ConsList.length == 0) {
          this.ConsList.push({ Connection_ID: item.connection_id, conname: item.conname })
        }
      }
    })
    // this.schemasbyiteration(data)
  }

  schemasbyiteration(value: string) {
    this.assessmentService.GetSchemasByRunId(value).subscribe((data: any) => {
      this.schemaList = data['Table1'];
    })
  }

  filterExecutionReports() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Execution_Logs/Assessment/"
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
    })
  }
  //Redis Command
  redisResp: any
  RedisCommand() {
    this.runinfospins = true;
    const obj: assessmentsetRedisCache = {
      projectId: this.projectId.toString(),
      option: 1,
      schema: this.schemaName.toString(),
      connection: this.conName,
      Object: this.OpName,
      srcConId: this.conId,
      jobName: "qmig-asses",
      iteration: this.currentRunNumber,
    }
    this.assessmentService.assessmentsetRedisCache(obj).subscribe((data: fileStatus) => {
      this.redisResp = data;
      this.runinfospins = false;
      this.schemaName = []
      this.selectedschemas = ''
      this.AssessmentForm.reset();
      $('#demo').offcanvas('hide');
      this.toast.success("Triggered Successfully")
    })
  }
  // Object Dropdown Details
  objCategory: any = [
    { values: 'all', option: 'ALL' },//ALL
    { values: 'storage', option: 'StorageObjects' }, // StorageObjects
    { values: 'Code_Objects', option: 'CodeObjects' } // CodeObjects
  ]
  objectType: any = [];
  obtypevalue: any
  SelectObjectTypes(value: any) {
    this.objectType = value;
  }
  validationreports: any;

  //Fetch ExtractionFiles
  assessmentFiles: any
  fetchAssessmentFiles() {
    this.assessmentFiles = []
    const path = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Assessment/"
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.assessmentFiles = data
    })
  }
  ExecutionLogs: any = []
  page4: number = 1
  fetchExecutionLogs() {
    this.ExecutionLogs = []
    const path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Execution_Logs/Assessment/"
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecutionLogs = data
    })
  }
  //Download File

  downloadFile(fileInfo: any) {
    this.assessmentService.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  //Download File1
  downloadFile1(fileInfo: any) {
    this.assessmentService.getFileFromList(fileInfo.filePath).subscribe((data: any) => {
      const base64content = data.message
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = ' data:application/octet-stream;base64,' + base64content
      downloadLink.download = fileName;
      downloadLink.click();
    })

  }
  //Hours
  hours: any = [{
    option: "ALL", value: "0"
  },
  {
    option: "1 hr", value: "1"
  }, {
    option: "5 hrs", value: "5"
  }, {
    option: "10 hrs", value: "10"
  }, {
    option: "1 Day", value: "24"
  },
  ]
  hrs: any
  gethrs(value: any) {
    this.hrs = value
    this.filterReports()
  }
  //Filter Reports
  filterReports() {
    if (this.hrs == "0") {
      this.assessmentFiles = []
      this.fetchAssessmentFiles()
    }
    else {
      const obj = {
        path: "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/ExecutionLogs/",
        hrs: this.hrs,
        validationFlag: true
      }
      this.assessmentService.ReportsFilterByTime(obj).subscribe((data: any) => {
        this.assessmentFiles = data
      })
    }
  }
  //Ref_spin

  selectIteration(value: any) {
    this.iterationselected = value
    this.fetchAssessmentFiles()
  }
  //Delete Table Data
  deleteResponse: any;
  deleteTableDatas(request_id: any) {
    const obj: deleteTableData = {
      projectId: this.projectId,
      requestId: request_id,
    };
    this.assessmentService.deleteTableData(obj).subscribe((data: deleteTabledata) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData();
    });
  }
  page3: number = 1
  datachangeLogs: any
  onKey() {
    this.p1 = 1;
    this.p = 1;
    this.p2 = 1;
  }
  iterationForLogs: any = []
  selectIterforlogs(value: any) {
    this.iterationForLogs = value;
    this.fetchExecutionLogs()
  }
  AssessmentResponseData: any = []
  AssessmentCommand() {
    this.runinfospins = true;
    let obj: any = {
      sourceConnectionId: this.conId,
      projectId: this.projectId.toString(),
      task: "Assessment",
      iteration: this.currentRunNumber,
      objectCategory: this.OpName,
      schema: this.schemaName.toString(),
      restartFlag: this.refreshChecked == true ? "True" : "False",
      jobName: "qmig-asses",
    }
    console.log(obj)
    this.assessmentService.AssessmentCommad(obj).subscribe((data: any) => {
      this.AssessmentResponseData = data;
      this.runinfospins = false;
      $('#demo').offcanvas('hide');
      this.toast.success("Code Scan Command Executed")
    },
      error => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong')
      })
  }
  refreshChecked: boolean = false
  getCheckValue($event: any): void {
    if ($event.target.checked == true) {
      this.refreshChecked = true
    }
    else {
      this.refreshChecked = false
    }
  }
}
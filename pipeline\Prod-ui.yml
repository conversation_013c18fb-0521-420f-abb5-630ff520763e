# Agent Queue 'Azure Pipelines' was used with unrecognized Agent Specification, vmImage property must be specified to determine image - https://docs.microsoft.com/en-us/azure/devops/pipelines/agents/hosted?view=azure-devops&tabs=yaml#software

trigger: none

name: $(category)-$(Build.BuildId)

resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/main

stages:
- stage: Build
  displayName: Build project
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: ubuntu-22.04
    steps:
    - checkout: self
      fetchDepth: 1
    - task: Docker@2
      displayName: build
      inputs:
        containerRegistry: qmigrator
        repository: qubeappv2
        command: build
        Dockerfile: '**/Dockerfile'
        tags: 'bi-$(category)-$(Build.BuildId)'
        arguments: '--build-arg DEPLOY=production --build-arg category=$(category)'
    - task: AquaSecurityOfficial.trivy-official.custom-build-release-task.trivy@1
      displayName: 'Trivy Scan'
      inputs:
        loginDockerConfig: true
        image: 'qmigrator.azurecr.io/qubeappv2:bi-$(category)-$(Build.BuildId)'
        severities: 'CRITICAL,HIGH,MEDIUM'
        ignoreUnfixed: true
        exitCode: '0'
    - task: Docker@2
      displayName: push
      inputs:
        containerRegistry: qmigrator
        repository: qubeappv2
        command: push
        tags: 'bi-$(category)-$(Build.BuildId)'

import { Component, ViewChild } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { DashboardService } from '../../../../services/dashboard.service';
import { CommonModule, PercentPipe } from '@angular/common';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartOptions, ChartType } from 'chart.js';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { FormsModule } from '@angular/forms';

interface OperationList {
  iteration: string,
  conname: string,
  schemaname: string,
  total_count: string,
  converted: string,
  percentage_passed: number
}

@Component({
  selector: 'app-performance',
  standalone: true,
  imports: [TabsComponent, PercentPipe, NgChartsModule, CommonModule, SearchFilterPipe, FormsModule],
  templateUrl: './performance.component.html',
  styles: ``
})
export class PerformanceComponent {
  @ViewChild('chart1') chart1: BaseChartDirective | undefined;
  @ViewChild('chart2') chart2: BaseChartDirective | undefined;
  @ViewChild('chart3') chart3: BaseChartDirective | undefined;
  @ViewChild('chart4') chart4: BaseChartDirective | undefined;
  @ViewChild('chart5') chart5: BaseChartDirective | undefined;

  connectionsList: Array<string> = [];
  schemaList: Array<string> = [];
  iterationList: Array<string> = [];
  operationsList: OperationList[] = [];
  operationsCopy: OperationList[] = this.operationsList
  operationsTable: OperationList[] = this.operationsList

  connectionID: string = '';
  connectionValue: string = '';
  schemaId: string = '';
  schemaName: string = '';
  iterationId: string = ''


  /*---- Search bar ---*/
  searchText: string = '';


  /*--- Long Running Queries Chart Data ---*/

  public longRunningChartLabels = ['Dev Cycle 2 Testing', 'Dev Cycle 3 Testing', 'SIT Testing'];
  public longRunningChartType = 'bar';
  public longRunningChartLegend = true;
  public longRunningChartData = [
    {
      data: [91, 87, 80],
      label: ' Count of long running queries',
      borderColor:'#8b36ff',
      backgroundColor: '#8b36ff',
      hoverBackgroundColor: '#4c00b5',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
      borderRadius: 4,
    }
  ];
  public longRunningChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

  /*---- Disc Space usage Chart ----*/

  // Custom plugin for displaying percentage in the center
  centerTextPlugin = {
    id: 'centerTextPlugin',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'discUsageChart') {
      const { ctx, chartArea: { width, height } } = chart;

      ctx.save();

      // Assuming you are showing the percentage of the first dataset
      const total = chart.config.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
      const value = chart.config.data.datasets[0].data[0];
      const percentage = ((value / total) * 100).toFixed(1) + '%';

      // Define style for the text
      ctx.font = 'bold 25px Geist';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Set position to center of the chart
      const centerX = width / 2;
      const centerY = height / 2 + 80;

      // Draw the text in the center
      ctx.fillText(percentage, centerX, centerY);
      ctx.restore();
    }
  }
  };

  public discUsageChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'Used',
      'Free',
    ],
    datasets: [
      {
        data: [95, 5],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
            '#9b7cdf',
            '#f0f0f0'
        ],        
        hoverBackgroundColor: ['#835cd7', '#e3daf6'],
        borderWidth: 0,        
      }
    ]
  };
  public discUsageChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 75,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        
      }
    }

  };
  public discUsageChartLegend = true;

  
  public discUsageChartPlugins = [this.centerTextPlugin];

  /*---- Pie Charts ----*/

  public duplicateIndexChartData: ChartConfiguration<'pie'>['data'] = {
    labels: [
      'Actual', 'Duplicate'
    ],
    datasets: [
      {
        data: [95.61, 4.39],
        borderWidth: 0,
        backgroundColor:[ '#a195f9','#f0f0f0'],
        hoverBackgroundColor: ['#a64dff', '#e3daf6'],
      }
    ]
  };
  public duplicateIndexChartOptions: ChartOptions<'pie'> = {
    responsive: true,
    spacing: 2,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 4 // Adjust this value for the desired border radius
      }
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        
      }
    }
  };
  public duplicateIndexChartLegend = true;

   /*---- Index Usage Charts ----*/

  public indexChartLabels:string[] = ['Dev Cycle 2 Testing', 'Dev Cycle 3 Testing', 'SIT Testing',];
  public indexChartType:ChartType = 'bar';
  public indexChartLegend:boolean = false;
  public indexChartData:any[] = [
    {
        "data": [
            37.08,
            100,
            100,
            100,
            100
        ],
        "label": "Percentage",
        "borderColor": "#F86624",
        "backgroundColor": "#F86624",
        "type": "line",
        "tension": 0.4
    },
    {
        "label": "Unused",
        "backgroundColor": "#707ff5",
        "hoverBackgroundColor": "#4601a6",
        "xAxisID": "x1",
        "data": [
            26,
            140,
            140,
            140,
            143
        ],
        "maxBarThickness": 30,
        "barPercentage": 0.6,
        "minBarLength": 2
    },
    {
        "label": "Used Index",
        "backgroundColor": "#a195f9",
        "hoverBackgroundColor": "#dab1fd",
        "data": [
            92,
            140,
            140,
            140,
            143
        ],
        "xAxisID": "x",
        "maxBarThickness": 50,
        "minBarLength": 2
    }
];
  public indexChartOptions:ChartOptions<'bar'> = {
    scales: {
      x: {
        display: true,
      },
      x1: {
        display: false,
      },
    },
    interaction:{
      mode:'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 3,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
           labelPointStyle: function(context) {
              return {
                 pointStyle: 'circle',
                 rotation: 0
              };
           }
        }
      }
    }
  };

  /*---- Index Usage Charts ----*/

  public monitorChartLabels:string[] = ['Dev Cycle 2 Testing', 'Dev Cycle 3 Testing', 'SIT Testing',];
  public monitorChartType:ChartType = 'bar';
  public monitorChartLegend:boolean = false;
  public monitorChartData:any[] = [
    {
        "label": "Total Indexes",
        "backgroundColor": "#4b4bc3",
        "hoverBackgroundColor": "#4601a6",
        "data": [
            26,
            140,
            140,
            140,
            143
        ],
        "maxBarThickness": 30,
        "minBarLength": 2
    },
    {
        "label": "Created Indexes",
        "backgroundColor": "#707ff5",
        "hoverBackgroundColor": "#dab1fd",
        "data": [
            92,
            140,
            140,
            140,
            143
        ],
        "maxBarThickness": 30,
        "minBarLength": 2
    },
    {
        "label": "Dropped Indexes",
        "backgroundColor": "#a195f9",
        "hoverBackgroundColor": "#dab1fd",
        "data": [
            10,
            15,
            18,
            21,
            23
        ],
        "maxBarThickness": 30,
        "minBarLength": 2
    }
];
  public monitorChartOptions:ChartOptions<'bar'> = {
    interaction:{
      mode:'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 3,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
          labelPointStyle: function(context) {
              return {
                pointStyle: 'circle',
                rotation: 0
              };
          }
        }
      }
    }
  };

  

  constructor(private readonly dbService: DashboardService) { }

  ngOnInit(): void {
    //this.getPerfDetails()
    //this.getOperationsList()
    this.getLongQuery()
    this.getDailyIndex()
    this.getDuplicateIndex()
    this.getIndexUsage()
    this.getDiscSpace()
  }

  /*--- Fetch Iteration ---*/
  getOperationsList() {
    // const obj = {
    //   dbname: 'null',
    //   Conid: 'null',
    //   schemaid: 'null',
    //   iteration: 'null',
    //   option: 0
    // }
    this.dbService.getPerfList('76').subscribe((data: any) => {
      this.operationsList = JSON.parse(JSON.stringify(data.data));
      // this.operationsList.forEach((item: any) => {
      //   this.connectionsList.push(item.conname)
      // })
      this.operationsTable = data.data
      this.operationsCopy = this.operationsList
      this.connectionsList = [...new Set(this.connectionsList)]
    })
  }

  /*--- Fetch Schemas ---*/
  getSchema(value: string) {
    this.schemaList = []
    this.schemaId = ''
    this.connectionID = value
    this.operationsCopy.filter((el: any) => {
      if (value == el.conname) {
        this.schemaList.push(el.schemaname)
      }
      this.schemaList = [...new Set(this.schemaList)]
    })
    this.operationsTable = this.operationsList.filter((fl: OperationList) => { return fl.conname == value })
  }


  /*--- Fetch Iteration ---*/
  getIteration(value: string) {
    this.iterationList = []
    this.schemaId = value
    this.iterationId = ''
    this.operationsCopy.filter((el: any) => {
      if (value == el.schemaname && this.connectionID == el.conname) {
        this.iterationList.push(el.iteration)
      }
      this.iterationList = [...new Set(this.iterationList)]
    })
    this.operationsTable = this.operationsList.filter((fl: OperationList) => { return fl.schemaname == value && fl.conname == this.connectionID })
  }


  /*--- Fetch Iteration ---*/
  getOperations(op: any) {
    this.iterationId = op
  }

  getLongQuery(){
    this.longRunningChartLabels = []
    this.longRunningChartData[0].data = []
    this.dbService.getLongQuery('76').subscribe((data: any) => { 
      data.forEach( (lq:any) => { this.longRunningChartLabels.push(lq.logDate.slice(0, 10)); this.longRunningChartData[0].data.push(lq.queryCount) }); 
      this.chart1?.update();  
    })
  }
  
  getDuplicateIndex(){
    this.duplicateIndexChartData.labels = []
    this.duplicateIndexChartData.datasets[0].data = []
    this.dbService.getDuplicateIndex('76').subscribe((data: any) => { 
      let labels = []
      data.forEach( (di:any) => {
        labels.push(di.indexType)
        this.duplicateIndexChartData.labels = labels
        this.duplicateIndexChartData.datasets[0].data.push(Number(di.percentage))    
        this.chart2?.update();    
      });
    })
  }

  getDailyIndex(){
    this.monitorChartLabels = []
    this.monitorChartData[0].data = []
    this.monitorChartData[1].data = []
    this.monitorChartData[2].data = []
    this.dbService.getDailyIndex('76').subscribe((data: any) => {
       let labels = []
       data.forEach( (ind:any) => {
         labels.push(ind.date.slice(0, 10))
         this.monitorChartLabels = labels
         this.monitorChartData[0].data.push(Number(ind.totalIndexes))    
         this.monitorChartData[1].data.push(Number(ind.createdIndexes))    
         this.monitorChartData[2].data.push(Number(ind.droppedIndexes))    
         this.chart5?.update();    
       });
    })
  }

  getIndexUsage(){
    this.indexChartLabels = []
    this.indexChartData[0].data = []
    this.indexChartData[1].data = []
    this.indexChartData[2].data = []
    this.dbService.getIndexUsage('76').subscribe((data: any) => {
       let labels = []
       data.forEach( (ind:any) => {
         labels.push(ind.date.slice(0, 10))
         this.indexChartLabels = labels
         this.indexChartData[0].data.push(Number(ind.unusedPercentage))    
         this.indexChartData[1].data.push(Number(ind.unusedIndexes))    
         this.indexChartData[2].data.push(Number(ind.usedIndexes))    
         this.chart4?.update();    
       });
    
    })
  }

  getDiscSpace(){
    this.discUsageChartData.datasets[0].data = []
    this.dbService.getDiscSpace('76').subscribe((data: any) => { 
      this.discUsageChartData.datasets[0].data.push(data[0].percentageUsedbyCurrentDB)
      this.discUsageChartData.datasets[0].data.push(Math.abs(data[0].percentageUsedbyCurrentDB-100))
      this.chart3?.update();    
    })
  }

  // getPerfDetails(){
  //   this.dbService.getPerfList('76').subscribe((data: any) => { 
  //     console.log(data) 
  //   })
  // }

  downloadCanvas(event:any) {
    // get the `<a>` element from click event
    var anchor = event.target;
    // get the canvas, I'm getting it by tag name, you can do by id
    // and set the href of the anchor to the canvas dataUrl
    anchor.href = document.getElementsByTagName('canvas')[4].toDataURL();
    // set the anchors 'download' attibute (name of the file to be downloaded)
    anchor.download = "test.png";
}

}

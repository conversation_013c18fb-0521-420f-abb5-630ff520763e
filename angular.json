{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"QMigratorQubeA17": {"projectType": "application", "schematics": {"@schematics/angular:component": {"skipTests": true, "inlineStyle": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/qmigrator-qube-a17", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@ng-select/ng-select/themes/default.theme.css", "node_modules/@ngxpert/hot-toast/src/styles/styles.css", "./node_modules/bootstrap/dist/css/bootstrap.min.css", "src/assets/css/main.css", "src/assets/css/responsive.css", "src/styles.css"], "scripts": ["./node_modules/bootstrap/dist/js/bootstrap.min.js", "src/assets/js/main.js"], "server": "src/main.server.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "src/app/environments/environment.ts", "with": "src/app/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "QMigratorQubeA17:build:production"}, "development": {"buildTarget": "QMigratorQubeA17:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "QMigratorQubeA17:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": "ff6e7705-e173-466e-a2d9-0572015a3219"}}
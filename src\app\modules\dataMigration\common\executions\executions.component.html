<div class="v-pageName">{{pageName}}</div>

<!-- operation details -->
<div class="body-main">
    <div class="qmig-card">
        <h3 class="main_h px-3 pt-3">Dag Executions</h3>
        <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="ExecutionForm" (ngSubmit)="getDagFormValue(ExecutionForm.value)">
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 ">
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Source
                                Connection Name</label>
                            <select class="form-select" #srcFile (change)="fetchSourceConfigFiles(srcFile.value)">
                                <option>Select a Source Connection</option>
                                @for ( src of ConsList; track src) {
                                <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 ">
                        <div class="form-group">
                            <label class="form-label" for="targtetConnection">Target Connection Name</label>
                            <select class="form-select" #tgtFile (change)="fetchTargetConfigFiles(tgtFile.value)">
                                <option>Select Target Connection</option>
                                @for(tgt of tgtList;track tgt; ){
                                <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                }
                            </select>
                        </div>
                    </div>
                    <!-- <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Operation</label>
                            <select class="form-select" #oper (change)="selectOperation(oper.value);filterConfigFiles()"
                                formControlName="operation">
                                <option>Select a Operations</option>
                                @for(dag of configData ;track dag; ){
                                <option value="{{ dag.configvalue }}"> {{ dag.configtype }} </option>
                                }
                            </select>

                            <!-- Validators
                            @if ( forms.operation.touched && forms.operation.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (forms.operation.errors.required) {operation is Required }
                            </p>
                            }
                        </div>
                    </div> -->
                    <!-- config details -->
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="Schema">Select Configuration File</label>
                            <select class="form-select" #config
                                (change)="selectDag(config.value);getConfigDags(config.value)" formControlName="config">
                                <option>Select a Config</option>
                                @for(Config of filteredConfigfiles ;track Config; ){
                                <option value="{{ Config.filePath }}"> {{ Config.fileName }} </option>
                                }
                            </select>

                            <!-- Validators -->
                            @if ( forms.config.touched && forms.config.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (forms.config.errors.required) {config is Required }
                            </p>
                            }
                        </div>
                    </div>
                    <!-- <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 pt-2">                        
                        <button class="btn btn-sync" (click)="refreshdags()">
                            Refresh
                            @if(reff_spin){
                            <app-spinner />
                            }
                        </button>
                    </div> -->
                    @if(dagSelected)
                    {

                    <div class="col-6 col-md-3 col-xl-3 mt-4">
                        <div class="body-header-button">
                            <button class="btn btn-upload w-100" data-bs-toggle="offcanvas"
                                data-bs-target="#updateOrgModal" (click)="openPopup();popup(1)"
                                [disabled]="!isCheckBoxSel">
                                <span class="mdi mdi-timer-play"></span>Schedule Dag
                            </button>
                        </div>
                    </div>
                    <!-- Trigger details -->
                    <div class="col-6 col-md-9 col-xl-9 mt-4"></div>

                    <div class="col-6 col-md-3 col-xl-3 mt-4">
                        <div class="body-header-button">
                            <button class="btn btn-sign w-100" (click)="getSCN();popup(0)" [disabled]="!isCheckBoxSel">
                                <span class="mdi mdi-cog-play"></span>
                                Trigger Dag @if(trigger_spin){<app-spinner />}</button>
                        </div>
                    </div>
                    }
                </div>
            </form>
        </div>
    </div>
</div>
@if(dagSelected)
{
<div class="body-main mt-4">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <div class="row">
                <div class="col-12 col-md-7 offset-5 d-flex">
                    <div class="custom_search cs-r me-3 my-3">
                        <span class="mdi mdi-magnify"></span>
                        <input type="text" placeholder="Search Dags" class="form-control" [(ngModel)]="searchText1"
                            (keyup)="onKey()">
                    </div>
                    <button class="btn btn-sync" (click)="refreshdags()">
                        @if(reff_spin){
                        <app-spinner />
                        }@else{
                        <span class="mdi mdi-refresh"></span>
                        }
                    </button>
                </div>
            </div>
        </div>
        <!-- Table details -->
        <div class="table-responsive">
            <table class="table table-hover qmig-table" id="example" style="width:100%">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <div class="form-check m-0">
                                <label class="form-check-label">
                                    <input type="checkbox" (click)="selectAll($event)" class="form-check-input"
                                        [checked]="showCheckStatus" [disabled]="isDisableAllCheck"> <i
                                        class="input-helper"></i>
                                </label>
                            </div>
                        </th>

                        <th>Dag</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- For pagination -->
                    @for(dag of dagsInfo | searchFilter: searchText1 |paginate: {itemsPerPage:10,currentPage:
                    p,id:'First'};track dag){
                    <tr>
                        <td>
                            <div class="form-check mt-33">
                                <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input" #chck
                                        (click)="checkboxselect($event, chck.value)" value="{{dag.dag_id}}"
                                        [checked]='dag.isSelected' [disabled]="dag.isDagAvailable==false">
                                    <i class="input-helper"></i>
                                </label>
                            </div>
                        </td>
                        <td>{{dag.dag_id}}</td>
                        @if(dag.isDagAvailable==true)
                        {<td><i class="mdi mdi-check green"></i>
                        </td>
                        }
                        @if(dag.isDagAvailable==false)
                        {<td><i class="mdi mdi-close red"></i>
                        </td>
                        }
                    </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="p = $event; updateVisibleDags()" id="First"></pagination-controls>
        </div>
    </div>
</div>
}
<!-- Reports and Logs -->
<div class="body-main mt-4">
    <div class="qmig-card">
        <!-- <h3 class="main_h px-3 pt-3">Reports & Logs</h3> -->
        <div class="accordion accordion-flush qmig-accordion" id="accordionFlushExample">

            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne1" aria-expanded="false" aria-controls="flush-collapseOne">
                        Dag Validation Reports
                    </button>
                </h2>
                <div id="flush-collapseOne1" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <!--- Reports List --->
                    <div class="body-main mt-4">
                        <div class="qmig-card">
                            <!-- <h3 class="main_h px-3 pb-1 pt-3">List of Reports</h3> -->

                            <div class="qmig-card-body">
                                <div class="row">
                                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="targtetConnection">Source
                                                Connection Name</label>
                                            <select class="form-select" #srcFile21
                                                (change)="selectSrc(srcFile21.value)">
                                                <option>Select a Source Connection</option>
                                                @for ( srccf of ConsList; track srccf) {
                                                <option value="{{ srccf.Connection_ID }}">{{ srccf.conname }}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                        <div class="form-group">
                                            <label class="form-label" for="targtetConnection">Target Connection
                                                Name</label>
                                            <select class="form-select" #tgtFile22
                                                (change)="selectTgt(tgtFile22.value)">
                                                <option>Select Target Connection</option>
                                                @for(tgt of tgtList;track tgt; ){
                                                <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                        <div class="form-group">
                                            <label class="form-label" for="targtetConnection">FileName</label>
                                            <select class="form-select" #tgtFile21
                                                (change)="fetchDagFolders(tgtFile21.value)">
                                                <option>Select FileName</option>
                                                @for(tgtfolder of foldersList;track tgtfolder; ){
                                                <option value="{{tgtfolder.folderpath}}">{{tgtfolder.folderName}}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                        <div class="form-group">
                                            <label class="form-label" for="targtetConnection">Dags</label>
                                            <select class="form-select" #dags
                                                (change)="fetchNewValidationFiles(dags.value)">
                                                <option>Select Dag</option>
                                                @for(dagfolder of DagFolders;track dagfolder; ){
                                                <option value="{{dagfolder.folderpath}}">{{dagfolder.folderName}}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12 col-sm-6 col-md-6 col-lg-7 offset-5 d-flex">
                                            <div class="custom_search cs-r me-3 my-3">
                                                <span class="mdi mdi-magnify"></span>
                                                <input type="text" placeholder="Search Reports" class="form-control"
                                                    [(ngModel)]="searchText" (keyup)="onKey()">
                                            </div>
                                            <button class="btn btn-sync" (click)="fetchNewValidationFiles1()">
                                                @if(getRunSpin1){
                                                <app-spinner />
                                                }@else{
                                                <span class="mdi mdi-refresh"></span>
                                                }
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="table-responsive">
                                    <table class="table table-hover qmig-table">
                                        <thead>
                                            <tr>
                                                <th>S.NO</th>
                                                <th>File Name</th>
                                                <th>Created Date</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for (Reports1 of NewValidationFiles | searchFilter: searchText1 | paginate:
                                            {
                                            itemsPerPage: pi3,
                                            currentPage: pageNumber1 , id:'second'} ; track Reports1; let i = $index) {
                                            <tr>
                                                <td>{{pageNumber1*pi3+i+1-pi3}}</td>
                                                <td>{{Reports1.fileName }}</td>
                                                <td>{{Reports1.created_dt}}</td>
                                                <td>
                                                    <button class="btn btn-download" (click)="downloadFile(Reports1)">
                                                        <span
                                                            class="mdi mdi-cloud-download-outline"></span>@if(spinDownload){<app-spinner />}
                                                    </button>
                                                    <button class="btn btn-delete"
                                                        (click)="deleteFiles(Reports1.filePath)">
                                                        <span class="mdi mdi-delete"></span>
                                                    </button>
                                                </td>
                                            </tr>
                                            } @empty {
                                            <tr>
                                                <td colspan="4">
                                                    <p class="text-center m-0 w-100">Empty list of Reports</p>
                                                </td>
                                            </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="custom_pagination">
                                    <pagination-controls (pageChange)="pageNumber1 = $event"
                                        id="second"></pagination-controls>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                        Execution Logs
                    </button>
                </h2>
                <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2 mt-3">
                                <div class="form-group">
                                    <select class="form-select" #sd (change)=" getArflowDagFolders(sd.value)">
                                        <option selected value=""> Select Dag</option>
                                        @for(Dag of dagsInfo ;track Dag; ){
                                        <option value="{{Dag.dag_id  }}"> {{ Dag.dag_id }} </option>
                                        }
                                    </select>
                                </div><!--<span class="ml-3 c-p pt-2"
                                ><i *ngIf="!getRunSpin"
                                    class="fa fa-refresh "></i><i *ngIf="getRunSpin"
                                    class="fa fa-refresh fa-spin "></i></span> -->
                            </div>

                            <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2 mt-3">
                                <div class="form-group">
                                    <select class="form-select" #sr (change)=" getArflowTaskFolders(sr.value)">
                                        <option value="">Select RunId</option>
                                        @for(Run of dagFolders ;track Run; ){
                                        <option value="{{Run.filePath }}"> {{ Run.fileName }} </option>
                                        }
                                    </select>
                                </div>
                            </div>

                            <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2 mt-3">
                                <div class="form-group">
                                    <select class="form-select" #st (change)=" getAirflowfiles(st.value)">
                                        <option value="">Select TaskId</option>
                                        @for(Task of taskFolders ;track Task; ){
                                        <option value="{{Task.filePath}}"> {{ Task.fileName}} </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-7 col-md-6 col-lg-6 col-xl-6 d-flex">
                                <div class="custom_search cs-r me-3 my-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Execution Logs" aria-controls="example"
                                        class="form-control" [(ngModel)]="searchText1" (keyup)="onKey()" />
                                </div>
                                <button class="btn btn-sync" (click)="getAirflowfiles1()">
                                    @if(getRunSpin2){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </div>

                        </div>
                    </div>
                    <!-- downloadFile -->
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(validrepo of logFiles
                                | searchFilter : datachange
                                | paginate
                                : {
                                itemsPerPage: pi,
                                currentPage: p1,
                                id: 'third'
                                };track validrepo
                                )
                                {
                                <tr>
                                    <td>{{p1*pi+$index+1-pi}}</td>
                                    <td>{{validrepo.fileName }}</td>
                                    <td>Project Docs</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p1 = $event" id="third"></pagination-controls>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingTwo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                        Cancel Dags
                    </button>
                </h2>
                <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <select class="form-select" #task1 (change)="getConfigDags1(task1.value)">
                                        <option selected value="">
                                            Select Config
                                        </option>
                                        @for(Config1 of configFiles ;track Config1; ){
                                        <option value="{{ Config1.filePath }}"> {{ Config1.fileName }}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <select class="form-select" #dagvalue1 (change)="selectRunDags(dagvalue1.value)">
                                        <option selected value="">
                                            Select Dag
                                        </option>
                                        @for(Dag1 of dagInfo2 ;track Dag1; ){
                                        <option value="{{Dag1.dag_id  }}"> {{ Dag1.dag_id }} </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <select class="form-select" #runid1 (change)="getStatus(runid1.value)">
                                        <option selected value="">
                                            Select Run Id
                                        </option>
                                        @for(Run1 of dagruns ;track Run1; ){
                                        <option value="{{Run1.dag_id }}"> {{ Run1.dag_id }} </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <button type="button" class="btn btn-upload w-100" [disabled]="!NoData"
                                    (click)="modifyDag();">
                                    <span class="mdi mdi-close"></span>Cancel Dag
                                </button>
                            </div>
                            <!-- downloadFile -->
                            <!-- <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(validrepo of logFiles
                                | searchFilter : datachange
                                | paginate
                                : {
                                itemsPerPage: pi,
                                currentPage: p1,
                                id: 'third'
                                };track validrepo
                                )
                                {
                                <tr>
                                    <td>{{p1*pi+$index+1-pi}}</td>
                                    <td>{{validrepo.fileName }}</td>
                                    <td>Project Docs</td>
                                    <td>
                                        <button class="btn btn-download"
                                            (click)="downloadFile(validrepo)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>

                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p1 = $event" id="third"></pagination-controls>
                        </div> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="offcanvas offcanvas-end" tabindex="-1" id="updateOrgModal">
            <div class="offcanvas-header">
                <h4 class="card-title">
                    <i class="fas fa-chalkboard-teacher"></i> Dag Execution
                </h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
            </div>
            <div class="offcanvas-body">
                <form class="form qmig-Form">
                    <div class="form-group">
                        @if(scheduleSelect){
                        <div>
                            <span>Selected Dags:{{count.length}}</span>
                            <div>

                                @if(initialselected){
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Type</label>
                                    <select class="form-select" #init (change)="selecttype(init.value)">>
                                        <option>Select a Types</option>
                                        @for(Types of initTypes ;track Types; ){
                                        <option value="{{ Types.value }}"> {{ Types.option }} </option>
                                        }
                                    </select>
                                </div>
                                }
                            </div>
                        </div>
                        <button class="btn btn-upload w-100" (click)="getSCN()">
                            Execute @if(executeSpin){<app-spinner />}</button>
                        }
                        @if(!scheduleSelect){
                        <div>
                            <span>Selected Dags:{{count.length}}</span>
                            <div>
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Schedule Date</label>
                                    <input type="datetime-local" class="form-control" id="schDate">
                                </div>
                                <button class="btn btn-upload w-100" (click)="scheduleDags()"> <span
                                        class="mdi btn-icon-prepend"></span>
                                    Schedule</button>
                            </div>
                        </div>
                        }

                        <!-- Execute button -->

                    </div>
                </form>
            </div>
        </div>
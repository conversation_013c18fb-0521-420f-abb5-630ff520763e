<div [hidden]="!Monitdate">
    <div class="row">

        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <button class="btn btn-sign w-100" (click)="exportexcelMonitor()">
                <i class="mdi mdi-download "></i> Download
                @if(<PERSON><PERSON><PERSON><PERSON>){<app-spinner />}</button>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-hover qmig-table">
            <thead>
                <tr>
                    <th>S.No</th>
                    <th>Type</th>
                    <th>SchemaName</th>
                    <th>TableName</th>
                    <th>IndexName</th>
                    <th>LogDate</th>
                    
                </tr>
            </thead>
            <tbody>
                @for (documents of indexMonitor | paginate: { itemsPerPage: 10, currentPage:
                pageNumber } ; track documents;) {
                <tr>
                    <td>{{pageNumber*10+$index+1-10}}</td>
                    <td>{{documents.type}}</td>
                    <td>{{documents.schemaname}}</td>
                    <td>{{documents.tablename}}</td>
                    <td>{{documents.indexname}}</td>
                    <td>{{documents.logdate}}</td>
                </tr>
                } @empty {
                <tr>
                    <td colspan="4">
                        <p class="text-center m-0 w-100">No Results</p>
                    </td>
                </tr>
                }
            </tbody>
        </table>
    </div>
    <div class="custom_pagination">
        <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
    </div>
</div>
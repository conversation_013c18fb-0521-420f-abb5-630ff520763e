export const dashboardAPIConstant = {
    getConnections: 'Assess/GetConnectionList?projectId=',
    postGetData: 'Assess/GetData',
    getConnectionsBasedOption:'Assess/GetConnections?option=',
    getSchema:'Assess/GetSchemasFroDashboard?Conid=',
    getIteration:'Assess/GetIterationsForDashboard',
    getOperation:'Assess/GetDashboardOperationsData',
    getDuplicateIndex:'Performance/DuplicatepiechartIndex?Conid=',
    getDailyIndex:'Performance/DailyIndexMonitoringChartQuery?Conid=',
    getLongQuery:'Performance/LongrunningQuerycountForDashBoard?Conid=',
    getDiscSpace:'Performance/GetDiskspaceUtilization?Conid=',
    getIndexUsage:'Performance/GetIndexusage?Conid=',
    getPerfDetails:'Performance/PerfDetailReport?Conid=',
    getPerDashboard:'Dashboard/DashBoardReport?Conid=',
    getDataMigrtion:'Dashboard/GetDataMigrationDashboardCounts?dbname=',
    getOperationNew:'Dashboard/GetDashBoardNewCounts',
    DashBoardAssessmentsCounts:'Dashboard/GetDashBoardAssessmentsCounts',
    dropdownDetails:'Dashboard/GetdashboardDropdownsData',
    objdropdownDetails:'Dashboard/GetObjectTypedashboardDropdownsData',
    getschemaa:'Ora2PgAssessment/ora2pg/GetSchema?conid=',
    GetSourceCounts:'Ora2PgAssessment/ora2pg/GetSourceCounts?conid=',
    GetThroughPut:'Dashboard/GetThroughPut',
    getTableCountBySchema:'Dashboard/GetTableCountBySchema?Conid=',
    getValidationDashboardData:'Dashboard/GetValidationDashboardData',
    getValidationCounts:'Dashboard/GetValidationCounts',
    getTgtDatat:'Dashboard/GetTgtData?Conid=',
    getCPUData:'Dashboard/GetCPUData',
    datamigrationNewCommand:'Migration/DataMigrationNewCommand',
    getTargetRowCount:'Dashboard/GetTargetRowCount?conid=',
    getSourceRowCounts:'Ora2PgAssessment/ora2pg/GetSourceRowCounts?conid=',
    GetValidationAuditData:'Dashboard/GetValidationAuditData',
    GetCDCAuditData:'Dashboard/GetCDCAuditData',
    GetFabricsData:'Ora2FabDashboard/ora2fab/GetFabricsData?conid=',
    GetFabricSchemas:'Ora2FabDashboard/ora2fab/GetFabricSchemas?conid=',
    GetFabricsRouwCount:'Ora2FabDashboard/ora2fab/GetFabricsRouwCount?conid=',
    GetmariasnapshotCounts:'Maria2MysqlAssessment/maria2mysql/GetMariaData?conid=',
    GetmariaTablesCounts:'Maria2MysqlAssessment/maria2mysql/GetMariaCounts?conid='
}
export const sqlAPIConstant = {
    PrjSqlScriptExecute: 'sqlmi/PrjSqlScriptInsert',
    PrjSqlScriptFunctionSelect: 'sqlmi/PrjSqlScriptFunctionSelect?projectId=',
    PrjSqlQueryExecute: 'sqlmi/PrjSqlQueryExecute',
    PrjSqlScriptcycleSelect: 'sqlmi/PrjSqlScriptcycleSelect?projectId=',
    PrjSqlScriptcycleSelectwithIteration: 'sqlmi/PrjSqlScriptcycleSelectwithIteration?projectId=',
    PrjSQLLogSelect: 'sqlmi/PrjSQLLogSelect?projectId=',
    PrjSqlScriptCategorySelect: 'sqlmi/PrjSqlScriptCategorySelect?projectId=',
    PrjSQLRuninfoIteration: 'sqlmi/PrjSQLRuninfoIteration?projectId=',
    DownloadLargeFile: 'DownloadLargeFile?filePath=',
    PrjIterationByCategorySelect: 'sqlmi/PrjIterationByCategorySelect?projectId=',
    GetProjectSrctgtconfSelect: 'GetProjectSrctgtconfSelect',
    GetProjectMigDetailSelect: 'GetProjectMigDetailSelect',
    InsertSQLMiCommand: 'sqlmi/InsertSQLMiCommand',
    GetConnectionList: 'GetConnectionList?projectId=',
    GetServerlist: 'SqlGetServerlist',
    getsqlServerKey: 'sqlmi/getsqlServerKey',
    GetInventoryFolders: 'SqlGetInventoryFolders',
    downloadScript: 'SqldownloadScript',
    downloadInventoryZip: 'sqlmi/downloadInventoryZip?serverIp=',
    InsertInventorDetalis: 'SqlInsertInventorDetalis',
}
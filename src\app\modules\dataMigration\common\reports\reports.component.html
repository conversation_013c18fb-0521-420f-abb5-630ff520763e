
<div class="v-pageName">{{pageName}}</div>
    <!--- Bread Crumb --->
    <div class="body-header">
        <div class="row">
            <div class="col-sm-6 col-md-6 col-xl-6 mt-r-3 offset-md-6">
                <div class="custom_search cs-r">
                    <span class="mdi mdi-magnify"></span>
                    <input type="text" placeholder="Search Airflow Reports" class="form-control" [(ngModel)]="searchText" (keyup)="onKey()">
                </div>
            </div>
        </div>
    </div>

    <!--- Reports List --->
    <div class="body-main mt-4">
        <div class="qmig-card">
            <h3 class="main_h px-3 pb-1 pt-3">List of Reports</h3>
            <div class="table-responsive">
                <table class="table table-hover qmig-table">
                    <thead>
                        <tr>
                            <th>S.NO</th>
                            <th>File Name</th>
                            <th>Created Date</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (Reports of airflowFiles | searchFilter: searchText | paginate: { itemsPerPage: pi,
                        currentPage: pageNumber } ; track Reports; let i = $index) {
                        <tr>
                            <td>{{pageNumber*pi+i+1-pi}}</td>
                            <td>{{Reports.fileName }}</td>
                            <td>{{Reports.created_dt}}</td>
                            <td>
                                <button class="btn btn-download" (click)="downloadFile(Reports)">
                                    <span class="mdi mdi-cloud-download-outline"></span>@if(spinDownload){<app-spinner />}
                                </button>
                                <button class="btn btn-delete" (click)="deleteFiles(Reports.filePath)">
                                    <span class="mdi mdi-delete"></span>
                                </button>
                            </td>
                        </tr>
                        } @empty {
                        <tr>
                            <td colspan="4">
                                <p class="text-center m-0 w-100">Empty list of Reports</p>
                            </td>
                        </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="custom_pagination">
                <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
            </div>
        </div>
    </div>
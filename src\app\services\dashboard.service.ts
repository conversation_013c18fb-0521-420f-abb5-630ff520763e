import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { dashboardAPIConstant } from '../constant/dashboardAPIConstant';
import { environment } from '../environments/environment';
import { conList, getConnections, getCounts } from '../models/interfaces/dashBoardTypes';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl

  /*--- Get data base connections ---*/
  getConnectionsListURL = this.apiURL + dashboardAPIConstant.getConnections;
  getConList = (body: string): Observable<getConnections> => {
    return this.apiService.get<getConnections>(this.getConnectionsListURL + body)
  }

  /*--- Check login ---*/
  postGetURL = this.apiURL + dashboardAPIConstant.postGetData
  getCounts = (body: getCounts): Observable<any> => {
    return this.apiService.post(this.postGetURL, body);
  };


  /*---- Assessment page ---*/
  getConURL = this.apiURL + dashboardAPIConstant.getConnectionsBasedOption
  getCons = (body: number): Observable<conList> => {
    return this.apiService.get(this.getConURL + body)
  }

  getSchemaURL = this.apiURL + dashboardAPIConstant.getSchema
  getSchemaList = (body: any): Observable<any> => {
    return this.apiService.get(this.getSchemaURL + body.Conid + "&option=" + body.option)
  }

  getIterationURL = this.apiURL + dashboardAPIConstant.getIteration
  getIterationList = (body: any): Observable<any> => {
    return this.apiService.post(this.getIterationURL, body)
  }

  getOperationURL = this.apiURL + dashboardAPIConstant.getOperation
  getOperationList = (body: any): Observable<any> => {
    return this.apiService.post(this.getOperationURL, body)
  }

  /*--- Performance Module ---*/
  getDuplicateIndexURL = this.apiURL + dashboardAPIConstant.getDuplicateIndex
  getDuplicateIndex = (body: string): Observable<any> => {
    return this.apiService.get(this.getDuplicateIndexURL + body)
  }

  getDailyIndexURL = this.apiURL + dashboardAPIConstant.getDailyIndex
  getDailyIndex = (body: string): Observable<any> => {
    return this.apiService.get(this.getDailyIndexURL + body)
  }

  getLongQueryURL = this.apiURL + dashboardAPIConstant.getLongQuery
  getLongQuery = (body: string): Observable<any> => {
    return this.apiService.get(this.getLongQueryURL + body)
  }

  getDiscSpaceURL = this.apiURL + dashboardAPIConstant.getDiscSpace
  getDiscSpace = (body: string): Observable<any> => {
    return this.apiService.get(this.getDiscSpaceURL + body)
  }

  getIndexUsageURL = this.apiURL + dashboardAPIConstant.getIndexUsage
  getIndexUsage = (body: string): Observable<any> => {
    return this.apiService.get(this.getIndexUsageURL + body)
  }

  getPerfDetailsURL = this.apiURL + dashboardAPIConstant.getPerfDetails
  getPerfList = (body: any): Observable<any> => {
    return this.apiService.get(this.getPerfDetailsURL + body)
  }


  getPerfDashBoardURL = this.apiURL + dashboardAPIConstant.getPerDashboard
  getPerfDashboardList = (body: any): Observable<any> => {
    return this.apiService.get(this.getPerfDashBoardURL + body)
  }

  /*--- Data Migration ---*/
  getDataMigrationURL = this.apiURL + dashboardAPIConstant.getDataMigrtion
  getDataMigrationList = (body: any): Observable<any> => {
    return this.apiService.get(this.getDataMigrationURL + body)
  }
  //getDashBoardAssessmentsCounts
  getDashBoardAssessmentsCountsURL = this.apiURL + dashboardAPIConstant.DashBoardAssessmentsCounts
  getDashBoardAssessmentsCounts = (body: any): Observable<any> => {
    return this.apiService.post(this.getDashBoardAssessmentsCountsURL, body)
  }
  getdropdownDetailsURL = this.apiURL + dashboardAPIConstant.dropdownDetails
  getdropdownDetails = (body: any): Observable<any> => {
    return this.apiService.post(this.getdropdownDetailsURL, body)
  }
  getNewOperationURL = this.apiURL + dashboardAPIConstant.getOperationNew
  getnewOperationList = (body: any): Observable<any> => {
    return this.apiService.post(this.getNewOperationURL, body)
  }
  getObjdropdownDetailsURL = this.apiURL + dashboardAPIConstant.objdropdownDetails
  getobjdropdownDetails = (body: any): Observable<any> => {
    return this.apiService.post(this.getObjdropdownDetailsURL, body)
  }
  getSchemaURl = environment.serviceUrl1 + dashboardAPIConstant.getschemaa
  getSchemalist = (body: any): Observable<any> => {
    return this.apiService.get(this.getSchemaURl + body)
  }
  GetSourceCountsURl = environment.serviceUrl1 + dashboardAPIConstant.GetSourceCounts
  GetSourceCountslist = (body: any): Observable<any> => {
    return this.apiService.get(this.GetSourceCountsURl + body.Conid + "&schema=" + body.schema)
  }
  //GetThroughPut
  getThroughPutURL = this.apiURL + dashboardAPIConstant.GetThroughPut
  getThroughPutList = (body: any): Observable<any> => {
    return this.apiService.post(this.getThroughPutURL , body)
  }
  getTableCountBySchemaURL = this.apiURL + dashboardAPIConstant.getTableCountBySchema
  getTableCountBySchema = (body: any): Observable<any> => {
    return this.apiService.get(this.getTableCountBySchemaURL + body)
  }
  getValidationDashboardDataURL = this.apiURL + dashboardAPIConstant.getValidationDashboardData
  getValidationDashboardData = (body: any): Observable<any> => {
    return this.apiService.post(this.getValidationDashboardDataURL, body)
  }
  getValidationCountsURL = this.apiURL + dashboardAPIConstant.getValidationCounts
  getValidationCounts = (body: any): Observable<any> => {
    return this.apiService.post(this.getValidationCountsURL, body)
  }
  getTgtDatatURL = this.apiURL + dashboardAPIConstant.getTgtDatat
  getTgtDatat = (body: any): Observable<any> => {
    return this.apiService.get(this.getTgtDatatURL + body.Conid + "&schema=" + body.schema)
  }
  getCPUDataURL = this.apiURL + dashboardAPIConstant.getCPUData
  getCPUData = (body: any): Observable<any> => {
    return this.apiService.post(this.getCPUDataURL, body)
  }
  //datamigrationNewCommand
  datamigrationNewCommandURL = this.apiURL + dashboardAPIConstant.datamigrationNewCommand
  datamigrationNewCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.datamigrationNewCommandURL, body)
  }
  //getTargetRowCount
  getTargetRowCountURL = this.apiURL + dashboardAPIConstant.getTargetRowCount
  getTargetRowCount = (body: any): Observable<any> => {
    return this.apiService.get(this.getTargetRowCountURL + body.conid + "&table=" + body.table)
  }
  GetSourceRowCountsURL = environment.serviceUrl1 + dashboardAPIConstant.getSourceRowCounts
  GetSourceRowCounts = (body: any): Observable<any> => {
    return this.apiService.get(this.GetSourceRowCountsURL + body.conid + "&table=" + body.table)
  }
  //GetValidationAuditData
  GetValidationAuditDataURL = this.apiURL + dashboardAPIConstant.GetValidationAuditData
  GetValidationAuditData = (body: any): Observable<any> => {
    return this.apiService.post(this.GetValidationAuditDataURL, body)
  }
  //GetCDCAuditData
  GetCDCAuditDataURL = this.apiURL + dashboardAPIConstant.GetCDCAuditData
  GetCDCAuditData = (body: any): Observable<any> => {
    return this.apiService.post(this.GetCDCAuditDataURL, body)
  }
  //GetFabricsData
  GetFabricsDataURL = environment.serviceUrl1 + dashboardAPIConstant.GetFabricsData
  GetFabricsData = (body: any): Observable<any> => {
    return this.apiService.get(this.GetFabricsDataURL + body.conid + "&schema=" + body.schema)
  }
  //GetFabricSchemas
  GetFabricSchemasURL = environment.serviceUrl1 + dashboardAPIConstant.GetFabricSchemas
  GetFabricSchemas = (body: any): Observable<any> => {
    return this.apiService.get(this.GetFabricSchemasURL + body)
  }
  //GetFabricRowCount
  GetFabricRowCountURL = environment.serviceUrl1 + dashboardAPIConstant.GetFabricsRouwCount
  GetFabricRowCount = (body: any): Observable<any> => {
    return this.apiService.get(this.GetFabricRowCountURL + body.conid + "&table=" + body.table)
  }
  //GetmariasnapshotCounts
  GetmariasnapshotCountsURL = environment.serviceUrl1 + dashboardAPIConstant.GetmariasnapshotCounts
  GetmariasnapshotCounts = (body: any): Observable<any> => {
    return this.apiService.get(this.GetmariasnapshotCountsURL+ body.Conid + "&schema=" + body.schema)
  }
  //GetmariaTablesCounts
  GetmariaTablesCountsURL = environment.serviceUrl1 + dashboardAPIConstant.GetmariaTablesCounts
  GetmariaTablesCounts = (body: any): Observable<any> => {
    return this.apiService.get(this.GetmariaTablesCountsURL + body.conid + "&table=" + body.table)
  }
}

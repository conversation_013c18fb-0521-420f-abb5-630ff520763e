<header>
  <div class="container-fluid">
    <div class="row">
      <div class="col-12 col-sm-12 col-md-6 ">
        <div class="d-flex">
          <div class="logo">
            <img src="assets/images/logo.png" alt="logo" class="logo" />
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-12 col-md-6 ">
        <div class="header_nav">
          <div class="hCard">
            <div class="avatar" [style.background-color]="'rgb(' + bgColor + ')'">{{avatar}}</div>
            <p>{{ username }}</p>
          </div>
          <div class="hCard">
            <span class="mdi mdi-bell-outline"></span>
          </div>
          <div class="hCard">
            <span class="mdi mdi-power" (click)="logout()"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
<section class="main-body">
  <div class="container-fluid">
    <div class="row flex-nowrap">
      <div class="close-sidebar" (click)="toggle()">
        <span class="mdi mdi-chevron-left"></span>
      </div>
      <div class="col-auto col-md-3 col-xl-2 p-xxl-2 sideNav">
        <div class="sideNav-flex">
          <nav class="navbar navbar-vertical navbar-expand-lg">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
              data-bs-target="#navbarVerticalCollapse" aria-controls="navbarsExample03" aria-expanded="false"
              aria-label="Toggle navigation">
              <span class="navbar-toggler-icon"></span>
            </button>
            <div class="res-navLogo">
              <div class="logo">
                <img src="assets/images/logo.png" alt="logo" class="logo" />
              </div>
            </div>
            <div class="res-nav">
              <div class="header_nav">
                <div class="hCard">
                  <img src="assets/images/face.png" alt="img" />
                  <p>{{ username }} </p>
                </div>
                <div class="hCard">
                  <span class="mdi mdi-bell-outline"></span>
                </div>
                <div class="hCard">
                  <span class="mdi mdi-power" (click)="logout()"></span>
                </div>
              </div>
            </div>
            <div class="collapse navbar-collapse w-100" id="navbarVerticalCollapse">
              <div class="navbar-vertical-content w-100">
                <ul class="navbar-nav flex-column w-100" id="navbarVerticalNav">
                  @if( projectIdMenu.includes('100')){
                  <!-- <li class="nav-item">
                    <a class="nav-link" routerLinkActive="active" routerLink="/dashboard">
                      <i class="mdi mdi-view-dashboard"></i> <span class="ms-1">Dashboard</span>
                    </a>
                  </li> -->
                  <li class="nav-item">
                    <a class="nav-link" routerLinkActive="active" routerLink="/dashboard/newSummary">
                      <i class="mdi mdi-view-dashboard"></i> <span class="ms-1">Dashboard</span>
                    </a>
                  </li>
                  }

                  @if( projectIdMenu.includes('205') ||
                  projectIdMenu.includes('210') ||
                  projectIdMenu.includes('215') ||
                  projectIdMenu.includes('220') ||
                  projectIdMenu.includes('225') ||
                  projectIdMenu.includes('230') ||
                  projectIdMenu.includes('235') ||
                  projectIdMenu.includes('245') ){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSet1.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-check"></i>
                          </div>
                          <span class="ms-1">Setup</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" routerLinkActive="show"
                          #childSet1="routerLinkActive" id="nv-home">
                          <li class="collapsed-nav-item-title d-none">Setup</li>
                          @if(projectIdMenu.includes('205')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/documents">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Documents</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('215')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/connections">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Connections</span>
                              </div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('235')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/projectDatabases">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Project
                                  Databases</span></div>
                            </a>
                          </li>
                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }
                  @if(projectIdMenu.includes('1200')){
                  <li class="nav-item">
                    <a class="nav-link" routerLinkActive="active" routerLink="/assessment/e2emigration">
                      <i class="mdi mdi-cloud-sync"></i> <span class="ms-1">E2E Migration</span>
                    </a>
                  </li>}

                  @if(projectIdMenu.includes('305') ||
                  projectIdMenu.includes('240') ||
                  projectIdMenu.includes('310') ||
                  projectIdMenu.includes('315') ||
                  projectIdMenu.includes('805') ||
                  projectIdMenu.includes('810') ||
                  projectIdMenu.includes('820') ||
                  projectIdMenu.includes('320') ||
                  projectIdMenu.includes('325') ||
                  projectIdMenu.includes('330')){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home1" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSeta1.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-key"></i>
                          </div>
                          <span class="ms-1">Assessment </span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" routerLinkActive="show"
                          #childSeta1="routerLinkActive" id="nv-home1">
                          @if(projectIdMenu.includes('240')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/extractSchemas">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Schema
                                  Management</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('320')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/codeExtraction">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Extraction</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('325')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/assessment">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Code Scan</span></div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('810')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/serverScan">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Server Scan</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('805')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/DBScan">
                              <div class="d-flex align-items-center"><span class="nav-link-text">DB Scan</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('820') && hideawr == false){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/awrScan">
                              <div class="d-flex align-items-center"><span class="nav-link-text">AWR Scan</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('330')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/reportsUpload">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Reports</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('245')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/serverParameters">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Server
                                  Parameters</span></div>
                            </a>
                          </li>
                          }
                          @if(migtypeid == '40'){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/assessment/help">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Help</span></div>
                            </a>
                          </li>
                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }
                  @if(projectIdMenu.includes('405') ||
                  projectIdMenu.includes('410') ||
                  projectIdMenu.includes('415') ||
                  projectIdMenu.includes('420') ||
                  projectIdMenu.includes('425') ||
                  projectIdMenu.includes('430') ||
                  projectIdMenu.includes('435') ||
                  projectIdMenu.includes('440') ||
                  projectIdMenu.includes('445') ||
                  projectIdMenu.includes('450')||
                  projectIdMenu.includes('455')){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home2" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSetb1.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-braces"></i>
                          </div>
                          <span class="ms-1">Code Migration</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" routerLinkActive="show"
                          #childSetb1="routerLinkActive" id="nv-home2">
                          <li class="collapsed-nav-item-title d-none">Code Migration</li>
                          @if(projectIdMenu.includes('405')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/storageObject">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Storage Objects </span>
                              </div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('410')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/codeObject">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Code Objects </span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('455')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/dalconversion">
                              <div class="d-flex align-items-center"><span class="nav-link-text"> @if(migtypeid ==
                                  '35'){Code Objects}@else{DALConversion}
                                </span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('415')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/validation">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Validation</span></div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('420')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/deploy">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Deploy</span></div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('425')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/manualConversion">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Manual
                                  Conversion</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('430')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/pgcheck">
                              <div class="d-flex align-items-center"><span class="nav-link-text">PG Check
                                </span></div>
                            </a>
                          </li>
                          }
                          <!-- @if(projectIdMenu.includes('455')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/dalconversion">
                              <div class="d-flex align-items-center"><span class="nav-link-text">DAL Conversion
                                </span></div>
                            </a>
                          </li>
                        } -->

                        </ul>
                      </div>
                    </div>
                  </li>
                  }
                  @if(projectIdMenu.includes('480')){
                  <li class="nav-item">
                    <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/conversionAgent">
                      <i class="mdi mdi-account-convert"></i> <span class="ms-1">Conversion Agent</span>
                    </a>
                  </li>}
                  @if(projectIdMenu.includes('480')){
                  <li class="nav-item">
                    <a class="nav-link" routerLinkActive="active" routerLink="/codeMigration/moduleAgent">
                      <i class="mdi mdi-account-tie"></i> <span class="ms-1">Module Agent</span>
                    </a>
                  </li>}
                  @if( projectIdMenu.includes('505') ||
                  projectIdMenu.includes('510') ||
                  projectIdMenu.includes('515') ||
                  projectIdMenu.includes('520') ||
                  projectIdMenu.includes('525')||
                  projectIdMenu.includes('530')||
                  projectIdMenu.includes('535')||
                  projectIdMenu.includes('540')||
                  projectIdMenu.includes('545')||
                  projectIdMenu.includes('550')||
                  projectIdMenu.includes('555')||
                  projectIdMenu.includes('560')||
                  projectIdMenu.includes('1005')
                  ){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home5" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSetc1.isActive }">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-sync"></i>
                          </div>
                          <span class="ms-1">Data Migration</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" routerLinkActive="show"
                          #childSetc1="routerLinkActive" id="nv-home5">
                          @if(projectIdMenu.includes('520')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/airflow">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Scheduler</span></div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('565')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/jobsAgent">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Job Agent</span></div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('530')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/newconfig">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Configurations</span>
                              </div>
                            </a>
                          </li>
                          }


                          <!-- @if(projectIdMenu.includes('530')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/configurations">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Configurations</span>
                              </div>
                            </a>
                          </li>
                          } -->


                          @if(projectIdMenu.includes('535')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/executions">
                              <div class="d-flex align-items-center"><span class="nav-link-text"> Executions</span>
                              </div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('540')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/validations">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Validation</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('570')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/cdc">
                              <div class="d-flex align-items-center"><span class="nav-link-text">CDC</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1005')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/goldenGate/setup">
                              <div class="d-flex align-items-center"><span class="nav-link-text">GoldenGate Setup</span>
                              </div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('545')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/dataCompare">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Data Compare</span>
                              </div>
                            </a>
                          </li>
                          }




                          <!-- @if(projectIdMenu.includes('555')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/uploadDagFile">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Upload Dag File </span>
                              </div>
                            </a>
                          </li>
                          } -->

                          @if(projectIdMenu.includes('550')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/dataLoadStatus">
                              <div class="d-flex align-items-center"><span class="nav-link-text"> Data Load
                                  Status</span></div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('560')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/podManagement">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Pods Management</span>
                              </div>
                            </a>
                          </li>
                          }



                          <!-- @if(projectIdMenu.includes('525')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/reports">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Reports</span>
                              </div>
                            </a>
                          </li>
                          } -->
                          <!-- @if(projectIdMenu.includes('565')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/dataValidation">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Data Validation</span>
                              </div>
                            </a>
                          </li>
                          } -->
                          @if(projectIdMenu.includes('575')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dataMigration/cutover">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Cut Over Plan</span>
                              </div>
                            </a>
                          </li>
                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }
                  @if( projectIdMenu.includes('605') ||
                  projectIdMenu.includes('610') ||
                  projectIdMenu.includes('615') ||
                  projectIdMenu.includes('620')){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home6" data-bs-toggle="collapse"
                        [ngClass]="{'active': dep1.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-arrow-up"></i>
                          </div>
                          <span class="ms-1">Deployment</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" routerLinkActive="show"
                          #dep1="routerLinkActive" id="nv-home6">
                          @if(projectIdMenu.includes('605')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/deployment/incremental">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Incremental</span>
                              </div>
                            </a>
                          </li>
                          }
                          <div class="collapse navbar-collapse w-100" id="navbarVerticalCollapses">
                            <div class="navbar-vertical-content w-100">
                              <ul class="navbar-nav flex-column w-100" id="navbarVerticalNav">
                                <li class="nav-item">
                                  <div class="nav-item-wrapper">
                                    <a class="nav-link dropdown-toggle" href="#nv-home7" data-bs-toggle="collapse"
                                      [ngClass]="{'active': dep2.isActive}">
                                      <div class="d-flex align-items-center">
                                        <div class="dropdown-indicator-icon">
                                          <i class="mdi mdi-cloud-sync"></i>
                                        </div>
                                        <span class="ms-1">Delta Process</span>
                                      </div>
                                    </a>
                                    <div class="parent-wrapper label-1">
                                      <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapses"
                                        routerLinkActive="show" #dep2="routerLinkActive" id="nv-home7">
                                        <!-- @if(projectIdMenu.includes('610')){
                                <li class="nav-item">
                                  <a class="nav-link" routerLinkActive="active" routerLink="/deployment/sourceUpload">
                                    <div class="d-flex align-items-center"><span class="nav-link-text">Delta Process</span>
                                    </div>
                                  </a>
                                </li>
                                 } -->

                                        @if(projectIdMenu.includes('610')){
                                        <li class="nav-item">
                                          <a class="nav-link" routerLinkActive="active" (click)="checked('TI')" #values
                                            routerLink="/deployment/targetintergate">
                                            <div class="d-flex align-items-center"><span class="nav-link-text">Complete
                                                Process</span>
                                            </div>
                                          </a>
                                        </li>
                                        }

                                        <a class="nav-link dropdown-toggle" href="#nv-home8" data-bs-toggle="collapse"
                                          [ngClass]="{'active': dep3.isActive}">
                                          <div class="d-flex align-items-center">
                                            <!-- <div class="dropdown-indicator-icon">
                                            <i class="mdi mdi-cloud-arrow-up"></i>
                                          </div> -->
                                            <span class="ms-1">S & T Compare</span>
                                          </div>
                                        </a>
                                        <div class="parent-wrapper label-1">
                                          <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapsess"
                                            routerLinkActive="show" #dep3="routerLinkActive" id="nv-home8">

                                            @if(projectIdMenu.includes('610')){
                                            <li class="nav-item">
                                              <a class="nav-link" routerLinkActive="active" (click)="checked('ST')"
                                                #values routerLink="/deployment/SandTCompare">
                                                <div class="d-flex align-items-center"><span
                                                    class="nav-link-text">Statement Compare</span>
                                                </div>
                                              </a>
                                            </li>
                                            }
                                            @if(projectIdMenu.includes('610')){
                                            <li class="nav-item">
                                              <a class="nav-link" routerLinkActive="active" (click)="checked('FS')"
                                                #values routerLink="/deployment/targetCurrent">
                                                <div class="d-flex align-items-center"><span class="nav-link-text">File
                                                    Compare</span>
                                                </div>
                                              </a>
                                            </li>
                                            }
                                          </ul>
                                        </div>
                                        <!-- @if(projectIdMenu.includes('610')){
                                        <li class="nav-item">
                                          <a class="nav-link" routerLinkActive="active"
                                            routerLink="/deployment/BandCCompare">
                                            <div class="d-flex align-items-center"><span class="nav-link-text">B & C
                                                Compare</span>
                                            </div>
                                          </a>
                                        </li>
                                        } -->

                                      </ul>
                                    </div>
                                  </div>
                                </li>
                              </ul>
                            </div>
                          </div>


                          @if(projectIdMenu.includes('615')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/deployment/deltaFiles">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Delta Files</span>
                              </div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('620')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/deployment/deltaControl">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Delta Control</span>
                              </div>
                            </a>
                          </li>
                          }

                        </ul>
                      </div>
                    </div>
                  </li>
                  }

                  <!-- @if( projectIdMenu.includes('1005')){
                  <li class="nav-item">
                    <!-- parent pages-
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home4" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSetd1.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-lock"></i>
                          </div>
                          <span class="ms-1">Golden Gate</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" routerLinkActive="show"
                          #childSetd1="routerLinkActive" id="nv-home4">
                          @if(projectIdMenu.includes('1005')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/goldenGate/setup">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Setup</span></div>
                            </a>
                          </li>
                          }

                        </ul>
                      </div>
                    </div>
                  </li>
                  } -->

                  @if( projectIdMenu.includes('905')||
                  projectIdMenu.includes('910')||
                  projectIdMenu.includes('915')||
                  projectIdMenu.includes('920')||
                  projectIdMenu.includes('925')||
                  projectIdMenu.includes('930')||
                  projectIdMenu.includes('935')||
                  projectIdMenu.includes('940')||
                  projectIdMenu.includes('945')||
                  projectIdMenu.includes('950')){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-homet6" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSette1.isActive }">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-search"></i>
                          </div>
                          <span class="ms-1"> Dev Testing</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" id="nv-homet6"
                          routerLinkActive="show" #childSette1="routerLinkActive">
                          <li class="collapsed-nav-item-title d-none">Testing</li>
                          @if(projectIdMenu.includes('950')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/baselineDeployment">
                              <div class="d-flex align-items-center"><span class="nav-link-text">BaseLine
                                  Deployment</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('905')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/workLoad">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Work Load</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('910')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/testCycle">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Test Cycles</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('940')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/testconversion">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Test Conversion</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('915')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/testAssign">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Test Assign</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('920')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/tcDataMigration">
                              <div class="d-flex align-items-center"><span class="nav-link-text">TC Data
                                  Migration</span></div>
                            </a>
                          </li>
                          }
                          <!-- <li class="nav-item">
                              <a class="nav-link"  routerLinkActive="active" routerLink="/testing/testReview" >
                                  <div class="d-flex align-items-center"><span class="nav-link-text">Test Review</span></div>
                              </a>
                          </li> -->
                          @if(projectIdMenu.includes('925')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/testcaseUpload">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Test Case Upload</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('930')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/userTestCases">
                              <div class="d-flex align-items-center"><span class="nav-link-text">User Test Cases</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('935')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/logFileUpload">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Log File Upload</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('945')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/testing/reports">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Reports</span>
                              </div>
                            </a>
                          </li>
                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }

                  @if( projectIdMenu.includes('905')||
                  projectIdMenu.includes('910') ){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-homet61" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSette61.isActive }">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-tags"></i>
                          </div>
                          <span class="ms-1"> SIT </span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" id="nv-homet61"
                          routerLinkActive="show" #childSette61="routerLinkActive">
                          @if(projectIdMenu.includes('905')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/sit/workLoad">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Work Load</span></div>
                            </a>
                          </li>

                          }
                          @if(projectIdMenu.includes('910')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/sit/testCycle">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Test Cycle</span></div>
                            </a>
                          </li>

                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }

                  @if( projectIdMenu.includes('1105') ||
                  projectIdMenu.includes('1110') ||
                  projectIdMenu.includes('1115')||
                  projectIdMenu.includes('1120')||
                  projectIdMenu.includes('1125')||
                  projectIdMenu.includes('1130')||
                  projectIdMenu.includes('1135')||
                  projectIdMenu.includes('1140')){
                  <li class="nav-item">
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home11" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSetf1.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-cog"></i>
                          </div>
                          <span class="ms-1">DBA</span>
                        </div>
                      </a>

                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" id="nv-home11"
                          routerLinkActive="show" #childSetf1="routerLinkActive">
                          @if(projectIdMenu.includes('1105')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/agent">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Agent</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1105')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/roles">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Roles</span></div>
                            </a>
                          </li>
                          }

                          @if(projectIdMenu.includes('1110')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/users"
                              #childSetd1="routerLinkActive">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Users</span></div>
                            </a>
                          </li>
                          }
                          <!-- @if(projectIdMenu.includes('1115')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/vacuum"
                              #childSetd1="routerLinkActive">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Vacuum Analyze</span>
                              </div>
                            </a>
                          </li>
                          } -->
                          @if(projectIdMenu.includes('1125')){
                          <li class="collapsed-nav-item-title d-none">Permissions</li>
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/permissions"
                              #childSetd1="routerLinkActive">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Permissions</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1120')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/bloating">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Bloating</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1115')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/vacuum"
                              #childSetd1="routerLinkActive">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Vacuum Analyze</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1130')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/Healthcheckreport">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Health Check
                                  Report</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1135')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/dba-monitoring">
                              <div class="d-flex align-items-center"><span class="nav-link-text">DBA Monitoring </span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1140')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/dba/reports">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Reports</span></div>
                            </a>
                          </li>
                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }
                  @if( projectIdMenu.includes('805') ||
                  projectIdMenu.includes('815') ){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home12" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSets1.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-check"></i>
                          </div>
                          <span class="ms-1">SQL</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" id="nv-home12"
                          #childSets1="routerLinkActive" routerLinkActive="show">
                          <!-- @if(projectIdMenu.includes('805')){
                          <li class="nav-item">
                            <a class="nav-link"  routerLinkActive="active" routerLink="/sql/sqlexecution" >
                                <div class="d-flex align-items-center"><span class="nav-link-text">SQL execution</span></div>
                            </a>
                          </li>
                        } -->

                          @if(projectIdMenu.includes('815')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/sql/sqlscript">
                              <div class="d-flex align-items-center"><span class="nav-link-text">SQL Script</span></div>
                            </a>
                          </li>
                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }

                  @if( projectIdMenu.includes('1300')||
                  projectIdMenu.includes('1305')||
                  projectIdMenu.includes('1310')||
                  projectIdMenu.includes('1315')||
                  projectIdMenu.includes('1320')||
                  projectIdMenu.includes('1325')||
                  projectIdMenu.includes('1330')||
                  projectIdMenu.includes('1335')){
                  <li class="nav-item">
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-homep5" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSetp1.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-percent"></i>
                          </div>
                          <span class="ms-1">Performance Tuning</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" id="nv-homep5"
                          routerLinkActive="active" #childSetp1="routerLinkActive">
                          <!-- @if(projectIdMenu.includes('705')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/performanceTesting">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Perf
                                  Optimization</span></div>
                            </a>
                          </li>
                          } -->
                           @if(projectIdMenu.includes('1300')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/agent">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Agent</span></div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1300')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/logRunningQueries">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Long Running
                                  Queries</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1305')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/logExplain">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Log Explain</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1310')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active"
                              routerLink="/perfTesting/PerformanceIndexTuning">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Index-Tuning</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1315')){

                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/duplicateIndex">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Duplicate Index</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1320')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/databaseIndex">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Index Monitoring</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1325')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/tablePartition">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Table Partition</span>
                              </div>
                            </a>
                          </li>
                          }
                          <!-- <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/PerformanceTuningInsights">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Performance-Insights</span>
                              </div>
                            </a>
                          </li> -->
                          @if(projectIdMenu.includes('1330')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/QueryInsights">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Query-Insights</span>
                              </div>
                            </a>
                          </li>
                          }
                          @if(projectIdMenu.includes('1335')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/perfTesting/perf-reports">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Reports</span>
                              </div>
                            </a>
                          </li>
                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }

                  @if( projectIdMenu.includes('1400') ){
                  <li class="nav-item">
                    <!-- parent pages-->
                    <div class="nav-item-wrapper">
                      <a class="nav-link dropdown-toggle" href="#nv-home67" data-bs-toggle="collapse"
                        [ngClass]="{'active': childSets11.isActive}">
                        <div class="d-flex align-items-center">
                          <div class="dropdown-indicator-icon">
                            <i class="mdi mdi-cloud-check"></i>
                          </div>
                          <span class="ms-1">Automation Testing</span>
                        </div>
                      </a>
                      <div class="parent-wrapper label-1">
                        <ul class="nav collapse parent" data-bs-parent="#navbarVerticalCollapse" id="nv-home67"
                          #childSets11="routerLinkActive" routerLinkActive="show">
                          @if(projectIdMenu.includes('1400')){
                          <li class="nav-item">
                            <a class="nav-link" routerLinkActive="active" routerLink="/automation/automationTest">
                              <div class="d-flex align-items-center"><span class="nav-link-text">Automation
                                  Script</span></div>
                            </a>
                          </li>
                          }
                        </ul>
                      </div>
                    </div>
                  </li>
                  }
                </ul>
              </div>
              <div class="res-navHelp">
                <div class="help_center">
                  <h3>Help Center</h3>
                  <div class="help_cd">
                    <p>Having trouble in Migration Please contact us for more questions</p>
                    <button class="btn btn-upload">Go to Help Center</button>
                  </div>
                </div>
              </div>
            </div>
          </nav>
          <!-- <div class="help_center">
            <img src="assets/images/help.png" alt="logo" />
            <div class="help_cd">
              <p>Having trouble in Migration Please contact us for more questions</p>
              <button class="btn btn-upload" routerLink="/helpCenter">Go to Help Center</button>
            </div>
          </div> -->
        </div>
      </div>
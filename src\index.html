<!doctype html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="title" content="Qmigrator">
  <meta name="description"
      content="Q-Migrator is a web-based tool for migrating on-prem or cloud databases such as Oracle/SQL Server to Open-source databases like PostgreSQL / MySQL ">
  <meta name="keywords" content="Migrator, sql to postgreSql">
  <meta name="robots" content="index, follow">
  <meta name="language" content="English">
  <meta name="revisit-after" content="10 days">
  <meta name="author" content="Quadrant">
  <meta name="subject" content="Migrator Application">
  <meta name="publisher" content="QuadrentResources">
  <meta name="og:locale" content="en_US">
  <meta name="og:type" content="website">
  <meta name="og:description"
      content="Q-Migrator is a web-based tool for migrating on-prem or cloud databases such as Oracle/SQL Server to Open-source databases like PostgreSQL / MySQL ">
  <meta name="og:title" content="Free Meta Tag Generator - SEOptimer">
  <meta name="og:url" content="https://qmigrator.ai/">
  <meta name="og:image" content="https://qmigrator.ai/img/report-preview.png">
  <meta name="og:site_name" content="Qmigrator: Migrator, sql to postgreSql">
  <meta name="twitter:description"
      content="Q-Migrator is a web-based tool for migrating on-prem or cloud databases such as Oracle/SQL Server to Open-source databases like PostgreSQL / MySQL ">
  <meta name="twitter:title" content="Migrator, sql to postgreSql">
  <meta name="twitter:image" content="https://qmigrator.ai/img/report-preview.png">
  <meta name="twitter:card" content="summary">
  <title>QMigrator Home</title>

  <link rel="icon" type="image/x-icon" href="assets/images/fav.png">

  <base href="/">
  <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.4.47/css/materialdesignicons.min.css" rel="stylesheet" />
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
</head>
<body>
  <app-root></app-root>
</body>
</html>

import { Component } from '@angular/core';
import { HotToastService } from '@ngxpert/hot-toast';
import { TestingService } from '../../../../services/testing.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { schemaListObj, SchemaTableList, RunInfoTable, workLoadReqObj, ConnectionObjTable } from '../../../../models/interfaces/types';
import { NgSelectModule, NgOption } from '@ng-select/ng-select';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { ActivatedRoute } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
declare let $: any;
@Component({
  selector: 'app-work-load',
  standalone: true,
  imports: [FormsModule, CommonModule, ReactiveFormsModule, NgSelectModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './work-load.component.html',
  styles: ``
})
export class WorkLoadComponent {
  // Basic Project Details
  project_name: string = '';
  getRole: string;
  projectId: string;
  conName: any;
  //reqtable
  tabledata: any=[]
  z: any;
  //Local variables
  runNo: string = '';
  ref_spin: boolean = false
  operationType: string = '';
  ConnectionID: string = '';
  schemaName: any;
  objectType: string = '';
  schemaList: SchemaTableList["Table1"] = [];
  spin: boolean = false;
  workLoadForm: any;
  ConsList: any;
  userData: any = [];
  runInfo: RunInfoTable["Table1"] = [];

  assessmentFiles: any;
  iterationselected: any
  //download file
  fileResponse: any;
  //get run no
  spin_dwld: any;
  runnoForReports: any
  getRunSpin: boolean = false;
  runNoData: any;
  //delete table
  deleteResponse: any
  //pagination
  datachange1: any;
  datachangeLogs: any;
  datachange: any;
  page2: number = 1;
  p: number = 1;
  p1: number = 1;
  p2: number = 1;
  page3: number = 1;
  datachange2: any;
  page: number = 1;
  //get individula logs
  r_id: any;
  logdata: any;
  disabledprevious: boolean = false;
  //Page Details
  pageTitle: string = "Work Load"
  pageIcon: string = "assets/images/fileUpload.png"
  //execution reports
  ExecututionFiles: any=[]
  iterationForLogs: any
  //open modol
  selectedschemas: string = '';
  //selected file
  conId: any;
  selectedConname: any;
  // Operation Types List
  OpName: any;
  operations: any = [

    { value: 'Workload_Create', option: 'Workload Create' },
    { value: 'Workload_Deploy', option: 'Workload Deploy' },
    { value: 'Workload_Copy', option: 'Workload Copy' },
  ];
  // Object Types
  ObjectTypes: any = [
    { value: 'All', option: 'All' },
    { value: 'Procedure', option: 'Procedure' },
    { value: 'Function', option: 'Function' },
    { value: 'Package', option: 'Package' }
  ];

  pageName: string = ''

  // Constructor
  constructor(private toast: HotToastService, public formBuilder: FormBuilder, public testingService: TestingService, private route: ActivatedRoute) {
    this.project_name = JSON.parse((localStorage.getItem('project_name') as string));
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') || 'null');
    this.pageName = this.route.snapshot.data['name'];

  }



  ngOnInit(): void {
    this.GetConsList();
    this.workLoadForm = this.formBuilder.group({
      runNo: ['', [Validators.required]],
      connectionType: [''],
      connectionName: ['', [Validators.required]],
      operationType: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      objectType: ['', [Validators.required]],
    });
    this.getRunInfoIteration()
    this.getreqTableData()
    this.getPrjRuninfoSelectAll()
    this.filterExecutionReports()
    this.fetchAssessmentFiles()
    //this.GetIndividualLogs("null");

  }

  get f(): any {
    return this.workLoadForm.controls;
  }

  //Fetch SchemaList
  getSchemasList(runNo: any) {
    // const obj: schemaListObj = {
    //   projectId: this.projectId,
    //   connectionId: ConnectionId,
    // };
    this.testingService.getSchemaList(runNo).subscribe((data: SchemaTableList) => {
      this.schemaList = data.Table1;
    });
  }

  // Fetching Iteration Details
  getRunInfoIteration() {
    this.testingService.getRunNumbers(this.projectId).subscribe((data: RunInfoTable) => {
      this.runInfo = data.Table1;
      this.runInfo = this.filterList(this.runInfo)

    });
  }


  // Fetch Unique run no
  filterList(listData: any) {
    let uniqueNames: any = []
    for (let k = 0; k < listData.length; k++) {
      if (uniqueNames.length == 0) {
        uniqueNames.push(listData[k])
      }
      else {
        var abc = uniqueNames.filter((item: any) => {
          return item.iteration === listData[k].iteration
        })
        if (abc.length == 0) {
          uniqueNames.push(listData[k])
        }
      }
    }
    return uniqueNames;
  }


  // Fetching ConnectionList
  GetConsList() {
    this.testingService.getConnectionsList(this.projectId).subscribe((data: ConnectionObjTable) => {
      const condata = data['Table1'];
      this.ConsList = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });

    });
  }

  //ExecuteButtonEvent
  executeWorkLoad() {
    this.spin = true;
    //console.log(this.spin);

    const obj: workLoadReqObj =
    {
      iterationNo: this.runNo,
      operationType: this.operationType,
      schemaName: this.schemaName.toString(),
      objectType: this.objectType,
      connectionId: this.ConnectionID,
      projectId: this.projectId.toString()
    };
    // let obj = {
    //   iterationNo: this.runNo,
    //   operationType: this.operationType,
    //   schemaName: this.schemaName.toString(),
    //   objectType: this.objectType,
    //   connectionId: this.sConnectionID,
    //   projectId: this.projectId
    // }
    this.testingService.testCaseWrkLoadCmd(obj).subscribe((data: string) => {
      //console.log(data);
      this.spin = false;
      this.toast.success("Executed Successfully")
    })
  }
  //on key
  onKey() {
    this.page2 = 1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;

  }
  //open modal

  openModal(value: any) {
    for (let i = 0; i < this.schemaName.length; i++) {
      this.selectedschemas = this.selectedschemas + this.schemaName[i]
      if (i < this.schemaName.length - 1) {
        this.selectedschemas = this.selectedschemas + ","
      }
    }
    $('#demo').offcanvas('show');
  }
  openPopup() {
    this.schemaName = []
    this.selectedschemas = ''
    this.workLoadForm.reset();
  }

  //select file type
  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;
    //   this.getprojectDocumentsDetail();
    //this.prjSchemaList();
  }
  operations1: any = [
    { value: '0', option: "ALL" },
    { value: '1', option: 'Work Load Create' },
    { value: '2', option: 'Work Load Deploy' },
    { value: '3', option: 'Work Load Copy' },
  ];
  //select operation
  selectOperation(id: any) {
    const op = this.operations.filter((item: any) => {
      return item.operation_id == id;
    });
    this.OpName = op[0].operation_name;

    this.workLoadForm.controls.schema.setValidators([Validators.required])
    this.workLoadForm.controls.schema.updateValueAndValidity()
  }
  //get req tables
  selectedoperation: string = ""


  getreqTableData() {
    this.selectedoperation = "Workload"
    const obj = {
      projectId: this.projectId,
      operationType: "Workload"
    }
    this.ref_spin = true
    this.testingService.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.ConsList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata.forEach((item: any) => {
          if (item.status === 'I') {
            item.status = 'Initialize'
          } else if (item.status === 'P') {
            item.status = 'Pending'
          } else if (item.status === 'C') {
            item.status = 'Completed'
          }
          //return item.operation_name == "Workload"
        })
      }
    })
  }
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.testingService.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData()
    })
  }
  //get individual logs
  selectedoperations: string = ""
  GetIndividualLogs(action: any) {
    var opers = ""
    if (action == '0') {
      opers = "Workload_Create"
    }
    else if (action == '1') {
      opers = "Workload_Deploy"
    }
    else if (action == '2') {
      opers = "Workload_Copy"
    }
    this.selectedoperation = opers
    this.page = 1
    if (action == "null") {
      this.r_id = "null"
    }
    if (action == "previous") {
      this.r_id = this.logdata[0].exelog_id;
    }
    if (action == "next") {
      this.r_id = this.logdata[this.logdata.length - 1].exelog_id;
      this.disabledprevious = false;
    }
    if (action == '') {
      action = 'null'
    }
    const logobj = {
      projectId: this.projectId.toString(),
      operationType: opers,
      operationName: "All",
      action: 'null',
      row_id: this.r_id,
      runno: this.selectvalue
    }
    this.testingService.GetIndividualLogs(logobj).subscribe((data: any) => {
      this.logdata = data['Table1'];
    });
  }
  // get run no details
  getPrjRuninfoSelectAll() {
    this.testingService.GetRunno(this.projectId).subscribe((data: any) => {
      this.getRunSpin = false
      this.runNoData = data['Table1'];
      this.runnoForReports = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoForReports = this.runnoForReports.filter((data: any) => { return ((data.operation_type == "Extraction") && data.iteration != '') })
      this.runNoData = this.runNoData.filter((item: any) => {
        if (item.iteration == "") {
          item.dbschema = "ALL"
        }
        return (item.operation_name == "Workload_Create" || item.operation_name == "Workload_Deploy" || item.operation_name == "Workload_Copy") || item.iteration == ""
      })
      this.runnoForReports = this.filterList(this.runnoForReports)
      this.runNoData = this.filterList(this.runNoData)
    });
  }
  //update run no 
  getUpdatedRunNumber() {
    this.getRunSpin = true
    this.getPrjRuninfoSelectAll()
    this.getreqTableData()

  }
  //download files details
  downloadFile(fileInfo: any) {
    this.testingService.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  //select iteration
  selectIteration(value: any) {
    this.iterationselected = value
    this.operationDepLogs()
    //this.fetchAssessmentFiles()
  }
  //fetch assessment files
  fetchAssessmentFiles() {
    const path = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Deployment_Logs/Testing/"+ this.selectedOpDep
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.assessmentFiles = data
    })
  }
  //select iteration for logs
  selectIterforlogs(value: any) {
    this.iterationForLogs = value
    
  }
  // filter execution reports
  filterExecutionReports() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Execution_Logs/Testing/"+this.selectedOpLog
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
    })
  }
  workLoadReport:any=[]
  workLoadReports() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterReports + "/Reports/Testing/"+this.selectedOpRepo
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.workLoadReport = data
    })
  }
  selectvalue: any;
  SelectIteration(value: any) {
    this.selectvalue = value;
  }
  TriggerWebTestingComand(formData: any) {
    let obj = {
      task: formData.operationType,
      SrcConId: formData.connectionName,
      iteration: formData.runNo,
      schema: this.schemaName.toString(),
      objectType: formData.objectType,
      projectId: this.projectId.toString()
    }
    this.testingService.webTestCommand(obj).subscribe((data: any) => {
      this.toast.success(data.message);
    })

  }
  selectedOpLog: any
  p3:number=1
  datachange4:any
  operationLogs(value: any) {
    this.selectedOpLog = value
    this.filterExecutionReports()
  }
  selectedOpDep: any
  operationDepLogs() {
    this.selectedOpDep = "Workload_Deploy"
    this.fetchAssessmentFiles()
  }
  selectedOpRepo: any
  operationReports(value: any) {
    this.selectedOpRepo = value
    this. workLoadReports()
  }
  iterReports:any
  SelectIterationForReports(value: any) {
    this.iterReports = value;
  }
}

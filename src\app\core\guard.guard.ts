import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';

import { Observable } from 'rxjs';

import { AuthService } from '../core/auth.service';

@Injectable({
  providedIn: 'root',
})

export class GuardGuard implements CanActivate {
  constructor(
    private router: Router,
    private authenticationService: AuthService
  ) { }

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    const some = state.url;
    sessionStorage.setItem('stopdirection', JSON.stringify(some));
    if (!this.authenticationService.isAuthenticate()) {
      this.router.navigate(['login']);
      return false;
    } else {
      const userAccount = next.data['userAccount'] as Array<string>;
      const match = this.authenticationService.accountMatch([userAccount]);
      if (match) {
        return true;
      } else {
        this.router.navigate(['error']);
        return false;
      }
    }
  }
}

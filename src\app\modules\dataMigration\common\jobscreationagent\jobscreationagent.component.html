<div class="v-pageName">{{pageName}}</div>

<div class="qmig-card">
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="getForm">
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="targtetConnection">Source
                            Connection Name</label>
                        <select class="form-select" formControlName="sourceConnection" #src
                            (change)="selectSrcCon(src.value)">
                            <option value="" disabled>Select a Source Connection</option>
                            @for ( list of ConsList; track list) {
                            <option value="{{ list.Connection_ID }}">{{ list.conname }}</option>
                            }
                        </select>
                        @if ( fs.sourceConnection.touched && fs.sourceConnection.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (fs.sourceConnection.errors.required) {Source Connection is Required
                            }
                        </p>
                        }
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label" for="targtetConnection">Target Connection Name</label>
                        <select class="form-select" formControlName="targetConnection" #tgt
                            (change)="getAuditTrackJobsStatus(tgt.value)">
                            <option value="" disabled>Select Target Connection</option>
                            @for(list of tgtlist;track list; ){
                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                            }
                        </select>
                        @if ( fs.targetConnection.touched && fs.targetConnection.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (fs.targetConnection.errors.required) {Target Connection is Required
                            }
                        </p>
                        }
                    </div>
                </div>
                @if(agentHide){
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 " (click)="triggerJobAgent(getForm.value)"
                            [disabled]="getForm.invalid"> <span class="mdi mdi-cog-play"></span>
                            Start Agent @if(spin){<app-spinner />}</button>
                    </div>
                </div>
                }
                @if(fetchHide){
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 " (click)="autoCall(getForm.value)"
                            [disabled]="getForm.invalid"> <span class="mdi mdi-cog-play"></span>
                            Fetch @if(dag_spin){<app-spinner />}</button>
                    </div>
                </div>
                }
            </div>

        </form>        
    </div>
       <!-- Job Agent Status -->
            @if(agentStatusHide){
            <div class="table-responsive">
                <table class="table table-hover qmig-table">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Source Connection</th>
                            <th>Target Connection</th>
                            <th>Status</th>
                            <th>Delete</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for(dags of agentTrackData |searchFilter: datachange1|paginate:{
                        itemsPerPage: 10, currentPage: p1, id:'fourth'};
                        track dags){
                        <tr>
                            <td>{{ p*10+$index+1-10 }}</td>
                            <td>{{dags.SourceConnection }}</td>
                            <td>{{dags.TargetConnection }}</td>
                            <td>{{dags.agent_status}}</td>
                            <td>
                                <button (click)="deleteTrackJobsStatus(dags.source_connection_id,dags.target_connection_id)" class="btn btn-delete">
                                    <span class="mdi mdi-delete btn-icon-prepend"></span>
                                </button>
                            </td>
                        </tr>
                        } @empty {
                        <tr>
                            <td colspan="4">
                                <p class="text-center m-0 w-100">Empty</p>
                            </td>
                        </tr>
                        }
                    </tbody>
                </table>
            </div>
            <!-- pagination -->
            <div class="custom_pagination">
                <pagination-controls (pageChange)="p = $event" id="fourth"></pagination-controls>
            </div>
        }
            <!-- for download file -->
                @if(dagsHide){
            <hr/>
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-6 mt-4">
                        <h3 class="main_h pt-2 ps-3">Dag configuration status</h3>
                    </div>
                    <div class="col-12 col-sm-6 col-md-6">
                        <div class="custom_search cs-r my-3 me-3">
                            <span class="mdi mdi-magnify"></span>
                            <input type="text" placeholder="Search Config " aria-controls="example"
                                class="form-control" [(ngModel)]="datachange" (keyup)="onKey()" />
                        </div>
                    </div>
                </div>
            <!-- <div class="table-responsive">
                <table class="table table-hover qmig-table">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Dag Name</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for(dags of auditlist |searchFilter: datachange|paginate:{
                        itemsPerPage: 10, currentPage: p, id:'third'};
                        track dags){
                        <tr>
                            <td>{{ p*10+$index+1-10 }}</td>
                            <td>{{dags.dag_name }}</td>
                            <td>{{dags.dag_status}}</td>

                        </tr>
                        } @empty {
                        <tr>
                            <td colspan="4">
                                <p class="text-center m-0 w-100">Empty</p>
                            </td>
                        </tr>
                        }
                    </tbody>
                </table>
            </div> -->
            
                    <!-- Fetch Reports  -->
                @if(configDetails.length > 1){
            <div class="table-responsive">                    
                <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                    <th>#</th>
                    <th>Config Name</th>
                    <th>Config Status</th>
                    <th>Start Time</th>
                    <th>End Time</th>
                    <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @for(item of filteredConfigDetails |searchFilter: datachange|paginate:{
                    itemsPerPage: 10, currentPage: p, id:'third'};
                    track item){
                    <!-- Row itself -->
                    <tr>
                        <td>{{ $index + 1 }}</td>
                        <td>{{ item.config_name }}</td>
                        <td>{{item.config_status}}</td>
                        <td>{{item.dag_start_time}}</td>
                        <td>@if(item.dag_end_time ){{{item.dag_end_time }}}@else{ - } </td>
                        <td>
                        <button class="btn btn-upload btn-small px-3"  (click)="toggleExpand($index, item.config_id, item.config_status)">  {{ expandedRowIndex === $index ? 'Collapse' : ' Dag Status' }} </button>
                        </td>
                    </tr>

                    <!-- Expanded row comes right after the clicked row -->
                     @if(updateStatus && (expandedRowIndex === $index)){
                        <tr>
                            <td colspan="6" class="text-end py-3">
                                    <button class="btn btn-sign btn-small px-3" (click)="updateAgentStatus()">Update Status</button>
                            </td>
                        </tr>
                     }
                    <tr *ngIf="expandedRowIndex === $index">
                        <td colspan="6">
                            <div class="table-responsive">
                                <table class="table table-hover qmig-table">
                                    <thead>
                                        <tr>
                                            <th>
                                                #
                                            </th>
                                            <th>Dag Name</th>
                                            <th>Start Time</th>
                                            <th>End Time</th>
                                            <th>Dag Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for(dags of configFilterData |searchFilter: configDataChange|paginate:{
                                        itemsPerPage: 10, currentPage: cp, id:'five'};
                                        track dags){
                                        <tr>
                                            <td>
                                                <div class="form-check">
                                                <input type="checkbox" [checked]="dags.isSelected" name="list_name" value="{{dags.dag_id}}"
                                                    (click)="onChange(dags,dags.dag_id, $event)" #checkValue class="form-check-input">
                                                </div>
                                            </td>
                                            <td>{{dags.dag_name }}</td>
                                            <td>{{dags.dag_start_time}}</td>
                                            <td>@if(dags.dag_end_time ){{{dags.dag_end_time }}}@else{ - } </td>
                                            <td>   
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" role="switch" id="flexSwitchCheckDefault" value="dags.dag_status" [checked]="dags.dag_status === 'Success'" [attr.aria-checked]="dags.dag_status === 'Success'" (change)="toggleStatus(dags.dag_id)">
                                                    <label class="form-check-label" for="flexSwitchCheckDefault">{{dags.dag_status }}</label>
                                                </div>
                                            </td>
                                        </tr>
                                        } @empty {
                                        <tr>
                                            <td colspan="3">
                                                <p class="text-center m-0 w-100">Empty</p>
                                            </td>
                                        </tr>
                                    }
                                    </tbody>
                                </table>
                            </div>
                            <!-- pagination -->
                            <div class="custom_pagination">
                                <pagination-controls (pageChange)="cp = $event" id="five"></pagination-controls>
                            </div>
                        </td>
                    </tr>                        
                    } @empty {
                    <tr>
                        <td colspan="4">
                            <p class="text-center m-0 w-100">Empty</p>
                        </td>
                    </tr>
                    }
                </tbody>
                </table>
            </div>
            }
            <!-- pagination -->
            <div class="custom_pagination">
                <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
            </div>
        }
</div>
/* You can add global styles to this file, and also import other style files */
.iframe-container {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 0;
    }
}
header:after {
    content: '';
    position: absolute;
    width: 101%;
    height: 15px;
    top: -15px;
    left: -10px;
    background: #fff;
}

  .input-section {
    display: flex;
    gap: 10px;
  }
  
  .output-section {
    margin-top: 20px;
  }
  
  .diff-line {
    display: flex;
    justify-content: space-between;
  }
  
  .removed {
    background-color: pink;
    width: 45%;
  }
  
  .added {
    background-color: lightgreen;
    width: 45%;
  }
  
  .highlight {
    background-color: yellow;
    opacity: 0.3;
  }
  
  .text-diff {
    width: 1400px;
  }
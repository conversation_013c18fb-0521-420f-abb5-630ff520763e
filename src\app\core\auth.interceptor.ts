import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthService } from './auth.service';
import { Router } from '@angular/router';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private authenticationService: AuthService,
    private router: Router
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // First, check if the token is available from the authentication service
    let jwtToken = this.authenticationService.currentUserValue;

    // If no token in the service, check localStorage
    if ((!jwtToken || jwtToken === 'null') && localStorage.getItem('currentUser')) {
      jwtToken = localStorage.getItem('currentUser');
    }

    // If no token is found, try fallback to <PERSON><PERSON> login response
    if ((!jwtToken || jwtToken === 'null') && this.authenticationService.KubeLogInResponse) {
      jwtToken = this.authenticationService.KubeLogInResponse.token;
    }

    if (!jwtToken) {
      return next.handle(req); // No token available, let the request go through unmodified
    }

    // Decode token to check expiry
    const tokenParts = jwtToken.split('.');
    if (tokenParts.length === 3) {
      try {
        const decodedToken = JSON.parse(atob(tokenParts[1]));
        const exp = decodedToken.exp;
        if (exp && Date.now() > exp * 1000) {
          // Token expired
          localStorage.clear();
          sessionStorage.clear();
          this.router.navigate(['/login']);
          return throwError(() => new Error('JWT token expired'));
        }
      } catch (err) {
        console.warn('Error decoding token', err);
      }
    }

    // Prepare headers
    let headers = req.headers.set('Authorization', `Bearer ${jwtToken}`);

    // Avoid setting Content-Type for FormData
    if (!(req.body instanceof FormData)) {
      headers = headers.set('Content-Type', 'application/json');
    }

    // Clone request with updated headers
    const clonedRequest = req.clone({ headers });

    return next.handle(clonedRequest).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          localStorage.clear();
          sessionStorage.clear();
          this.router.navigate(['/login']);
        }
        return throwError(() => error);
      })
    );
  }
}

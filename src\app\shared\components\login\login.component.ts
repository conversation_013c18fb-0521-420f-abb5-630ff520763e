import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HotToastService } from '@ngxpert/hot-toast';
import { AuthService } from '../../../core/auth.service';
import { CommonService } from '../../../services/common.service';
import { SpinnerWhiteComponent } from '../spinner-white/spinner-white.component';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ReactiveFormsModule, RouterOutlet, FormsModule, CommonModule, SpinnerWhiteComponent, RouterLink],
  templateUrl: './login.component.html',
  styles: ``
})
export class LoginComponent {

  /*--- Login form ---*/
  loginForm = this.formBuilder.group({
    email: ['', [Validators.required, Validators.email]],
    key: ['', [Validators.required, Validators.minLength(3)]],
    file: [''],
  })
  spinner: boolean = false
  userID: string | undefined;
  fileResult: any;
  fileName: string = ''
  fileExists: boolean = true;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private toast: HotToastService,
    private route: ActivatedRoute,
    private auth: AuthService,
    private commonService: CommonService
  ) {
    if (localStorage.length!=0) {
      localStorage.clear()
    }
    if (sessionStorage.getItem('stopdirection')) {
      const some = JSON.parse(sessionStorage.getItem('stopdirection') || '{}');
      this.router.navigate([some]);
    } else {
      this.router.navigate(['/login']);
    }
    this.route.params.subscribe((params) => {
      this.userID = params['id'];
    });
    this.route.queryParams.subscribe((params) => {
      if (params["email"] != null && params['token'] != null && params['project_id'] != null) {
        this.userID = params['email'];
        localStorage.setItem("Email", params['email'].split("@")[0]);
        this.auth.setSession(params);
      }
    });
    this.fileCheck()
  }

  /*--- Login form controls ---*/
  get f(): any { return this.loginForm.controls; }

  resetUser() {
    this.loginForm.controls.email.reset()
  }

  resetPassword() {
    this.loginForm.controls.key.reset()
  }

  /*--- Login form method ---*/
  onSubmit(formValue: any) {
    this.spinner = true
    if (this.fileResult == undefined) {
      this.fileResult = "null"
    }
    else {
      this.fileResult = this.fileResult.split(",")[1]
    }
    localStorage.setItem("Email", formValue.email.split("@")[0])
    this.auth.kubeLogin(formValue, this.fileResult);
    this.auth.LoginResponse.subscribe((data: any) => data == null ? '' : this.spinner = false)
  }

  /*--- Kuberenetes File Check ---*/
  fileCheck() {
    this.commonService.checkQubeFile().subscribe((data: any) => {
      this.fileExists = data.message
      if (!this.fileExists) {
        this.loginForm.controls.file.setValidators([Validators.required])
      } else {
        this.loginForm.controls.file.clearValidators()
      }
      this.loginForm.controls.file.updateValueAndValidity()
    })
  }

  sendValue($event: any): void {
    this.readThis($event.target);
  }
  readThis(inputValue: any): void {
    const file: File = inputValue.files[0];
    this.fileName = inputValue.files[0].name;
    const myReader: FileReader = new FileReader();
    myReader.onloadend = () => {
      this.fileResult = myReader.result;
    };
    myReader.readAsDataURL(file);
  }
}

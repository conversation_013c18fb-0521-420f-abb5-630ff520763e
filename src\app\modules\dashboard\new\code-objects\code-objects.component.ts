import { Component, ViewChild } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { DashboardService } from '../../../../services/dashboard.service';
import { CommonModule, PercentPipe } from '@angular/common';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartOptions, ChartType } from 'chart.js';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { FormsModule } from '@angular/forms';
import { Connection, ConnectionData } from '../../../../models/interfaces/dashBoardTypes';
import { AssessmentService } from '../../../../services/assessment.service';

interface OperationList {
  iteration: string,
  conname: string,
  schemaname: string,
  total_count: string,
  converted: string,
  percentage_passed: number
}


@Component({
  selector: 'app-code-objects',
  standalone: true,
  imports: [TabsComponent, PercentPipe, NgChartsModule, CommonModule, SearchFilterPipe, FormsModule],
  templateUrl: './code-objects.component.html',
  styles: ``
})
export class NewCodeObjectsComponent {

  @ViewChild('chart1') chart1: BaseChartDirective | undefined;
  @ViewChild('chart2') chart2: BaseChartDirective | undefined;
  @ViewChild('chart3') chart3: BaseChartDirective | undefined;

  connectionsList: Array<String> = [];
  schemaList: Array<String> = [];
  iterationList: Array<String> = [];
  operationsList: OperationList[] = [];
  operationsCopy: OperationList[] = this.operationsList
  operationsTable: OperationList[] = this.operationsList

  connectionID: string = '';
  connectionValue: string = '';
  schemaId: string = '';
  schemaName: string = '';
  iterationId: string = ''


  /*---- Search bar ---*/
  searchText: string = '';


  /*--- Long Running Queries Chart Data ---*/

  public longRunningChartLabels = [];
  public longRunningChartType = 'bar';
  public longRunningChartLegend = true;
  public longRunningChartData = [
    {
      data: [],
      label: ' Object Count',
      borderColor: '#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [],
      label: ' Match count',
      borderColor: '#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [],
      label: ' Missing count',
      borderColor: '#cf30fc',
      backgroundColor: '#f75989',
      hoverBackgroundColor: '#f5346e',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public longRunningChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          autoSkip: false,   // Show all labels
          maxRotation: 90,   // Rotate labels if needed
          minRotation: 45
        }
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };


  /*--- Long Running Queries Chart Data ---*/

  public sObjectChartLabels = [];
  public sObjectChartType = 'bar';
  public sObjectChartLegend = true;
  public sObjectChartData = [
    {
      data: [],
      label: ' Object Count',
      borderColor: '#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [],
      label: ' Match count',
      borderColor: '#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public sObjectChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        stacked: true,
        ticks: {
          autoSkip: false,   // Show all labels
          maxRotation: 90,   // Rotate labels if needed
          minRotation: 45
        }
      },
      y: {
        stacked: true
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

  /*---- line Charts ----*/


  // Custom plugin for displaying percentage in the center
  centerTextPlugin = {
    id: 'centerTextPlugin',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'myDoughnutChart') {
        const { ctx, chartArea: { width, height } } = chart;

        ctx.save();
        // Get conversionPercentage from the component
        const percentage = this.conversionPercentage;

        // Define style for the text
        ctx.font = 'bold 25px Geist';
        ctx.fillStyle = '#333';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Set position to center of the chart
        const centerX = width / 2;
        const centerY = height / 2 + 20;

        // Draw the text in the center
        ctx.fillText(percentage, centerX, centerY);
        ctx.restore();
      }
    }
  };

  public lineChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'Source Count',
      'Extraction Count',
    ],
    datasets: [
      {
        data: [],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
          '#8b36ff',
          '#f0f0f0'
        ],
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,

      }
    ]
  };
  public lineChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }

  };
  public lineChartLegend = false;
  public lineChartPlugins = [this.centerTextPlugin];

  /*--- source vs extraction % ---*/
  public sourceVSChartLabels = [];
  public sourceVSChartType = 'bar';
  public sourceVSChartLegend = true;
  public sourceVSChartData = [
    {
      data: [],
      label: ' Converted',
      borderColor: '#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [],
      label: ' Pending',
      borderColor: '#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public sourceVSChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
          label: (tooltipItem) => {
            const dataPoint = tooltipItem.formattedValue;
            // Return multiple labels for the tooltip
            return [
              " Count" + ': ' + dataPoint,  // Main dataset label
            ];
          }
        }
      }
    }
  };
  selectedConnectionId: string | null = null;
  totalCount: number = 0;
  connectionCounts: any[] = [];
  schemaCounts: any[] = [];
  convertedCount: number = 0;
  conversionPercentage: string='';
  ConsList: Connection[] = [];
  filteredConsList: any[] = [];
  Schemadisable:boolean=false;
   // Pagination for sObjectChart
   public sObjectChartCurrentPage: number = 1;
   public sObjectChartItemsPerPage: number = 10;
   public sObjectChartTotalPages: number = 1;
   public sObjectChartPaginatedLabels: string[] = [];
   public sObjectChartPaginatedData: any[] = [];
 
   // Pagination for longRunningChart
   public longRunningChartCurrentPage: number = 1;
   public longRunningChartItemsPerPage: number = 10;
   public longRunningChartTotalPages: number = 1;
   public longRunningChartPaginatedLabels: string[] = [];
   public longRunningChartPaginatedData: any[] = [];
  constructor(private dbService: DashboardService, private assessmentService: AssessmentService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
  }

  ngOnInit(): void {
    this.getOperationsList(null, null, null, null, null);
    this.GetConsList();
    this.getschemaDetails();
    this.getdropdowndbnameDetails("null");
    this.getdropdownschemaDetails("null", "null");
    this.getdropdownobjecttypeDetails("null","null","null")
    this.getdropdownItDetails("null","null","null")
  }
 

  /*--- Fetch Iteration ---*/
  getOperationsList(connectionName: string | null, dbname: string | null, schemaname: string | null, objectType: string | null, iteration: string | null) {
    const obj = {
      operation: "code_objects",
      conid: connectionName || "null",
      dbname: dbname || "null",
      schemaname: schemaname || "null",
      iteration: iteration || 'null',
      objectType: objectType || "null"
    }
    this.dbService.getnewOperationList(obj).subscribe((data: any) => {
      this.operationsList = data;
      this.totalCount = data.totalCount!== undefined && data.totalCount !== null && data.totalCount !== '' ? data.totalCount : 0;
      this.convertedCount = data.matchedCount!== undefined && data.matchedCount !== null && data.matchedCount !== '' ? data.matchedCount : 0;
      this.conversionPercentage = data.conversionPercentage!== undefined && data.conversionPercentage !== null && data.conversionPercentage !== '' ? data.conversionPercentage : 0;
      this.lineChartData = {
        labels: ['Source Count', 'Extraction Count'],
        datasets: [
          {
            data: [data.totalCount, data.matchedCount],
            label: 'Daily Count of Unused Indexes',
            circumference: 180,
            rotation: 270,
            backgroundColor: ['#8b36ff', '#f0f0f0'],
            hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
            borderWidth: 0,
          }
        ]
      };
      const connectionData = data.connection;
      this.sourceVSChartLabels = connectionData.map((item: any) => item.conName);
      this.sourceVSChartData[0].data = connectionData.map((item: any) => parseInt(item.matchedCount, 10));
      this.sourceVSChartData[1].data = connectionData.map((item: any) => parseInt(item.unmatchedCount, 10));

      const schemaData = data.schemaName;
      this.longRunningChartLabels = schemaData.map((item: any) => item.schemaname);
      this.longRunningChartData[0].data = schemaData.map((item: any) => parseInt(item.totalCount, 10));
      this.longRunningChartData[1].data = schemaData.map((item: any) => parseInt(item.matchedCount, 10));
      this.longRunningChartData[2].data = schemaData.map((item: any) => parseInt(item.unmatchedCount, 10));
      this.longRunningChartTotalPages = Math.ceil(this.longRunningChartLabels.length / this.longRunningChartItemsPerPage);

      const objectTypeData = data.objectType;
      this.sObjectChartLabels = objectTypeData.map((item: any) => `${item.schemaname} (${item.objecttype})`);
      this.sObjectChartData[0].data = objectTypeData.map((item: any) => parseInt(item.totalCount, 10));
      this.sObjectChartData[1].data = objectTypeData.map((item: any) => parseInt(item.matchedCount, 10));
      this.sObjectChartTotalPages = Math.ceil(this.sObjectChartLabels.length / this.sObjectChartItemsPerPage);
      this.updatePagination();
    },
      (error) => {
        console.error("Error:", error);
      }
    );
  }

  // ConsList: any;
  projectId: string = '';
  GetConsList() {
    this.assessmentService.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.filteredConsList = [...this.ConsList];
      this.getdropdownDetails();
    });
  }
  
  conList: any[] = [];
  filterConsList: any = []
  onConnectionChange(event: any) {
    const selectedConId = event.target.value;
    this.selectedDbName = '';
    this.SchemaList = [];
    this.selectedConnectionId = selectedConId;

    this.filteredConsList = this.ConsList.filter(item => item.Connection_ID === selectedConId);
    this.getOperationsList(selectedConId, null, null, null, null);
    this.getdropdowndbnameDetails(selectedConId);
  }
  getdropdownDetails() {
    const obj = {
      iteration: "null",
      conId: "null",
      schemaname: "null",
      dbname: "null",
      objectname: 'null',
      operation: "conname"
    }

    this.dbService.getdropdownDetails(obj).subscribe((data: any) => {
      this.filterConsList = this.conList.map((dropdownItem: any) => {
        const match = this.ConsList.find((consItem: any) => consItem.conname === dropdownItem.conname);
        return match ? { ...dropdownItem, conid: match.Connection_ID } : null;
      }).filter((item: any) => item !== null);
    });
  }
  dbnameList: any[] = []
  getdropdowndbnameDetails(conId: string) {
    const obj = {
      iteration: "null",
      conId: conId || "null",
      schemaname: "null",
      dbname: "null",
      objectname: 'null',
      operation: "dbname"
    }
    this.dbService.getdropdownDetails(obj).subscribe((data: any) => {
      this.dbnameList = data['Table1'];
    })

  }
  
  selectedDbName: string = '';
  onDbNameChange(event: any) {
   // const dbname = event.target.value;
    // this.selectedDbName = dbname;

    // if (this.selectedConnectionId && dbname) {
    //   this.getOperationsList(this.selectedConnectionId, dbname, null, null, null);
    //   this.getdropdownschemaDetails(this.selectedConnectionId, dbname);
    //   this.getschemaDetails(this.selectedConnectionId, this.selectedDbName);
    // }
    const dbname = event.target.value;
    this.selectedDbName = dbname;

    if (this.selectedConnectionId && dbname) {
      this.getOperationsList(this.selectedConnectionId, dbname, null, null, null);
      this.getdropdownschemaDetails(this.selectedConnectionId, dbname);
      this.getschemaDetails(this.selectedConnectionId, this.selectedDbName);
    } else {
      this.getOperationsList(this.selectedConnectionId, dbname, null, null, null);
    }
  }
  selectedSchema: string = '';
  onSchemaChange(event: any) {
    // const schema = event.target.value;
    // this.selectedSchema = schema;
    // if (this.selectedConnectionId && this.selectedDbName && schema) {
    //   this.getOperationsList(this.selectedConnectionId, this.selectedDbName, schema, null, null);
    //   this.getdropdownobjecttypeDetails(this.selectedConnectionId, this.selectedDbName, schema);
    //   this.getdropdownItDetails(this.selectedConnectionId, this.selectedDbName, schema );
    // }
    const schema = event.target.value;
    this.selectedSchema = schema;
    if (this.selectedConnectionId && this.selectedDbName && schema) {
      this.Schemadisable=true;
      this.getOperationsList(this.selectedConnectionId, this.selectedDbName, schema, null, null);
      this.getdropdownobjecttypeDetails(this.selectedConnectionId, this.selectedDbName, schema);
      this.getdropdownItDetails(this.selectedConnectionId, this.selectedDbName, schema)
    } else {
      this.getOperationsList(
        this.selectedConnectionId || null,
        this.selectedDbName || null,
        schema,
        this.selectedObjectType || null,
        this.selectedIteration || null
      );
    }
  }
  SchList: any[] = []
  getdropdownschemaDetails(conId: string, dbname: string) {
    const obj = {
      iteration: "null",
      conId: conId || "null",
      schemaname: "null",
      dbname: dbname || "null",
      objectname: 'null',
      operation: "schema_name"
    };
    this.dbService.getdropdownDetails(obj).subscribe((data: any) => {
      this.SchList = data['Table1'];
    })

  }
  SchemaList: any[] = []
  getschemaDetails(conId: string = 'null', dbname: string = 'null') {
    const obj = {
      iteration: "null",
      conId: conId || "null",
      schemaname: "null",
      dbname: dbname || "null",
      objectname: 'null',
      operation: "schema_name"
    };

    this.dbService.getdropdownDetails(obj).subscribe((data: any) => {
      this.SchemaList = data['Table1'] || [];
    });
  }
 
 
  selectedObjectType: string = '';
  onObjectTypeChange(event: any) {
    const objType = event.target.value;
    this.selectedObjectType = objType;

    // if (this.selectedConnectionId && this.selectedDbName && this.selectedSchema && objType) {
    //   this.getOperationsList(this.selectedConnectionId, this.selectedDbName, this.selectedSchema, objType, null);
     
    // }
    if (this.selectedConnectionId && this.selectedDbName && this.selectedSchema && objType) {
      this.getOperationsList(this.selectedConnectionId, this.selectedDbName, this.selectedSchema, objType, null);
    }else{
      this.getOperationsList(this.selectedConnectionId, this.selectedDbName, this.selectedSchema, objType, null);
    }
  }
  ObjList: any[] = []
  getdropdownobjecttypeDetails(conId: string, dbname: string, schemaname: string) {
    const obj = {
      iteration: "null",
      conId: conId || "null",
      dbname: dbname || "null",
      schemaname: schemaname || "null",
      objecttype: 'null',
      operation: "code_objects"
    };
    this.dbService.getobjdropdownDetails(obj).subscribe((data: any) => {
      this.ObjList = data['Table1'];
    })

  }
  itList: any[] = []
  getdropdownItDetails(conId: string, dbname: string, schemaname: string) {
    const obj = {
      iteration: "null",
      conId: conId || "null",
      dbname: dbname || "null",
      schemaname: schemaname || "null",
      objectname: "null",
      operation: "iteration_id"
    };
    this.dbService.getdropdownDetails(obj).subscribe((data: any) => {
      this.itList = data['Table1'];
    })

  }
  selectedIteration: string = '';
  onIterationChange(event: any) {
    this.selectedIteration = event.target.value;
    // if (
    //   this.selectedConnectionId &&
    //   this.selectedDbName &&
    //   this.selectedSchema &&
    //   this.selectedIteration
    // ) {
    //   this.getOperationsList(
    //     this.selectedConnectionId,
    //     this.selectedDbName,
    //     this.selectedSchema,
    //     this.selectedObjectType,
    //     this.selectedIteration
    //   );
    // }
    if (
      this.selectedConnectionId &&
      this.selectedDbName &&
      this.selectedSchema &&
      this.selectedObjectType &&
      this.selectedIteration
    ) {
      this.getOperationsList(
        this.selectedConnectionId,
        this.selectedDbName,
        this.selectedSchema,
        this.selectedObjectType,
        this.selectedIteration
      );
    }else{
      this.getOperationsList(
        this.selectedConnectionId,
        this.selectedDbName,
        this.selectedSchema,
        this.selectedObjectType,
        this.selectedIteration
      );
    }
  }
  selectedSchemaChart: string | null = null;

  onSchemaChartChange(event: any) {
    const schema = event.target.value;
    this.selectedSchemaChart = schema;
  
    if ( schema) {
      this.updateSchemaChartOnly(schema);
    } else {
      this.longRunningChartLabels = [];
      this.longRunningChartData.forEach(series => (series.data = []));
    }
  }
  updateSchemaChartOnly(schema: string) {
    const obj = {
      conid:  'null',
      dbname: 'null',
      schemaname: schema,
      iteration: 'null',
      objectname: 'null'
    };
  
    this.dbService.getDashBoardAssessmentsCounts(obj).subscribe((data: any) => {
      const schemaData = data.schemaName;
  
      if (schemaData?.length > 0) {
        this.longRunningChartLabels = schemaData.map((item: any) => item.schemaname);
        this.longRunningChartData[0].data = schemaData.map((item: any) => parseInt(item.totalCount, 10));
        this.longRunningChartData[1].data = schemaData.map((item: any) => parseInt(item.extractedCount, 10));
        this.longRunningChartData[2].data = schemaData.map((item: any) => parseInt(item.invalidCount, 10));
        this.longRunningChartTotalPages = Math.ceil(this.longRunningChartLabels.length / this.longRunningChartItemsPerPage);
        this.updatePagination(); 
      } else {
        this.longRunningChartLabels = [];
        this.longRunningChartData.forEach(series => (series.data = []));
      }
    });
  }
  
  trackBySchema(index: number, item: any): string {
    return item.schema_name;
  }
  updatePagination() {
    const sObjectStartIndex = (this.sObjectChartCurrentPage - 1) * this.sObjectChartItemsPerPage;
    const sObjectEndIndex = sObjectStartIndex + this.sObjectChartItemsPerPage;

    this.sObjectChartPaginatedLabels = this.sObjectChartLabels.slice(sObjectStartIndex, sObjectEndIndex);
    this.sObjectChartPaginatedData = this.sObjectChartData.map((dataset: any) => ({
      ...dataset,
      data: dataset.data.slice(sObjectStartIndex, sObjectEndIndex),
    }));

    const longRunningStartIndex = (this.longRunningChartCurrentPage - 1) * this.longRunningChartItemsPerPage;
    const longRunningEndIndex = longRunningStartIndex + this.longRunningChartItemsPerPage;

    this.longRunningChartPaginatedLabels = this.longRunningChartLabels.slice(longRunningStartIndex, longRunningEndIndex);
    this.longRunningChartPaginatedData = this.longRunningChartData.map((dataset: any) => ({
      ...dataset,
      data: dataset.data.slice(longRunningStartIndex, longRunningEndIndex),
    }));
  }
  prevPage() {
    if (this.sObjectChartCurrentPage > 1) {
      this.sObjectChartCurrentPage--;
      this.updatePagination();
    }
  }

  nextPage() {
    if (this.sObjectChartCurrentPage < this.sObjectChartTotalPages) {
      this.sObjectChartCurrentPage++;
      this.updatePagination();
    }
  }
  schprevPage() {
    if (this.longRunningChartCurrentPage > 1) {
      this.longRunningChartCurrentPage--;
      this.updatePagination();
    }
  }

  schnextPage() {
    if (this.longRunningChartCurrentPage < this.longRunningChartTotalPages) {
      this.longRunningChartCurrentPage++;
      this.updatePagination();
    }
  }
}



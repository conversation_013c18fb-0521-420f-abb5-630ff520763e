import { Component, EventEmitter, Output, ViewChild, output } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { TabsComponent } from '../tabs/tabs.component';
import { DeploymentService } from '../../../../services/deployment.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';
import { __values } from 'tslib';


declare let $: any;

@Component({
  selector: 'app-delta-process',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, TabsComponent],
  templateUrl: './delta-process.component.html',
  styles: ``
})
export class DeltaProcessComponent {

  /*--- BreadCrumb --*/

  pi: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;
  fileData: any;
  datachange: any;
  datachange1:any;
  TgtUpdateTgt: any;
  logfile: any;
  uploadedData: any = [];
  pageNumber: number = 1;
  p: number = 1;
  pages: number = 1;
  page: number = 1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  prjSrcTgtData: any;
  sourceDelta: any;
  datachanges: any;
  datachanges1: any;
  datachanges2: any;

  // sourceDelta: FormGroup = new FormGroup({});
  sourceFileUpload: FormGroup = new FormGroup({});

  // @ViewChild(PlyrComponent)
  // plyr: PlyrComponent | undefined;

  // // or get it from plyrInit event
  // player: Plyr | undefined;
  // poster = 'assets/videos/qmig_ui.jpg';



  schemaName: any = [];


  selDate: any;
  selDis: boolean = false
  checked: string = "FALSE"

  hide_files: boolean = false;
  hide_db: boolean = false;
  isGit: boolean = true;
  isRefernce: boolean = true
  DataBaseType: string = '';
  UserInfo: any;
  sourceext: any;
  sourcecon: any;
  scheman: any;
  date: any;
  sourcefile: any;
  fileupload: any;
  uploadfileSpin: boolean = false;
  selectFile: any;
  fileAdd: boolean = false;
  selectedItems = [];
  pageName: string = '';
  DeltaProcess: any;
  sorcedeltaform: any;
  DeltaProcessExtract: any;
  constructor(public fb: FormBuilder,
    private route: ActivatedRoute,
    public deployment: DeploymentService,
    private toast: HotToastService
  ) {
    this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    let userJson = localStorage.getItem('userData') as string;
    this.UserInfo = JSON.parse(userJson);
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
    this.sourceDelta = this.fb.group({
      runnumber: ['', [Validators.required]],
      sourceext: ['', [Validators.required]],
      sourcecon: ['', [Validators.required]],
      scheman: [''],
      date: [''],
      sourcefile: [''],
      objectt: [''],
    });
    this.sourceFileUpload = this.fb.group({
      fileName: ['', [Validators.required]],
    });
    this.sorcedeltaform = this.fb.group({
      scrun: ['', [Validators.required]],
      scschema: ['', [Validators.required]],
    });
    this.getInfraSelect();
    this.getRunNumber();
    this.getBlobfileData();
    this.getRunNumbers();
    this.GetRequestTableData();
    this.getPrjExeLogSelectTask("null");
  }
  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }
  get validate() {
    return this.sourceDelta.controls;
  }
  get validates() {
    return this.sorcedeltaform.controls;
  }
  Delta_Spin:Boolean=false;
  Deltacom_Spin:Boolean=false;
  DeltaCommandExtract(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.sourceext,
      srcCon: value.sourcecon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      extraction_Category: "Source_Current",
      task: "Source_Current",
      fileName: value.sourcefile,
    }
    this.Delta_Spin=true;
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      this.DeltaProcessExtract = data;
      this.Delta_Spin=false;
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data);
      }
      this.Delta_Spin=false;
    })
  }

  DeltaCommand(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.sourceext,
      srcCon: value.sourcecon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      date: this.deployment.formatDate(value.date),
      extraction_Category: "Source_Current",
      task: "Source_Current",
      fileName: "",
    }
    this.Deltacom_Spin=true;
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      this.DeltaProcess = data;
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data.message);
      }
      this.Deltacom_Spin=false;
    })
  }


  fileuploadvalue(value: any) {
    this.fileupload = value.fileupload
  }
SourceCo:any;
  GetDBConnections() {
    const projectId = this.projectId.toString();
    this.deployment.getConList(projectId).subscribe((data) => {
      this.prjSrcTgtData = data['Table1'];
      this.SourceCo = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "S" && item.migsrctype == "D";
      })
    })
  }
  runNumbers: any;
  getRunNumber() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbers = data['Table1'];
      for (const element of this.runNumbers) {
        if (element.iteration == "") {
          element.iteration = "New"
          break;
        }
      }
    });
  }
  runNumbersData: any;
  runNumbersDatas:any;
  getRunNumbers() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbersData = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
      this.runNumbersDatas = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }

  refresh()
  {
    this.GetRequestTableData();
    this.getRunNumbers();
  }
  conid: any;
  SourceConn: any;
  schemaList: any = []
  prjSchemaLists(conid: any) {
    this.SourceConn = conid;
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnection: conid.toString()
    }
    this.deployment.getschemaList(obj).subscribe((data) => {
      this.schemaList = data['Table1'];
    });

  }
  blobfile: any;
  getBlobfileData() {
    // this.project.GetBlobfileByFileName("source_delta.mp4").subscribe((data: any) => {
    //   this.blobfile = data.fileUrl;
    // })
  }
  get getControl() {
    return this.sourceDelta.controls;
  }

  GetOutputVal($event: any) {
    this.pageName = $event
  }

  TgtCodeShow() {
    this.TgtUpdateTgt = true;
  }
  TgtCodeShows() {
    this.TgtUpdateTgt = false;
  }

  sortValue(value: any) {
    this.pi = value;
    if (value == 'all') {
      this.pi = this.uploadedData.length;
      this.p = 1;

    }
    else if (value == '20') {
      this.p = 1;
    }
    else if (value == '50') {
      this.p = 1;
    }
    else if (value == '100') {
      this.p = 1;
    }
  }
  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }
  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }
  fileName: string = '';
  fileResult: any;
  project_name: any
  projectId: any
  getRole: any;
  Spin: any

  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
      this.selectFile = file;
    this.fileName = event.target.files[0].name;
    let myReader: FileReader = new FileReader();

    myReader.onloadend = (e) => {
      this.fileResult = myReader.result;
    };
    myReader.readAsDataURL(file);
  }

  UpLoadFilesToSubFolder() {
    // let BinaryFile = {
    //   projectID: this.projectId.toString(),
    //   containerName: 'qmigratorfiles' + this.projectId,
    //   folderName: "Code",
    //   fileName: this.fileName,
    //   content: this.fileResult.split(',')[1],
    //   subFolder: "External_Source"
    // };
    // this.Spin = true;
    // this.project.UpLoadFilesToSubFolder(BinaryFile).subscribe(
    //   (data: any) => {
    //     this.Spin = false;
    //     if (data.message == "File uploaded Successfully") {
    //       this.alertService.success(data.message);
    //     }
    //   },
    //   (error) => {
    //     this.Spin = false;
    //     this.alertService.danger('Something went wrong!');
    //   }
    // );
  }
  onKey() {
    this.pageNumber = 1;
    this.page2=1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3=1;
  }
  spin_process: any;
  getFiles() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iteration + "/Target_Current_Zip"
    };
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.uploadedData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }
  LogsData: any;
  GetFilesExecutionLogsFromDirectory() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iterationForLogs + "/Execution_Logs/Deployment/Source_Current"
    };
    this.LogsData=[];
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.LogsData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }
  spin_delete: any;
  DeleteClientFile(fileName: any) {
    let obj = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "External_Source",
      fileName: fileName,
    }
    this.spin_delete = true
    // this.client.deleteClientFile(obj).subscribe(
    //   (data) => {
    //     this.spin_delete = false
    //     if (data.message === fileName + " Deleted") {
    //       this.alertService.success(data.message);
    //     }
    //     if (data.message === fileName + " DoesNot Exist") {
    //       this.alertService.danger(data.message);
    //     }
    //     if (data.message === fileName + " DoesNot Exist Or Already Deleted") {
    //       this.alertService.danger(data.message);
    //     }
    //   },
    //   (error) => {
    //     this.spin_delete = false
    //     this.alertService.danger('Something happened');
    //   }
    // );
  }
  isLoading: any = [];
  isload(value: any) {
    this.isLoading[value] = true
  }
  isProcessLoading: any = [];
  isProcessload(value: any) {
    this.isProcessLoading[value] = true
  }
  infraData: any
  filtered: any
  getInfraSelect() {
    let id = this.projectId;
    // this.project.InfraSelect(id).subscribe((data) => {
    //   this.infraData = data['jsonResponseData']['Table1'];
    //   this.filtered = this.infraData.filter((element: any) => {
    //     return element.active == 'True';
    //   });
    // });
  }

  iteration: any
  selectIteration(iter: any) {
    this.iteration = iter;
    this.getFiles();
  }

  // prjSchemaList(sourceConection: any) {
  //   this.SourceConn = sourceConection;
  //   let obj = {
  //     projectId: this.projectId.toString(),
  //     dbConnection: 'null',
  //     sourceConnection: sourceConection.toString()
  //   }
  //   // this.project.prjSchemaListSelect(obj).subscribe((data) => {
  //   //   this.schemaList = data['Table1'];
  //   // });
  // }
  dt: any
  dateMon: any
  ExecuteCommandOnVM(filename: any) {
    this.spin_process = true
    const month = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
    this.dt = (<HTMLInputElement>(document.getElementById("selectedDate"))).value
    let mon = this.dt.split("-")[1]
    if (mon < 10) {
      let mont = mon.slice(1);
      this.dateMon = month[mont - 1]
    }
    else {
      this.dateMon = month[mon - 1]
    }
    let convertedDate = this.dt.split("-")[2] + "-" + this.dateMon + "-" + this.dt.split("-")[0]
    let obj = {
      projectID: this.projectId.toString(),
      command: 'delta.sh',
      ObjectType: this.UserInfo.email,
      id: this.projectId.toString(),
      filename: filename,
      Connection: this.schemaName.toString(),
      Schema: this.SourceConn,//Connection 
      ConnectionName: this.iteration,//iteration or run no.
      DbName: convertedDate,
      Operation: this.checked,
      FileName: this.DataBaseType
    }
    // this.project.ExecuteCommandOnVM(obj).subscribe(
    //   (data: any) => {
    //     this.spin_process = false;
    //     if ((data.message == 'Command Executed Successfully')) {
    //       this.alertService.success(data.message);
    //       localStorage.setItem('SourceDeltaFile', filename);
    //     }
    //   },
    //   (error) => {
    //     this.spin_process = false;
    //     this.alertService.danger(
    //       'Something Went Wrong please try Again Later!'
    //     );
    //   }
    // );
  }
  selectExtraction: any;
  selectedDropdown(data: any) {
    if (data == "Database") {
      this.hide_files = false;
      this.hide_db = true;
      this.isGit = false
      this.isRefernce = true
      this.sourceDelta.controls['runnumber'].setValidators([Validators.required]);
      this.sourceDelta.controls['sourceext'].setValidators([Validators.required]);
      this.sourceDelta.controls['sourcecon'].setValidators([Validators.required]);
      this.sourceDelta.controls['scheman'].setValidators([Validators.required]);
      this.sourceDelta.controls['date'].setValidators([Validators.required]);
      this.sourceDelta.controls['objectt'].setValidators([Validators.required]);
      this.sourceDelta.controls['sourcefile'].clearValidators();
      this.GetDBConnections();
    } else if (data == "Fileshare") {
      this.hide_files = true;
      this.hide_db = false;
      this.isGit = true;
      this.isRefernce = true;
      this.sourceDelta.controls['runnumber'].setValidators([Validators.required]);
      this.sourceDelta.controls['sourceext'].setValidators([Validators.required]);
      this.sourceDelta.controls['sourcecon'].setValidators([Validators.required]);
      this.sourceDelta.controls['scheman'].clearValidators();
      this.sourceDelta.controls['date'].clearValidators();
      this.sourceDelta.controls['objectt'].clearValidators();
      this.sourceDelta.controls['sourcefile'].setValidators([Validators.required]);
      this.GetDBConnections();
    } else {
      this.hide_db = false;
      this.hide_files = false;
      this.selectExtraction = data;
      this.sourceDelta.controls['runnumber'].updateValueAndValidity();
      this.sourceDelta.controls['sourceext'].updateValueAndValidity();
      this.sourceDelta.controls['sourcecon'].updateValueAndValidity();
      this.sourceDelta.controls['scheman'].updateValueAndValidity();
      this.sourceDelta.controls['date'].updateValueAndValidity();
      this.sourceDelta.controls['sourcefile'].updateValueAndValidity();
      this.sourceDelta.controls['objectt'].updateValueAndValidity();
    }
  }


  checkboxselect($event: any): void {
    this.selDis = false;
    this.checked = "FALSE";
    this.selDate = (<HTMLInputElement>document.getElementById('selectedDate')).value;
    if ($event.target.checked) {
      this.checked = "TRUE";
      if (this.iteration == "New") {
        confirm("Please Select Existing Run Number")
        //this.iteration = this.runNumbers[1].iteration
        //this.runNumbers[1].checked = true
      }
      this.selDate = ''
      this.selDis = true;
    } else {
      this.selDis = false;
      this.checked = "FALSE";
    }
  }
  run_no: any = ""
  spin_upload: boolean = false
  runNumberIncrease() {
    this.spin_upload = true
    if (this.iteration == "New") {
      let obj = {
        projectId: this.projectId.toString(),
        migType: "DB_Extraction",
        migSrcType: "F",
        fileName: this.fileName,
        dbconname: "null",
        operation: "null",
        schema: "ALL",
        objectType: "null",
        objectPattern: "null",
        acl: ""
      }
      // this.project.RunInfoInsert(obj).subscribe((data: any) => {
      //   var resp = data['jsonResponseData']['Table1']
      //   this.run_no = resp[0].run_no;
      //   if (this.run_no != "") {
      //     this.uploadFiles()
      //   }
      //   else {
      //     this.spin_upload = false
      //     this.alertService.danger("Failed to create Run Number");
      //   }
      // })
    }
    else {
      this.run_no = this.iteration
      // this.uploadFiles()
    }
  }
  uploadResponse: any
  uploadFile() {
    this.uploadfileSpin = true
    const sourceFileUpload: FormData = new FormData();
    sourceFileUpload.append('file', this.selectFile, this.selectFile.name);
    sourceFileUpload.append('path', "PRJ1167SRC/Delta_process/"+ this.iteration + "/Target_Current_Zip/");
    this.deployment.UploadCloudFiles(sourceFileUpload).subscribe(
      (response: any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.getFiles();
        this.toast.success(response.message)
      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.toast.error('Something went wrong')
      }
    )
  }


  // select schema data
  selectschema(data: any) {
    this.schemaName = data
    //console.log(data)
  }

  onItemSelect(item: any) {
    this.schemaName.push(item.schemaname);
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.schemaname);
    this.schemaName.splice(inde, 1);
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.schemaname);
    });
  }
  onDeSelectAll(items: any) {
    this.schemaName = [];
  }

  ExeLog: any = {};
  prjLogData: any;
  selectedrun: any;
  NoDataHide: boolean = false;
  getPrjExeLogSelectTask(value: any) {
    this.selectedrun = value;
    if (value == "") {
      value = "null";
    }
    this.prjLogData = [];
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operationType = "Source_Current";
    this.ExeLog.action = "null";
    this.ExeLog.row_id = "null";
    this.ExeLog.runno = value;
    this.ExeLog.operationName = "null";
    this.deployment.PrjExelogSelectTask(this.ExeLog).subscribe((data: any) => {
      this.prjLogData = data['Table1'];
      this.NoDataHide = true;
    })
  }
  ref_spin: boolean = false
  RequestTableData: any;
  GetRequestTableData() {
    let obj = {
      projectId: this.projectId.toString(),
      operationType: 'Source_Current'
    }
    this.ref_spin = true;
    this.deployment.GetRequestTableData(obj).subscribe((data: any) => {
      this.RequestTableData = data['Table1'];
      this.ref_spin = false;
    });
  }

  deleteResponse: any;
  deleteResponses:any;
  // delete request table data
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.deployment.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      if (data['Table1'][0].v_status) {
        this.toast.success(data['Table1'][0].v_status);
      }
      else {
        this.toast.error(data);
      }
	  this.GetRequestTableData();
    })
   
  }
     
  iterationForLogs: any;
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value;
    this.GetFilesExecutionLogsFromDirectory();
  }
  fileResponse: any;
  spin_dwld: any;
  //download files
  downloadFile(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }

  downloadFiless(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  GetSourceFilePathData:any;
  GetSourceFilePath()
  {
    const obj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.run + "/Source/Database_Current/"+this.deltaschema.toString()+"/Procedure/"
    }
    this.GetSourceFilePathData=[];
    this.deployment.getFiles(obj).subscribe((data: any) => {
      this.GetSourceFilePathData = data;
    });
  }
  downloadFiles(path:string,filename:string) {
    var pth=path.split("/mnt/pypod/")[1]
    pth="/mnt/eng/"+pth
    this.deployment.downloadLargeFiles(pth).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = filename;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
   // sc schemalist
   schemaNamess:any;
   GetSCSchemaList(runno: any) {
     this.schemaNamess = []
     let obj = {
       projectId: this.projectId,
       runno: runno
     }
     this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
       this.schemaNamess = data['Table1'];
     });
   }
   deltaschema:any;
  sourceSelected: boolean = false
  sourceSel(value: string) {
    value != '' ? this.sourceSelected = true : this.sourceSelected = false;
    this.deltaschema=value;
    this.GetSourceFilePath();
  }
  run:any;
  selectRunnumber(itera: any) {
    this.run = itera;
    this.GetSCSchemaList(itera);
  }
}
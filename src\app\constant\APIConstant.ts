export const APIConstant = {
    login: {
        getLocalStorage: 'Client/getLocalStorage?userId=',
        checkLogin: 'Security/Login/',
        kubeLogin: "Security/Verify",
        checkQubeFile: 'Security/fileExist',
        verifywithEmail: 'Security/VerifywithEmail'
    },
    header: {
        getProjectMenu: 'Security/GetSecUserProjectMenu?projectId='
    },
    common: {
        GetRunno: 'GetRunNoForstmts?projectId=',
        GetSchemasByRunId: 'GetSchemasByRunId?runid=',
        deleteFile: 'DeleteFiles?path=',
        DataCompareFiles: 'fetchDataCompareReports?path=',
        GetTablesByschema: 'GetTablesByschema?schemaname=',
        insertTablesCommand: 'AirFlowCommand',
        GetFilesFromExpath: 'GetFilesFromDirectoryEx?path=',
        GetTablesByschemaGG: 'GetTablesByschemaGG?schemaname=',
        GetGGShellDirectoryEx: 'GetGGShellDirectoryEx?path=',
        UploadProjectDocs: 'UploadExtraFiles',
        getGoldenGatePod: 'getGGPod',
        executeInPodFile:'ExecuteInPodFile',
        getCdcData:'GetCDCData',
        TriggerCDCCommand:'Redis/TriggerCDCCommand',
        DataMigrationCommand:'DataMigrationCommand',
        DataMigrationNewCommand:'DataMigrationNewCommand',
        GetMariaTables:'Maria2MysqlAssessment/maria2mysql/GetMySqlTables?conid=',
        GetPostgresTablesBySchema:'GetPostgresTablesBySchema?conid=',
        getAgent:'dba-agent',
        getPerAgent:'performance-agent',
        clearChatHistory:'clear-history',
        getConnections: 'master/GetConnectionList?projectId=',
        getZipFiles:'Common/Master/DownloadZipFiles?path='
    }
}
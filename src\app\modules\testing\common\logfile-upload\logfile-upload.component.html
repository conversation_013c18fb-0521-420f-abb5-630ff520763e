<div class="v-pageName">{{pageName}}</div>

<!-- File Upload -->
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    File Upload
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="uploadForm" (ngSubmit)="uploadFile()">
                        <div class="form-group">
                            <label for="formFile" class="form-label d-required">Upload File</label>
                            <div class="custom-file-upload">
                                <input class="form-control" type="file" id="formFile" formControlName="file"
                                    (change)="onFileSelected($event)" />
                                <div class="file-upload-mask">
                                    @if (fileName == '') {
                                    <img src="assets/images/fileUpload.png" alt="img" />
                                    <p>Drag and drop deployment file here or click add deployment file</p>
                                    <button class="btn btn-upload">Add File</button>
                                    }
                                    <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                        <p>{{ fileName }}</p>
                                    </div>
                                </div>

                            </div>
                        </div>
                        @if ( f.file.touched && f.file.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.file.errors.required) { File is required }
                        </p>
                        }
                        <div class="row">
                            <div class="col-md-9"></div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <button class="btn btn-upload w-100" [disabled]="uploadForm.invalid"><span class="mdi mdi-file-plus"></span>Upload
                                        File
                                        @if(uploadfileSpin){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>

        <!-- Document search -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Execution
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <!-- <div class="accordion-item"> -->
          <div class="qmig-card">                   
                        <div class="qmig-card-body">
                            <div class="row">
                                <div class="col-md-6 col-xl-6">
                                    <div class="row">
                                        <div class="col-md-4 col-lg-4 col-xl-4">
                                            <div class="form-group">
                                                <label for="" class="form-label">Execution Time limit</label>
                                                <input type="text" class="form-control form-small" value="{{timelimit}}"
                                                    id="timeLimit" placeholder="Execution Time limit">
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-lg-4 col-xl-4">
                                            <div class="form-group">
                                                <label for="" class="form-label">Module Name</label>
                                                <input type="text" class="form-control form-small"
                                                    value="{{moduleName}}" id="moduleName" placeholder="Module Name">
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-lg-4 col-xl-4">
                                            <div class="form-group">
                                                <label for="" class="form-label">Batch ID</label>
                                                <input type="text" class="form-control form-small" value="{{batchId}}"
                                                    id="batchId" placeholder="Batch ID">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="custom_search">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Document" class="form-control"
                                            [(ngModel)]="searchText" (keyup)="onKey()">
                                    </div>
                                </div>
                                @if(processBtn){
                                    <div class="col-md-12">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <label class="form-label d-required" for="name">Value</label>
                                                <select class="form-select ml-0" (change)="proSortValue(pcheckValue.value)"
                                                    #checkValue>
                                                    <option selected value="">Choose Value</option>
                                                    <option value="S">Source</option>
                                                    <option value="T">Target</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mt-3 pt-2">
                                                    <div class="form-check form-check-flat form-check-primary mt-m10">
                                                        <label class="form-check-label">
                                                            <input type="checkbox" value="UNIQUE" multiple
                                                                (click)="uniqueCheked(uniqueValue.value, $event)" #uniqueValue
                                                                class="form-check-input">
                                                            <i class="input-helper"></i> Unique
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mt-1">
                                                    <button class="btn btn-upload w-100 mt-4" type="button">
                                                        Process @if(spinner){<app-spinner />} </button>
                                                </div>
                                            </div>
        
                                        </div>
                                    </div>
                                    }
                                </div>
                            </div>
        
                            </div>
                        </div>

                    <!-- Files List -->
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>

                                </tr>
                            </thead>
                            <tbody>
                                @for (file of ExecututionFiles | searchFilter: searchText | paginate: {
                                itemsPerPage: 10,
                                currentPage:
                                pageNumber1 } ; track file; let i = $index)
                                {
                                <tr>

                                    <td>
                                        <div class="form-check m-0">
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    (click)="currentlyCheked(checkValue.value, $event)" #checkValue> <i
                                                    class="input-helper"></i>
                                            </label>
                                        </div>
                                    </td>
				                   <td>{{i+1}}</td>
                                    <td>{{file.fileName}}</td>
                                    <td>Project Docs</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(file)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                        <button class="btn btn-delete" (click)="DeleteClientFile(file.filePath)">
                                            <span class="mdi mdi-trash-can-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pageNumber1 = $event"></pagination-controls>
                        </div>
                    </div>
                </div>

            </div>
        </div>


        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Log File Reports
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
            <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-md-6 col-xl-6">

                            </div>
                            <div class="col-md-6 col-xl-6">
                                <div class="custom_search cs-r">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Document" class="form-control" (keyup)="onKey()">
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <!-- Files List -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>File Name</th>
                                <th>Date and Time</th>
                                <th>Action</th>

                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Workload_copy_log_2023-11-13-15-11-33.727971.xlsx </td>
                                <td>2023-11-13 </td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(file)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Workload_copy_log_2023-11-13-10-56-44.293943.xlsx </td>
                                <td>2023-11-13 </td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(file)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>Workload_copy_log_2023-11.xlsx </td>
                                <td>2023-11-13 </td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(file)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="custom_pagination">
                    <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                </div>
            </div>
        </div>
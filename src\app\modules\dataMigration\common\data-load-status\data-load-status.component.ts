import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GetConfigData, GetFilesFromExpath, Table, airflowValidationCommand, conList, configFiles, dataLoadStatus, dataStatus, deleteFile, documentsList, getSchemas, projectConRunTblInsert, redisCommand, setRedisCache } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-data-load-status',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './data-load-status.component.html',
  styles: ``
})
export class DataLoadStatusComponent {

  /*--Upload Dag File Form --*/
  execProjectForm = this.formBuilder.group({
    sourceConnection: ['', [Validators.required]],
    targetConnection: [''],
    schema: ['', [Validators.required]],
  });

  /*--Global Variable --*/
  projectId: string;

  /*--Pagination  --*/
  pageNumber: number = 1;

  /*--Source Config --*/
  auditlist: Table[] = [];
  audConId: string = '';

  /*--Schema  --*/
  schemaList: any;
  selectedConfigId: string = ''
  configFiles: any;
  selected_schema: string = ''

  /*-- Table Status --*/
  tableStatus: any;

  /*-Source Config--*/
  configData: any;
  pageName:string = ''
  searchText: string = ''

  constructor(private titleService: Title,private route: ActivatedRoute,private toast: HotToastService, private datamigrationservice: DataMigrationService, public formBuilder: FormBuilder) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getConsList();
    this.fetchConfigFiles();
  }

  /*--Validation  --*/
  get f() {
    return this.execProjectForm.controls;
  }

  onKey() {
   this.pageNumber =1
  }

  /*-- get Source connection--*/
  getConsList() {
    this.datamigrationservice.getConList(this.projectId.toString()).subscribe((data: conList) => {
      this.auditlist = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'A' && item.conname != '';
      });
      if (this.auditlist.length > 0) {
        this.audConId = this.auditlist[0].Connection_ID
      }
      //console.log(this.auditlist)
    });
  }
  /*-- get schema --*/
  getSchema(Data: string) {
    this.tableStatus = []
    var confData = this.configFiles.filter((itemz: any) => {
      return itemz.filePath == Data
    })
    this.selectedConfigId = confData[0].config_id
    this.execProjectForm.controls.schema.setValidators([Validators.required])
    this.execProjectForm.controls.schema.updateValueAndValidity()
    this.getSchemaList(Data)
  }

  getSchemaList(path: string) {
    this.datamigrationservice.getSchemas(path).subscribe((data: getSchemas) => {
      this.schemaList = data;
      //console.log(this.schemaList)
    });
  }
  selectSchema(value: string) {
    this.selected_schema = value
    this.dataLoadStatus()
  }

  /*-- Fetch Files --*/
  fetchConfigFiles() {
    const path = "AIRFLOW_FILES/Data_Migration_Reports/"
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: GetFilesFromExpath) => {
      this.configFiles = data
      this.configFiles.filter((itemz: any) => {
        itemz.config_id = ''
        //console.log(this.configFiles)
      })
      this.schemaList = []
      this.getConfigData()
    })
  }

  /*-- Table Data Files --*/

  dataLoadStatus() {
    this.tableStatus = []
    let obj: dataLoadStatus = {
      schema: this.selected_schema,
      configId: this.selectedConfigId,
      audConId: this.audConId
    }
    this.datamigrationservice.dataLoadStatus(obj).subscribe((data: dataStatus) => {
      this.tableStatus = data['Table1']
      //console.log(this.tableStatus)
    })
  }

  getConfigData() {
    this.datamigrationservice.GetConfigData().subscribe((data: GetConfigData) => {
      this.configFiles = data['Table1']
      // this.configData.filter((item: any) => {
      //   for (let k = 0; k < this.configFiles.length; k++) {
      //     var filename = item.config_file_name
      //     if (filename == this.configFiles[k].fileName.split(".")[0]) {
      //       this.configFiles[k].config_id = item.config_id
      //       break;
      //     }
      //   }
      // })
      // //console.log(this.configFiles)
      // this.configFiles = this.configFiles.filter((itemk: any) => {
      //   if (itemk.config_id != "") {
      //     return itemk
      //   }
      // })
      //console.log(this.configFiles)
    })

  }

  dagExecustionList:any;
  dagExecustionListCopy:any;
  dagTables:any = []
  getDagExecutionlog(data:string){
    const dagObj ={
      configID:data,
      tablename:'null'
    }    
    this.datamigrationservice.getDagsExecutionLog(dagObj).subscribe((data: GetConfigData) => {
      this.dagExecustionList = data['Table1']
      this.dagExecustionListCopy = data['Table1']
      if(data['Table1']){
        this.dagTables = [...new Set(this.dagExecustionList.map((table:any) => table.table_name))]
      }
    })
  }

  filterDags(value:string){
    this.dagExecustionList =this.dagExecustionListCopy.filter( (vl:any) => vl.table_name == value )
  }

}

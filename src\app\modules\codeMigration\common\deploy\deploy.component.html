<!-- Deplyment -->
<div class="v-pageName">{{pageName}}</div>

<div class="body-main">
    <div class="qmig-card">
        <div class="accordion accordion-flush  qmig-accordion" id="accordionPanelsStayOpenExample">

            <!-- Uploading Deployment File -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-heading">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                        Deployment
                    </button>
                </h2>
                <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <div class="row">
                            <form class="form add_form" [formGroup]="deployForm">
                                <div class="form-group">
                                    <label for="formFile" class="form-label d-required">Upload Deployment File </label>
                                    <div class="custom-file-upload">
                                        <input class="form-control" type="file" id="formFile"
                                            (change)="onFileSelected1($event)">
                                        <div class="file-upload-mask">
                                            @if (fileName == '') {
                                            <img src="assets/images/fileUpload.png" alt="img" />
                                            <p>Drag and drop deployment file here or click add deployment file </p>
                                            <button class="btn btn-upload"> Add File </button>
                                            } @else{
                                            <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                                <p> {{ fileName }} </p>
                                            </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-xl-3 offset-md-9">
                                        <div class="form-group">
                                            <button class="btn btn-upload w-100" (click)="uploadFile()"> <span
                                                    class="mdi mdi-file-plus"></span>
                                                Upload@if(upload_spin){<app-spinner />}</button>
                                        </div>
                                    </div>
                                </div>
                                <hr class="mt-0" />
                                <div class="row">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="Schema">Deploy Operation</label>
                                            <select class="form-control form-select" aria-label="Default select example"
                                                formControlName="deployOp" #deployy
                                                (change)="getDeploySequence(deployy.value);">
                                                <!-- selectPath(filepath.value) -->
                                                <option selected>Select a Operation</option>
                                                @for( dep of depOperations;track dep;){
                                                <option value="{{dep.deployoperation}}">{{ dep.deployoperation }}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="Schema">Object Type Folder
                                            </label>
                                            <select class="form-control form-select" aria-label="Default select example"
                                                formControlName="fname" #filepath
                                                (change)="fetchTableFiles(filepath.value);">
                                                <!-- selectPath(filepath.value) -->
                                                <option selected>Select Object Type Folder</option>
                                                @for( depseq of depSequence;track depseq;){
                                                <option value="{{depseq.objecttype}}">{{depseq.deployobjecttype}}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="Schema">File Name </label>
                                            <select class="form-control form-select" aria-label="Default select example"
                                                formControlName="formFileName" #filepath1
                                                (change)="selectPath(filepath1.value);">
                                                <option selected value="">Select File Name</option>
                                                @for( fileList of tableFiles ;track fileList;){
                                                <option value="{{fileList.filePath}}">{{fileList.fileName}}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Source {{schemalable}} Name</label>
                                            <input class="form-control" type="text" id="srcSchema" value="{{substring}}"
                                                disabled />
                                        </div>
                                    </div>

                                    <!-- @if(migtypeid!="30")
                                    {
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Source Schema Name</label>
                                            <input class="form-control" type="text" id="srcSchema" />
                                        </div>
                                    </div>
                                    } -->
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="Schema">Target Connection</label>
                                            <select class="form-control form-select" aria-label="Default select example"
                                                formControlName="targetConnection" #tgt
                                                (change)="selectTarget(tgt.value);getSchemasList(tgt.value,0)">
                                                <option selected>Select Target </option>
                                                @for(role of targetList;track role; ){
                                                <option value="{{ role.Connection_ID}}"> {{ role.conname }} </option>
                                                }
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label for="name" class="form-label">Target {{schemalable}} Name</label>
                                            <!-- <input class="form-control" type="text" id="tgtSchema" /> -->
                                            <select class="form-control form-select" aria-label="Default select example"
                                                #tgtsch (change)="selectTargetschema(tgtsch.value,0)">
                                                <option selected>Select Target {{schemalable}}</option>
                                                @for(tgtlist of targetSchemaList;track tgtlist; ){
                                                <option value="{{ tgtlist.schema_name}}"> {{ tgtlist.schema_name }}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-xl-3 mt-4">
                                        <div class="body-header-button">
                                            <button class="btn btn-upload w-100 " (click)="ConversionCommand(0)">
                                                <span class="mdi mdi-cog-play-outline btn-icon-prepend"></span>
                                                Deploy </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create Deployment File -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingTest">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                        Create Deployment File with Conversion
                    </button>
                </h2>
                <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <form class="form manual_form mt-3" [formGroup]="createFileForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Run
                                            Number</label>
                                        <select class="form-control form-select" #runinfo
                                            (change)="getSchema(runinfo.value,'')" formControlName="iteration">
                                            <option selected value="">Select Run Number</option>
                                            @for(list of runNumbersList; track list;){
                                            <option value="{{list.iteration_id}}">
                                                {{list.iteration_id}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( fs.iteration.touched && fs.iteration.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fs.iteration.errors?.['required']) { Run Number is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">{{schemalable}}</label>
                                        <select class="form-control form-select" (change)="getObjectType(sch.value)"
                                            #sch formControlName="schema">
                                            <option selected>Select {{schemalable}}</option>
                                            @for( schema of schemaList;track schema;){
                                            <!-- @if(migtypeid=="30"){
                                             <option value="{{schema.tgt_schema_id}}"> {{schema.tgt_schemaname }}
                                            </option> 
                                            <option value="{{schema.schema_id}}"> {{schema.schemaname }} </option>
                                            
                                        } -->
                                            <option value="{{schema.schema_id}}"> {{schema.schema_name }} </option>

                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( fs.schema.touched && fs.schema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fs.schema.errors?.['required']) { {{schemalable}} is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Object type</label>
                                        <ng-select [placeholder]="'Select object type'" [items]="objectTypeList"
                                            formControlName="objectType" [multiple]="true" bindLabel="object_type"
                                            [closeOnSelect]="false" bindValue="object_type"
                                            [(ngModel)]="selectedObjItems">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.object_type}}
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if ( fs.objectType.touched && fs.objectType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fs.objectType.errors?.['required']) { Object Type is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-3">
                                    <div class="form-group">
                                        <div class="form-check form-check-flat form-check-primary mt-4">
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    (change)="singleFilevalue($event)">
                                                <i class="input-helper"></i> Single File
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-3 offset-xl-9">
                                    <div class="body-header-button">
                                        <button type="button" class="btn btn-upload w-100 "
                                            [disabled]="createFileForm.invalid" (click)="ConversionCommand(1)">
                                            <span class="mdi mdi-file-cog-outline btn-icon-prepend"></span>
                                            Create File@if(file_iter_spin){<app-spinner />}</button>
                                    </div>
                                </div>

                            </div>
                        </form>
                    </div>
                </div>
            </div>


            <!-- Create Deployment File with Target Connection -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                        Create Deployment File with Target Connection
                    </button>
                </h2>
                <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <form class="form manual_form mt-3" [formGroup]="createForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Target
                                            connection</label>
                                        <select class="form-control form-select" #tgtSchema
                                            (change)="getTgtSchema(tgtSchema.value)" formControlName="targetConnection">
                                            <option selected value="">Select Target Connection </option>
                                            @for(role of targetList;track role; ){
                                            <option value="{{ role.Connection_ID}}"> {{ role.conname }} </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( ffs.targetConnection.touched && ffs.targetConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (ffs.targetConnection.errors?.['required']) { Target Connection is
                                                required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">{{schemalable}}</label>
                                        <select class="form-control form-select" formControlName="schema"
                                            (change)="getTgtObject(tgtSchema1.value)" #tgtSchema1>
                                            <option selected value="">Select {{schemalable}}</option>
                                            @for(sch of tgtSchemaList;track sch; ){
                                            <option value="{{ sch.schemaname}}"> {{ sch.schemaname }} </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( ffs.schema.touched && ffs.schema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (ffs.schema.errors?.['required']) { {{schemalable}} is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Object type</label>
                                        <ng-select [placeholder]="'Select object type'" formControlName="objectType"
                                            [items]="objectType" [multiple]="true" bindLabel="object_type"
                                            [closeOnSelect]="false" bindValue="object_type"
                                            [(ngModel)]="selectedObjItem">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.object_type}}
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if ( ffs.objectType.touched && ffs.objectType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (ffs.objectType.errors?.['required']) { Object Type is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-3">
                                    <div class="form-group">
                                        <div class="form-check form-check-flat form-check-primary mt-4">
                                            <label class="form-check-label">
                                                <input type="checkbox" class="form-check-input"
                                                    (change)="singleTgtFilevalue($event)">
                                                <i class="input-helper"></i> Single File
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-3 offset-xl-9">
                                    <div class="body-header-button">
                                        <button type="button" class="btn btn-upload w-100 "
                                            [disabled]="createForm.invalid" (click)="ConversionCommand(2)">
                                            <span class="mdi mdi-file-cog-outline btn-icon-prepend"></span>
                                            Create File@if(file_db_spin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Single Object Deployment -->
            <div class="accordion-item">
                <h3 class="accordion-header" id="flush-headingTwo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                        Single Object Deployment
                    </button>
                </h3>
                <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <form class="form add_form mt-3" [formGroup]="deployFormSingle">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Run
                                            Number</label>
                                        <select class="form-control form-select" #run
                                            (change)="getSchema(run.value,'single')" formControlName="iteration">
                                            <option selected value="">Select Run Number</option>
                                            @for(list of runNumbersList; track list;){
                                            <option value="{{list.iteration_id}}">
                                                {{list.iteration_id}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( fsd.iteration.touched && fsd.iteration.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fsd.iteration.errors?.['required']) { Iteration required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">{{schemalable}}</label>
                                        <select class="form-control form-select"
                                            (change)="getObjectTypeForDeploy(schema1.value)" #schema1
                                            formControlName="tgtschema">
                                            <option selected value="">Select {{schemalable}}</option>
                                            @for( schema of schemaSingletgt;track schema;){
                                            <option value="{{schema.schema_id}}"> {{schema.schema_name }} </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( fsd.tgtschema.touched && fsd.tgtschema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fsd.tgtschema.errors?.['required']) { Target {{schemalable}}
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Object Types</label>
                                        <select class="form-control form-select" aria-label="Default select example"
                                            formControlName="objTye" #objtype
                                            (change)="fetchObjectNames(objtype.value);">
                                            <option selected value="">Select Object Types</option>
                                            @for( depseq of objectTypeListForDeploy; track depseq;){
                                            <option value="{{depseq.object_type}}">
                                                {{depseq.object_type}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( fsd.objTye.touched && fsd.objTye.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fsd.objTye.errors?.['required']) { Object Types required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Object Names</label>
                                        <ng-select [placeholder]="'Select object type'" [items]="objectNames"
                                            [multiple]="true" bindLabel="object_name" formControlName="objnames"
                                            [closeOnSelect]="false" bindValue="object_name"
                                            [(ngModel)]="selectedObjItems1">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.object_name}}
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if ( fsd.objnames.touched && fsd.objnames.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fsd.objnames.errors?.['required']) { Object Names required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Target
                                            connection</label>
                                        <select class="form-control form-select" formControlName="targetConnection"
                                            #sigtgt (click)=" getSchemasList(sigtgt.value,1)">
                                            <option selected value="">Select Target </option>
                                            @for(role of targetList;track role; ){
                                            <option value="{{ role.Connection_ID}}"> {{ role.conname }} </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( fsd.targetConnection.touched && fsd.targetConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fsd.targetConnection.errors?.['required']) { Target Connection is
                                                required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label">Target {{schemalable}} Name</label>
                                        <select class="form-control form-select" aria-label="Default select example"
                                            #tgtsig (change)="selectTargetschema(tgtsig.value,1)">
                                            <option selected value>Select Target {{schemalable}} </option>
                                            @for(tgtsingle of singletargetSchemaList;track tgtsingle; ){
                                            <option value="{{ tgtsingle.schema_name}}"> {{ tgtsingle.schema_name }}
                                            </option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-3 offset-xl-6">
                                    <div class="body-header-button">
                                        <button type="button" class="btn btn-upload w-100 "
                                            [disabled]="deployFormSingle.invalid" (click)="ConversionCommand(3)">
                                            <span class="mdi mdi-cog-play-outline btn-icon-prepend"></span>
                                            Deploy @if(spin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="accordion-item">
                <h3 class="accordion-header" id="flush-headingFive">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseFive" aria-expanded="false"
                        aria-controls="flush-collapseFive">Deployment Logs
                    </button>
                </h3>
                <div id="flush-collapseFive" class="accordion-collapse collapse" aria-labelledby="flush-headingFive"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <div class="row">
                            <!-- <div class="col-6 col-sm-6 col-md-3 col-lg-3 col-xl-2">
                                <div class="form-group">
                                    <select class="form-control form-select" #iter1
                                        (change)="selectIterForLogs(iter1.value)">
                                        <option selected>Select Run Number</option>
                                        @for(list of runnoForReports; track list;){
                                        <option value="{{list.run_id}}">
                                            {{list.run_id}}</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-6 col-sm-6 col-md-3 col-lg-3 col-xl-2">
                                <div class="form-group">
                                    <select class="form-control ml-2 form-select" #obj1
                                        (change)="selectObjType(obj1.value)" aria-label="Default select example">
                                        <option selected value="">Select Object Type</option>
                                        @for(list of objectType;track list;){
                                        <option value="{{list.objecttype_name}}">
                                            {{list.objecttype_name}}</option>
                                        }
                                    </select>
                                </div>
                            </div> -->

                            <div class="col-12 col-sm-6 col-md-7 d-flex offset-5">
                                <div class="custom_search cs-r me-3 my-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Deployment Logs" class="form-control"
                                        (keyup)="onKey()" [(ngModel)]="datachangeLogs">
                                </div>
                                <button class="btn btn-sync" (click)="fetchDeployLogs()">
                                    @if(getDeploySpin){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>

                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for ( logs of depLogs | searchFilter: datachangeLogs | paginate: {
                                itemsPerPage: 10, currentPage: p } ; track logs; let i
                                = $index) { <tr>
                                    <td>{{i + 1}}</td>
                                    <td>{{logs.fileName }}</td>
                                    <td>Project Docs</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(logs)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                        <!-- <button class="btn btn-delete" (click)="downloadFile(logs.fileName)">
                                                        <span class="mdi mdi-delete"></span>
                                                    </button> -->
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p  = $event"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
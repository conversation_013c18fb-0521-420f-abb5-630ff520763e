import { Component } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-airflow',
  standalone: true,
  imports: [],
  templateUrl: './airflow.component.html',
  styles: ``
})
export class AirflowComponent {
  safeSrc: any
  pageName:string = ''
  constructor(private titleService: Title,private sanitizer: DomSanitizer, private route: ActivatedRoute) {
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getAirFlowUrl()
  }

  getAirFlowUrl() {
    this.safeSrc = this.sanitizer.bypassSecurityTrustResourceUrl((window.location.origin + '/airflow'))   
    // this.safeSrc = this.sanitizer.bypassSecurityTrustResourceUrl(('http://20.219.214.112/airflow'))
  }
}

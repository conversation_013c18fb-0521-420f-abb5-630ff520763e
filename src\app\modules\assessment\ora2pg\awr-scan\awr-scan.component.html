<div class="v-pageName">{{pageName}}</div>


<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Upload AWR Files
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <form class="form add_form mt-3" [formGroup]="uploadForm" (ngSubmit)="uploadFile()">
                                <div class="form-group">
                                    <div class="custom-file-upload">
                                        <input class="form-control" type="file" id="formFile"
                                            (change)="onFileSelected($event)" formControlName="file">
                                        <div class="file-upload-mask">
                                            @if (fileName == '') {
                                            <img src="assets/images/fileUpload.png" alt="img" />
                                            <p>Drag and drop deployment file here or click add deployment file </p>
                                            <button class="btn btn-upload"> Add File </button>
                                            } @else{
                                            <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                                <p> {{ fileName }} </p>
                                            </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-xl-3 offset-md-9">
                                        <button class="btn btn-upload w-100" [disabled]="uploadForm.invalid"> <span
                                                class="mdi mdi-file-plus"></span>
                                            Upload@if(upload_spin){<app-spinner />}
                                        </button>
                                    </div>


                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!---Flies----->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Uploaded Files
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">                        
                        <div class="col-4 col-sm-4 col-md-3 col-xl-3">
                            <h3 class="main_h">Uploaded Files 
                                <button class="btn btn-sync" (click)="filterExecutionReports()">
                                    @if(getRunSpin){
                                    <app-spinner />
                                    }@else{
                                        <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </h3>
                        </div>
                        <div class="col-md-6 col-xl-6">
                            <div class="custom_search">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Flie Name" class="form-control"  [(ngModel)]="datachange1" (keyup)="onKey()">
                               
                            </div>
                        </div>
                        <div class="col-md-2 col-xl-2 offset-md-1">
                            <button type="button" class="btn btn-upload w-100" [disabled]="!isCheckBoxSel" (click)="AssessmentCommand()"> <span
                                class="mdi mdi-cached" ></span>
                            Execute
                                </button>
                        </div>
                    </div>
                </div>

                    <div class="table-responsive">
                        <table class="table table-hover qmig-table" id="example" style="width: 100%">
                            <thead>
                                <tr>
                                    <th>
                                        #
                                    </th>
                                    <th>File Name</th>
                                    <th>Created Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of ExecutionFiles| searchFilter: datachange1| paginate:{ itemsPerPage: piA,
                                currentPage:
                                p1,
                                id:'second' };
                                track con;) {
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="inlineCheckbox1"
                                                value="{{con.fileName}}" #chck (click)="checkboxselect($event, chck.value)">
                                        </div>
                                    </td>
                                    <td>{{con.fileName}}</td>
                                    <td>{{con.created_dt}}</td>                                    
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(con)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                        <button (click)="deleteFiles(con.filePath)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                    
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>

                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p1 = $event" id="second"></pagination-controls>
                    </div>
            </div>
        </div>
        <!---Report----->
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    AWR Report
                    
                </button>
            </h3>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-md-6 col-xl-6">                            
                            <h3 class="main_h pt-3 ps-3"> AWR Reports List
                            <button class="btn btn-sync"(click)="filterExecutionReports1()">
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                    <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                            </h3>
                        </div>
                        <div class="col-md-6 col-xl-6">
                            <div class="custom_search cs-r mb-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Awr Reports" class="form-control">                               
                            </div>
                        </div>
                    </div>
                </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table" id="example" style="width: 100%">
                            <thead>
                                <tr>
                                    <th>S.NO</th>
                                    <th>File Name</th>
                                    <th>Created Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of AwrReports| searchFilter: datachange1| paginate:{ itemsPerPage: piB,
                                    currentPage:
                                    p2,
                                    id:'first' };
                                    track con;) {
                                <tr>
                                    <td>{{p2*piB+$index+1-piB}}</td>
                                    <td>{{con.fileName}}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(con)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                       
                                    </td>
                                </tr>
                            } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p2 = $event" id="first"></pagination-controls>
                    </div>
            </div>
        </div>
         <!---Awr Execution Report--->
         <div class="accordion-item"> 
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    AWR Execution Status
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-6">
                            <h3 class="main_h py-4 ps-3"> AWR Execution Status
                                <button class="btn btn-sync"(click)="getreqTableData()">
                                    @if(status_spin){
                                    <app-spinner />
                                    }@else{
                                        <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </h3>
                        </div>
                        <div class="col-12 col-sm-6 col-md-6">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Sno</th>
                                    <th>File Name</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of tabledata| searchFilter: datachange1| paginate:{ itemsPerPage: piC,
                                currentPage:
                                p3,
                                id:'third' };
                                track con;) {
                                <tr>
                                    <td>{{p3*piC+$index+1-piC}}</td>
                                    <td>{{con.object_type}}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        @switch (con.status) {
                                        @case ('I') {
                                        <span>Initialize</span>
                                        }
                                        @case ('P') {
                                        <span>Pending</span>
                                        }
                                        @default {
                                        <span>Completed</span>
                                        }
                                        }
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p3 = $event" id="third"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
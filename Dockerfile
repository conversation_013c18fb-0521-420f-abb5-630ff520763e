FROM node:20.12.2-slim as build

ARG DEPLOY

WORKDIR /app

COPY package.json package-lock.json ./

# RUN npm config set registry https://registry.npmmirror.com/
RUN npm install

ARG category

COPY . .

RUN if [ -n "$category" ] ; then mv "src/app/app.routes.ts.$category" "src/app/app.routes.ts" ; fi
RUN echo "Using.. $category" && ls "src/app/"

RUN npm run build -- --output-path=./dist/out --configuration=$DEPLOY

###

FROM nginx:1.27.0-alpine3.19

RUN apk upgrade && apk update

COPY nginx/modules/ /usr/lib/nginx/modules/
COPY nginx.conf /etc/nginx/nginx.conf
COPY --from=build /app/dist/out/browser /usr/share/nginx/html

RUN chown -R nginx:nginx /usr/share/nginx/html && chmod -R 755 /usr/share/nginx/html && \
        chown -R nginx:nginx /etc/nginx/conf.d
        #  && \
        # chown -R nginx:nginx /var/cache/nginx && \
        # chown -R nginx:nginx /var/log/nginx

# RUN touch /var/run/nginx.pid && \
#         chown -R nginx:nginx /var/run/nginx.pid
        
USER nginx
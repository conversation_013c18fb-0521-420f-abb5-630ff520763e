import { Directive, ElementRef, HostListener, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appResizeTable]',
  standalone: true
})
export class ResizeTableDirective {
  private isResizing = false;
  private startX: any;
  private startWidth: any;

  constructor(private el: ElementRef, private renderer: Renderer2) {
    // Set default styles for the element
    this.renderer.setStyle(this.el.nativeElement, 'position', 'relative');
    this.renderer.setStyle(this.el.nativeElement, 'cursor', 'ew-resize');
  }

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent) {
    this.isResizing = true;
    this.startX = event.pageX;
    this.startWidth = this.el.nativeElement.offsetWidth;

    // Add mousemove and mouseup event listeners
    document.addEventListener('mousemove', this.onMouseMove);
    document.addEventListener('mouseup', this.onMouseUp);
  }

  @HostListener('window:mousemove', ['$event'])
  onMouseMove = (event: MouseEvent) => {
    if (!this.isResizing) return;

    const width = this.startWidth + (event.pageX - this.startX);
    this.renderer.setStyle(this.el.nativeElement, 'width', `${width}px`);
  };

  @HostListener('window:mouseup')
  onMouseUp = () => {
    this.isResizing = false;

    // Remove mousemove and mouseup event listeners
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
  };

}

import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { environment } from '../environments/environment';
import { dbaAPIConstant } from '../constant/DBAAPIConstant';
import { Observable } from 'rxjs';
import { deleteFile, getdbs, getroles, uploadDocs } from '../models/interfaces/types';


@Injectable({
  providedIn: 'root'
})
export class DbaService {

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl + "DBA/";
  // Connection details
  getconnectionurl = this.apiURL + dbaAPIConstant.getConnections
  getConList = (body: any): Observable<getroles> => {
    return this.apiService.get(this.getconnectionurl + body);
  };
  // Database details
  dbnameurl = this.apiURL + dbaAPIConstant.getdbnames
  getdbnames = (body: any): Observable<getdbs> => {
    return this.apiService.get(this.dbnameurl + body);
  };
  // Roles details
  rolenameurl = this.apiURL + dbaAPIConstant.getroles
  getrolenames = (body: any): Observable<getroles> => {
    return this.apiService.get(this.rolenameurl + body.ConId + "&dbname=" + body.dbname);
  };
  //user details

  usernameurl = this.apiURL + dbaAPIConstant.getusers
  getusernames = (body: any): Observable<any> => {
    return this.apiService.get(this.usernameurl + body.ConId + "&dbname=" + body.dbname);
  };
  //vacuum details
  FetchVacuumDataurl = this.apiURL + dbaAPIConstant.FetchVacuumData
  FetchVacuumData = (body: any): Observable<getroles> => {
    return this.apiService.get(this.FetchVacuumDataurl + body.ConId + "&dbname=" + body.dbname + "&schemas=" + body.schemas);
  };

  GetAnalyzeDataurl = this.apiURL + dbaAPIConstant.GetAnalyzeData
  GetAnalyzeData = (body: any): Observable<getroles> => {
    return this.apiService.get(this.GetAnalyzeDataurl + body.ConId + "&dbname=" + body.dbname + "&schemas=" + body.schemas);
  };

  // Search role button
  fetchRoleInfourl = this.apiURL + dbaAPIConstant.fetchRoleInfo                       //this.apiURL 
  fetchRoleInfo = (body: any): Observable<any> => {
    return this.apiService.get(this.fetchRoleInfourl + body.ConId + "&dbname=" + body.dbname + "&role=" + encodeURIComponent(body.role))
  };
  // Search serverparameter button
  GetServerParamsDataurl = this.apiURL + dbaAPIConstant.GetServerParamsData                       //this.apiURL 
  GetServerParamsData = (body: any): Observable<any> => {
    return this.apiService.get(this.GetServerParamsDataurl + body.conid + "&option=" + body.option)
  };
  GetUsersInfourl = this.apiURL + dbaAPIConstant.GetUsersInfo                       //this.apiURL 
  GetUsersInfo = (body: any): Observable<any> => {
    return this.apiService.get(this.GetUsersInfourl + body.ConId + "&dbname=" + body.dbname + "&username=" + encodeURIComponent(body.user))
  };

  vacuumurl = this.apiURL + dbaAPIConstant.Vacuumschema
  vaccum = (body: any): Observable<any> => {
    return this.apiService.get(this.vacuumurl + encodeURIComponent(body.schema) + "&conid=" + body.conid)
  };
  Analyzeurl = this.apiURL + dbaAPIConstant.Analyzeschema
  analyze = (body: any): Observable<any> => {
    return this.apiService.get(this.Analyzeurl + encodeURIComponent(body.schema) + "&conid=" + body.conid)
  };
  tgtSchemaSelectwithdburl = this.apiURL + dbaAPIConstant.tgtSchemaSelectwithdb
  tgtSchemaSelectwithdb(data: any) {
    return this.apiService.get(this.tgtSchemaSelectwithdburl + data.conid + "&dbname=" + data.dbname);
  };
  getobjecttypeurl = this.apiURL + dbaAPIConstant.getobjecttype
  getobjecttype = (body: any): Observable<any> => {
    return this.apiService.post(this.getobjecttypeurl, body);
  };
  getexecutedataurl = this.apiURL + dbaAPIConstant.getexecutedata
  getexecutedata = (body: any): Observable<any> => {
    return this.apiService.get(
      this.getexecutedataurl + body.ConId + "&dbnames=" + body.dbname);
  };
  getobjectnamesurl = this.apiURL + dbaAPIConstant.getobjectnames
  getobjectnames = (body: any): Observable<any> => {
    return this.apiService.post(this.getobjectnamesurl, body);
  };
  getpermissionsurl = this.apiURL + dbaAPIConstant.getpermissions
  getpermissions = (body: any): Observable<any> => {
    return this.apiService.post(this.getpermissionsurl, body);
  };
  GetbloatingDetailsurl = this.apiURL + dbaAPIConstant.GetbloatingDetails
  GetbloatingDetails = (body: any): Observable<any> => {
    return this.apiService.post(this.GetbloatingDetailsurl, body);
  };
  grantpermissionsurl = this.apiURL + dbaAPIConstant.grantpermissions
  grantpermissions = (body: any): Observable<any> => {
    return this.apiService.post(this.grantpermissionsurl, body);

  };
  triggerVacuumURL = this.apiURL + dbaAPIConstant.triggerVacuum
  triggerVacuum = (body: any): Observable<getroles> => {
    return this.apiService.post(this.triggerVacuumURL, body);
  };

  GetReqDataURL = this.apiURL + dbaAPIConstant.GetReqData
  GetReqData = (body: any): Observable<any> => {
    return this.apiService.get(this.GetReqDataURL + body.projectId + '&operationType=' + body.operationType);
  };
  deleteTableDataURL = this.apiURL + dbaAPIConstant.deleteTableData;
  deleteTableData = (body: any): Observable<any> => {
    return this.apiService.get(this.deleteTableDataURL + body.projectId + '&requestId=' + body.requestId);
  };
  //getRunno
  GetRunnoUrl = this.apiURL + dbaAPIConstant.GetRunno
  GetRunno = (body: any): Observable<any> => {
    return this.apiService.get(this.GetRunnoUrl + body);
  };
  DeleteIterationByRangeurl = this.apiURL + dbaAPIConstant.DeleteIterationByRange
  DeleteIterationByRange = (body: any): Observable<any> => {
    return this.apiService.post(this.DeleteIterationByRangeurl, body);
  };
  downloadLargeFilesURL = this.apiURL + dbaAPIConstant.downloadLargeFiles;
  downloadLargeFiles = (body: string): Observable<any> => {
    return this.apiService.get(this.downloadLargeFilesURL + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }
  //GetFilesFromDir
  GetFilesFromDirURL = this.apiURL + dbaAPIConstant.GetFilesFromDir;
  GetFilesFromDir = (body: any): Observable<string> => {
    return this.apiService.get(this.GetFilesFromDirURL + body)
  }
  uploadLargeDocumentURL = this.apiURL + dbaAPIConstant.uploadLargeFiles
  uploadLargeDocument = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.uploadLargeDocumentURL, body);
  };
  deleteFileURL = this.apiURL + dbaAPIConstant.deleteFile;
  deleteFile = (body: string): Observable<deleteFile> => {
    return this.apiService.get(this.deleteFileURL + body)
  }
  DBAScriptInsertionURL = this.apiURL + dbaAPIConstant.DBAScriptInsertion
  DBAScriptInsertion = (body: any): Observable<any> => {
    return this.apiService.post(this.DBAScriptInsertionURL, body);
  };
  DBACategoryNameURL = this.apiURL + dbaAPIConstant.DBACategoryName
  DBACategoryName = (body: any): Observable<any> => {
    return this.apiService.get(this.DBACategoryNameURL + body.category);
  };
  DBATaskListURL = this.apiURL + dbaAPIConstant.DBATaskList;
  DBATaskList = (body: any): Observable<any> => {
    return this.apiService.get(this.DBATaskListURL + body.projectId)
  }

  QueryExecutionURL = this.apiURL + dbaAPIConstant.QueryExecution
  DBAScriptQueryExecution = (body: any): Observable<any> => {
    return this.apiService.post(this.QueryExecutionURL, body);
  };
  DBARunidsURL = this.apiURL + dbaAPIConstant.GetDBARunids;
  GetDBARunids = (body:any): Observable<any> => {
    return this.apiService.post(this.DBARunidsURL,body)
  }
  DBAExecResultsURL = this.apiURL + dbaAPIConstant.GetDBAExeResults;
  GetDBAExecResultsByRunid = (body:any): Observable<any> => {
    return this.apiService.get(this.DBAExecResultsURL+body)
  }
 
}


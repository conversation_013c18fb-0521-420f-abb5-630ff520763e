import { Component } from '@angular/core';
import { HotToastService } from '@ngxpert/hot-toast';
import { TestingService } from '../../../../services/testing.service';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RunInfo, RunInfoTable } from '../../../../models/interfaces/types';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgxPaginationModule } from 'ngx-pagination';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-test-review',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, NgSelectModule, SearchFilterPipe, NgxPaginationModule],
  templateUrl: './test-review.component.html',
  styles: ``
})
export class TestReviewComponent {

  projectId: any;
  //runInfo: any = [];
  runInfo: RunInfoTable["Table1"] = [];
  IterationNo: string = '';
  iteration: any;
  Schema: any;
  spin: boolean = false;
  FeatureObjData: any;
  FeatureId: any;
  AssignDropdownData: any;
  AssignSelectdata: any;
  SelectId: any;
  WorkStatus: any;
  PrjObjectfeaturesSelectData: any = [];
  CaReviewForm: any;
  GroupId: any;
  TgtUpdateTgt: boolean = false;
  ObjectSelected: boolean = false;
  user: any;
  uname: any;
  ObjectTgtCode: any;
  ObjectSrcCode: any;
  ObjectUpdateTgtCode: any;
  selectcheck: any;
  PrjsrcObjectsSelectData: any = [];
  tgtObjectsSelectCdcData: any = [];
  EstimateTime: any;
  TotalTime: any;
  project_name: any;
  getRole: any;
  token: any;
  PrjsrcObjectsSelect: any;
  FeatureName: any;
  ObservationLog: any;
  farmatobservations: any;
  runid: any;

  pageName: string = ''


  constructor(private toast: HotToastService, private testingService: TestingService, public formBuilder: FormBuilder, private fb: FormBuilder, private route: ActivatedRoute) {
    this.project_name = localStorage.getItem('project_name');
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') || 'null');
    this.token = localStorage.getItem('currentUser');
    this.user = JSON.parse(localStorage.getItem('userData') || 'null')
    this.uname = localStorage.getItem('uName');

    this.pageName = this.route.snapshot.data['name'];

    this.CaReviewForm = this.formBuilder.group({
      overRideCheckBox: [''],
    });

  }
  ngOnInit(): void {
    this.CaReviewForm = this.fb.group({
      overRideCheckBox: [''],
    });
    this.getRunInfoIteration();
    this.postPrjSrcfeaturesSelect();
    //this.getInfraSelect();
    //this.GetSecUserSelectByProject();
    this.selectcheck = false;
    this.PrjsrcObjectsSelect();


  }



  selectedIteration(value: any) {
    const selectedValue = this.runInfo.filter((item: any) => {
      return item.iter_schema_operation === value;
    });
    this.iteration = selectedValue[0].iteration;
    this.Schema = selectedValue[0].schemaname;
    this.runid = selectedValue[0].run_id;
    this.postPrjSrcfeaturesSelect();
  }
  // GrouP iD DropDown
  postPrjSrcfeaturesSelect() {
    const FeatureObj = {
      projectId: this.projectId.toString(),
      iteration: this.runid,
      groupId: 'null',
      runid: this.runid
    };
    this.spin = true;
    this.testingService.testPrjSrcfeaturesSelect(FeatureObj).subscribe((data: any) => {
      const Crfeature: any = data['Table1'];
      const key = "feature_id";
      const index = Crfeature.indexOf(key, 9999);
      if (index >= -1) {
        Crfeature.splice(index, 1);
      }
      this.FeatureObjData = Crfeature;
      this.spin = false;
      if (this.FeatureId != undefined) {
        const SelectedFeatureIdValue = this.FeatureObjData.filter((item: any) => {
          return item.feature_id === this.FeatureId;
        });

        this.ObservationLog = SelectedFeatureIdValue[0]?.observations;
        if (this.ObservationLog != undefined) {
          this.farmatobservations = this.ObservationLog.split(";").join("\n");
        }
      }
    });
  }
  //  Get GroupId DropDown FeatureId Vallue

  SelectedFeatureId(value: any) {
    const SelectedFeatureIdValue = this.FeatureObjData.filter((item: any) => {
      return item.feature_name === value;
    });
    this.FeatureId = SelectedFeatureIdValue[0].feature_id;
    this.FeatureName = SelectedFeatureIdValue[0].feature_name;
    this.ObservationLog = SelectedFeatureIdValue[0].observations;
    if (this.ObservationLog != undefined) {
      this.farmatobservations = this.ObservationLog.split(";").join("\n");
    }
    this.AssignDropdownData = [];
    this.GetProjectSrcfeaturesAssignSelect();
    this.groupLead();
    // this.PrjsrcObjectsSelectCDCDropDown();
    this.PrjObjectfeaturesSelect();
    this.estimatedHours();

  }

  //   Assign DropDown
  GetProjectSrcfeaturesAssignSelect() {
    const AssignSelectObj = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      feature_id: "null",
      grpid: "null"
    };
    this.testingService.testPrjSrcfeaturesAssignSelect(AssignSelectObj)
      .subscribe((data: any) => {
        this.AssignSelectdata = data['Table1'];
        this.spin = false;
        const SelectedWorkStatus = this.AssignSelectdata.filter((item: any) => {
          return item.feature_id === this.FeatureId;
        });
        this.SelectId = SelectedWorkStatus[0].id;
        this.WorkStatus = SelectedWorkStatus[0].status;
        // this.GetProjectTgtDeployStatusSelect();
      });
  }
  groupleadData: any;
  leadfilterdata: any;
  groupLead() {
    const obj = {
      projectId: this.projectId.toString(),
      feature_id: this.FeatureId,
      iteration: this.iteration,
    };
    this.testingService.testPrjFeaturesGroupLeadSelect(obj).subscribe((data: any) => {
      this.leadfilterdata = data['Table1'];
    });
  }
  PrjObjectfeaturesSelect() {
    const CodeObjDrop = {
      projectId: this.projectId.toString(),
      feature_id: this.FeatureId,
      src_id: 'null',
    };
    this.testingService.testPrjObjectsfeaturesSelect(CodeObjDrop).subscribe((data: any) => {
      this.PrjObjectfeaturesSelectData = data['Table1'];
    });
  }
  estimateData: any = [];
  feature: any;
  estHours: any;
  featureCount: any;
  estimatedHours() {
    const obj = {
      projectId: this.projectId.toString(),
      feature_id: this.FeatureId,
    };
    this.testingService.testPrjSrcFeaturesEstTimeCountSelect(obj).subscribe((data: any) => {
      this.estimateData = data['Table1']; //feature_level_esttime
      if (this.estimateData != undefined) {
        this.featureCount = this.estimateData[0].mapped_objcnt;
        this.estHours = this.estimateData[0].feature_level_esttime;
      }
    });

  }
  // getRunInfoIteration() {
  //   this.testingService.testPrjRuninfoIteration(this.projectId).subscribe((data: any) => {
  //     this.runInfo = data['Table1'];
  //   });
  // }
  getRunInfoIteration() {
    this.testingService.getRunNumbers(this.projectId).subscribe((data: RunInfoTable) => {
      this.runInfo = data.Table1;
    });
  }

  selectedGroupIdValue: any;
  rmNames: any;
  // Get Group Id Dropdown in Group Id Value

  selectedGroupId(value: any) {
    this.selectedGroupIdValue = this.leadfilterdata.filter((item: any) => {
      return item.resource_nm === value;
    });
    this.GroupId = this.selectedGroupIdValue[0].groupid;
    this.rmNames = this.selectedGroupIdValue[0].resource_nm;
    this.AssignDropdownSelect();
    this.GetProjectSrcfeaturesTotalEsttime();
  }
  AssignDropdownSelect() {
    const assignobj = {
      projectId: this.projectId.toString(),
      opt: 'R',
      grpid: this.GroupId,
      rm_name: 'null',
      // userid:this.user.nameid
    };
    this.testingService.testPrjFeatureGroupSelect(assignobj)
      .subscribe((data: any) => {
        this.AssignDropdownData = data['Table1'];
      });
  }

  Actualtime: any;
  featuresTotalEsttime: any = [];
  GetProjectSrcfeaturesTotalEsttime() {
    const featuresTotalEsttime = {
      projectId: this.projectId.toString(),
      feature_id: this.FeatureId,
      iteration: this.iteration,
      grpid: this.GroupId,
    };
    this.testingService.testPrjSrcfeaturesAssignSelect(featuresTotalEsttime)
      .subscribe((data: any) => {
        this.featuresTotalEsttime = data['Table1'];
        const featuresTotalEsttimes = this.featuresTotalEsttime.filter(
          (item: any) => {
            return item.groupid === this.GroupId;
          }
        );
        this.Actualtime = featuresTotalEsttimes[0].actual_time;
      });

  }
  selectedCodeObject: any;
  src_id: any;
  objType: any;
  srcCode: any;
  selectCodeObject(value: any) {
    this.ObjectSelected = true;
    this.selectedCodeObject = value;
    this.GetExecutionLog(value);
    this.ObjectTgtCode = '';
    this.ObjectUpdateTgtCode = '';
    this.GetProjectSrcfeaturesAssignSelect();
    if (this.selectcheck == true) {
      this.PrjsrcObjectsSelectCDC(value);
      this.PrjtgtObjectsSelectCdc();
    } else {
      // this.PrjsrcObjectsSelect(value);
      // this.GetProjectTgtobjectsSelect();
      const obj = this.PrjsrcObjectsSelectData.filter((item: any) => {
        return item.objectname == this.selectedCodeObject;
      });
      this.src_id = obj?.src_id;
      this.objType = obj?.objecttype;
      this.srcCode = obj?.objectcode;
      this.ObjectSrcCode = this.srcCode;
    }
  }
  logDetails: any;
  logdata: any;
  GetExecutionLog(value: any) {
    const obj = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      objectName: value
    }
    this.testingService.GetExecutionLog(obj).subscribe((data: any) => {
      this.logDetails = data['Table1'];

    })
  }
  PrjsrcObjectsSelectDataCdc: any;
  PrjsrcObjectsSelectCDC(value: any) {
    const CodeObjDrop = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      schemaName: this.Schema,
      objectName: 'null',
    };
    this.spin = true;
    this.testingService.PrjsrcObjectsSelectCDC(CodeObjDrop).subscribe((data: any) => {
      this.PrjsrcObjectsSelectDataCdc = data['jsonResponseData']['Table1'];
      this.spin = false;
      if (this.PrjsrcObjectsSelectData != undefined) {
        const selectedObjectCodeCdc = this.PrjsrcObjectsSelectDataCdc.filter(
          (item: any) => {
            return item.objectname === this.selectedCodeObject;
          }
        );
        if (selectedObjectCodeCdc != null) {
          this.ObjectSrcCode = selectedObjectCodeCdc[0].objectcode;
          this.EstimateTime = selectedObjectCodeCdc[0].estimation_time;
        }
      } else {
        this.ObjectSrcCode =
          'ObjectSrcCode  is not Available for ' + this.selectedCodeObject;
      }
    });
  }
  PrjtgtObjectsSelectCdc() {
    const CodeObjDrop = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      objectName: this.selectedCodeObject,
      schemaName: this.Schema,
      option: 'null',
    };
    this.testingService.PrjtgtObjectsSelectCDC(CodeObjDrop).subscribe((data: any) => {
      this.tgtObjectsSelectCdcData = data['jsonResponseData']['Table1'];
      if (this.tgtObjectsSelectCdcData != undefined) {
        const selectedObjectCodecdc = this.tgtObjectsSelectCdcData.filter(
          (item: any) => {
            return item.objectname === this.selectedCodeObject;
          }
        );
        this.ObjectTgtCode = selectedObjectCodecdc[0].objectcode;
        this.ObjectUpdateTgtCode = selectedObjectCodecdc[0].updated_code;
      } else {
        this.ObjectTgtCode =
          'Target Code is not Available for ' + this.selectedCodeObject;
      }
    });
  }


}

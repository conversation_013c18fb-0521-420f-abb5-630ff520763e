import { Routes } from '@angular/router';
import { ErrorComponent } from './shared/components/error/error.component';
import { LoginComponent } from './shared/components/login/login.component';
import { HelpCenterComponent } from './shared/components/help-center/help-center.component';
import { E2eMigrationComponent } from './shared/components/e2e-migration/e2e-migration.component';
import { LayoutComponent } from './shared/components/layout/layout.component';

export const routes: Routes = [
    { path: '', redirectTo: 'login', pathMatch: 'full' },
    { path:'login', component:LoginComponent},
    { path:'help', component:HelpCenterComponent},
    { path:'e2eMigration', component:LayoutComponent, children:[
        {path:'', component:E2eMigrationComponent}
    ]},
    { path:'assessment', loadChildren:() => import('./modules/assessment/assessment.module').then(a => a.AssessmentModule)},
    { path:'codeMigration', loadChildren:() => import('./modules/codeMigration/codeMigration.module').then(b => b.CodeMigrationModule)},
    { path:'dataMigration', loadChildren:() => import('./modules/dataMigration/dataMigration.module').then(c => c.DataMigrationModule)},
    { path:'goldenGate', loadChildren:() => import('./modules/goldenGate/goldenGate.module').then(d => d.GoldenGateModule)},
    { path:'testing', loadChildren:() => import('./modules/testing/testing.module').then(e => e.TestingModule)},
    { path:'dba', loadChildren:() => import('./modules/dba/dba.module').then(f => f.DbaModule)},
    { path:'dashboard', loadChildren:() => import('./modules/dashboard/dashboard.module').then(g => g.DashboardModule)},
    { path:'deployment', loadChildren:() => import('./modules/deployment/deployment.module').then(h => h.DeploymentModule)},
    { path:'perfTesting', loadChildren:() => import('./modules/performanceTesting/performanceTesting-routing.module').then(i => i.PerformanceTestingRoutingModule)},
    { path:'sql',loadChildren:() => import('./modules/sql/sql.module').then(j=> j.SqlAssessmentModule)},
    { path:'sit', loadChildren:() => import('./modules/sit/sit.module').then(k => k.SitModule)},
    { path: '**', component: ErrorComponent },
    { path: 'error', component: ErrorComponent },
];

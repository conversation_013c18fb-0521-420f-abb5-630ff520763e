import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GetFilesFromExpath, GetTablesByschemaGG, SchemaSelect, UploadProjectDocs, airflowValidationCommand, conList, createFile, deleteFile, documentsList, fileStatus, ggCreateFileReq, projectConRunTblInsert, redisCommand, runGGScripts, schemalist } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { GoldenGateService } from '../../../../services/goldenGate.service';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
declare let $: any;
@Component({
  selector: 'app-setup',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './setup.component.html',
  styles: ``
})
export class SetupComponent {

  //search testbox
  searchText1: string = '';
  searchText: string = '';
  fileName: string = '';
  filename: string = '';

  // global variables
  projectId: string;

  //Create File from Template 
  fileandtemplateForm: any;

  //Create Template
  createTemplateList: any = ["Extract", "Globals", "Mgr", "Replicat", "Odbc", "obeystart", "Instruction", "Goldengate"];
  selectdocumentFile: any;

  //create template
  createForm: any;
  createSpin: boolean = false;



  //Script File Upload
  scriptForm = this.formBuilder.group({
    podName: ['', Validators.required],
    file: ['', Validators.required],
  });
  scriptSpin: boolean = false;
  scriptFile: any;


  // create file button
  createFileSpin: boolean = false;

  //target connection name
  tgtList: any

  //select pod name
  templateFiles: any;

  //table name
  tableList: any;

  //source connection Name
  getTableSrcId: string = '';
  ConsList: any;

  //schema name
  schemaList: any;

  // Extract Tables
  operation: any;
  extractForm: any;
  extracttableSpin: boolean = false;

  //delete files
  fileStatus: any;
  deleteSpin: boolean = false;
  isLoading = [];

  //Execute Scripts
  executescriptsForm: any;

  //pod name
  shellScriptList: any;
  podList: any;

  //run script button
  runScriptspin: boolean = false;

  //template type
  templateList: any = ["Extract", "Globals", "Mgr", "Odbc"]

  //golden gate file pagination
  goldengatepageNumber: number = 1;

  //Template Files pagination
  configFiles: any = [];
  templatefilepageNumber: number = 1;

  pageName:string = ''

  
  templateForm: any;

  constructor(private titleService: Title,private toast: HotToastService, private goldengateservices: GoldenGateService, private datamigrationservice: DataMigrationService, private assessment: AssessmentService, public formBuilder: FormBuilder,private route: ActivatedRoute) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList();
    this.getPodList()
    this.fetchAssessmentFiles()
    this.fileandtemplateForm = this.formBuilder.group({
      podName: ['', Validators.required],
      template: ['', Validators.required],
      sourceConnection: ['', Validators.required],
      targetConnection: ['', Validators.required],
      schema: ['', Validators.required],
      table: [''],
      podService: [''],
      trailPath: [''],
      oracleHome: [''],
      ggHome: ['', Validators.required],
    });
    this.goldengatepageNumber = 1
    this.extractForm = this.formBuilder.group({
      schema: ['', Validators.required],
      sourceConnection: ['', Validators.required],
    });

    this.createForm = this.formBuilder.group({
      template: ['', Validators.required],
      file: ['', Validators.required],
    });
    this.executescriptsForm = this.formBuilder.group({
      podName: ['', Validators.required],
      shellScript: ['', Validators.required],
      argument: [''],
    });

    this.templateForm = this.formBuilder.group({
      filename: ['', Validators.required],
      size: ['', Validators.required],
      databasename: ['', Validators.required],
      storageclass: ['', Validators.required],
      image: ['', Validators.required],
    });
  }
  openPopup() {
    this.createForm.reset();
    this.scriptForm.reset();
  }
  get f() {
    return this.extractForm.controls;
  }
  get ff() {
    return this.fileandtemplateForm.controls;
  }
  get fs() {
    return this.scriptForm.controls;
  }
  get fc() {
    return this.createForm.controls;
  }
  get fe(){
    return this.executescriptsForm.controls;
  }
  get fg(){
    return this.templateForm.controls;
  }


  //Extract Table
  getSchemasList(ConnectionId: any) {
    //console.log(ConnectionId)
    this.getTableSrcId = ConnectionId
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId
    }
    this.goldengateservices.SchemaListSelect(obj).subscribe((data: SchemaSelect) => {
      this.schemaList = data['Table1'];
      this.schemaList = this.schemaList.filter((item: any) => {
        return item.schema_id != "0" && item.schema_name != "ALL";
      });
      //console.log(this.schemaList)
    })
  }
  getSchemaValue(value: any) {
    let obj: GetTablesByschemaGG = {
      schema: value,
      srcId: this.getTableSrcId
    }
    this.goldengateservices.GetTablesByschemaGG(obj).subscribe((data: any) => {
      this.tableList = data['Table1']
    })
  }

  //create validation
  extractValue(value: any) {
    let obj = {
      operation: "Table_Extraction",
      projectId: this.projectId.toString(),
      schema: value.schema,
      srcId: value.sourceConnection
    }
    //console.log(obj)
    this.extracttableSpin = true
    this.goldengateservices.insertTablesCommand(obj).subscribe((data: fileStatus) => {
      this.extracttableSpin = false
      this.toast.success("Tables Extracted Successfully")
    },
      error => {
        this.extracttableSpin = false
        this.toast.error("Failed to Tables Extracted")
      })
  }

  GetConsList() {
    const projectId = this.projectId.toString()
    this.datamigrationservice.getConList(projectId).subscribe((data: conList) => {
      const condata = data['Table1'];
      this.ConsList = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = condata.filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
    })
  }

  gtReports:any
  fetchAssessmentFiles() {
    const path = ["GG_Templates/","GG_Templates/Generated"];
    path.forEach( (el:any) => {
      this.goldengateservices.GetFilesFromExpath(el).subscribe((data: GetFilesFromExpath) => {
        el == 'GG_Templates/' ? this.configFiles = data : this.gtReports = data;
            const filtered = Object.fromEntries(
              Object.entries(this.gtReports).filter(([key, value]) => (typeof value === 'string') && value.endsWith('.yaml'))
            );
            this.gtReports = filtered
      })
    })



  }

  //execute scripts
  getPodList() {
    this.goldengateservices.getGoldenGatePod().subscribe((data: any) => this.podList = data)
  }
  callShellScript(value: any) {
    this.goldengateservices.GetGGShellDirectoryEx(value + '/scripts').subscribe((data: GetFilesFromExpath) => this.shellScriptList = data)
  }

  runScriptValue(data: any) {
    this.runScriptspin = true
    let scriptObject: runGGScripts = {
      cmd: data.argument,
      podName: data.podName,
      fileName: data.shellScript
    }
    this.goldengateservices.runGGScripts(scriptObject).subscribe(
      (response: any) => {
        this.executescriptsForm.reset()
        this.runScriptspin = false
        this.toast.success("Submitted Execution Successfully")
      },
      error => {
        this.runScriptspin = false
        this.toast.warning("Something went wrong")
      })
  }

  /* -- Template Values --*/
  getTemplateValue(value: any) {
    if (value == 'Extract') {
      this.fileandtemplateForm.controls.podService.setValidators([Validators.required])
      this.fileandtemplateForm.controls.trailPath.setValidators([Validators.required])
      this.fileandtemplateForm.controls.oracleHome.setValidators([Validators.required])
      this.fileandtemplateForm.controls.table.clearValidators()
    } else if (value == 'Globals' || value == 'Mgr') {
      this.fileandtemplateForm.controls.trailPath.setValidators([Validators.required])
      this.fileandtemplateForm.controls.podService.clearValidators()
      this.fileandtemplateForm.controls.oracleHome.clearValidators()
      this.fileandtemplateForm.controls.table.clearValidators()

    } else if (value == 'Replicat') {
      this.fileandtemplateForm.controls.trailPath.clearValidators()
      this.fileandtemplateForm.controls.oracleHome.clearValidators()
      this.fileandtemplateForm.controls.oracleHome.clearValidators()
      this.fileandtemplateForm.controls.table.setValidators([Validators.required])

    } else {
      this.fileandtemplateForm.controls.trailPath.clearValidators()
      this.fileandtemplateForm.controls.oracleHome.clearValidators()
      this.fileandtemplateForm.controls.oracleHome.clearValidators()
      this.fileandtemplateForm.controls.table.clearValidators()

    }
    this.fileandtemplateForm.controls.podService.updateValueAndValidity()
    this.fileandtemplateForm.controls.trailPath.updateValueAndValidity()
    this.fileandtemplateForm.controls.oracleHome.updateValueAndValidity()
    this.fileandtemplateForm.controls.table.updateValueAndValidity()
  }

  /*-- Create File --*/
  createFile(formData: any) {
    const schemaId = this.schemaList.filter((el: any) => el.schemaname == formData.schema)
    this.createFileSpin = true;
    let createFileObject: ggCreateFileReq = {
      task:"GG_Template_Load",
      podName: formData.podName,
      operation: formData.template,
      schema: formData.schema,
      schemaId: schemaId[0].schema_id,
      srcId: formData.sourceConnection,
      tgtId: formData.targetConnection,
      podService: formData.podService || '',
      trailPath: formData.trailPath || '',
      ggHome: formData.ggHome,
      oracleHome: formData.oracleHome || '',
      tableList: formData.table || '',
      projectId: this.projectId.toString()
    }
    // let obj={
    //   task:"",
    //   operation:formData.template,
    //   srcId:formData.sourceConnection,
    //   tgtId:formData.targetConnection,
    //   schema:formData.schema,
    //   schemaId:schemaId[0].schema_id,
    //   tableList:formData.table || '',
    //   podService:formData.podService || '',
    //   trailPath:formData.trailPath || '',
    //   ggHome:formData.ggHome,
    //   oracleHome:formData.oracleHome || '',
    //   podName:formData.podName

    // }
    this.goldengateservices.DataMigrationCommand(createFileObject).subscribe((response:any) => {
      this.createFileSpin = false
      this.toast.success(response.message)
    },
      error => {
        this.createFileSpin = false
        this.toast.warning(error)
      })
  }


  //Template files

  getTempFiles(value: any) {
    this.goldengateservices.getGGFiles(value).subscribe((data: GetFilesFromExpath) => {
      this.templateFiles = data
    })
  }
  /*--- Delete Project Reports   ---*/

  DeleteFiles(path: any) {
    this.deleteSpin = true
    this.goldengateservices.deleteFiles(path).subscribe((data: fileStatus) => {
      this.fileStatus = data.message
      this.fetchAssessmentFiles();
      this.toast.success(this.fileStatus)
      this.deleteSpin = false
    })
  }

  /*--- Apply Filter   ---*/

  applyFilter(filterValue: string) {
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
    this.templateFiles = this.templateFiles.filter((el: any) => { return el.fileName.includes(filterValue) })
  }

  /* Download Files--*/
  downloadFile(title: any) {
    this.goldengateservices.downloadLargeFiles(title.filePath).subscribe((blob: any) => {
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = title.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
    })
  }

  //template files
  onKey() {
    this.templatefilepageNumber = 1;
    this.goldengatepageNumber = 1;
  }

  //upload
  uploadDocument(event: any) {
    const file: File = event.target.files[0];
    this.selectdocumentFile = file
    this.fileName = event.target.files[0].name;
  }

  /* Create Template Button*/
  createTemplate(Valu: any) {
    this.createSpin = true
    console.log(Valu)
    const formData: FormData = new FormData();
    formData.append('file', this.selectdocumentFile, this.fileName + ".txt");    
    Valu.template == "Goldengate" ? formData.append('path', "extra/GG_Templates") : formData.append('path', "GG_Templates");    
    this.goldengateservices.UploadProjectDocs(formData).subscribe(
      (response: any) => {
        this.createSpin = false
        this.toast.success(response.message)
        $('#demo').offcanvas('hide');
      },
      error => {
        this.createSpin = false
        this.toast.error('Something went wrong')
        this.openPopup()
        $('#demo').offcanvas('hide');
      }
    );
  }

  /* Scripts Files*/
  sendScriptFile(event: any) {
    const file: File = event.target.files[0];
    this.scriptFile = file
    this.filename = event.target.files[0].name;
    let ext = this.scriptFile.name.split('.').pop();
    if (ext !== "sh") {
      this.toast.warning("File Must Be Shell Script")
      this.scriptForm.reset()
      this.filename = ''
    }
  }

  /* -- Upload Scripts--*/
  uploadScript() {
    this.scriptSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.scriptFile, this.scriptFile.name);
    formData.append('path', `${this.scriptForm.controls['podName'].value}/scripts`);

    var excutePodObject = {
      podName: this.scriptForm.controls['podName'].value,
      fileName: this.scriptFile.name
    }
    this.goldengateservices.UploadProjectDocs(formData).subscribe(
      (response: any) => {
        this.goldengateservices.ExecuteInPod(excutePodObject).subscribe((data: any) => console.log(data))
        this.scriptSpin = false
        this.toast.success(response.message)
      },
      error => {
        this.scriptSpin = false
        this.toast.warning(error.message)
      }
    );

  }

  exe_spin:boolean = false
  generateTemplate(data:any){
    this.exe_spin = true
    this.goldengateservices.generateGGPod(data).subscribe( (el:any)=>{
      this.exe_spin = false
      this.fetchAssessmentFiles()
    })
  }
}

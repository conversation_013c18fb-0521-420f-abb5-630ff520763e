<div class="v-pageName">{{pageName}}</div>
<!--- Performance optimisation List --->
<div class="qmig-card">
    <h3 class="main_h px-3 pt-3"> Log Explain Plans <button class="btn btn-sync " (click)="refresh()">
        @if(ref_spin){
            <app-spinner />
            }@else{
                <span class="mdi mdi-refresh"></span>
            }
    </button></h3>
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="perfCallForm">
            <div class="row">             
                <div class="col-md-3 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="targtetConnection"> Connection
                            Name</label>
                        <select formControlName="tgtconnection" #Myselect2 (change)="
                        selTgtId(Myselect2.value);GetperfSchemas() " class="form-select">
                        <option value="" disabled selected>Select Connection Name</option>
                            @for(list of tgtList;track list; ){
                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                            }
                        </select>
                        <div class="alert">
                            @if(validate.tgtconnection.touched && validate.tgtconnection.invalid) {
                            <p class="text-start text-danger mt-1">Connection Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <!--- Log Explain  --->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label" for="name">Exec Plan Type</label>
                        <select class="form-select" #plan (change)="selectLog(plan.value)">
                            <option>Select Plan</option>
                            <option value="1">Proc Execution</option>
                            <option value="0">Show Execution Plans</option>
                        </select>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!hideL">
                    <div class="form-group" >
                        <label class="form-label  d-required" for="name">Call Statement</label>
                        <input type="text" class="form-control" formControlName="callstatement"
                            placeholder="Enter Call Statement" id="call_stmt">
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!hideL">
                    <div class="mt-4 pt-2" >
                        <button class="btn btn-upload w-100" (click)="InputCall(perfCallForm.value)">Submit
                            @if(insertspin){<app-spinner />}</button>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!hideS">
                    <div class="form-group">
                        <label class="form-label" for="name">From Date</label>
                        <input type="date" class="form-control" formControlName="fromDate" id="exec_date">
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!hideS">
                    <div class="form-group">
                        <label class="form-label" for="name">Procedure Name</label>
                        <input type="text" class="form-control" formControlName="ProcudureName"
                            placeholder="ProcudureName" id="Prodc_name">
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-md-9" [hidden]="!hideS">
                    <div >
                        <button class="btn btn-upload w-100"
                            (click)="SubmitShowLogs(perfCallForm.value)">Fetch Data
                            @if(Monspinner){<app-spinner />}</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <div [hidden]="!hideTab">
        <hr class="dash-dotted" />
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-md-9 ps-2 pe-3">
            <div [hidden]="!hideS">
                <button class="btn btn-sign w-100" (click)="exportexcelShowlogs()">
                    <span class="mdi mdi-arrow-up-thin"></span>Export
                    @if(excelSpinn){<app-spinner />}</button>
            </div>
        </div>
        <div class="table-responsive mt-3">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>S.No</th>
                        <th>SchemaName</th>
                        <th>Procedure</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    @for (documents of Showlogsdata | paginate: { itemsPerPage: 10,
                    currentPage: pageNumber } ; track documents) {
                    <tr>
                        <td>{{pageNumber*10+$index+1-10}}</td>
                        <td>{{documents.schema}}</td>
                        <td>{{documents.procedure}}</td>
                        <td>{{documents.exec_time}}</td>
                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="4">
                            <p class="text-center m-0 w-100">No Results</p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
    </div>
</div>



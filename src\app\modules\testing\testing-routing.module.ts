import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { LogfileUploadComponent } from './common/logfile-upload/logfile-upload.component';
import { TcDatamigrationComponent } from './common/tc-datamigration/tc-datamigration.component';
import { TestAssignComponent } from './common/test-assign/test-assign.component';
import { TestCycleComponent } from './common/test-cycle/test-cycle.component';
import { TestReviewComponent } from './common/test-review/test-review.component';
import { TestCaseUploadComponent } from './common/testcase-upload/testcase-upload.component';
import { UsertestCasesComponent } from './common/usertest-cases/usertest-cases.component';
import { WorkLoadComponent } from './common/work-load/work-load.component';
import { TestConversionComponent } from './common/test-conversion/test-conversion.component';
import { ReportsComponent } from './common/reports/reports.component';
import { BaselineDeploymentComponent } from './common/baseline-deployment/baseline-deployment.component';
import { TestCyclesNewComponent } from './common/test-cycles-new/test-cycles-new.component';

const routes: Routes = [
  {path:'', component:LayoutComponent, children:[
    {path:'baselineDeployment', component:BaselineDeploymentComponent, data:{ name:'Base Line Deployment '}},
    {path:'logFileUpload', component:LogfileUploadComponent, data: { name: 'Log File Upload' }},
    {path:'tcDataMigration', component:TcDatamigrationComponent, data: { name: 'Tc Data Migration'}},
    {path:'testAssign', component:TestAssignComponent, data: { name: 'Test Assign'}},
    // {path:'testCycle', component:TestCycleComponent, data: { name: 'Test Cycle'}},
    {path:'testCycle', component:TestCyclesNewComponent, data: { name: 'Test Cycles'}},
    {path:'testReview', component:TestReviewComponent, data: { name: 'Test Review'}},
    {path:'testcaseUpload', component:TestCaseUploadComponent, data: { name: 'Test case Upload'}},
    {path:'userTestCases', component:UsertestCasesComponent, data: { name: 'User Test Cases'}},
    {path:'workLoad', component:WorkLoadComponent, data: { name: 'Work Load'}},
    {path:'testconversion', component:TestConversionComponent, data: { name: 'Test Conversion'}},
    {path:'reports', component:ReportsComponent, data: { name: 'Reports'}},
  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TestingRoutingModule { }

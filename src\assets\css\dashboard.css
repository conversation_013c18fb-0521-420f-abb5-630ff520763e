/*-------  Q-Migrator  style sheet V1.o Created and Developed By Quadrant --------*/

@font-face {
  font-family: Aeonik Pro;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Aeonik Pro"), local("AeonikPro"),
    url(../fonts/aeonik-pro/AeonikPro-Regular.woff2) format("woff2");
}

@font-face {
  font-family: Aeonik Pro;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Aeonik Pro"), local("AeonikPro"),
    url(../fonts/aeonik-pro/AeonikPro-Medium.woff2) format("woff2");
}

@font-face {
  font-family: Aeonik Pro;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local("Aeonik Pro"), local("AeonikPro"),
    url(../fonts/aeonik-pro/AeonikPro-Bold.woff2) format("woff2");
}

* {
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-font-smoothing: antialiased;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
  overflow-x: hidden;
  background-color: #f7f7fb;
  color: #313e5b;
  position: relative;
  font-family: "Hanken Grotesk", sans-serif;
}

a,
a:hover {
  text-decoration: none;
}

ul {
  margin: 0;
  padding: 0;
}

li {
  list-style-type: none;
  display: inline-block;
}

p {
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.6;
  color: #5d5d5d;
  color: #525f81;
  margin-bottom: 15px;
}

.btn,
a {
  border-radius: 0px;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.btn:focus,
.btn.focus {
  outline: 0;
  box-shadow: unset;
}

button:focus {
  outline: 1px dotted;
  outline: none;
}

.btn:hover,
a:hover {
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: transparent;
  box-shadow: 2px 3px 5px 0px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 2px 3px 5px 0px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: #000d1b !important;
  border-radius: 2px;
  -webkit-box-shadow: 2px 3px 5px 0px rgba(0, 0, 0, 0.3);
  box-shadow: 2px 3px 5px 0px rgba(0, 0, 0, 0.3);
}

.form-control:focus {
  box-shadow: none;
}

/* header */

nav.navbar-top .navbar-brand img {
  width: 170px;
}
.navbar-top {
  width: 100%;
  font-size: 0.8rem;
  font-weight: 600;
  min-height: 4rem;
  padding: 0 1.5rem;
  border-bottom: 1px solid #e9ebf0;
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  background: hsla(0, 0%, 100%, 0.88);
}

.navbar-logo {
  height: 40px;
}
.dropdown-toggle::after {
  display: none;
}
.dropdown .dropdown-menu {
  left: auto;
  right: -0.5625rem;
  margin-top: 1rem;
  min-width: 18.3125rem;
  border-color: #cbd0dd;
  box-shadow: 0px 2px 4px -2px rgba(36, 40, 46, 0.08) !important;
  padding: 0;
}
.navbar .dropdown .dropdown-menu:after {
  content: "";
  position: absolute;
  z-index: -1;
  width: 1.5rem;
  height: 1.5rem;
  background: #000;
  top: -10px;
  right: 0.9rem;
  left: auto;
  transform: rotate(45deg);
  background: #fff;
  background: inherit;
  border-top-left-radius: 0.25rem;
  border-width: 1px 0 0 1px;
  border-style: solid;
  border-color: #cbd0dd;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
.dropdown-profile .nav .nav-link {
  padding: 0.5rem 2.5rem 0.5rem 2.5rem;
  font-weight: 600;
}
.dropdown-profile .nav .nav-link {
  padding: 0.5rem 2.5rem 0.5rem 2.5rem;
  font-weight: 500;
  font-size: 0.8rem;
  color: #222834;
  letter-spacing: 0.02rem;
  border-radius: 0.375rem;
}
.dropdown-profile .nav .nav-link:hover {
  background-color: #eeeef1;
}
.btn-phoenix-secondary {
  letter-spacing: 0.02rem;
  display: inline-block !important;
  padding: 0.625rem 1.2rem;
  font-family: "Aeonik Pro";
  font-size: 0.8rem;
  font-weight: 500;
  line-height: 1.2;
  color: #31374a;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid #cbd0dd;
  border-radius: 0.375rem;
  background-color: #f5f7fa;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0), 0 1px 1px rgba(0, 0, 0, 0);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.btn:hover {
  color: #222834;
  background-color: #e3e6ed;
  border-color: #e6e9ef;
  text-decoration: none;
}
hr {
  margin: 0;
}
.navbar-nav-icons .nav-item {
  font-size: 20px;
  display: flex;
  align-items: center;
  padding: 0px 10px;
}
.dropdown-profile .nav-item {
  display: block;
}
.nav_icon {
  max-width: 20px;
}

/* side nav */
nav.navbar-vertical {
  height: calc(100vh - 4rem);
  width: 16.875rem;
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  background: hsla(0, 0%, 100%, 0.88);
  border-right: 1px solid #e9ebf0;
  position: fixed;
  display: inline-block;
  z-index: 1020;
  top: 4rem;
  padding: 0;
  left: 0;
}
.navbar-vertical.navbar-expand-lg .navbar-collapse {
  width: 100%;
  flex-direction: column;
  display: flex !important;
  padding-top: 10px;
  padding-left: 30px;
}
.navbar-vertical-content {
  width: 100%;
}
.navbar-vertical .nav-link svg {
  fill: #c3c3c6;
  height: 18px;
  transform: translate(0px, -1px);
}
.navbar-vertical .nav-link svg path {
  fill: #c3c3c6;
}

.navbar-vertical .nav-item.active .nav-link svg path {
  fill: #ff7900;
}
.navbar-vertical .nav-link {
  color: #57575c;
  font-size: 14px;
  padding: 10px;
}
.navbar-vertical .nav-item {
  border-radius: 8px;
  line-height: 24px;
}
.navbar-vertical .nav-item:hover {
  background-color: #eeeef1;
}
.navbar-vertical .nav-item.active .nav-link {
  color: #000;
  font-weight: 600;
}
.navbar-vertical-label {
  text-transform: uppercase;
  font-weight: 700;
  font-size: 0.7rem;
  color: #313e5b;
  margin-bottom: 0.2rem;
  margin-top: 1.5rem;
  letter-spacing: 1px;
  font-family: "circular";
}
.navbar-vertical-footer {
  position: fixed;
  width: 100%;
  bottom: 0;
}

/* main-content */

.main-content {
  margin-left: 16.875rem;
  min-height: 100vh;
  padding: calc(4rem + 2rem) 1.5rem 6.375rem 1.5rem;
  position: relative;
  padding-bottom: 4rem;
}
.cont_img img {
  max-width: 400px;
}
.content_header {
  background: #fff;
  border-radius: 1.2rem;
  padding: 30px;
  margin-top: 50px;
  box-shadow: 0px 1px 10px rgba(0, 0, 0, 0.05);
}
.cont_img {
  margin-top: -100px;
}
.btn-more {
  font-size: 14px;
}
.btn-more:hover {
  text-decoration: underline;
}
.dash_card {
  background: #4d4cac;
  padding: 20px;
  border-radius: 1rem;
  position: relative;
  border: none;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);
}
.dash_card h4 {
  color: #fff;
  background: #5a5ab2;
  height: 40px;
  min-width: 40px;
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 0.7rem;
  font-size: 20px;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.6px;
}
.dash_card .card-body {
  padding: 0 1rem;
}
.dash_card .card-body h3 {
  color: #fff;
  font-size: 1.2rem;
  margin-bottom: 5px;
}
.dash_card .card-body p {
  color: rgb(255 255 255 / 80%);
  margin: 0;
  letter-spacing: 2px;
}
.col-md-3:nth-child(2) .dash_card {
  background: #8486c7;
}
.col-md-3:nth-child(2) .dash_card h4 {
  background: #a2a3da;
}
.col-md-3:nth-child(3) .dash_card {
  background: #5e81f4;
}
.col-md-3:nth-child(3) .dash_card h4 {
  background: #7c97f1;
}
.col-md-3:nth-child(4) .dash_card {
  background: #ff808b;
  background: #f97b85;
}
.col-md-3:nth-child(4) .dash_card h4 {
  background: #ff919b;
}

.header_head {
  background: #fff;
  padding: 20px;
  border-radius: 1rem;
  box-shadow: 0 0px 10px rgba(0, 0, 0, 0.05);
}
.form-control {
  border-radius: 0.8rem;
  min-height: 45px;
  font-size: 14px;
}
.header_head h4 {
  margin-bottom: 0px;
}
.btn-head {
  margin-top: -5px;
}
.form-group {
  margin-bottom: 10px;
  position: relative;
}
input[type="file"] {
  display: none;
}
.custom-file-upload {
  border: 1px dashed #ccc;
  padding: 4.3rem 2rem;
  border-radius: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  text-align: center;
}
.btn-submit.btn-phoenix-secondary {
  padding: 0.9rem;
}
.blogs_add_sec {
  box-shadow: 0 0px 10px rgba(0, 0, 0, 0.05);
  background: #fff;
  border-radius: 1rem;
  padding: 40px;
}

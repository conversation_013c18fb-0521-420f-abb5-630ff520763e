import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { SetupComponent } from './common/setup/setup.component';

const routes: Routes = [
  {path:'', component:LayoutComponent, children:[
    {path:'setup', component:SetupComponent,data: { name: 'Setup' }}
  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class GoldenGateRoutingModule { }

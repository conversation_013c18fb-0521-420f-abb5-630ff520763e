<div class="v-pageName">{{pageName}}</div>
<div class="text-right">
    <div class="body-header-button">
        <button (click)="testcycleexecute()" class="btn btn-upload"><span class="mdi mdi-cloud-upload-outline "></span>
            &nbsp; Execute </button>
    </div>
</div>
<div class="row">
<div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
    <div class="qmig-Form">
        <label class="form-label d-required" for="name">Source Connection </label>
        <select class="form-select" #Selection (change)="selectsourceconnection(Selection.value)"
            formControlName="Connection">
            <option selected value="">Source Connection </option>
            @for(list of sourceData; track list; ){
            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
            }
        </select>
        @if ( f.Connection.touched && f.Connection.invalid) {
        <p class="text-start text-danger mt-1">
            @if (f.Connection.errors.required) {source Connection is Required }
        </p>
        }

    </div>
</div>

<div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
    <div class="form-group">
        <div class="qmig-Form">
            <label class="form-label d-required" for="name">Target
                Connection</label>
            <select class="form-select" #tgtSelection (change)="testCaseSelections(tgtSelection.value)"
                formControlName="TargetConnection">
                <option selected value="">Target Connection</option>
                @for(list of targetData; track list; ){
                <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                }
            </select>
            @if ( f.TargetConnection.touched && f.TargetConnection.invalid) {
            <p class="text-start text-danger mt-1">
                @if (f.TargetConnection.errors.required) {Target Connection is
                Required }
            </p>
            }
        </div>
    </div>
</div>
<div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
    <div class="form-group">
        <label class="form-label d-required" for="name">Value</label>
        <select class="form-select" (change)="selectValue(checkValue.value)" #checkValue formControlName="valuee">
            <option selected value="">Select Value</option>
            <option value="yes">YES</option>
            <option value="no">NO</option>
        </select>
        @if ( f.valuee.touched && f.valuee.invalid) {
        <p class="text-start text-danger mt-1">
            @if (f.valuee.errors.required) {Value is Required }
        </p>
        }
    </div>
</div>
<div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
    <div class="form-group mb-0 mt-4 pt-1">
        <button (click)="OpenMap()" class="btn btn-sign btn-formAdd" [disabled]="cycleForm.invalid"
            class="btn btn-upload w-100" data-bs-toggle="offcanvas" data-bs-target="#demo"><i
                class="mdi mdi-cloud-upload-outline btn-icon-prepend"></i>&nbsp;Mapping</button>
    </div>
</div>


<div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" >
    <div class="row">
        <div class="col-md-12">
            <div class="form-check form-check-flat form-check-primary mt-4">
                <label class="form-check-label ">
                    <input type="checkbox" #chck (click)="checkboxselect($event)" class="form-check-input"> <i
                        class="input-helper"></i><i class="input-helper"></i> Continue
                </label>
            </div>
        </div>
    </div>
</div>


<div>
    <hr />
    <div class="row">
        <div class="col-md-8">
            <h3 class="main_h">Target </h3>
        </div>
        <div class="col-md-2" [hidden]="!chooseExport">
            <button type="button" class="btn btn-upload w-100" (click)="generatePDF()">Convert to PDF </button>
        </div>
        <div class="col-md-2" [hidden]="!chooseExport">
            <button class="btn btn-sign w-100" (click)="exportexcel()" type="button"><i
                    class="mdi mdi-cloud-upload-outline btn-icon-prepend"></i>&nbsp; Export
                Excel </button>
        </div>
    </div>
    <hr />
    <div class="row">
        <div class="col-md-6">
            <div class="row">
                <div class="col-md-5 col-sm-3">
                    <div class="form-group mb-0">
                        <input type="text" id="start" class="form-control" placeholder="BatchID start">
                    </div>
                </div>
                <div class="col-md-5 col-sm-3">
                    <div class="form-group mb-0">
                        <input type="text" id="end" class="form-control" placeholder="BatchID end">
                    </div>
                </div>
                <div class="col-md-2 col-sm-3">
                    <button class="btn btn-sign" type="button" (click)="selectRange()">Fetch</button>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="custom_search cs-r">
                <span class="mdi mdi-magnify"></span>
                <input type="text" placeholder="Search Document" class="form-control" [(ngModel)]="datachange1"
                    (keyup)="onKey()">
            </div>
        </div>
    </div>


    <table class="table table-hover qmig-table">
        <thead>
            <tr>
                <!-- <th class="py-1">
                                                <div class="form-check form-check-flat form-check-primary mt-m10">
                                                    <label class="form-check-label"
                                                          [(ngModel)]="masterSelected">
                                                        <input type="checkbox" 
                                                            name="list_name" value="m1" (change)="checkUncheckAll($event)"
                                                            class="form-check-input" /> <i class="input-helper"></i>
                                                    </label>
                                                </div>
                                            </th> -->
                <th style="width: 50px;">
                    <div class="form-check m-0">
                        <label class="form-check-label">
                            <input type="checkbox" (click)="checkUncheckAll($event)" [(checked)]="masterSelected"
                                class="form-check-input"> <i class="input-helper"></i>
                        </label>
                    </div>
                </th>


                <th class="status-col">
                    <form class="status-form">
                        <select class="form-select form-small" #bid
                            (change)="FunctionalTestingTgtTable(bid.value);usertestCaseselect()">
                            <option selected value="">batch id list</option>
                            <!-- <option *ngFor="let iter of batchId" value="{{iter.iteration}}">{{iter.iteration}}</option> -->
                            @for(iter of batchId; track iter; ){
                            <option value="{{iter.iteration}}">{{iter.iteration}}
                            </option>
                            }
                        </select>
                    </form>
                </th>
                <th class="status-col">
                    <form class="status-form">
                        <select class="form-select form-small" #tid
                            (change)="PrjTgttestcasehdrSelect(tid.value);usertestCaseselect()">
                            <option selected value="">User Testcase list</option>
                            <!-- <option *ngFor="let ite of usertestCases" value="{{ite.values}}">{{ite.option}}</option> -->
                            @for(ite of usertestCases; track ite; ){
                            <option value="{{ite.values}}">{{ite.option}}
                            </option>
                            }
                        </select>
                    </form>
                </th>
                <th>Test Case ID</th>
                <th>Object Name</th>
                <th class="status-col">
                    <select class="form-select form-small" (change)="filterStatus(tgtStatus.value)" #tgtStatus>
                        <option value="All">All</option>
                        <option value="Success">Success</option>
                        <option value="Failed">Failed</option>
                    </select>
                </th>
                <th>Last Exec Date</th>
                <th>Duration Time</th>
                <th>Last Exec Cycle</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            @for (documents of FunctionalTestingTgtTableData | searchFilter:
            datachange1| paginate: { itemsPerPage: pi, currentPage: p } ; track
            documents; let i = $index) {

            <tr>
                <!-- <td class="py-1">
                                                <div class="form-check form-check-flat form-check-primary mt-m10">
                                                    <label class="form-check-label">
                                                        [(ngModel)]="documents.isSelected"
                                                        <input type="checkbox" 
                                                            name="list_name" value="{{documents.tgt_test_id}}"
                                                            (click)="onChange(documents,documents.tgt_test_id, $event)"
                                                            #checkValue class="form-check-input" />
                                                        <i class="input-helper"></i>
                                                    </label>
                                                </div>
                                            </td> -->
                <td>
                    <div class="form-check mt-33">
                        <label class="form-check-label">
                            <input type="checkbox" class="form-check-input" #checkValue
                                (click)="onChange(documents,documents.tgt_test_id, $event)"
                                [checked]="documents.isSelected">
                            <i class="input-helper"></i>
                        </label>
                    </div>
                </td>
                <td>{{documents.iteration}}</td>
                <td>{{documents.user_testcase_id}}</td>
                <td>{{documents.tgt_test_id}}</td>
                <td>{{documents.object_name}}</td>
                <td hidden>{{documents.object_signature}}</td>
                <td>{{documents.testcase_status}}</td>
                <td>{{documents.last_execution_dt}}</td>
                <td>{{documents.execution_time}}</td>
                <td>{{documents.test_cycles}}</td>
                <td>
                    <button class="btn btn-download" data-bs-toggle="offcanvas" data-bs-target="#test"
                        (click)="updateRecord(documents)">
                        <span class="mdi mdi-pencil btn-icon-prepend"></span>
                    </button>
                </td>
            </tr>
            }
        </tbody>
    </table>

    <div class="custom_pagination">
        <pagination-controls (pageChange)="p = $event"></pagination-controls>
    </div>
</div>
<div class="offcanvas offcanvas-end" tabindex="-1" id="test">
    <div class="offcanvas-header">
        <h4 class="main_h">Test cycle Details </h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="TestCaseForm">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="object_type">Execute Type</label>
                            <input class="form-control" type="text" id="objecttype_name" placeholder=""
                                formControlName="objecttype_name">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-0">
                            <label class="d-block" for="name">Execution Time</label>
                            <input class="form-control" type="text" id="execution_time"
                                formControlName="exec_time_limit" placeholder="">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="d-block" for="name">Execute Statement</label>
                            <textarea class="form-control" type="text" id="name" placeholder="Execution Statement Name"
                                formControlName="objectname" rows="5"></textarea>
                            <!-- <div class="alert"
                                    *ngIf="getControl.code_object_name.invalid && (getControl.code_object_name.dirty || getControl.code_object_name.touched)">
                                    <div *ngIf="getControl.code_object_name.errors?.required">
                                      Execution Statement is required.
                                    </div>
                                  </div> -->
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group mb-0">
                            <label class="d-block" for="name">Initial Error messsage</label>
                            <input class="form-control" type="text" id="error_log" formControlName="initial_error_log"
                                placeholder="">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group mb-0">
                            <label class="d-block mt-3" for="name">Error messsage</label>
                            <input class="form-control" type="text" id="error_log" formControlName="error_log"
                                placeholder="">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="text-right mt-3">
                            <button (click)="saveBtn(this.TestCaseForm.value)" class="btn btn-sign ">Save
                                <!-- <i
                                      *ngIf="Spin" class="fa fa-spinner fa-spin"></i> -->
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

</div>

<!-- Test Cycle Mapping  -->

<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
    <div class="offcanvas-body p-5">
        <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="form addcontact">
                            <h5 class="card-title">Test Cycle Mapping</h5>
                            <hr>
                            <form class="form add_form" [formGroup]="mappingForm">
                                <div class="row" [hidden]="showSource">
                                    <table class="table table-striped display custom-table" id="example"
                                        style="width: 60px">
                                        <thead>
                                            <tr>
                                                <th>Service DB</th>
                                                <th>Source Connection</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for (sname of ServiceNames; track sname; ){
                                            <tr>
                                                <td>{{ sname }}</td>
                                                <td>
                                                    <select class="form-select ml-0" #srcSelection
                                                        formControlName="sconnection"
                                                        (change)="selectSourceMapValues(sname,srcSelection.value)">
                                                        <option selected value="">Source Connection</option>
                                                        @for(list of sourceData; track list;){
                                                        <option value="{{list.Connection_ID}}">{{list.conname}}

                                                        </option>
                                                        }
                                                    </select>
                                                </td>
                                            </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="row">
                                    <table class="table table-striped display custom-table" id="example"
                                        style="width:100%">
                                        <thead>
                                            <th>Service DB</th>
                                            <th>Target Connection</th>
                                        </thead>
                                        <tbody>
                                            @for (sname of ServiceNames; track sname;){
                                            <tr>
                                                <td>{{sname}} </td>
                                                <td>
                                                    <select class="form-select ml-0" #tgtSelection
                                                        formControlName="tconnection"
                                                        (change)="selectTargetMapValues(sname,tgtSelection)">
                                                        <option selected value="">Target Connection</option>
                                                        @for(list of targetData; track list;){
                                                        <option value="{{list.Connection_ID}}">{{list.conname}}
                                                            {{list.dbconname}}
                                                        </option>
                                                        }
                                                    </select>
                                                </td>
                                            </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="text-right">
                                            <button class="btn btn-sign " (click)="onCancel()">Cancel </button>
                                            &nbsp;
                                            <button class="btn btn-sign " (click)="OnSave()">Save
                                                @if(Add_Spin){<app-spinner />}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
import { Component } from '@angular/core';
import { HotToastService } from '@ngxpert/hot-toast';
import { TestingService } from '../../../../services/testing.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgxPaginationModule } from 'ngx-pagination';
import { deletefiles, docList, documentsList } from '../../../../models/interfaces/types';
import { ActivatedRoute } from '@angular/router';


declare let $: any;

@Component({
  selector: 'app-logfile-upload',
  standalone: true,
  imports: [SpinnerComponent, SearchFilterPipe, NgxPaginationModule, FormsModule, ReactiveFormsModule],
  templateUrl: './logfile-upload.component.html',
  styles: ``


})

export class LogfileUploadComponent {
  pageTitle: string = "Documents"
  spinner: boolean = false
  fileResult: any;
  Spin: any;
  projectId: any;
  logfile: any = [];
  data: any;
  uploadfileSpin: boolean = false;
  p: number = 1;
  pi: number = 10;
  moduleName: any;
  batchId: any;
  processBtn: boolean = false;
  FileNameGet: any = [];
  file: any;
  searchText: string = '';
  pageNumber: number = 1;
  pageNumber1: number = 1;
  spin_delete: any;
  fileAdd: boolean = false;
  projectDocuments: any;
  projectDocumentFilter: any;
  project_name: any;
  getRole: any;
  fileResponse: any;
  fileStatus: any;
  spin_process: any;
  timelimit: number = 1;
  pcheckValue: any;
  projectDocs: any;
  isDataAvailable: boolean = false;
  ExecututionFiles: any
  datachange4: any
  plog: any
  iterationselected: any;

  uniqueCheckValue: string = 'ALL'



  /*-- Document--*/
  uploadForm = this.formBuilder.group({
    file: ['', [Validators.required]],
  })
  selectFile: any;
  fileName: string = '';
  pageName: string = ''


  constructor(private toast: HotToastService, private testingService: TestingService, public formBuilder: FormBuilder,
    private route: ActivatedRoute) {
    this.project_name = localStorage.getItem('project_name');
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') || 'null');
    this.pageName = this.route.snapshot.data['name'];

  }

  ngOnInit(): void {
    this.filterExecutionReports();

  }
  get f(): any {
    return this.uploadForm.controls;
  }

  // sendValue($event: any): void {
  //   this.readThis($event.target);
  // }

  // readThis(inputValue: any): void {
  //   const file: File = inputValue.files[0];
  //   this.fileName = inputValue.files[0].name;
  //   const myReader: FileReader = new FileReader();
  //   myReader.onloadend = () => {
  //     this.fileResult = myReader.result;
  //   };
  //   myReader.readAsDataURL(file);
  // }

  /* Reports */
  onKey() {
    this.p = 1;
    this.pageNumber = 1;
    this.pageNumber1 = 1
  }

  /* Upload files */
  uploadBtn() {
    this.UpLoadFilesToSubFolder();
    //this.getprojectDocumentsDetail();

  }

  /* Upload File */
  UpLoadFilesToSubFolder() {
    const BinaryFile = {
      projectID: this.projectId.toString(),
      containerName: 'qmigratorfiles' + this.projectId,
      folderName: "TestCases",
      fileName: this.fileName,
      content: this.fileResult.split(',')[1],
      subFolder: "TestCaseLogs"
    };
    this.Spin = true;
    // this.testingService.UpLoadFilesToSubFolder(BinaryFile).subscribe(
    //   (data: any) => {
    //     this.Spin = false;
    //     if (data.message == "File uploaded Successfully") {
    //       this.toast.success(data.message);
    //     }
    //   },
    //   () => {
    //     this.Spin = false;
    //     this.toast.error('Something went wrong!');
    //   }
    // );

  }

  /* Document Details */
  // getprojectDocumentsDetail() {
  // const requestObj = {
  //   projectID: this.projectId.toString(),
  //   containerName: "qmigratorfiles" + this.projectId,
  //   folderName: "TestCases",
  //   subFolderName: "TestCaseLogs",
  // };
  // this.testingService.Files(requestObj).subscribe((data:any) => {
  //   this.logfile = data;
  //   // this.ReportData = data;
  // });

  //}


  /* Filter Report */

  filterExecutionReports() {
    var path = 'PRJ' + this.projectId + 'SRC'
    // /mnt/eng/PRJ1167SRC/Schemas.csv
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
      //console.log(this.ExecututionFiles)
    })
  }

  // sortValue(value: any) {
  //   // this.pi = value;
  //   // if (value == 'all') {
  //   //   this.pi = this.logfile.length;
  //   //   this.pi = this.ReportData.length;
  //   // }
  //   this.pi = value;
  //   if (value == 'all') {
  //     this.pi = this.logfile.length;
  //     this.p = 1;
  //   }
  //   else if (value == '20') {
  //     this.p = 1;} 
  //   else if (value == '50') {
  //     this.p = 1;} 
  //   else if (value == '100') {
  //     this.p = 1; }
  //} 


  // proSortValue(pdata: any) {
  //   this.pcheckValue = pdata
  //   //console.log(this.pcheckValue)
  // }

  // currentlyCheked(checkvalue: any, isChecked: any) {
  //   this.processBtn = true;
  //   //console.log(checkvalue)
  //   //console.log(isChecked.target.checked)
  //   if (isChecked.target.checked) {
  //     this.FileNameGet.push(checkvalue);
  //   } else {
  //     const index = this.FileNameGet.indexOf(checkvalue);
  //     this.FileNameGet.splice(index, 1);
  //   }
  //   //console.log(this.FileNameGet)
  // }



  /* Delete File*/
  DeleteClientFile(fileName: any) {
    const obj = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "TestCases",
      subFolderName: "TestCaseLogs",
      fileName: fileName,
    }
    this.spin_delete = true
    this.testingService.deleteFiles(obj).subscribe(
      (data: any) => {
        this.spin_delete = false
        if (data.message === fileName + " Deleted") {
          this.toast.success(data.message);
          //this.getprojectDocumentsDetail()
        }
        if (data.message === fileName + " DoesNot Exist") {
          this.toast.error(data.message);
        }
        if (data.message === fileName + " DoesNot Exist Or Already Deleted") {
          this.toast.error(data.message);
        }
      },
      () => {
        this.spin_delete = false
        this.toast.error('Something happened');
      }
    );
  }

  // openPopup() {
  //   this.uploadForm.reset();
  //   this.fileName = ''
  // }


  /*--- File upload    ---*/

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
  }


  /* Upload File*/
  uploadFile() {
    //console.log("uploaded")
    this.uploadfileSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', "projectdoc");
    this.testingService.uploadDocuments(formData).subscribe(
      (response: any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        //this.getDocuments();
        this.toast.success(response.message)
        $('#demo').offcanvas('hide');
      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.toast.error('Something went wrong')
        //this.openPopup()
        $('#demo').offcanvas('hide');
      }
    )
  }

  /*--- Download file   ---*/

  downloadFile(title: any) {
    this.testingService.downloadFiles(title).subscribe((blob: any) => {
      this.fileResponse = blob;
      //console.log(blob)
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = title.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
    })
  }

  /*--- Delete Project Document   ---*/
  //  deleteFiles(path: string) {
  //   this.testingService.deleteFile(path).subscribe({
  //     next: (data: deletefiles) => {
  //       //console.log(data)
  //       this.fileStatus = data.message
  //       //this.getDocuments();
  //       this.toast.success(this.fileStatus)
  //     },
  //     error: (error) => {
  //       this.toast.error(error.message)
  //     },
  //   });
  // }

  /*--- Get Documents   ---*/

  // getDocuments() {
  //   const path = "projectdoc/"
  //   this.testingService.getFiles(path).subscribe((data: documentsList) => {
  //     this.projectDocuments = data
  //     this.projectDocumentFilter = data
  //   })
  // }

  currentlyCheked(checkvalue: any, isChecked: any) {
    this.processBtn = true;
    if (isChecked.target.checked) {
      this.FileNameGet.push(checkvalue);
    } else {
      let index = this.FileNameGet.indexOf(checkvalue);
      this.FileNameGet.splice(index, 1);
    }
  }
  uniqueCheked(uniqueValue: any, isChecked: any) {
    if (isChecked.target.checked) {
      this.uniqueCheckValue = uniqueValue
    } else {
      this.uniqueCheckValue = "ALL"
    }
  }

  proSortValue(pdata: any) {
    this.pcheckValue = pdata
  }
}

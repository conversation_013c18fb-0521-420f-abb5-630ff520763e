import { Component, ViewChild } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartOptions, ChartType } from 'chart.js';
import { CommonModule, DatePipe } from '@angular/common';
import { DashboardService } from '../../../../services/dashboard.service';

@Component({
  selector: 'app-performance1',
  standalone: true,
  imports: [TabsComponent, NgChartsModule, CommonModule],
  templateUrl: './performance1.component.html',
  providers: [DatePipe], // Provide DatePipe explicitly
  styles: ``
})
export class Performance1Component {  
  @ViewChild('chart1') chart1: BaseChartDirective | undefined;
  @ViewChild('chart2') chart2: BaseChartDirective | undefined;
  @ViewChild('chart3') chart3: BaseChartDirective | undefined;
  @ViewChild('chart4') chart4: BaseChartDirective | undefined;
  @ViewChild('chart5') chart5: BaseChartDirective | undefined;

  /*--- Dropped & Created Indexes Chart Data ---*/

  public DCIndexChartLabels = ['18 Nov', '25 Nov', '2 Dec', '9 Dec'];
  public DCIndexChartLegend = true;
  public DCIndexChartData = [
    {
      data: [91, 87, 80, 50],
      label: ' Created Indexes',
      borderColor:'#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [82,79,72,45],
      label: ' Dropped Indexes',
      borderColor:'#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
  ];
  public DCIndexChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode:'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

  /*--- Unused Indexes Vs Used Indexes Chart Data ---*/

  public UNUIndexChartLabels = ['18 Nov', '25 Nov', '2 Dec', '9 Dec'];
  public UNUIndexChartLegend = true;
  public UNUIndexChartData = [
    {
      data: [91, 87, 80, 50],
      label: ' Used Indexes',
      borderColor:'#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [82,79,72,45],
      label: ' Unused Indexes',
      borderColor:'#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public UNUIndexChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode:'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

  /*--- No issues Vs Resolved Issues by Log date Chart Data ---*/

  public NORIssuesChartLabels = ['5 Dec','6 Dec', '7 Dec ', '8 Dec', '9 Dec', '10 Dec', '11 Dec'];
  public NORIssuesChartLegend = true;
  public NORIssuesChartData = [
    {
      data: [2,10,42,12,16,24,13],
      label: ' Total Issues',
      borderColor:'#4c00b5',
      backgroundColor: 'rgb(76 0 181 / 20%)',
      hoverBackgroundColor: '#8b36ff',
      fill:true
    },    
    {
      data: [0,5,40,10,10,18,9],
      label: ' No Issues',
      borderColor:'#a25eff',
      backgroundColor: 'rgb(162 94 255 / 20%)',
      hoverBackgroundColor: '#8b36ff',
      fill:true
    },
    {
      data: [2,5,2,2,6,6,4],
      label: ' Resloved',
      borderColor:'#ff7900',
      backgroundColor: 'rgb(255 121 0 / 10%)',
      hoverBackgroundColor: '#4c00b5',
      fill:true
    }
  ];
  public NORIssuesChartOptions: ChartOptions<'line'> = {
    responsive: true,
    interaction: {
      mode:'index'
    },
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    plugins: {
      filler: {
        propagate: false,
      },
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
        }
      }
    }
  };
  
  /*--- source vs extraction % ---*/
  
  public sourceVSChartLabels = [ 'Aug', 'Sep', 'Oct', 'Nov'];
  public sourceVSChartLegend = true;
  public sourceVSChartData = [
    {
      data: [2,1,1,2],
      label: ' Recommended Partitions',
      borderColor:'#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public sourceVSChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode:'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

  /*--- Index Validation Total by Index Type ---*/

  public IValChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'Actual ',
      'Duplicate',
      'Recommended',
    ],
    datasets: [
      {
        data: [94.14,4.32, 1.54],
        label: 'Daily count of un used indexes',
        backgroundColor: [
            '#8b36ff',
            '#ff7900',
            '#f0f0f0'
        ],        
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,
        
      }
    ]
  };
  public IValChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 55,
    spacing: 0,
    elements: {
      arc: {
        borderWidth: 0,
        borderColor: '#fff',
        borderRadius: 0 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        position: 'right',
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        mode: 'index',  // Use 'index' to show the tooltip for the entire dataset
        position: 'nearest', // Position the tooltip nearest to the data point
      }
    }

  };
  public IValChartLegend = true;

  dashboardDetails:any

  constructor(private readonly dbService: DashboardService, private datePipe: DatePipe) { }

  ngOnInit(): void {
    this.getDashboardDetails()
  }

  /*--- Fetch Iteration ---*/
  totalIndex:number = 0
  createdIndex:number = 0
  droppedIndex:number = 0
  
  unUsedIndex:number = 0
  usedIndex:number = 0
  unUsedIndexPer:number = 0
  
  totalIssues:number = 0
  noIssues:number = 0
  reslovedIssues:number = 0
  getDashboardDetails() {

    this.DCIndexChartLabels = []
    this.DCIndexChartData[0].data = []
    this.DCIndexChartData[1].data = []

    this.UNUIndexChartLabels = []
    this.UNUIndexChartData[0].data = []
    this.UNUIndexChartData[1].data = []


    this.NORIssuesChartLabels = []
    this.NORIssuesChartData[0].data = []
    this.NORIssuesChartData[1].data = []
    this.NORIssuesChartData[2].data = []

    this.sourceVSChartLabels = []
    this.sourceVSChartData[0].data = []

    
    this.IValChartData.labels = []
    this.IValChartData.datasets[0].data = []

    this.dbService.getPerfDashboardList('76').subscribe((data: any) => {
      this.dashboardDetails = data
      this.dashboardDetails.duplicateindex.forEach( (el:any) => {
        this.DCIndexChartLabels.push(this.datePipe.transform(el.date, 'dd MMM') || '')
        this.DCIndexChartData[0].data.push(el.createdindex)
        this.DCIndexChartData[1].data.push(el.droppedindex)
        this.totalIndex = this.totalIndex + el.totalindex
        this.createdIndex = this.createdIndex + el.createdindex
        this.droppedIndex = this.droppedIndex + el.droppedindex
      });
      this.dashboardDetails.indexusage.forEach( (el:any) => {
        this.UNUIndexChartLabels.push(this.datePipe.transform(el.date, 'dd MMM') || '')
        this.UNUIndexChartData[0].data.push(el.usedindex)
        this.UNUIndexChartData[1].data.push(el.unusedindex)
        this.unUsedIndex = this.unUsedIndex + el.unusedindex
        this.usedIndex = this.usedIndex + el.usedindex
        this.unUsedIndexPer = this.unUsedIndexPer + el.unusedpercentage
      });
      
      this.dashboardDetails.longrunning.forEach( (el:any) => {
        this.NORIssuesChartLabels.push(this.datePipe.transform(el.logdate, 'dd MMM') || '')
        this.NORIssuesChartData[0].data.push(el.querycount)
        this.NORIssuesChartData[1].data.push(el.noissues)
        this.NORIssuesChartData[2].data.push(el.resolveissues)
        this.totalIssues = this.totalIssues + el.querycount
        this.noIssues = this.noIssues + el.noissues
        this.reslovedIssues = this.reslovedIssues + el.resolveissues
      });
      
      this.dashboardDetails.partionresponse.forEach( (el:any) => {
        this.sourceVSChartLabels.push(el.month)
        this.sourceVSChartData[0].data.push(el.recommendedpartition)
      })
      this.dashboardDetails.indexMonitoring.forEach( (el:any) => {
        this.IValChartData.labels?.push(el.indextype)
        this.IValChartData.datasets[0].data.push(el.percentage)
      })
      this.chart1?.update();   
      this.chart2?.update();   
      this.chart3?.update();    
      this.chart4?.update();     
      this.chart5?.update();   
    })
  }
}

<div class="body-main ChatBoxWidth">
    <div class="row">
        <div class="col-xl-3">
            <select class="form-select" #tgV (change)="Getdbname(tgV.value)">
                <option selected value="">Select a Target Connection</option>
                @for(list of targetConnections();track list; ){
                <option value="{{ list.Connection_ID }}"> {{ list.conname }}</option>
                }
            </select>
        </div>
        <div class="offset-3 col-xl-6 justify-content-end d-flex">
            <button class="btn btn-clear" type="button" (click)="onClearHistory()"> <img src="../../../../assets/images/delete-agent.svg" alt="delete" /> Clear </button>
        </div>
    </div>
    <div class="chatInterface">
        <div class="chatBox">
            @for (entry of History(); track entry.question) {
            <div class="chat-entry">
                <div class="question">{{ entry.question }}</div>
                @if(entry.answer){
                    <div class="answer">
                        <markdown [data]="entry.answer"></markdown>
                    </div>
                }@else{
                    <div class="bouncing-dots">
                        <div class="dot"></div>
                        <div class="dot delay-1"></div>
                        <div class="dot delay-2"></div>
                    </div>
                }
            </div>
            }@empty{
                <div class="align-items-center d-flex chatHeight">
                    <p class="text-muted text-center w-100 display-6">Start a conversation...</p>
                </div>
            }
            <div #chatEnd></div>
        </div>
    </div>
    <div class="chatFooter">
        <form (submit)="handleSubmit(); $event.preventDefault()" class="form-box">
            <input
                type="text"
                [(ngModel)]="question"
                name="question"
                class="form-control"
                placeholder="Type your question..."
            />
            <button type="submit" class="btn btn-sign" [disabled]="!question"> <span class="mdi mdi-send"></span> Submit </button>
        </form>
    </div>
</div>
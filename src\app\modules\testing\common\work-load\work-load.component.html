<div class="v-pageName">{{pageName}}</div>
<!-- WorkLoad UI -->
<div class="body-main mt-3">
    <div class="qmig-card">
        <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-heading">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                        Work Load
                    </button>
                </h2>
                <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="workLoadForm">
                            <div class="row">
                                <!-- Operations Dropdown -->
                                <div class="col-md-3 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Operation</label>
                                        <select class="form-select" formControlName="operationType"
                                            [(ngModel)]="operationType">
                                            <option selected value="">Select Operation</option>
                                            @for(operation of operations;track operation; ){
                                            <option value="{{ operation.value }}"> {{ operation.option }} </option>
                                            }
                                        </select>
                                        @if ( f.operationType.touched && f.operationType.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.operationType.errors.required) {Operation is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- Runno Dropdown -->
                                <div class="col-md-3 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="iteration">Run No</label>
                                        <select class="form-select" [(ngModel)]="runNo" formControlName="runNo"
                                            (change)="getSchemasList(runNo)">
                                            <option selected value="">Select Run No</option>
                                            @for(list of runInfo;track list; ){
                                            <option value="{{list.iteration}}"> {{ list.dbschema}} </option>
                                            }
                                        </select>
                                        @if ( f.runNo.touched && f.runNo.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.runNo.errors.required) {Run No is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- Connections List -->
                                <div class="col-md-3 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source Connection
                                            Name</label>
                                        <select class="form-select" [(ngModel)]="ConnectionID"
                                            formControlName="connectionName">
                                            <option selected value="">Select Connection Name</option>
                                            @for(clist of ConsList;track clist; ){
                                            <option value="{{clist.Connection_ID}}"> {{ clist.conname }} </option>
                                            }
                                        </select>
                                        @if ( f.connectionName.touched && f.connectionName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.connectionName.errors.required) {Source Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- SchemaList  -->
                                <div class="col-md-3 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Schema Name</label>
                                        <ng-select [items]="schemaList" [multiple]="true" bindLabel="schemaname"
                                            id="schema_id" placeholder='Select Schema Name' [selectableGroup]="true"
                                            bindValue="schemaname" formControlName="schema" [(ngModel)]="schemaName">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                        </ng-select>
                                        @if ( f.schema.touched && f.schema.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.schema.errors.required) {Schema Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- ObjectType Dropdown-->
                                <div class="col-md-3 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Object Type</label>
                                        <select class="form-select" [(ngModel)]="objectType"
                                            formControlName="objectType">
                                            <option selected value="">Select Connection Name</option>
                                            @for(oType of ObjectTypes;track oType; ){
                                            <option value="{{oType.option}}"> {{ oType.option }} </option>
                                            }
                                        </select>
                                        @if ( f.objectType.touched && f.objectType.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.objectType.errors.required) {Object Type is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- Execute Button -->
                                <div class="col-md-3 col-xl-3 mt-4">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100"
                                            (click)="TriggerWebTestingComand(workLoadForm.value)"
                                            [disabled]="workLoadForm.invalid">
                                            <i class="mdi mdi-cog-play"></i> Execute
                                            @if(spin){<app-spinner />}</button>
                                    </div>
                                </div>

                            </div>
                        </form>

                    </div>
                </div>
            </div>

            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingTest">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                        Execution Status
                    </button>
                </h2>
                <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-6">
                                <h3 class="main_h py-4 ps-3">
                                    Work Load Status
                                    <button class="btn btn-sync" (click)="getreqTableData()">
                                        @if(ref_spin){
                                        <app-spinner />
                                        }@else{
                                        <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </h3>
                            </div>
                            <div class="col-12 col-sm-6 col-md-6">
                                <div class="custom_search cs-r my-3 me-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Status" class="form-control"
                                        [(ngModel)]="datachange1" (keyup)="onKey()">
                                </div>
                            </div>
                            <!-- <div class="row">
                                <div class="col-12 col-sm-6 col-md-6"> -->
                            <!-- <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Operation</label>
                                        <select class="form-select" [(ngModel)]="operationType" #op1
                                            (change)="getreqTableData(op1.value)">
                                            <option selected value="">Select Operation</option>
                                            @for(operation of operations;track operation; ){
                                            <option value="{{ operation.value }}"> {{ operation.option }} </option>
                                            }
                                        </select>
                                    </div> -->
                            <!-- </div> -->

                            <!-- search status of WorkLoad status -->
                            <!-- <div class="col-12 col-sm-6 col-md-6">
                                    <div class="custom_search cs-r my-3 me-3">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Status" class="form-control"
                                            [(ngModel)]="datachange1" (keyup)="onKey()">
                                    </div>
                                </div> -->
                            <!-- </div> -->
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table" id="example" style="width: 100%">
                                <thead>
                                    <tr>
                                        <th>RunNo</th>
                                        <th>Connection</th>
                                        <th>Operation</th>
                                        <th>Schema Name</th>
                                        <th>Object Type</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (con of tabledata |searchFilter: datachange1| paginate: { itemsPerPage: 10,
                                    currentPage: page2 ,id:'second'} ;track con) {

                                    <tr>
                                        <td>{{con.run_id}}</td>
                                        <td>{{ con.conname }}</td>
                                        <td>{{ con.operation_name }}</td>
                                        <td>{{ con.schemaname }}</td>
                                        <td>{{ con.objecttype }}</td>
                                        <td>{{con.created_dt}}</td>
                                        <td>{{con.updated_dt}}</td>
                                        <td>
                                            @switch (con.status) {
                                            @case ('I') {
                                            <span>Initialize</span>
                                            }
                                            @case ('P') {
                                            <span>Pending</span>
                                            }
                                            @default {
                                            <span>Completed</span>
                                            }
                                            }
                                        </td>
                                        <td>
                                            <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                                <span class="mdi mdi-delete btn-icon-prepend"></span>
                                            </button>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty list of Documents</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>

                            </table>
                        </div>
                        <!-- pagination -->
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="page2 = $event" id="second"></pagination-controls>
                        </div>

                    </div>
                </div>
            </div>
            <!--Execution Logs-->
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingThree">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                        Execution Logs
                    </button>
                </h2>
                <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                    data-bs-parent="#accordionFlushExample">
                    <div class="accordion-body">
                        <!-- <div class="card mt-1 pt-1">
                                        <div id="SME" class="collapse" data-parent="#accordion"> -->

                        <form class="form qmig-Form">
                            <div class="row">
                                <div class="col-md-4 col-xl-3 ">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Run
                                            No.</label>
                                        <select class="form-select" #code (change)="selectIterforlogs(code.value)">
                                            <option selected value="">Select Run Number </option>
                                            @for( list of runnoForReports;track list;){
                                            <option value="{{ list.iteration}}">{{list.dbschema}}</option>
                                            }
                                        </select>

                                    </div>
                                </div>
                                <div class="col-md-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Operation</label>
                                        <select class="form-select" formControlName="operationType" #oplog
                                            [(ngModel)]="operationType" (change)="operationLogs(oplog.value)">
                                            <option selected value="">Select Operation</option>
                                            @for(operation of operations;track operation; ){
                                            <option value="{{ operation.value }}"> {{ operation.option }} </option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 col-xl-5 offset-md-1">
                                    <div class="custom_search mt-4 mb-3 mr-auto ">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Logs " aria-controls="example"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>File Name</th>
                                        <th>Created Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(logs of ExecututionFiles |searchFilter: datachangeLogs|paginate:{
                                        itemsPerPage: 10, currentPage: page3, id:'Four'};
                                        track logs; let i=$index){
                                    <tr>
                                        <td>{{10*page3+i+1-10}}</td>
                                        <td>{{logs.fileName }}</td>
                                        <td>{{logs.created_dt }}</td>
                                        <td>
                                            <button class="btn btn-download" (click)="downloadFile(logs)">
                                                <span class="mdi mdi-cloud-download-outline"></span>
                                            </button>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty list of Reports</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="page3 = $event" id="four"></pagination-controls>
                        </div>
                        
                        <!-- pagination -->
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="page3 = $event" id="Four">
                            </pagination-controls>
                        </div>
                    </div>
                    <!-- </div>
                                    </div> -->
                </div>
            </div>
            <!-- Deployment Logs-->
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                        Deployment Logs
                    </button>
                </h2>
                <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <div class="accordion-body">
                        <!-- <h3 class="main_h">Reports & Logs</h3> -->

                        <div class="row">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-3 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="targtetConnection">Run
                                                No</label>
                                            <select class="form-select" formControlName="operationType" #iter
                                                (change)="selectIteration(iter.value)">
                                                <option selected value="">Select Run No</option>
                                                @for(role of runnoForReports ;track role; ){
                                                <option value="{{ role.iteration }}"> {{ role.dbschema
                                                    }}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <!-- <div class="col-md-3 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required"
                                                for="targtetConnection">Operation</label>
                                            <select class="form-select" formControlName="operationType" #opdeplog
                                                [(ngModel)]="operationType" (change)="operationDepLogs(opdeplog.value)">
                                                <option selected value="">Select Operation</option>
                                                @for(operation of operations;track operation; ){
                                                <option value="{{ operation.value }}"> {{ operation.option }}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div> -->
                                    <div class="col-md-3 col-xl-3"></div>
                                    <div class="col-md-3 offset-md-1">
                                        <div class="custom_search mt-4">
                                            <span class="mdi mdi-magnify"></span>
                                            <input type="text" placeholder="Search Reports" aria-controls="example" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>File Name</th>
                                        <th>Created Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(validrepo of assessmentFiles |searchFilter:
                                datachange|paginate:{
                                itemsPerPage: 10, currentPage: p, id:'third'};
                                track validrepo; let i=$index){
                                    <tr>
                                        <td>{{10*p+i+1-10}}</td>
                                        <td>{{validrepo.fileName }}</td>
                                        <td>{{validrepo.created_dt }}</td>
                                        <td>
                                            <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                                <span class="mdi mdi-cloud-download-outline"></span>
                                            </button>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty list of Reports</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                        </div>
                        <!-- <div class="row">
                            <div class="col-md-12 mt-2">
                                @for(validrepo of assessmentFiles |searchFilter:
                                datachange|paginate:{
                                itemsPerPage: 10, currentPage: p, id:'third'};
                                track validrepo; let i=$index){
                                <div class="client_contact mt-0">
                                    <div class="row">
                                        <div class="col-md-12 px-0">
                                            <ul>
                                                <li>
                                                    <b text="Download" class="toltip">
                                                        <i class="fas fa-cloud-download-alt"></i> :
                                                    </b>
                                                    <a data-toggle="tooltip" data-placement="bottom" download=""
                                                        id="validrepo" class="btn btn-download table-data-fixed"
                                                        (click)="downloadFile(validrepo)"
                                                        title="{{ validrepo.fileName }}">{{
                                                        validrepo.fileName }}</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                }

                            </div>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                        </div> -->
                    </div>

                </div>
            </div>
            <!-- Reports -->
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne4" aria-expanded="false" aria-controls="flush-collapseOne">
                        Reports
                    </button>
                </h2>
                <div id="flush-collapseOne4" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <div class="accordion-body">
                        <!-- <h3 class="main_h">Reports & Logs</h3> -->

                        <div class="row">

                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Run
                                        No</label>
                                    <select class="form-select" #iter4
                                        (change)="SelectIterationForReports(iter4.value)">
                                        <option selected value="">Select Run No</option>
                                        @for(role of runnoForReports ;track role; ){
                                        <option value="{{ role.iteration }}"> {{ role.dbschema
                                            }}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Operation</label>
                                    <select class="form-select" #oprep [(ngModel)]="operationType"
                                        (change)="operationReports(oprep.value)">
                                        <option selected value="">Select Operation</option>
                                        @for(operation of operations;track operation; ){
                                        <option value="{{ operation.value }}"> {{ operation.option }}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-3 offset-md-1">
                                <div class="custom_search mt-4">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Reports" aria-controls="example" />
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>File Name</th>
                                        <th>Created Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(validrepo4 of workLoadReport |searchFilter:
                                    datachange4|paginate:{
                                    itemsPerPage: 10, currentPage: p3, id:'fourth'};
                                    track validrepo4; let i=$index){
                                    <tr>
                                        <td>{{10*p3+i+1-10}}</td>
                                        <td>{{validrepo4.fileName }}</td>
                                        <td>{{validrepo4.created_dt }}</td>
                                        <td>
                                            <button class="btn btn-download" (click)="downloadFile(validrepo4)">
                                                <span class="mdi mdi-cloud-download-outline"></span>
                                            </button>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty list of Reports</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p3 = $event" id="fourth"></pagination-controls>
                        </div>
                    </div>
                </div>

            </div>

            <!-- <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingTwo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                        Page Activity Log
                    </button>
                </h2>

                <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                    data-bs-parent="#accordionFlushExample">
                    <div class="accordion-body">
                        <div class="qmig-card">
                            <div class="row">
                                <div class="col-sm-6 col-md-4 col-xl-2">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Operation</label>
                                        <select class="form-select" [(ngModel)]="operationType" #op
                                            (change)="GetIndividualLogs(op.value)"
                                            [ngModelOptions]="{standalone: true}">
                                            <option selected value="">Select Operation</option>
                                            @for(operation of operations;track operation; ){
                                            <option value="{{ operation.value }}"> {{ operation.option }} </option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <form class="form table-form">
                                        <div class="d-flex show-sort form-group mb-0">
                                            <label class="d-block form-sel_rn" for="Number">Run
                                                No</label>
                                            <select class="form-select" #workload
                                                (change)="SelectIteration(workload.value)">
                                                <option selected value="">
                                                    Select Run Number
                                                </option>
                                                @for(role of runNoData ;track role; ){
                                                <option value="{{ role.iteration }}"> {{ role.dbschema }}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </form>
                                </div>


                                <div class="col-md-4">
                                    <div class="custom_search mb-3 mr-auto">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search " aria-controls="example" />
                                    </div>
                                </div>
                            </div>
                            <div class="qmig-card">
                                <table class="table table-hover qmig-table">
                                    <thead>
                                        <tr>
                                            <th>SNo</th>
                                            <th>Run No</th>
                                            <th>Date and Time</th>
                                            <th>Task</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for (documents of logdata |searchFilter: datachange2| paginate: { itemsPerPage:
                                        10,
                                        currentPage: page , id: 'first'} ;track documents;){
                                        <tr>
                                            <td>{{page*10+$index+1-10}}</td>
                                            <td>{{ documents.iteration }}</td>
                                            <td>{{ documents.activity_date }}</td>
                                            <td>{{ documents.migtask }}</td>
                                        </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            <div class="custom_pagination">
                                <pagination-controls (pageChange)="page = $event" id="first">
                                </pagination-controls>
                            </div>
                        </div>

                    </div>
                </div>
            </div> -->

        </div>
    </div>
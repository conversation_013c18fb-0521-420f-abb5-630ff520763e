<div class="v-pageName">{{pageName}}</div>

<!---Reports & Logs---->
<div class="qmig-card">
    <!-- <h3 class="main_h px-3 pt-3">Extraction </h3> -->
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Code Extraction
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <!---Code Extraction List --->
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="codeextractionForm">
                            <div class="row">
                                <!-- Run No -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Run No</label>
                                        <select class="form-select" formControlName="runNumber"
                                            (change)="getRunNumber(getrun.value)" #getrun>
                                            <option>Select a Run No</option>
                                            @for(list of runnoExtraction; track list;){
                                            <option value="{{ list.iteration }}">{{ list.iteration }}</option>
                                            }
                                        </select>
                                        @if ( f.runNumber.touched && f.runNumber.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.runNumber.errors.required) {Run No is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                @if(codeextractionForm.value.runNumber != "New"){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Extraction
                                            Operation</label>
                                        <select class="form-select" formControlName="exeop" #exeopera
                                            (change)="selectExeOperation(exeopera.value)">
                                            <option>Select a Operation</option>
                                            @for(exe of extractionOpeartion; track exe;){
                                            <option value="{{ exe.value }}">{{ exe.option }}</option>
                                            }
                                        </select>
                                        @if ( f.exeop.touched && f.exeop.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.exeop.errors.required) {Extraction Operation is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                }
                                <!-- Connection Name -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Connection
                                            Name</label>
                                        <select class="form-select" formControlName="connectionName" #Myselect (change)="
                          operationSelect(Myselect.value);
                          selectedfiletype(Myselect.value);
                          getAllObjectTypes()
                        " aria-label="Default select example">

                                            <option>Select a Connection Name</option>
                                            @for(Conslist of ConsList;track Conslist; ){
                                            <option value="{{Conslist.Connection_ID}}">{{Conslist.conname}}</option>
                                            }
                                        </select>
                                        @if ( f.connectionName.touched && f.connectionName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.connectionName.errors.required) {Connection Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- Operation -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Operation</label>
                                        <select class="form-select" formControlName="operation"
                                            (change)="selectOperation(MyOp.value)" #MyOp>
                                            <option>Select Operation</option>
                                            @for(list of opeList;track list;){
                                            <option value="{{list.operation_category }}">
                                                {{ list.operation_category }}
                                            </option>
                                            }
                                        </select>
                                        @if ( f.operation.touched && f.operation.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.operation.errors.required) {Operation is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- Schema Name -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">
                                            @if(migtypeid=="31"){Database} @else{Schema} Name </label>
                                        <ng-select groupBy="type" [selectableGroup]="true"
                                            (change)="changeFn(selectedItems)" formControlName="schema"
                                            [items]="schemaList" [multiple]="true" bindLabel="schema_name"
                                            [closeOnSelect]="false" bindValue="schema_name" [(ngModel)]="selectedItems">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schema_name}}
                                            </ng-template>
                                            <!-- <ng-template ng-multi-label-tmp let-items="items">
                                                @if(items.length == schemaList.length){<div class="ng-value">
                                                    All Selected
                                                </div>
                                                }
                                                @else{
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.schema_name}}
                                                </div>
                                                }
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template> -->
                                        </ng-select>
                                        @if ( f.schema.touched && f.schema.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.schema.errors.required) {{{schemalable}} Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Object type</label>
                                        <!-- <select class="form-select" #obj (change)="selectObj(obj.value)"
                                            formControlName="objectType">
                                            <option selected value="">Select Object type</option>
                                            @if(migtypeid == '48'){
                                            <option value="Pipeline">Pipeline</option>
                                            }@else{
                                            @for(list of objectType;track list; ){
                                            <option value="{{list.object_type}}">{{list.object_type}}</option>
                                            }
                                            }
                                        </select> -->
                                        <ng-select groupBy="type" [selectableGroup]="true"
                                            (change)="selectObj(selectedobjecttypes)"
                                            formControlName="objectType" [items]="objectType" [multiple]="true"
                                            bindLabel="object_type" [closeOnSelect]="false" bindValue="object_type"
                                            [(ngModel)]="selectedobjecttypes">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" value={{item.object_type}} />
                                                {{item.object_type}}
                                            </ng-template>
                                        </ng-select>
                                        @if ( f.objectType.touched && f.objectType.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.objectType.errors.required) {ObjectType is Required }
                                        </p>
                                        }
    
                                    </div>
                                </div>
                                <!-- Check Details -->
                                <div class="row">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group mt-1">
                                            <div class="text-right">
                                                <button class="btn btn-upload w-100" (click)="openModal1()"
                                                    data-bs-toggle="offcanvas" data-bs-target="#demo1"> <span
                                                        class="mdi mdi-checkbox-marked-circle-outline"
                                                        aria-hidden="true"></span>Extraction with
                                                    File</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group mt-1">
                                            <div class="text-right">
                                                <button class="btn btn-upload w-100"
                                                    (click)="openModal(this.codeextractionForm.value)"
                                                    data-bs-toggle="offcanvas" data-bs-target="#demo"
                                                    [disabled]="codeextractionForm.invalid"> <span
                                                        class="mdi mdi-checkbox-marked-circle-outline"
                                                        aria-hidden="true"></span>Check
                                                    Details</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Execution Status
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <!--- codeexcration List --->
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-7 offset-5 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                            <button class="btn btn-sync" (click)="getreqTableData()">
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>

                    <!-- Code Extraction Status Table -->
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Run No</th>
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>Operation Category</th>
                                    <th>{{schemalable}} Name</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of tabledata| searchFilter: datachange1| paginate:{ itemsPerPage: piA,
                                currentPage: page2,
                                id:'first'};
                                track con;) {
                                <tr>
                                    <!-- <td>{{page2*piA+$index+1-piA}}</td> -->
                                    <td>{{con.iteration_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{con.operation_name}}</td>
                                    <td>{{ con.operation_category }}</td>
                                    <td>{{ con.schema_name }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        {{con.status =='Error'?con.error:con.status}}
                                    </td>

                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>

                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="page2 = $event" id="first"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Execution Logs
                </button>
            </h3>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-sm-4 col-md-4 col-xl-4 mt-3">
                            <div class="form-group">
                                <select class="form-select" #code (change)="selectIterforlogs(code.value)">
                                    <option selected value="">
                                        Select Run Number
                                    </option>
                                    @for( list of runnoForReports;track list;){
                                    <option value="{{ list.iteration}}">{{list.iteration}}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-2 col-md-2 col-xl-2">
                            <div class="col-4 col-sm-4 col-md-4 col-xl-4 mt-2 ps-1">
                                
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-6 col-xl-6 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="getUpdatedRunNumber1()">
                                @if(getRunSpin1){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(logs of ExecutionLogs |searchFilter:
                            datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: page3, id:'Four'};
                            track logs){
                            <tr>
                                <td>{{ page3*10+$index+1-10 }}</td>
                                <td>{{logs.fileName }}</td>
                                <td>{{logs.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(logs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page3 = $event" id="Four">
                    </pagination-controls>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Reports
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <!-- Run Number -->

                        <div class="row">
                            <div class="col-12 col-sm-3 col-md-3 mt-3">
                                <div class="form-group">
                                    <select class="form-select" (change)="selectIteration(iter.value)" #iter>
                                        <option>Select Run Number</option>
                                        @for( list of runnoForReports;track list;){
                                        <option value="{{ list.iteration }}">
                                            {{ list.iteration }}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-3 col-md-2"></div>
                            <div class="col-12 col-sm-6 col-md-7 d-flex">
                                <div class="custom_search cs-r my-3 me-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Extraction Reports" class="form-control"
                                        [(ngModel)]="datachange" (keyup)="onKey()">
                                </div>
                                <button class="btn btn-sync" (click)="getUpdatedRunNumber()">
                                    @if(getRunSpin){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- downloadFile -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.NO</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(validrepo of extractionFiles |searchFilter: datachange|paginate:{
                            itemsPerPage: pi, currentPage: p, id:'second'};
                            track validrepo;){
                            <tr>
                                <td>{{p*pi+$index+1-pi}}</td>
                                <td>{{validrepo.fileName }}</td>
                                <td>{{validrepo.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty list of Reports</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="p = $event" id="second"></pagination-controls>
                </div>
            </div>
        </div>

        <!---Code Extraction--->
        <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
            <div class="offcanvas-header">
                <h4 class="main_h"> Code Extration</h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
            </div>
            <!--Connection Name--->
            <div class="offcanvas-body">
                <form class="form qmig-Form checkForm">
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Run Number</label>
                        : &nbsp;{{ currentRunNumber }}
                    </div>
                    @if(this.currentRunNumber!='New'){
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Extraction Operation</label>
                        : &nbsp;{{ exeoperation }}
                    </div>}
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Connection Name</label>
                        : &nbsp;{{ conName }}
                    </div>
                    <!--Operation-->
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Operation</label>
                        : &nbsp;{{ OpName }}
                    </div>
                    <!--Schema Name-->
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Schema Name</label>
                        : &nbsp;{{ schemaName.toString() }}
                    </div>
                    <!-- <div class="col-12 col-md-4 col-xl-9 mt-4"> -->
                    <div class="form-group">
                        <div class="form-check form-check-flat form-check-primary mt-3">
                            <label class="form-check-label form-label">
                                <input type="checkbox" class="form-check-input" (click)="getCheckValue($event)"
                                    formControlName="refreshFlag">
                                <i class="input-helper"></i> Restart
                            </label>
                        </div>
                    </div>
                    <!-- </div> -->
                    <!--Execute-->
                    <div class="form-group">
                        <div class="body-header-button">
                            <!-- projectConRunTblInserts(codeextractionForm.value, false)" -->
                            <button class="btn btn-upload w-100 me-1" (click)="AssessmentCommand()">
                                Execute @if(runinfospins){<app-spinner />}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="offcanvas offcanvas-end" tabindex="-1" id="demo1">
            <div class="offcanvas-header">
                <h4 class="main_h"> Upload File</h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
            </div>
            <!--Connection Name--->
            <div class="offcanvas-body">
                <form class="form add_form mt-3" [formGroup]="uploadForm">
                    <div class="form-group">
                        <div class="custom-file-upload">
                            <input class="form-control" type="file" id="formFile" (change)="onFileSelected($event)"
                                formControlName="file">
                            <div class="file-upload-mask">
                                @if (fileName == '') {
                                <img src="assets/images/fileUpload.png" alt="img" />
                                <p>Drag and drop DDL file here or click add DDL file </p>
                                <button class="btn btn-upload"> Add File </button>
                                } @else{
                                <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                    <p> {{ fileName }} </p>
                                </div>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <!-- <div class="col-md-3 col-xl-3 offset-md-9"> -->
                        <button class="btn btn-upload w-100" (click)="AssessmentCommandwithFile()"
                            [disabled]="uploadForm.invalid"> <span class="mdi mdi-file-plus"></span>
                            Upload & Extract@if(upload_spin){<app-spinner />}
                        </button>
                        <!-- </div> -->
                    </div>
                </form>
            </div>
        </div>
    </div>
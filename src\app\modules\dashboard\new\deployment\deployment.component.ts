import { Component } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { DashboardService } from '../../../../services/dashboard.service';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartOptions, ChartType } from 'chart.js';

interface OperationList {
  iteration: string,
  conname: string,
  schemaname: string,
  total_count: string,
  converted: string,
  percentage_passed: number
}


@Component({
  selector: 'app-deployment',
  standalone: true,
  imports: [TabsComponent, NgChartsModule],
  templateUrl: './deployment.component.html',
  styles: ``
})
export class NewDeploymentComponent {
  
  connectionsList: Array<String> = [];
  schemaList: Array<String> = [];
  iterationList: Array<String> = [];
  operationsList: OperationList[] = [];
  operationsCopy: OperationList[] = this.operationsList
  operationsTable: OperationList[] = this.operationsList

  connectionID: string = '';
  connectionValue: string = '';
  schemaId: string = '';
  schemaName: string = '';
  iterationId: string = ''


  /*---- Search bar ---*/
  searchText: string = '';
  /*---- line Charts ----*/


  // Custom plugin for displaying percentage in the center
  centerTextPlugin = {
    id: 'centerTextPlugin',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'myDoughnutChart') {
      const { ctx, chartArea: { width, height } } = chart;

      ctx.save();

      // Assuming you are showing the percentage of the first dataset
      const total = chart.config.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
      const value = chart.config.data.datasets[0].data[0];
      const percentage = ((value / total) * 100).toFixed(1) + '%';

      // Define style for the text
      ctx.font = 'bold 25px Geist';
      ctx.fillStyle = '#333';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Set position to center of the chart
      const centerX = width / 2;
      const centerY = height / 2 + 20;

      // Draw the text in the center
      ctx.fillText(percentage, centerX, centerY);
      ctx.restore();
    }
  }
  };

  public lineChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'Source Count',
      'Extraction Count',
    ],
    datasets: [
      {
        data: [95, 5],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
            '#8b36ff',
            '#f0f0f0'
        ],        
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,
        
      }
    ]
  };
  public lineChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }

  };
  public lineChartLegend = false;
  public lineChartPlugins = [this.centerTextPlugin];


  /*--- No issues Vs Resolved Issues by Log date Chart Data ---*/

  public NORIssuesChartLabels = ['Code Object', 'Table ', 'View'];
  public NORIssuesChartLegend = true;
  public NORIssuesChartData = [
    {
      data: [180, 50, 30],
      label: ' Matching Count ',
      borderColor:'#ff7900',
      backgroundColor: 'rgb(255 121 0 / 10%)',
      hoverBackgroundColor: '#4c00b5',
      fill:true
    },    
    {
      data: [0,0,0],
      label: ' Not Matching Count ',
      borderColor:'#a25eff',
      backgroundColor: 'rgb(162 94 255 / 20%)',
      hoverBackgroundColor: '#8b36ff',
      fill:true
    }
  ];
  public NORIssuesChartOptions: ChartOptions<'line'> = {
    responsive: true,
    interaction: {
      mode:'index'
    },
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    plugins: {
      filler: {
        propagate: false,
      },
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
        }
      }
    }
  };

    /*--- source vs extraction % ---*/
  
    public sourceVSChartLabels = ['Hrpay', 'Ggdb2'];
    public sourceVSChartLegend = true;
    public sourceVSChartData = [
      {
        data: [120, 105],
        label: ' Matching Count',
        borderColor:'#a25eff',
        backgroundColor: '#a25eff',
        hoverBackgroundColor: '#8b36ff',
        barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
        maxBarThickness: 70, // Set max bar thickness if auto-calculating
      },
      {
        data: [95, 83],
        label: ' Not Matching Count',
        borderColor:'#cf30fc',
        backgroundColor: '#f75989',
        hoverBackgroundColor: '#f5346e',
        barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
        maxBarThickness: 70, // Set max bar thickness if auto-calculating
      }
    ];
    public sourceVSChartOptions: ChartOptions<'bar'> = {
      responsive: true,
      scales: {
        x: {
          beginAtZero: true,
        },
        y: {
          beginAtZero: true,
        },
      },
      interaction: {
        mode:'index'
      },
      plugins: {
        legend: {
          display: true  // This line disables the legend
        },
        tooltip: {
          enabled: true,
          usePointStyle: true,
          titleSpacing: 5,
          backgroundColor: '#ffffff', // White background
          titleColor: '#333333', // Dark title color
          bodyColor: '#666666', // Lighter body text color
          borderColor: '#cccccc', // Light gray border
          borderWidth: 1, // Thin border
          cornerRadius: 8, // Rounded corners
          padding: 10, // Padding inside the tooltip
        }
      }
    };
  
  constructor(private dbService: DashboardService) { }

  ngOnInit(): void {
    this.getOperationsList()
  }

  /*--- Fetch Iteration ---*/
  getOperationsList() {
    const obj = {
      dbname: 'null',
      Conid: 'null',
      schemaid: 'null',
      iteration: 'null',
      option: 0
    }
    this.dbService.getOperationList(obj).subscribe((data: any) => {
      this.operationsList = JSON.parse(JSON.stringify(data['Table1']));
      this.operationsList.forEach((item: any) => {
        this.connectionsList.push(item.conname)
      })
      this.operationsTable = data['Table1']
      this.operationsCopy = this.operationsList
      this.connectionsList = [...new Set(this.connectionsList)]
    })
  }

  /*--- Fetch Schemas ---*/
  getSchema(value: string) {
    this.schemaList = []
    this.schemaId = ''
    this.connectionID = value
    this.operationsCopy.filter((el: any) => {
      if (value == el.conname) {
        this.schemaList.push(el.schemaname)
      }
      this.schemaList = [...new Set(this.schemaList)]
    })
    this.operationsTable = this.operationsList.filter((fl: OperationList) => { return fl.conname == value })
  }


  /*--- Fetch Iteration ---*/
  getIteration(value: string) {
    this.iterationList = []
    this.schemaId = value
    this.iterationId = ''
    this.operationsCopy.filter((el: any) => {
      if (value == el.schemaname && this.connectionID == el.conname) {
        this.iterationList.push(el.iteration)
      }
      this.iterationList = [...new Set(this.iterationList)]
    })
    this.operationsTable = this.operationsList.filter((fl: OperationList) => { return fl.schemaname == value && fl.conname == this.connectionID })
  }


  /*--- Fetch Iteration ---*/
  getOperations(op: any) {
    this.iterationId = op
  }

}

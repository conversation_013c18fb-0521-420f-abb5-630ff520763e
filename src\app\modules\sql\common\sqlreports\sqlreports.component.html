<!--- Bread Crumb --->
<div class="body-header">
  <div class="row">
    <div class="col-md-6 col-xl-6">
      <div class="pageName">
        <img src="assets/images/connections.svg" alt="documents">
        <span>SQL Reports</span>
      </div>
    </div>
  </div>
  <div class="body-main mt-4">
    <div class="qmig-card">
      <div class="qmig-card-body">
        <form class="form qmig-Form">
          <div class="row">
            <div class="col-md-3 col-xl-3">
              <div class="form-group">
                <label class="form-label d-required">SQL Category</label>
                <select class="form-select" formControlName="ddlCategory"
                  (change)="ddlCategorycheckedChnaged(Myselect.value)" #Myselect>
                  <option selected value="">Select SQL Connection</option>
                  @for(list of categoryData;track list;){
                  <option value="{{ list.scriptcategory }}">{{ list.scriptcategory}}</option>
                  }
                </select>
              </div>
            </div>
            <div class="col-md-3 col-xl-3">
              <div class="form-group">
                <label class="form-label d-required" for="name">iteration</label>
                <select class="form-select" formControlName="ddlCategory" #Myselect1
                  (change)="ddlIterationcheckedChnaged(Myselect.value)">
                  <option selected value="">iteration</option>
                  @for(ConsList of iterationData;track ConsList;){
                  <option value="{{ ConsList.iteration }}">{{ ConsList.iteration}}</option>
                  }
                </select>
              </div>
            </div>
            <div class="col-md-2 col-xl-2 mt-4 ">
              <div class="body-header-button">
                <button class="btn btn-upload w-100" (click)="downloadFile()"> Get Report
                </button>
              </div>
            </div>

          </div>
        </form>
      </div>
    </div>
  </div>

</div>
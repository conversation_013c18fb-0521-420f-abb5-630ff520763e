<div class="v-pageName">{{pageName}}</div>

    <!--Select Dag File  -->

    <div class="qmig-card">
        <h3 class="main_h px-3 pt-3">Select Dag File</h3>
        <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="getForm" (ngSubmit)="uploadFile()">
                <div class="form-group">
                    <label for="formFile" class="form-label d-required">Upload Dag File </label>
                    <div class="custom-file-upload">
                        <input class="form-control" formControlName="document" (change)="onFileSelected1($event)"
                            type="file" id="formFile">
                        <div class="file-upload-mask">
                            @if (fileName == '') {
                                <img src="assets/images/fileUpload.png" alt="img" />         
                            <p>Drag and drop Dag file here or click and add Dag file</p>
                            <button class="btn btn-upload"> Add Dag File </button>
                            }@else{
                                    <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                        <p> {{ fileName }} </p>
                                    </div>
                                }
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-9">

                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <button class="btn btn-upload w-100"> <span class="mdi mdi-file-plus"></span>
                                Upload@if(getSpin){<app-spinner />}</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
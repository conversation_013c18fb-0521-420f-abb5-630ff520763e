<div class="v-pageName">{{pageName}}</div>
<!--- Documents List --->
<div class="body-main">
    <div class="row">
        <div class="col-12 col-md-6 col-xl-6">
            <div class="custom_search ms-0">
                <span class="mdi mdi-magnify"></span>
                <input type="text" placeholder="Search Report" class="form-control" [(ngModel)]="searchText"
                    (keyup)="onKey()">
            </div>
        </div>
        <div class="col-6 col-md-6 col-xl-6">
            <div class="body-header-button">
                <button class="btn btn-upload" data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                        class="mdi mdi-file-document-plus-outline"></span> Upload Report</button>
            </div>
        </div>
    </div>

    <!--- Reports List --->
    <div class="qmig-card mt-4">
        <h3 class="main_h px-3 pb-1 pt-3">List of Reports</h3>
        <!-- <div class="row">            
            <div class="col-md-6">
            </div>
            <div class="col-md-6">
                <div class="form-group mb-0">
                    <select class="form-select form-small">
                        <option selected value="">Type of Reports</option>
                        <option selected value="">AWR</option>
                        <option selected value="">Code Scan</option>
                    </select>
                </div>
            </div>
        </div> -->
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>S.NO</th>
                        <th>Report Name</th>
                        <th>Created Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @for (documents of assessmentFiles | searchFilter: searchText | paginate: { itemsPerPage: 10,
                    currentPage: pageNumber } ; track documents; let i = $index) {
                    <tr>
                        <td>{{pageNumber*10+i+1-10}}</td>
                        <td>{{documents.fileName }}</td>
                        <td>{{documents.created_dt}}</td>
                        <td>
                            <button class="btn btn-download" (click)="downloadFile(documents)">
                                <span class="mdi mdi-cloud-download-outline"></span>@if(spinDownload){<app-spinner />}
                            </button>
                            <button class="btn btn-delete" (click)="deleteFiles(documents.filePath)">
                                <span class="mdi mdi-delete"></span>
                            </button>
                        </td>
                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="4">
                            <p class="text-center m-0 w-100">Empty list of Reports</p>
                        </td>
                    </tr>
                    }
                </tbody>
               </table>
        </div>
        @if(projectDocuments.length > 0){
        <div class="custom_pagination mt-0">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
        }
    </div>
</div>


<!--- Upload Documnets  --->
<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
    <div class="offcanvas-header">
        <h4 class="main_h">Upload Document</h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="uploadForm" (ngSubmit)="uploadFile()">
            <div class="form-group">
                <label for="formFile" class="form-label d-required">Upload Deployment File </label>
                <div class="custom-file-upload">
                    <input class="form-control" type="file" id="formFile" formControlName="file"
                        (change)="onFileSelected($event)">
                    <div class="file-upload-mask">
                        @if (fileName == '') {
                        <img src="assets/images/fileUpload.png" alt="img" />
                        <p>Drag and drop deployment file here or click add deployment file </p>
                        <button class="btn btn-upload"> Add File </button>
                        }
                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                            <p> {{ fileName }} </p>
                        </div>
                    </div>
                </div>
                @if ( f.file.touched && f.file.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (f.file.errors.required) { File is required }
                </p>
                }
            </div>
            <div class="form-group">
                <button class="btn btn-upload w-100" [disabled]="uploadForm.invalid"> <span
                        class="mdi mdi-file-plus"></span> Upload File @if(uploadfileSpin){<app-spinner />}</button>
            </div>
        </form>
    </div>
</div>
import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { CommonService } from '../../../../services/common.service';
import { Title } from '@angular/platform-browser';
import { AIagentComponent } from '../../../../shared/components/aiagent/aiagent.component';


interface ChatEntry {
  question: string;
  answer: string;
}

@Component({
  selector: 'app-agent',
  standalone: true,
  imports: [AIagentComponent],
  templateUrl: './agent.component.html',
  styles: ``
})
export class AgentComponent {
  
  pageName: string = '';
  projectId: string = '';

  chatHistory: ChatEntry[] = [];
  

  @ViewChild('chatEnd') chatEndRef!: ElementRef;

  targetConnections:any[] = []

  constructor(
    private readonly titleService: Title,
    private readonly toast: HotToastService,
    private readonly service: CommonService,
    private readonly route: ActivatedRoute
  ) {
    this.pageName = this.route.snapshot.data['name'];
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getTgtList()
  }

getTgtList() {
  this.service.getConList(this.projectId.toString()).subscribe((data: any) => {
    this.targetConnections = data['Table1'].filter((item: any) => {
      return item.migsrctgt == 'T' && item.conname != '';
    });
  });
}

  onQuestionSubmit(event: { question: string; targetConnection: string }) {
    const newEntry: ChatEntry = { question: event.question, answer: '' };
    this.chatHistory.push(newEntry);

    const obj = { query: event.question, target_connection_id: Number(event.targetConnection)};
    this.service.getAgent(obj, 'Performance').subscribe({
      next: (data: any) => {
        newEntry.answer = data.formatted_display;
        setTimeout(() => this.scrollToBottom(), 100);
      },
      error: (error) => {
        this.toast.error('Failed to load chat history');
        console.error('Error fetching chat history:', error);
      }
    });
  }

  onClearChatHistory() {
    if (confirm('Are you sure you want to clear the chat history?')) {
    this.service.clearHistory('', 'Performance').subscribe( {
      next: (data: any) => {
        this.toast.success('Chat history cleared successfully')
        this.chatHistory = []
      },
      error: (error) => {
        this.toast.error('Failed to clear chat history');
      }
    } )
  }
  }


  scrollToBottom() {
    setTimeout(() => {
      if (this.chatEndRef) {
        this.chatEndRef.nativeElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  }
}

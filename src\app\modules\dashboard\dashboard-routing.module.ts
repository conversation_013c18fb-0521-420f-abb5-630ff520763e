import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { HomeComponent } from './common/home/<USER>';
import { AssessmentComponent } from './common/assessment/assessment.component';
import { CodeObjectsComponent } from './common/code-objects/code-objects.component';
import { StorageObjectsComponent } from './common/storage-objects/storage-objects.component';
import { PerformanceComponent } from './common/performance/performance.component';
import { CodeExtractionComponent } from './common/code-extraction/code-extraction.component';
import { Performance1Component } from './common/performance1/performance1.component';
import { KotakAssessmentComponent } from './common/kotak-assessment/kotak-assessment.component';

import { NewHomeComponent } from './new/home/<USER>';
import { NewAssessmentComponent } from './new/assessment/assessment.component';
import { NewCodeExtractionComponent } from './new/code-extraction/code-extraction.component';
import { NewCodeObjectsComponent } from './new/code-objects/code-objects.component';
import { NewDataMigrationComponent } from './new/data-migration/data-migration.component';
import { NewDbaComponent } from './new/dba/dba.component';
import { NewDeploymentComponent } from './new/deployment/deployment.component';
import { NewPerformanceTunningComponent } from './new/performance-tunning/performance-tunning.component';
import { NewStorageObjectsComponent } from './new/storage-objects/storage-objects.component';
import { NewTestingComponent } from './new/testing/testing.component';
import { ValidationsComponent } from './new/validations/validations.component';
import { DataMigrationNewComponent } from './new/data-migration-new/data-migration-new.component';

const routes: Routes = [
  // {
  //   path: '', component: LayoutComponent, children: [
  //     {path:'', redirectTo: '/dashboard/summary', pathMatch: 'full'},
  //     {path:'summary', component: HomeComponent },
  //     {path:'codeExtraction', component:AssessmentComponent},
  //     {path:'codeObjects', component:CodeObjectsComponent},
  //     {path:'storageObjects', component:StorageObjectsComponent},
  //     {path:'performance', component:PerformanceComponent},
  //     {path:'new_performance', component:Performance1Component},
  //     {path:'code-extraction', component:CodeExtractionComponent},
  //     {path:'kotakAssessment', component:KotakAssessmentComponent}    
  //   ]
  // },
  {
    path:'', component:LayoutComponent, children:[
      {path:'', redirectTo:'/dashboard/newSummary', pathMatch:'full'},
      {path:'newSummary', component:NewHomeComponent},
      {path:'Assessment', component:NewAssessmentComponent},
      {path:'newCodeExtraction', component:NewCodeExtractionComponent},
      {path:'newCodeObjects', component:NewCodeObjectsComponent},
      {path:'newDataMigration', component:NewDataMigrationComponent},
      {path:'newDba', component:NewDbaComponent},
      {path:'newDeployment', component:NewDeploymentComponent},
      {path:'newPerformance', component:NewPerformanceTunningComponent},
      {path:'newStorageObjects', component:NewStorageObjectsComponent},
      {path:'newTesting', component:NewTestingComponent},
      {path:'validation', component:ValidationsComponent},
      {path:'DataMigrationnew', component:DataMigrationNewComponent},
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DashboardRoutingModule { }

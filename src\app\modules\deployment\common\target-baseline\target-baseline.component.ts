import { Component } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { TabsComponent } from '../tabs/tabs.component';
import { DeploymentService } from '../../../../services/deployment.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-target-baseline',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, TabsComponent],
  templateUrl: './target-baseline.component.html',
  styles: ``
})
export class TargetBaselineComponent {

  pi: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;
  fileData: any;
  datachange: any;
  TgtUpdateTgt: any;
  logfile: any;
  project_name: any
  projectId: any
  getRole: any;
  isGit: boolean = true;
  TargetConn: any;


  // @ViewChild(PlyrComponent)
  // plyr: PlyrComponent | undefined;

  // // or get it from plyrInit event
  // player: Plyr | undefined;
  poster = 'assets/videos/qmig_ui.jpg';
  UserInfo: any;
  targetbaseform: any;
  targetdeltaform: any;
  SourceConn: any;
  SourceCo: any;
  schemaList: any = [];
  pageName: string = '';
  datachanges: any;
  datachanges1: any;
  datachanges2: any;
  datachanges3: any;
  pageNumber: number = 1;
  p: number = 1;
  page: number = 1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  datachange1:any;
  pages: number = 1;
  sorcedeltaform: any;

  constructor(public fb: FormBuilder,
    private route: ActivatedRoute,
    public deployment: DeploymentService,
    private toast: HotToastService
  ) {
    this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    let userJson = localStorage.getItem('userData') as string;
    this.UserInfo = JSON.parse(userJson);
    this.pageName = this.route.snapshot.data['name'];
  }
  dropdownList = [];
  selectedItems = [];

  schemaName: any = [];
  ngOnInit(): void {
    this.targetbaseform = this.fb.group({
      runnumber: ['', [Validators.required]],
      conname: ['', [Validators.required]],
      targetcon: ['', [Validators.required]],
      scheman: ['', [Validators.required]],
      sourcefile: [''],
      objectt: ['', [Validators.required]],
      sourcecon:['', [Validators.required]],
      // scrun: ['', [Validators.required]],
      // scschema: ['', [Validators.required]],
    });
    this.targetdeltaform = this.fb.group({
      scrun: ['', [Validators.required]],
      scschema: ['', [Validators.required]],
    });
    this.sorcedeltaform = this.fb.group({
      scrun: ['', [Validators.required]],
      scschema: ['', [Validators.required]],
    });
    this.getInfraSelect();
    this.getBlobfileData();
    this.getRunNumber();
    this.getRunNumber();
    this.getRunNumbers();
    this.getFiles();
    this.getPrjExeLogSelectTask("null");
    this.GetRequestTableData();
  }

  // select schema data
  selectschema(data: any) {
    this.schemaName = data
    //console.log(data)
  }
  runNumbers: any;
  getRunNumber() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbers = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }
  blobfile: any;
  getBlobfileData() {
    // this.project.GetBlobfileByFileName("target_baseline.mp4").subscribe((data: any) => {
    //   this.blobfile = data.fileUrl;
    // })
  }
  hide_files: any = true;
  hide_db: any = true;
  selectExtraction: any;
  selectedDropdown(data: any) {
    if (data == "1") {
      this.hide_files = true;
      this.hide_db = true;
      this.isGit = false;
    }
    if (data == "2") {
      this.hide_files = true;
      this.hide_db = false;
      this.isGit = true;
      this.targetbaseform.controls['runnumber'].setValidators([Validators.required]);
      this.targetbaseform.controls['conname'].setValidators([Validators.required]);
      this.targetbaseform.controls['targetcon'].setValidators([Validators.required]);
      this.targetbaseform.controls['sourcecon'].setValidators([Validators.required]);
      this.targetbaseform.controls['scheman'].setValidators([Validators.required]);
      this.targetbaseform.controls['objectt'].setValidators([Validators.required]);
      this.targetbaseform.controls['sourcefile'].clearValidators();
      this.GetDBConnections();
    }
    if (data == "3") {
      this.hide_files = false;
      this.hide_db = true;
      this.isGit = true;
      this.targetbaseform.controls['runnumber'].setValidators([Validators.required]);
      this.targetbaseform.controls['conname'].setValidators([Validators.required]);
      this.targetbaseform.controls['sourcecon'].setValidators([Validators.required]);
      this.targetbaseform.controls['targetcon'].setValidators([Validators.required]);
      this.targetbaseform.controls['scheman'].setValidators([Validators.required]);
      this.targetbaseform.controls['sourcefile'].setValidators([Validators.required]);
      this.targetbaseform.controls['objectt'].setValidators([Validators.required]);
      
      this.GetDBConnections();
      this.getuploadedFiles();
    }
    else {
      this.selectExtraction = data;
      this.targetbaseform.controls['runnumber'].updateValueAndValidity();
      this.targetbaseform.controls['conname'].updateValueAndValidity();
      this.targetbaseform.controls['sourcecon'].updateValueAndValidity();
      this.targetbaseform.controls['targetcon'].updateValueAndValidity();
      this.targetbaseform.controls['scheman'].updateValueAndValidity();
      this.targetbaseform.controls['sourcefile'].updateValueAndValidity();
      this.targetbaseform.controls['objectt'].updateValueAndValidity();
    }
  }
  get validate() {
    return this.targetbaseform.controls;
  }
  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }
  TgtCodeShow() {
    this.TgtUpdateTgt = true;
  }
  TgtCodeShows() {
    this.TgtUpdateTgt = false;
  }

  onKey() {
    this.pageNumber = 1;
    this.page2=1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3=1;
  }
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all') {
      this.pi = this.uploadedData.length;
      this.p = 1;

    }
    else if (value == '20') {
      this.p = 1;
    }
    else if (value == '50') {
      this.p = 1;
    }
    else if (value == '100') {
      this.p = 1;
    }
  }
  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }
  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }
  getFiles() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iteration + "/Target_Current_Zip"
    };
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.uploadedData1 = data;
      this.uploadedData1 = this.uploadedData1.reverse();
    });
  }
  uploadedData: any = [];
  uploadedData1: any = [];
  getuploadedFiles() {
    let requestObj = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "Archive",
      subFolderName1: "PostgreSQL",
    };
    // this.project.Files(requestObj).subscribe((data) => {
    //   this.uploadedData = data;
    // });
  }

  prjSrcTgtData: any
  prjSrcSrcData: any
  GetDBConnections() {
    const projectId = this.projectId.toString();
    this.deployment.getConList(projectId).subscribe((data) => {
      this.prjSrcTgtData = data['Table1'];
      this.TargetConn = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "T" && item.migsrctype == "D";
      });
      this.SourceCo = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "S" && item.migsrctype == "D";
      });
    })
  }
  
  selecttgtConnection(tgtcon: any) {
    this.targetConn = tgtcon
  }



  onItemSelect(item: any) {
    this.schemaName.push(item.schemaname);
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.schemaname);
    this.schemaName.splice(inde, 1);
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.schemaname);
    });
  }
  onDeSelectAll(items: any) {
    this.schemaName = [];
  }

  prjSchemaList(sourceConection: any) {
    this.SourceConn = sourceConection;
    let obj = {
      projectId: this.projectId.toString(),
      dbConnection: 'null',
      sourceConnection: sourceConection.toString()
    }
    this.deployment.getschemaList(obj).subscribe((data) => {
      this.schemaList = data['Table1'];
    });
  }

  spin_process: any
  infraData: any
  filtered: any
  getInfraSelect() {
    let id = this.projectId;
    // this.project.InfraSelect(id).subscribe((data) => {
    //   this.infraData = data['jsonResponseData']['Table1'];
    //   this.filtered = this.infraData.filter((element: any) => {
    //     return element.active == 'True';
    //   });
    // });
  }
  targetConn: any;
  iteration: any
  selectIteration(iter: any) {
    this.iteration = iter;
    this.getFiles();
  }
  spin_db: boolean = false;
  ExecuteCommandOnVM(filename: any, val: any) {

    let obj: any = {
      projectID: this.projectId.toString(),
      command: 'delta_target_baseline.sh',//'delta_target_dump.sh',
      ObjectType: this.UserInfo.email,
      id: this.projectId.toString(),
      ConnectionName: this.iteration,//iteration or run no.
      Connection: this.targetConn,
      Schema: this.schemaName.toString()
    }
    if (val == 1) {
      this.spin_db = true;
      obj.Operation = "Database";
    }
    if (val == 2) {
      this.spin_process = true
      obj.Operation = "Fileshare";
      obj.filename = filename;
    }


    // this.project.ExecuteCommandOnVM(obj).subscribe(
    //   (data: any) => {
    //     this.spin_process = false;
    //     this.spin_db = false
    //     if ((data.message == 'Command Executed Successfully')) {
    //       this.alertService.success(data.message);
    //       localStorage.setItem('Targetfilename', filename);
    //     }
    //   },
    //   (error) => {
    //     this.spin_db = false
    //     this.spin_process = false;
    //     this.alertService.danger(
    //       'Something Went Wrong please try Again Later!'
    //     );
    //   }
    // );
  }

  isLoading: any = [];
  isload(value: any) {
    this.isLoading[value] = true
  }
  objCategory: any = [
    { values: '1', option: 'ALL' },
    { values: '2', option: 'StorageObjects' },
    { values: '3', option: 'CodeObjects' }
  ]
  objectType: any = [];
  obtypevalue: any

  // buttons


  DeltaCommand(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.conname,
      srcCon: value.sourcecon,
      tgtCon: value.targetcon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      extraction_Category: "Target_Baseline",
      task: "Target_Baseline",
      fileName: "",
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      debugger;
      this.Deltatargetbaseline = data;
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data.message);
      }
    })
  }
  Deltatargetbaseline: any;
  targetbaselineextract:any;


  DeltaCommandExtract(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.conname,
      targetConn: value.targetcon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      extraction_Category: "Target_Baseline",
      task: "Target_Baseline",
      fileName: value.sourcefile,
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      debugger;
      this.targetbaselineextract = data;
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data.message);
      }
    })
  }


  ExeLog: any = {};
  prjLogData: any;
  selectedrun: any;
  NoDataHide: boolean = false;
  getPrjExeLogSelectTask(value: any) {
    this.selectedrun = value;
    if (value == "") {
      value = "null";
    }
    this.prjLogData = [];
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operationType = "Target_Baseline";
    this.ExeLog.action = "null";
    this.ExeLog.row_id = "null";
    this.ExeLog.runno = value;
    this.ExeLog.operationName = "null";
    this.deployment.PrjExelogSelectTask(this.ExeLog).subscribe((data: any) => {
      this.prjLogData = data['Table1'];
      this.NoDataHide = true;
    })
  }
  ref_spin: boolean = false
  refresh()
  {
    this.GetRequestTableData();
  }

  RequestTableData: any;
  GetRequestTableData() {
    let obj = {
      projectId: this.projectId,
      operationType: 'Target_Baseline'
    }
    this.ref_spin=true;
    this.deployment.GetRequestTableData(obj).subscribe((data: any) => {
      this.RequestTableData = data['Table1'];
      this.ref_spin=false;
    });
  }
  runNumbersData: any;
  getRunNumbers() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbersData = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }

  deleteResponse: any;
  // delete request table data
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.deployment.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      if (data['Table1'][0].v_status) {
        this.toast.success(data['Table1'][0].v_status);
      }
      else {
        this.toast.error(data);
      }
	  this.GetRequestTableData();
    })
  }
  iterationForLogs: any;
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value;
    this.GetFilesExecutionLogsFromDirectory();
  }
  LogsData: any;
  GetFilesExecutionLogsFromDirectory() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iterationForLogs + "/Execution_Logs/Deployment/Target_Baseline"
    };
    this.LogsData=[];
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.LogsData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }
  fileResponse: any;
  spin_dwld: any;
  //download files
  downloadFile(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  
	get validates() {
    return this.sorcedeltaform.controls;
  }
	
	 GetSourceFilePathData:any;
  GetSourceFilePath()
  {
    const obj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.run + "/Target/Database_baseline/"+this.deltaschema.toString()+"/Procedure/"
    }
    this.GetSourceFilePathData=[];
    this.deployment.getFiles(obj).subscribe((data: any) => {
      this.GetSourceFilePathData = data;
      for (const element of this.GetSourceFilePathData) {
        let schemaobjtype = element.baseline_filepath.split('/')[6] + "/" + element.baseline_filepath.split('/')[7]
        element.baselineFile = element.objectname + "_baseline.sql"
        element.currentFile = element.objectname + "_current.sql"
        element.htmlFile = element.objectname + ".html"
        element.schemaobj = schemaobjtype
        element.lastmodified = ""
        element.basefilename = element.objectname
      }
    });
  }
  downloadFiles(path:string,filename:string) {
    var pth=path.split("/mnt/pypod/")[1]
    pth="/mnt/eng/"+pth
    this.deployment.downloadLargeFiles(pth).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = filename;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
   // sc schemalist
   schemaNamess:any;
   GetSCSchemaList(runno: any) {
     this.schemaNamess = []
     let obj = {
       projectId: this.projectId,
       runno: runno
     }
     this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
       this.schemaNamess = data['Table1'];
     });
   }
   deltaschema:any;
  sourceSelected: boolean = false
  sourceSel(value: string) {
    value != '' ? this.sourceSelected = true : this.sourceSelected = false;
    this.deltaschema=value;
    this.GetSourceFilePath();
  }
  run:any;
  selectRunnumber(itera: any) {
    this.run = itera;
    this.GetSCSchemaList(itera);
  }

  downloadFiless(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
}

import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { HotToastService } from '@ngxpert/hot-toast';

declare let $: any;
//interface DagsList { name: string; name_space: string; node_name: string; phase: string; start_time: string; age: string; events: Array<any>; status: Array<string>; isSelected:boolean }
interface DagsList {
  taskinstance: {
      task_id: string,
      dag_id: string,
      dag_run_id: string,
      execution_date: string,
      start_date: string,
      end_date: string,
      duration: number,
      state: string,
      try_number: number,
      hostname: string,
      queued_when: string
  },
basicPods: null,
isSelected:boolean
}
@Component({
  selector: 'app-pod-management',
  standalone: true,
  imports: [SpinnerComponent, NgxPaginationModule, SearchFilterPipe, CommonModule, FormsModule, ReactiveFormsModule,],
  templateUrl: './pod-management.component.html',
  styles: ``
})

export class PodManagementComponent {

  pageName: string = ''
  refresh_spin: boolean = false
  searchText: string = ''
  podList: DagsList[] = []//{ "name": "grafana-5f69f99bd7-tdg5s", "name_space": "qmig-ns", "node_name": "aks-agent-41925642-vmss00002r", "phase": "Running", "start_time": "07/18/2024 01:35:32", "age": "7.12:17:45.9290755", "events": [null, null, null, null], "status": [""] }, { "name": "ingress-nginx-admission-create-hzf9b", "name_space": "qmig-ns", "node_name": "aks-agent-41925642-vmss00002t", "phase": "Succeeded", "start_time": "07/22/2024 14:15:50", "age": "2.23:37:27.9299618", "events": [null, null, null, null], "status": [""] }, { "name": "ingress-nginx-admission-patch-fdnxp", "name_space": "qmig-ns", "node_name": "aks-agent-41925642-vmss00002s", "phase": "Failed", "start_time": "07/22/2024 14:15:50", "age": "2.23:37:27.9302992", "events": [null, null, null, null], "status": [""] }, { "name": "ingress-nginx-controller-cbf4d676f-5bsn8", "name_space": "qmig-ns", "node_name": "aks-agent-41925642-vmss00002s", "phase": "Running", "start_time": "07/18/2024 01:35:22", "age": "7.12:17:55.9303126", "events": [null, null, null, null], "status": [""] }]
  dagsListDuplicate: DagsList[] = this.podList
  nodelist: any[] =[];
  p: number = 1

  //checkbox Functionality  
  isCheckBoxSel:boolean = false
  checkValue:string = '';
  showCheckStatus: boolean = false  
  selPods:DagsList[] = []
  filterval:string[] = ["upstream_failed", "up_for_reschedule", "scheduled","deferred", "removed","restarting"]

  constructor(private titleService: Title, private route: ActivatedRoute, private datamigration: DataMigrationService, private toast: HotToastService,) {
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getpods()
  }

  refreshdags() {
    this.getpods()
  }

  onKey() { }

  filterPhase(data: Event) {
    const filterValue = (data.target as HTMLInputElement).value
    if(filterValue == "others"){
      this.podList = this.dagsListDuplicate.filter((el: DagsList) => this.filterval.includes(el.taskinstance['state']))
    }else if(filterValue == "all"){
      this.podList = this.dagsListDuplicate
    }
    else{
      this.podList = this.dagsListDuplicate.filter((el: DagsList) => el.taskinstance['state'] == filterValue)
    } 
  }

  filterNode(data: Event) {
    const filterValue = (data.target as HTMLInputElement).value
    this.podList = this.dagsListDuplicate.filter((el: DagsList) => el.basicPods?.['node_name'] == filterValue)
  }

  filterPod(data: Event) {
    const filterValue = (data.target as HTMLInputElement).value
    this.podList = this.dagsListDuplicate.filter((el: DagsList) => el.basicPods?.['phase'] == filterValue)
  }

  selectAll(data:Event){
    this.showCheckStatus = (data.target as HTMLInputElement).checked
    if ((data.target as HTMLInputElement).checked) {
      this.selPods = []
      this.podList.filter((item: DagsList) => {
        item.isSelected = true
        this.selPods.push(item)
      })
      this.isCheckBoxSel = true
    } else {
      this.selPods = []
      for (const element of this.podList) {
        element.isSelected = false
      }
      this.isCheckBoxSel = false
    }
  }
  checkboxselect(data:Event){
    this.checkValue =  (data.target as HTMLInputElement).value
    
    if ((data.target as HTMLInputElement).checked) {
      this.isCheckBoxSel = true
      this.podList.filter( (al:DagsList, index:any) => {
        if(index == this.checkValue){
          this.selPods.push(al)
          al.isSelected = true
        }
      })
      const selPodsIndex = this.selPods.filter((itemz: DagsList) => {return itemz.isSelected == true})
      selPodsIndex.length == this.podList.length ? this.showCheckStatus = true : this.showCheckStatus = false
      this.selPods.length === null ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
     }else{
      this.isCheckBoxSel = false
      this.podList.filter( (al:DagsList, inde:any) => {
        if(inde == this.checkValue){
          al.isSelected = false
        }
      })
      this.selPods = this.selPods.filter( (sl:DagsList,ind:any) => { return ind != this.checkValue })
      const selPodsIndex = this.selPods.filter((itemz: DagsList) => {return itemz.isSelected == true})
      selPodsIndex.length == this.podList.length ? this.showCheckStatus = true : this.showCheckStatus = false
      this.selPods.length === 0 ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
     }
  }
  deletePOD(){
    var temppod:Array<String>=[]
    this.selPods.forEach( (tp:DagsList) => {
      tp.basicPods == null ? '' : temppod.push(tp.basicPods['name'])
    })
    this.datamigration.deletePOD(temppod).subscribe( (dl:string) => {
      $("#confirmBox").fadeOut(1000)
      this.toast.success('PODs Deleted Successfully ')
    })
  }

  getTime = new Date(Date.now() - 60 * 60 * 1000).toISOString(); // one Hour Ago

  getPodsBasedonTime(value:string){
    this.getTime = value == '30mins' ? new Date(Date.now() - 30 * 60 * 1000).toISOString() :  value == '3hour' ? new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString() : value == '6hour' ? new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString() : value == '12hour' ? new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString() : this.getTime
    this.getpods()
  }

  getpods() {
    const currentDate = new Date().toISOString();
    const twoDaysAgo = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString();
    let obj = {
      "labels": "component=worker",
      "execution_date_gte": this.getTime,
      "execution_date_lte": currentDate
    }
    this.datamigration.Getpods(obj).subscribe((data: any) => {
      this.podList = data
      this.dagsListDuplicate=this.podList
      this.nodelist = [...new Set(this.podList.map((all) => all.basicPods?.["node_name"] ))].filter(Boolean)
    })
  }

  actionsPost(value:string){
    const transformedArray = this.selPods.map((al:any) => ({
      task_id: al.taskinstance.task_id,
      dag_id: al.taskinstance.dag_id,
      dag_run_id:al.taskinstance.dag_run_id,
      state:  value == 'mark_failed' ? 'failed' : value == 'mark_success' ? 'success':  value == 'mark_skipped' ? 'skipped': al.taskinstance.state
    }));
    if(value == 'mark_failed' || value == 'mark_success' || value == 'mark_skipped'){
      this.datamigration.markTask(transformedArray).subscribe( (data:any) => {
      })
    }else{
      const clearValue = value == 'clear' ? 'true' : 'false'
      this.datamigration.clearTask(clearValue,transformedArray).subscribe( (data:any) => {
      })
    }
  }

  openConfirm(){    
    $("#confirmBox").show();
  }
  closeConfirm(){
    $("#confirmBox").fadeOut(1000)
  }
}

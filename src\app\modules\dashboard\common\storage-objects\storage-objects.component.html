<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-3">
            <h3 class="main_h mt-2">Storage Objects Reports </h3>
        </div>
        <div class="col-md-9">
                <dash-tabs></dash-tabs>            
        </div>
    </div>
    
    <!--- Main Content --->
    <div class="qmigTabs mt-3">        
        <div class="row">
            <div class="col-md-2 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #con (change)="getSchema(con.value)">
                        <option selected disabled>Select Source Connection</option>
                        @for(con of connectionsList ; track con;){
                            <option value="{{con}}">{{con}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 p-0 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #sc (change)="getIteration(sc.value)">
                        <option selected disabled>Select Schema Name</option>
                        @for(sc of schemaList; track sc;){
                            <option value="{{sc}}">{{sc}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #it (change)="getOperations(it.value)">
                        <option selected disabled>Select Iteration</option>
                        @for(it of iterationList; track it;){
                            <option value="{{it}}">{{it}}</option>
                        }
                    </select>
                </div>
            </div>
        </div>
    </div>
    @if(barChartLabels.length != 0){
        <div class="qmig-card mt-3">        
            <div class="qmig-card-body">
                @if(connectionID == '' && schemaId == ''){
                    <div class="custom-legend">
                        <div class="legend-card">Converted Count</div>
                        <div class="legend-card">Total Count</div>
                    </div>
                }
                <div style="display: block; width: 100%">
                    <canvas 
                    height="100%"
                    baseChart
                    [datasets]="barChartData"
                    [options]="barChartOptions"
                    [labels]="barChartLabels">
                    </canvas>
                </div>
            </div>
        </div>  
    }
    
    <div class="qmig-card pt-3 mt-3">        
        <div class="row pb-3">
            <div class="col-md-6">
                <h3 class="main_h px-3 pt-3">Detail Report</h3>
            </div>
            <div class="col-md-6 pe-3">
                <div class="custom_search me-0">
                    <span class="mdi mdi-magnify"></span>
                    <input type="text" placeholder="Search Document" class="form-control" [(ngModel)]="searchText">
                </div>                
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>Sr No</th>
                        <th>Iteration</th>
                        <th>Conname</th>
                        <th>Schema Name</th>
                        <th>Total Count</th>
                        <th>Converted Count</th>
                        <th>Percentage </th>
                    </tr>
                </thead>
                <tbody>
                    @for (list of operationsTable | searchFilter: searchText ; track list; let i = $index) {
                    <tr>
                        <td>{{i + 1}}</td>
                        <td>{{list.iteration}}</td>
                        <td>{{list.conname}}</td>
                        <td>{{list.schemaname}}</td>
                        <td>{{list.total_count}}</td>
                        <td>{{list.converted}}</td>
                        <td>
                            <span class="badge" [ngClass]="{
                                'badge-csuccess': list.percentage_passed <= 100 && list.percentage_passed >= 90,
                                'badge-cwarning': list.percentage_passed <= 90 && list.percentage_passed >= 50, 
                                'badge-cdanger': list.percentage_passed <= 50 && list.percentage_passed >= 0 }">
                                {{list.percentage_passed /100 | percent: '1.0-0'}}
                            </span>
                        </td>
                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="7">
                            <p class="text-center m-0 w-100">Empty list of Documents</p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
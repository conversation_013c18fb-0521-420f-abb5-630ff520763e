import { Component, ViewChild } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { DashboardService } from '../../../../services/dashboard.service';
import { CommonModule, PercentPipe } from '@angular/common';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartOptions, ChartType } from 'chart.js';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { FormsModule } from '@angular/forms';

interface OperationList {
  iteration: string,
  conname: string,
  schemaname: string,
  total_count: string,
  converted: string,
  percentage_passed: number
}

@Component({
  selector: 'app-testing',
  standalone: true,
  imports: [TabsComponent, PercentPipe, NgChartsModule, CommonModule, SearchFilterPipe, FormsModule],
  templateUrl: './testing.component.html',
  styles: ``
})
export class NewTestingComponent {


  @ViewChild('chart1') chart1: BaseChartDirective | undefined;
  @ViewChild('chart2') chart2: BaseChartDirective | undefined;
  @ViewChild('chart3') chart3: BaseChartDirective | undefined;

  connectionsList: Array<String> = [];
  schemaList: Array<String> = [];
  iterationList: Array<String> = [];
  operationsList: OperationList[] = [];
  operationsCopy: OperationList[] = this.operationsList
  operationsTable: OperationList[] = this.operationsList

  connectionID: string = '';
  connectionValue: string = '';
  schemaId: string = '';
  schemaName: string = '';
  iterationId: string = ''


  /*---- Search bar ---*/
  searchText: string = '';

  /*---- totalMax Charts ----*/
  // Custom plugin for displaying percentage in the center
  centerTextPlugin1 = {
    id: 'centerTextPlugin1',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'totalAvgCPUChart') {
        const { ctx, chartArea: { width, height } } = chart;

        ctx.save();

        // Assuming you are showing the percentage of the first dataset
        const total = chart.config.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
        const value = chart.config.data.datasets[0].data[0];
        const percentage = ((value / total) * 100).toFixed(1) + '%';

        // Define style for the text
        ctx.font = 'bold 25px Geist';
        ctx.fillStyle = '#333';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Set position to center of the chart
        const centerX = width / 2;
        const centerY = height / 2 + 20;

        // Draw the text in the center
        ctx.fillText(percentage, centerX, centerY);
        ctx.restore();
      }
    }
  };

  public totalAvgChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'January',
      'February',
    ],
    datasets: [
      {
        data: [95, 5],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
          '#8b36ff',
          '#f0f0f0'
        ],
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,

      }
    ]
  };
  public totalAvgChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This totalMax disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
          label: (tooltipItem) => {
            const dataPoint = tooltipItem.formattedValue;
            // Return multiple labels for the tooltip
            return [
              " Count" + ': ' + dataPoint,  // Main dataset label
            ];
          }
        }
      }
    }

  };
  public totalAvgChartLegend = false;
  public totalAvgChartPlugins = [this.centerTextPlugin1];

  constructor(private dbService: DashboardService) { }

  ngOnInit(): void {
    this.getOperationsList()
  }

  /*--- Fetch Iteration ---*/
  getOperationsList() {
    const obj = {
      dbname: 'null',
      Conid: 'null',
      schemaid: 'null',
      iteration: 'null',
      option: 0
    }
    this.dbService.getOperationList(obj).subscribe((data: any) => {
      this.operationsList = JSON.parse(JSON.stringify(data['Table1']));
      this.operationsList.forEach((item: any) => {
        this.connectionsList.push(item.conname)
      })
      this.operationsTable = data['Table1']
      this.operationsCopy = this.operationsList
      this.connectionsList = [...new Set(this.connectionsList)]
    })
  }

  /*--- Fetch Schemas ---*/
  getSchema(value: string) {
    this.schemaList = []
    this.schemaId = ''
    this.connectionID = value
    this.operationsCopy.filter((el: any) => {
      if (value == el.conname) {
        this.schemaList.push(el.schemaname)
      }
      this.schemaList = [...new Set(this.schemaList)]
    })
    this.operationsTable = this.operationsList.filter((fl: OperationList) => { return fl.conname == value })
  }


  /*--- Fetch Iteration ---*/
  getIteration(value: string) {
    this.iterationList = []
    this.schemaId = value
    this.iterationId = ''
    this.operationsCopy.filter((el: any) => {
      if (value == el.schemaname && this.connectionID == el.conname) {
        this.iterationList.push(el.iteration)
      }
      this.iterationList = [...new Set(this.iterationList)]
    })
    this.operationsTable = this.operationsList.filter((fl: OperationList) => { return fl.schemaname == value && fl.conname == this.connectionID })
  }


  /*--- Fetch Iteration ---*/
  getOperations(op: any) {
    this.iterationId = op
  }


}

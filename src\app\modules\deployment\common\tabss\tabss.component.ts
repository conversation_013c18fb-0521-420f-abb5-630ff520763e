import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';

@Component({
  selector: 'app-tabss',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, CommonModule],
  templateUrl: './tabss.component.html',
  styles: ``
})
export class TabssComponent {
 @Input() icon:string = 'mdi-databases'
  @Input() pageName:string = 'Deployment'
}

import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { NgxPaginationModule } from 'ngx-pagination';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CommonModule } from '@angular/common';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { interval, Subscription } from 'rxjs';

declare let $: any;

@Component({
  selector: 'app-jobscreationagent',
  standalone: true,
  imports: [NgxPaginationModule, SearchFilterPipe, CommonModule, FormsModule, ReactiveFormsModule, SpinnerComponent],
  templateUrl: './jobscreationagent.component.html',
  styles: ``
})
export class JobscreationagentComponent {
  projectId: any
  migtypeid: any
  pageName: any
  getForm: any
  private subscription: Subscription | null = null;
  constructor(private titleService: Title, private route: ActivatedRoute, private formBuilder: FormBuilder,
    private datamigration: DataMigrationService, private toast: HotToastService
  ) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getForm = this.formBuilder.group({
      sourceConnection: ['', Validators.required],
      targetConnection: ['', Validators.required]
    })
    if (this.subscription) {
      this.subscription.unsubscribe();
    }

  }
  ngOnInit(): void {
    this.GetConsList()
  }
  get fs() {
    return this.getForm.controls;

  }
  agentHide: boolean = false
  fetchHide: boolean = false
  agentStatusHide: boolean = false
  dagsHide: boolean = false
  ConsList: any = [];
  schemaList: any = []
  tgtlist: any = []
  spin: boolean = false
  GetConsList() {
    this.datamigration.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtlist = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }

  triggerJobAgent(fd: any) {
    this.spin = true
    let obj = {
      task: "Job_Agent",
      projectId: this.projectId.toString(),
      sourceConnectionId: fd.sourceConnection,
      targetConnectionId: fd.targetConnection,
      jobName: "qmig-migrt",
      codeoption:true
      //namSpace:"ns-"+this.projectId,
    }
    this.datamigration.DataMigrationNewCommand(obj).subscribe((data: any) => {
      this.objectData = fd
      this.spin = false
      this.toast.success("Job Agent Triggered Successfully")
      this.autoCall(this.objectData)
      this.fetchConfigDetails(obj)
    })
  }
  auditlist: any = []
  datachange: any
  p: number = 1
  datachange1: any
  p1: number = 1
dag_spin:any=false
  getAuditDags(fd: any) {
    this.auditlist = []
    let obj = {
      srcId: fd.sourceConnection,
      tgtId: fd.targetConnection
    }
    this.datamigration.GetAuditDagsStatus(obj).subscribe((data: any) => {
      if ("message" in data) {
        if (data.message != "") {
         this.dag_spin = false

          this.toast.error(data.message)
        }
      }
      else {
        this.auditlist = data['Table1']
        if (this.auditlist != undefined) {
          if (this.auditlist.length > 0) {
            this.dagsHide = true;
          }
        }
        else {
          this.dagsHide = false;
        }
      }
    this.dag_spin = false
    });
  }
  srcid: any
  selectSrcCon(fd: any) {
    this.srcid = fd
  }
  agentTrackData: any
  objectData: any = []
  getAuditTrackJobsStatus(fd: any) {
    //this.spin = true
    this.auditlist = []
    let obj = {
      srcId: this.srcid,
      tgtId: fd
    }
    this.datamigration.GetAuditJobTrackStatus(obj).subscribe((data: any) => {
      if ("message" in data) {
        if (data.message != "") {
         // this.spin = false
          this.toast.error(data.message)
        }
      }
      else {
        this.agentTrackData = data['Table1']
        if (this.agentTrackData != undefined) {
          if (this.agentTrackData.length > 0) {
            this.agentTrackData.forEach((element: any) => {
              this.ConsList.forEach((item: any) => {
                if (item.Connection_ID == element.source_connection_id) {
                  element.SourceConnection = item.conname
                }
              })
              this.tgtlist.forEach((item: any) => {
                if (item.Connection_ID == element.target_connection_id) {
                  element.TargetConnection = item.conname
                }
              })
            });
            if (this.agentTrackData[0].agent_status == "Running") {
              this.agentHide = false;
              this.fetchHide = true
            }
            else {
              this.agentHide = true;
              this.fetchHide = true;
            }
            this.agentStatusHide = true;
          }
          else {
            this.agentHide = true;
            this.fetchHide = true
            this.agentStatusHide = false;
          }
        }
        else {
          this.agentHide = true;
          this.fetchHide = true
          this.agentStatusHide = false;
        }
      }
     // this.spin = false
    });
  }
  onKey() {
    this.p = 1
  }
  autoCall(value: any) {
    this.fetchConfigDetails(value)
    this.dag_spin =true
    this.getAuditDags(value)
    this.subscription = interval(60000).subscribe(() => {
      this.getAuditDags(value)
    });
  }
  selectTgt() {
    this.agentHide = true;
    this.fetchHide = true
  }
  deleteStatus: any
  deleteTrackJobsStatus(src: any, tgt: any) {
    let obj = {
      srcId: src,
      tgtId: tgt
    };
  
    this.datamigration.deleteJobTrackStatus(obj).subscribe((data: any) => {
      if ("message" in data && data.message !== "") {
        this.toast.error(data.message); 
      } else {
        this.deleteStatus = data['Table1'];
        this.toast.success("Job Agent Deleted Successfully"); 
        this.getAuditTrackJobsStatus(tgt);
        this.getForm.reset()
      }
    });
  }

  configDetails: any = [];
  filteredConfigDetails: any = [];
  currentConfigObj: any;
  fetchConfigDetails(data:any) {
    this.currentConfigObj = data
    const configObj = {
      srcid: data.sourceConnection,
      tgtid: data.targetConnection
    };
    this.datamigration.getConfigData(configObj).subscribe((data: any) => {
      this.configDetails = data['jsonResponseData']['Table1'] || [];
      this.filteredConfigDetails = this.configDetails.filter((item:any, index:any, self:any) =>
        index === self.findIndex((t:any) => t.config_id === item.config_id)
      );
    });
  }

  
  expandedRowIndex: number | null = null;
  configFilterData: any = [];
  configDataChange: any;
  cp:number = 1;
  selectedConfigID: string = '';
  toggleExpand(index: number, configID: number, configStatus:string): void {
    this.updateStatus = false;
    this.selectedConfigID = configID.toString();
    this.expandedRowIndex = this.expandedRowIndex === index ? null : index;  
    this.configFilterData = []; // Reset the filter data for each toggle
    (this.configDetails ?? []).forEach((item: any) => {
      if (item.config_id === configID) {
        this.configFilterData.push(item);
      }
    });
    this.configFilterData = this.configFilterData.sort((a:any, b:any) => +b.dag_id - +a.dag_id);    
  }


  // Update Functionality

    toggleStatus(id: string) {
      this.configFilterData.forEach((data: any) => {
        if (data.dag_id === id) {
          data.dag_status = data.dag_status === 'Success' ? 'fail' : 'Success';
        }
      });
    }

    configChecked: boolean = false
    selectedItems: any[] = [];
    customConfigData: any = [];
    updateStatus: boolean = false;
    selectedConfigStatus: string = '';
    onChange(item: any, id: string, isChecked: any) {
      this.updateStatus = true;
    if (isChecked.target.checked) {
      this.selectedItems.push(item);
      this.configChecked = true;   
    } else {
      this.configChecked = false;
      const index = this.selectedItems.findIndex((data: any) => data.dag_id === id);
      this.selectedItems.splice(index, 1);  
    }
      
    this.customConfigData = [];
    this.selectedItems.forEach((item: any) => {
      this.customConfigData.push(item.dag_id + "-" + item.dag_status);
    });

    this.selectedItems.forEach((data: any) => {
      if (data.dag_status === 'Fail' || data.dag_status === 'fail') {
        this.selectedConfigStatus = 'Fail';
      } else {
        this.configFilterData.forEach((data: any) => {
          if (data.dag_status === 'Fail') {
            this.selectedConfigStatus = 'Fail';
          } else {
            this.selectedConfigStatus = 'Success';
          }
        });
      }
    });
  }
  
  updateAgentStatus() {
    const obj = {
      config_id: this.selectedConfigID,
      dag_id: this.customConfigData.toString(),
      config_status: this.selectedConfigStatus,
      dag_status: '',
      task_status: '',
    };
    console.log(obj);
    // this.datamigration.updateAgentStatus(obj).subscribe((data: any) => {
    //   this.updateStatus = false;
    //   this.fetchConfigDetails(this.currentConfigObj);
    //     this.toast.success("Config status updated successfully");
    // });
  }
}

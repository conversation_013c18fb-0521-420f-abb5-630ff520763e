import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SqlexecutionComponent } from './common/sqlexecution/sqlexecution.component';
import { SqlreportsComponent } from './common/sqlreports/sqlreports.component';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { ServerscanComponent } from './common/serverscan/serverscan.component';
import { SqlScriptComponent } from './common/sql-script/sql-script.component';

const routes: Routes = [
  {path:'', component:LayoutComponent, children:[
    {path:'sqlexecution', component:SqlexecutionComponent,  data: { name: 'SQL Execution' }},
      {path:'sqlreports', component:SqlreportsComponent,  data: { name: 'SQL Reports' }},
      {path:'sqlrscan', component:ServerscanComponent,  data: { name: '<PERSON><PERSON> Scan' }},
      {path:'sqlscript',component:SqlScriptComponent,  data: { name: 'SQL Script' }}
  ]}
];
  

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SqlAssessmentRoutingModule { }

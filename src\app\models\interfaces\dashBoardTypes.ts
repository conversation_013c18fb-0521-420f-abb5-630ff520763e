export interface getCounts {
    projectID: string,
    token: string,
    sp: string
}

export interface connections {
    conname: string,
    migsrctgt: string,
}

export interface getConnections {
    Table1: {
        Connection_ID: string;
        conname: string;
        filename: string;
        migsrctgt: string;
        migsrctype: string;
    }[];
}

export interface conList {
    Table1: {
        Connection_ID: string,
        conname: string
    }
}
export interface ConnectionData {
    conName: string;
    dbname: string;
    totalCount: string;
    matchedCount: string;
    unmatchedCount: string;
    conversionPercentage: string;
}
export interface Connection {
    Connection_ID: string;
    conname: string;

}
 export interface ConnectionWithCountItem {
    source_Connection_Id: string;
    target_Connection_Id: string;
    source_Schema: string;
    target_Schema: string;
    sum: string;
    count: string | null;
  }
import { Component, ViewChild } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { DashboardService } from '../../../../services/dashboard.service';
import { CommonModule, PercentPipe } from '@angular/common';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartData, ChartOptions, ChartType } from 'chart.js';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { FormsModule } from '@angular/forms';

interface OperationList {
  iteration: string,
  conname: string,
  schemaname: string,
  total_count: string,
  converted: string,
  percentage_passed: number
}


@Component({
  selector: 'app-storage-objects',
  standalone: true,
  imports: [TabsComponent, PercentPipe, NgChartsModule, CommonModule, SearchFilterPipe, FormsModule],
  templateUrl: './storage-objects.component.html',
  styles: ``
})
export class StorageObjectsComponent {

  @ViewChild(BaseChartDirective, { static: true }) chart: BaseChartDirective | undefined;

  connectionsList: Array<String> =[];
  schemaList: Array<String> =[];
  iterationList: Array<String> =[];
  operationsList: OperationList[] = [];
  operationsCopy:OperationList[] = this.operationsList
  operationsTable:OperationList[] = this.operationsList


  connectionID: string = '';
  connectionValue: string = '';
  schemaId: string = '';
  schemaName: string = '';
  iterationId: string = ''

  /*--- Chart Data ---*/

  schemaData:any = []
  tooltipList:any = []
  public barChartLabels:string[] = [];
  public barChartType:ChartType = 'bar';
  public barChartLegend:boolean = false;
  public barChartData:any[] = [];
  public barChartOptions:ChartOptions<'bar'> = {
    scales: {
      x: {
        display: true,
      },
      x1: {
        display: false,
      },
    },
    interaction:{
      mode:'point'
    },
    plugins: {
      legend: {
        display: false  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 3,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
           labelPointStyle: function(context) {
              return {
                 pointStyle: 'circle',
                 rotation: 0
              };
           },
           title: (tooltipItems) => {
            const datasetIndex = tooltipItems[0].datasetIndex;
            const dataIndex = tooltipItems[0].dataIndex;
            return this.customTitles[datasetIndex][dataIndex] || 'Default Title';
          },
        }
      }
    }
  };
  
  customTitles: string[][] = [];

  /*---- Search bar ---*/
  searchText: string = '';

  constructor(private dbService: DashboardService) { }

  ngOnInit(): void {
    this.getOperationsList()
  }

  /*--- Fetch Iteration ---*/
  getOperationsList() {    
    this.barChartLabels = [];
    this.barChartData = [];
    this.schemaData = []
    this.tooltipList = []

    const obj = {
      dbname: 'null',
      Conid: 'null',
      schemaid: 'null',
      iteration: 'null',
      option: 2
    }
    this.dbService.getOperationList(obj).subscribe((data: any) => {
      this.operationsList = JSON.parse(JSON.stringify(data['Table1']));
      this.operationsTable = data['Table1']
      this.operationsCopy = this.operationsList
          //get Connections list
    this.operationsList.forEach((item:any) => {
      this.connectionsList.push(item.conname)
    })
    this.connectionsList = [...new Set(this.connectionsList)]
    // seperate list connection based
    const groupedObjects = data['Table1'].reduce((result:any, obj:any) => {
      (result[obj.conname] = result[obj.conname] || []).push(obj);
      return result;
    }, {});
    for (let x in groupedObjects) {
      const totalData:any = [];
      const convertedData:any = [];
      const schemaList:any = []
      let schemaMax = 0;
      this.barChartLabels.push(x)
      const length = groupedObjects[x].length;
        if (length > schemaMax) {
            schemaMax = length;
        }
      groupedObjects[x].forEach( (xl:any) => {   
        schemaList.push(xl.schemaname)
        convertedData.push(xl.converted);
        totalData.push(xl.total_count);
      });
      this.tooltipList.push(schemaList)
      this.schemaData.push(convertedData)
      this.schemaData.push(totalData)

      const ensureTwoElements = (array:any) => array.length === 2 ? array : [...array, "null"];
      this.tooltipList = this.tooltipList.map(ensureTwoElements);
      

    const addPrefix = (prefix:any, value:any) => `${prefix}${value !== undefined ? value : '0'}`;
        // Transpose the array and add prefixes
    const transposedArray = this.tooltipList[0].flatMap((_:any, colIndex:any) => [
      this.tooltipList.map((row:any)=> addPrefix('Converted Count - ', row[colIndex])),
      this.tooltipList.map((row:any) => addPrefix('Total Count - ', row[colIndex]))
      ]);
      this.customTitles = transposedArray
      const setsCount = this.schemaData.length / 2;
      const maxLength = Math.max(...this.schemaData.map((arr:any) => arr.length));
        // Iterate through each index of the arrays
      for (let i = 0; i < maxLength; i++) {
        // Create a group of interleaved arrays
        let converted = [];
        let total_count = [];

        // Iterate through each set and add the corresponding element to the group
        for (let j = 0; j < setsCount; j++) {
            converted.push(this.schemaData[j * 2][i] !== undefined ? this.schemaData[j * 2][i] : null);
            total_count.push(this.schemaData[j * 2 + 1][i] !== undefined ? this.schemaData[j * 2 + 1][i] : null);
        }
        // Add the interleaved groups to the result
        if(setsCount > this.connectionsList.length - 1){
          this.barChartData.push({
            label: `Converted Count -`,
            backgroundColor: '#6200E8',
            hoverBackgroundColor: '#4601a6',
            xAxisID: 'x1',
            data: converted,
            maxBarThickness: 30,
            minBarLength: 2,
            barPercentage: 0.6,
            skipNull: true
        });        
        this.barChartData.push({
            label: `Total Count -`,
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            hoverBackgroundColor: '#dab1fd',
            xAxisID: 'x',
            data: total_count,
            maxBarThickness: 50,
            minBarLength: 2,
            skipNull: true
        });
        }
      }
    }
    this.chart?.update();   
    });
  }

  

  chartPercentage:any = []
  chartConverted:any = []
  chartTotal:any = []

  updateChart(){
    this.barChartLabels = []
    this.barChartData = [];
    this.chartPercentage = []
    this.chartConverted = []
    this.chartTotal = []    
    this.barChartOptions.plugins!.legend!.display = true;
    this.operationsCopy.forEach((el: OperationList) => {
      //console.log(el)
      if(this.connectionID !== '' && this.schemaId == ''){
        if(this.connectionID == el.conname){
          this.pushCode(el)
        }
      }else if(this.schemaId !== '' && this.iterationId == ''){
        if(this.schemaId == el.schemaname && this.connectionID == el.conname){
          this.pushCode(el)
        }
      }else if( this.iterationId !== ''){
        if(this.iterationId == el.iteration && this.connectionID == el.conname && this.schemaId == el.schemaname){
          this.pushCode(el)
        }
      } else{
        this.pushCode(el)
      }
    });
    this.chart?.update();   
  }
  pushCode(el:OperationList){
    this.barChartLabels.push(el.schemaname)
    this.chartPercentage.push(Number(el.percentage_passed))
    this.chartConverted.push(Number(el.converted))
    this.chartTotal.push(Number(el.total_count))
    this.barChartData = [
      {
        data: this.chartPercentage,
        label: 'Percentage',
        borderColor: '#F86624',
        backgroundColor: '#F86624',
        type: 'line',
        tension: 0.4, // Adjust this value to get the desired curvature
      },
      {
        label: 'Converted Count',
        backgroundColor: '#6200E8',
        hoverBackgroundColor: '#4601a6',
        xAxisID: 'x1',
        data: this.chartConverted,
        maxBarThickness: 30,
        barPercentage: 0.6,
        minBarLength: 2,
      },
      {
        label: 'Total Count',
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        hoverBackgroundColor: '#dab1fd',
        data: this.chartTotal,
        xAxisID: 'x',
        maxBarThickness: 50,
        minBarLength: 2,
      },
    ]
  }

  /*--- Fetch Schemas ---*/
  getSchema(value: string) {
    this.schemaList = []
    this.schemaId = ''
    this.connectionID = value
    this.operationsCopy.filter((el: any) => {
      if (value == el.conname) { 
        this.schemaList.push(el.schemaname)
       }
       this.schemaList = [...new Set(this.schemaList)]
    })
    this.updateChart()
    this.operationsTable = this.operationsList.filter( (fl:OperationList) => {return fl.conname == value })
  }


  /*--- Fetch Iteration ---*/
  getIteration(value: string) {
    this.iterationList = []
    this.schemaId = value
    this.iterationId = ''
    this.operationsCopy.filter((el: any) => {
      if (value == el.schemaname && this.connectionID == el.conname) { 
        this.iterationList.push(el.iteration)
       }
       this.iterationList = [...new Set(this.iterationList)]
    })
    this.updateChart()
    this.operationsTable = this.operationsList.filter( (fl:OperationList) => {return fl.schemaname == value && fl.conname == this.connectionID })
  }


  /*--- Fetch Iteration ---*/
  getOperations(op: any) {
    this.iterationId = op
    this.updateChart()
  }
}

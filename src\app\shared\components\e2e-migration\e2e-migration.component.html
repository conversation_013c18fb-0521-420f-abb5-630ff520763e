<!-- <header -->
    <div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="execProjectForm">
            <div class="row">
                <!-- source connection list -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group" form>
                        <label class="form-label d-required" for="Schema">Source Connection</label>
                        <select class="form-select"formControlName="connectionName">
                            <option selected value="">Select Connection Name</option>
                        </select>
                        @if ( f.connectionName.touched && f.connectionName.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.connectionName.errors.required) {ConnectionName is Required }
                        </p>
                        }
                    </div>
                </div>
                <!-- target connection list -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Target Connection</label>
                        <select class="form-select" formControlName="targetconnection">
                            <option selected value="">Select Target Connection Name</option>
                        </select>
                        @if ( f.targetconnection.touched && f.targetconnection.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.targetconnection.errors.required) {Target Connection is Required }
                        </p>
                        }
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Source Schema Name</label>
                        <select class="form-select" formControlName="schema">
                            <option selected value="">Select Source Schema</option>
                        </select>
                        @if ( f.schema.touched && f.schema.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.schema.errors.required) {Source Schema is Required }
                        </p>
                        }
                    </div>
                </div>
                <!-- get object type list -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Object Type</label>
                        <select class="form-select" formControlName="objectType">
                            <option selected value="">Select Object type</option>
                        </select>
                        @if ( f.objectType.touched && f.objectType.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.objectType.errors.required) {ObjectType is Required }
                        </p>
                        }
                    </div>
                </div>
                <!-- check details button -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-md-9">
                    <button  [disabled]="execProjectForm.invalid" class="btn btn-upload w-100">
                        Execute </button>
                </div>
            </div>
        </form>
    </div>
</div>
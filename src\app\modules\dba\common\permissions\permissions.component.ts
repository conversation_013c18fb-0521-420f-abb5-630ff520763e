import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DbaService } from '../../../../services/dba.service';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { HotToastService } from '@ngxpert/hot-toast';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { ActivatedRoute } from '@angular/router';
import { sequence } from '@angular/animations';

declare let $: any;

@Component({
  selector: 'app-permissions',
  standalone: true,
  imports: [CommonModule, NgSelectModule, FormsModule, CommonModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './permissions.component.html',
  styles: ``
})
export class PermissionsComponent {
  roleForm: any
  projectId: any
  ConsList: any
  ConsListForPermissions: any
  tableHide: boolean = false
  pageNumber: number = 1;
  p2: number = 1;
  page1: number = 1;
  pi: number = 10;
  searchText1: string = '';
  fileResponse: any;
  permissionForm: any
  exeForm: any
  disablecheckbox: boolean = true
  spinner_exe: boolean = false
  pageName: string = ''

  constructor(private fb: FormBuilder, private dba: DbaService, private toast: HotToastService, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }
  ngOnInit(): void {

    this.GetConsList()
    this.permissionForm = this.fb.group({
      connection: ['', [Validators.required]],
      schemaname: ['', Validators.required],
      objName: ['', Validators.required],
      objType: ['', Validators.required],
      usr_rol: ['', Validators.required],
      // category: ['', Validators.required],
      database: ['', Validators.required],
    })
    this.exeForm = this.fb.group({
      db: ['', [Validators.required]],
      tgtCon: ['', [Validators.required]],
    })
  }
  selectedItems = [];
  selectedItems1 = [];
  checkAll = [];
  schemaList: any = [];
  spinner: boolean = false
  get getControl() {
    return this.exeForm.controls;
  }
  get f() {
    return this.permissionForm.controls;

  }
  // Connection details
  GetConsList() {
    this.dba.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      this.ConsListForPermissions = this.ConsList
    });
  }

  // Database details
  dblist: any
  connId: any
  Getdbname(conId: string) {
    this.dblist = []
    this.connId = conId
    this.dba.getdbnames(conId).subscribe((data: any) => {
      this.dblist = data
    });
  }

  getusernames: any
  Userlist: any
  selecteddbname: any
  userSelected: boolean = false
  roleSelected: boolean = false
  SelectUserorRole(data: any) {
    if (data == 0) {
      this.userSelected = true
      this.roleSelected = false
      this.GetUsers()
    }
    else if (data == 1) {
      this.userSelected = false
      this.roleSelected = true
      this.Getroles()
    }
  }

  selectDB(data: any) {
    this.selecteddbname = data.toLowerCase()
  }
  GetUsers() {
    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname
    }
    this.dba.getusernames(obj).subscribe((data: any) => {
      this.Userlist = data
      // this.Userlist.filter((item: any) => {
      //   item.type = "ALL"
      // })
    });

  }
  rolelist: any
  Getroles() {
    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname
    }
    this.dba.getrolenames(obj).subscribe((data: any) => {
      this.rolelist = data
      // this.rolelist.filter((item: any) => {
      //   item.type = "ALL"
      // })
      // this.rolelist[0].push(obj)
    });

  }
  selectedusrrole: string = ""
  selectusrrole(value: string) {
    this.selectedusrrole = value
  }
  objectlist: any
  objecttypelist: any
  GetObjectTypes() {
    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname,
      role: this.selectedschema.toString()//this.selectedusrrole
    }
    this.dba.getobjecttype(obj).subscribe((data: any) => {
      this.objecttypelist = data
    });

  }
  selectedTgtCon: any
  selectedDbName: any
  getSchemas() {
    let obj = {
      conid: this.connId,
      dbname: this.selecteddbname.toLowerCase()
    }
    this.dba.tgtSchemaSelectwithdb(obj).subscribe((data: any) => {
      this.schemaList = data
      this.schemaList.sort((a: any, b: any) => a.schemaname.localeCompare(b.schemaname));
      this.schemaList.filter((item: any) => {
        item.type = "ALL"
      })
    })
  }
  selectedcategory: string = ""
  selectcategory(value: string) {
    this.selectedcategory = value
  }
  selectedobjtype: string = ""
  SequenceSelect: boolean = false
  TableSelect: boolean = false
  FunctionSelect: boolean = false
  selectobjtype(value: string) {
    this.selectedobjtype = "'" + value + "'"
    if (value == "SEQUENCE") {
      this.SequenceSelect = true
      this.TableSelect = false
      this.FunctionSelect = false

    }
    if (value == "TABLE" || value == "VIEW" || value == "MVIEW") {
      this.SequenceSelect = false
      this.TableSelect = true
      this.FunctionSelect = false
    }
    if (value == "FUNCTION" || value == "PROCEDURE") {
      this.SequenceSelect = false
      this.TableSelect = false
      this.FunctionSelect = true
    }
  }
  selectedschema: string = ""
  selectschema(value: any) {
    var temp: any = []
    if (value.toString() == "ALL") {
      this.selectedschema = ""
      this.schemaList.filter((item: any) => {
        temp.push("'" + item.schemaname.toUpperCase() + "'")
      })
      this.selectedschema = temp.toString()
      //this.GetObjects()
    }
    else if (value.length > 0) {
      if (value.includes(",")) {
        value.filter((item: any) => {
          temp.push("'" + item.toLowerCase() + "'")
        })
      }
      else {
        temp.push("'" + value.toLowerCase() + "'")
      }

      this.selectedschema = temp.toString()
      //this.GetObjects()
    }
    else if (value.length == 0) {
      this.objectlist = []
    }

  }
  getobjectnames: any
  spinnerexe: boolean = false
  GetObjects() {
    this.spinnerexe = true
    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname,
      role: this.selectedusrrole,
      objtype: this.selectedobjtype,
      objschema: this.selectedschema
    }
    this.dba.getobjectnames(obj).subscribe((data: any) => {
      this.objectlist = data
      this.objectlist.filter((item: any) => {
        item.type = "ALL"
      })
      this.objectlist[0].push(obj)
      this.spinnerexe = false
    });

  }
  //validations


  GetExecutedata() {
    this.spinner_exe = true
    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname
    }
    this.dba.getexecutedata(obj).subscribe((data: any) => {
      this.toast.success(data.message);
      this.spinner_exe = false
    });

  }
  selectedobjectname: string = ""
  selectobjectname(value: any) {
    this.selectedobjectname = value
    var temp: any = []
    if (value.toString() == "ALL") {
      this.selectedobjectname = ""
      this.objectlist.filter((item: any) => {
        temp.push("'" + item.object_Name + "'")
      })
      this.selectedobjectname = temp.toString()
    }
    else {
      value.filter((item: any) => {
        temp.push("'" + item.toUpperCase() + "'")
      })
      this.selectedobjectname = temp.toString()
    }
  }
  searchresult: any
  hideTable: boolean = false
  disableEdit: boolean = false
  GetPermissions(value: any) {
    if (value == 0) {
      this.spinner = true
    }

    let obj = {
      // category: this.selectedcategory,
      ConId: this.connId,
      dbname: this.selecteddbname,
      role: this.selectedusrrole,
      objtype: this.selectedobjtype.toUpperCase(),
      objname: this.selectedobjectname,
      objschema: this.selectedschema.toUpperCase()
    }

    this.dba.getpermissions(obj).subscribe((data: any) => {
      this.searchresult = data
      if (this.searchresult.length > 0) {
        this.disableEdit = false
      }
      else {
        this.disableEdit = true
      }
      this.searchresult.forEach((item: any) => {
        item.schemaObject = ""
        item.newGrants = []
        item.revoke = []
      })
      this.hideTable = true
      this.spinner = false
    });

  }
  editcheckbox() {
    this.disablecheckbox = false
  }
  perm: any = []
  getMethod($event: any, objname: any, permission: any) {
    this.searchresult.filter((item: any) => {
      if (item.object_Name == objname) {
        if ($event.target.checked) {  //if check box is true
          if (!item.grants.includes(permission.toUpperCase())) { // if permission is there are not in Grants
            if (item.newGrants.length == 0) { //if newGrants length is 0 or not 
              item.newGrants.push(permission) // pushing the permission into newGrants
            } else {
              if (!item.newGrants.includes(permission)) { //if newgrants contains permission or not 
                item.newGrants.push(permission)  // pushing the permission into newGrants
              }
            }
          }
          else {
            if (item.revoke.length > 0) {
              if (item.revoke.includes(permission)) {
                var index1 = item.revoke.indexOf(permission)
                item.revoke.splice(index1, 1)
              }
            }
          }
        }
        else {
          if (item.grants.includes(permission.toUpperCase())) {
            if (item.revoke.length == 0) {
              item.revoke.push(permission)
            } else {
              if (!item.revoke.includes(permission)) {
                item.revoke.push(permission)
              }
            }
          }
          else {
            if (item.newGrants.includes(permission)) {
              var index = item.newGrants.indexOf(permission)
              item.newGrants.splice(index, 1)
            }
          }
        }
      }
    })
  }

  spin_save: boolean = false
  submitGrants() {
    this.spin_save = true
    var grantrequest: any = []
    this.searchresult.filter((item: any) => {
      if (item.newGrants.length > 0 || item.revoke.length > 0) {
        let obj = {
          schemaobject: item.schema + "." + item.object_Name,
          objectType: item.object_Type,
          grants: item.newGrants,
          revoke: item.revoke
        }
        grantrequest.push(obj)
      }
    })

    let grantObj = {
      ConId: this.connId,
      dbname: this.selecteddbname,
      role: this.selectedusrrole, ///user
      grantsObject: grantrequest
    }
    this.dba.grantpermissions(grantObj).subscribe((data: any) => {
      this.toast.success(data.message);
      this.GetPermissions(1)
      this.spin_save = false
    })
  }
}

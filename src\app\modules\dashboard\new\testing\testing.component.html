<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-12">
            <dash-tabs></dash-tabs>
        </div>
    </div>

    <!--- Main Content --->
    <div class="qmigTabs mt-3">
        <div class="row">
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #con (change)="getSchema(con.value)">
                        <option selected disabled>Select Source Connection</option>
                        @for(con of connectionsList ; track con;){
                        <option value="{{con}}">{{con}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #sc (change)="getIteration(sc.value)">
                        <option selected disabled>Select Schema Name</option>
                        @for(sc of schemaList; track sc;){
                        <option value="{{sc}}">{{sc}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #it (change)="getOperations(it.value)">
                        <option selected disabled>Select Iteration</option>
                        @for(it of iterationList; track it;){
                        <option value="{{it}}">{{it}}</option>
                        }
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3 dashCH">
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Dev Cycle-2 Testing</h5>
                    <div style="display: block;">
                        <canvas id="totalAvgCPUChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="totalAvgChartData" [options]="totalAvgChartOptions"
                            [legend]="totalAvgChartLegend" [plugins]="totalAvgChartPlugins">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Dev Cycle-3 Testing</h5>
                    <div style="display: block;">
                        <canvas id="totalMemCPUChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="totalAvgChartData" [options]="totalAvgChartOptions"
                            [legend]="totalAvgChartLegend" [plugins]="totalAvgChartPlugins">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Dev Cycle-3 Debug Testing</h5>
                    <div style="display: block;">
                        <canvas id="totalMAvgCPUChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="totalAvgChartData" [options]="totalAvgChartOptions"
                            [legend]="totalAvgChartLegend" [plugins]="totalAvgChartPlugins">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
/*-------  Q-Migrator  style sheet V1.o Created and Developed By Quadrant --------*/

@font-face {
  font-family: "Geist";
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: local("Geist"), local("Geist"),
    url(../fonts/geist/Geist-UltraLight.ttf) format("truetype");
}

@font-face {
  font-family: "Geist";
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: local("Geist"), local("Geist"),
    url(../fonts/geist/Geist-Thin.ttf) format("truetype");
}

@font-face {
  font-family: "Geist";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Geist"), local("Geist"),
    url(../fonts/geist/Geist-Light.ttf) format("truetype");
}

@font-face {
  font-family: "Geist";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: local("Geist"), local("Geist"),
    url(../fonts/geist/Geist-Regular.ttf) format("truetype");
}

@font-face {
  font-family: "Geist";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: local("Geist"), local("Geist"),
    url(../fonts/geist/Geist-Medium.ttf) format("truetype");
}

@font-face {
  font-family: "Geist";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: local("Geist"), local("Geist"),
    url(../fonts/geist/Geist-SemiBold.ttf) format("truetype");
}

@font-face {
  font-family: "Geist";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: local("Geist"), local("Geist"),
    url(../fonts/geist/Geist-Bold.ttf) format("truetype");
}

@font-face {
  font-family: "Geist";
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: local("Geist"), local("Geist"),
    url(../fonts/geist/Geist-Black.ttf) format("truetype");
}

:root {
  --background-main: #F7F7FB;
  --background-secondary: #f2e3ff;
  --color-white: #fff;
  --color-primary: #6200E8;
  --color-secondary: #7926E8;
  --color-blue: hsl(270deg 75% 60%);
  --font-main: #1C274C;
  --font-secondary: #212529;
  --font-optional: #373140;
  --border-color: #E0E2E7;
}

* {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-font-smoothing: antialiased;
  line-height: 1.5;
  scroll-behavior: smooth;
  background-color: #fff;
  color: #313e5b;
  position: relative;
  font-family: "Geist", sans-serif;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

#root,
#__next {
  isolation: isolate;
}

a,
a:hover {
  text-decoration: none;
}

ul {
  margin: 0;
  padding: 0.5rem;
}

li {
  list-style-type: none;
  display: inline-block;
}

p {
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.6;
  color: var(--font-main);
  margin-bottom: 10px;
}

.btn,
a {
  border-radius: 0px;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

/* .btn:focus,
.btn.focus {
  outline: 0;
  box-shadow: unset;
}

button:focus {
  outline: 1px dotted;
  outline: none;
} */

.btn:hover,
a:hover {
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: transparent;
  box-shadow: 2px 3px 5px 0px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: 2px 3px 5px 0px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: #000d1b !important;
  border-radius: 2px;
  -webkit-box-shadow: 2px 3px 5px 0px rgba(0, 0, 0, 0.3);
  box-shadow: 2px 3px 5px 0px rgba(0, 0, 0, 0.3);
}

/* .form-control:focus {
  box-shadow: none;
} */

/*--- Buttons ---*/
.btn-default {
  border-color: var(--border-color);
  border-radius: 1rem;
  font-size: 0.9em;
  font-weight: 600;
  padding: 12px 25px;
  color: var(--font-main);
}

.btn-default:hover {
  border-color: #fff;
  background: var(--border-color);
}

.btn-success {
  background: #dcf8e8;
  border-radius: 1rem;
  font-size: 0.9em;
  font-weight: 600;
  padding: 12px 25px;
  border: none;
  color: #13cd68;
}

.btn-success:hover {
  background: #13cd68;
  color: #fff;
}

.btn-warning {
  background: #fef1db;
  border-radius: 1rem;
  font-size: 0.9em;
  font-weight: 600;
  padding: 12px 25px;
  border: none;
  color: #fba10d;
}

.btn-warning:hover {
  background: #fba10d;
  color: #fff;
}

.btn-danger {
  background: #FFEBEE;
  border-radius: 1rem;
  font-size: 0.9em;
  font-weight: 600;
  padding: 12px 25px;
  border: none;
  color: #ff3b56;
}

.btn-danger:hover {
  background: #ff3b56;
  color: #fff;
}

i.mdi.mdi-check.green {
  color: #13cd68;
  font-size: 1.25em;
}

i.mdi.mdi-check.red {
  color: #ff3b56;
  font-size: 1.25em;
}

hr {
  border-color: #e0e2e7;
  opacity: 1;
}

.pageName i {
  font-size: 22px;
  line-height: 28px;
}


/*--- Header ---*/
.logo {
  max-width: 170px;
  transition: all 0.5s;
}

.logo img {
  transition: all 0.5s;
}

header {
  background: var(--background-main);
  margin: 15px;
  border-radius: 15px;
  padding: 10px;
  position: fixed;  
  width: -moz-available;
  width: -webkit-fill-available;
  z-index: 999;
  background: rgb(231 231 232 / 35%);
  background: rgb(231 231 232 / 90%);
  background: rgba(231, 231, 232, 0.45);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
}

.hCard {
  background: var(--color-white);
  border-radius: 12px;
  padding: 7px;
  display: flex;
  flex-wrap: wrap;
  width: fit-content;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  box-shadow: 0px 4px 15px #0b0b0b08;
  margin: 0px 5px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:not(:first-child):hover {
    background: var(--color-secondary);
    transition: all 0.3s ease;
    color: #fff;
  }

  img {
    width: 30px;
    height: 30px;
    margin-right: 7px;
  }

  p {
    line-height: 1.8rem;
    margin: 0 5px;
  }
}

.header_nav {
  display: flex;
  justify-content: flex-end;
}

.hCard span.mdi {
  padding: 0px 7px;
  font-size: 18px;
  line-height: 30px;
}

.sideNav {
  background: var(--background-main);
  border-radius: 15px;
  margin-left: 15px;
  height: calc(100vh - 7rem);
  width: 15rem;
  position: fixed;
  overflow: auto;
  transition: all 0.5s;

  .nav-link {
    color: #4A4C56;
    transition: all 0.3s ease;
    z-index: 1;
    position: relative;
    padding-left: 1.5rem !important;

    i {
      font-size: 20px;
      width: 25px;
      display: inline-block;
    }

    &.active,
    &:hover {
      color: var(--color-secondary);

      i {
        animation: left-edge 0.5s linear 1;
      }
    }

    &.active::before,
    &:hover::before {
      content: '';
      position: absolute;
      background-color: #ffffff;
      top: 0px;
      left: 0px;
      height: 100%;
      width: 100%;
      border-radius: 15px;
      z-index: -1;
      box-shadow: 0px 5px 18.7px 0px rgba(0, 0, 0, 0.05);
      transition: 0.3s ease top;
    }
  }
}

@keyframes left-edge {
  0% {
    transform: rotate(0deg);
  }

  35% {
    transform: rotate(-25deg);
  }

  75% {
    transform: rotate(25deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

.res-nav,
.res-navHelp {
  display: none;
}

ul li.nav-item {
  margin: 2px 0px;
}

.dropdown-toggle::after {
  position: absolute;
  left: 5px;
  top: 20px;
  transform: rotate(-90deg);
  transition: 0.3s ease transform;
  border-top: .3em solid #acacae;
}

.dropdown-toggle[aria-expanded="true"]::after {
  transform: rotate(0deg);
  transition: 0.3s ease transform;
}

.sideNav .nav-link span.ms-1,
.sideNav .parent-wrapper .nav-link {
  font-weight: 500;
  font-size: .9em;
}

.sideNav .parent-wrapper .nav-link {
  padding-left: 2rem !important;

  &::after {
    content: '';
    position: absolute;
    background: #acacae;
    top: 44%;
    left: 16px;
    height: 6.5px;
    width: 6.5px;
    border-radius: 50px;
    z-index: -1;
  }

  &:hover::after,
  &.active::after {
    background: var(--color-secondary);
  }

  &.active::before,
  &:hover::before {
    height: 100%;
  }
}

.parent-wrapper {
  padding-left: 20px;

  .nav li.nav-item {
    width: 100%;
  }
}

/*--- main Body ---*/
section.main-body {
  padding-top: 6rem;
}

.main_body {
  padding-left: 17rem !important;
  transition: all 0.5s;
  /* margin-top: -37px; */
}

.pageName {
  display: flex;
  align-items: center;
  justify-items: center;
  /* flex-wrap: wrap; */
  background: var(--color-secondary);
  color: #fff;
  padding: 11px 25px;
  border-radius: 1rem;
  font-weight: 500;
  width: fit-content;
  font-size: 1em;
  letter-spacing: 0.3px;
  min-height: 50px;
  transition: all 0.5s;

  span {
    padding: 2px 7px;
  }

  span.mdi {
    font-size: 20px;
    display: inline-block;
  }
}

.qmig-card {
  box-shadow: 0px 5px 18.47px rgba(0, 0, 0, 0.05);
  border-radius: 1rem;
}

.qmig-table {
  th {
    font-weight: 600;
    font-size: 14px;
    color: #96A5B8;
    color: var(--font-main);
    background: #F9FAFB;
    border-bottom: 1px solid #EAECF0 !important;
    padding: 8px 15px;
    vertical-align: middle;
    text-wrap: nowrap;
  }

  td {
    color: var(--font-optional);
    font-size: 12.5px;
    padding: 8px 15px;
    vertical-align: middle;

    button {
      margin: -5px 0px;
    }
  }
}

.table-hover>tbody>tr:hover {
  --bs-table-accent-bg: #F9FAFB;
}

.table-hover>tbody>tr:hover>* {
  --bs-table-bg-state: #F9FAFB;
}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder,
.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input {
top: auto;
/* padding-bottom: 0px; */
/* padding-left: 10px; */
font-size: 0.85em !important;
/* color: var(--font-optional); */
}


/* .ng-select-container .ng-value-container .ng-input */

.ng-select.ng-select-single .ng-select-container .ng-value-container,
.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input {
  top: auto;
  /* padding-bottom: 0px; */
  /* padding-left: 10px; */
  font-size: 0.85em !important;
}

.checkForm .form-label {
  width: 40%;
}

.btn-delete {
  color: #FF746A;
  padding: 3px 7px;
  font-size: 1em;

  &:hover {
    background: rgb(255 116 106 / 20%);
    color: #FF746A;
    border-radius: 0.4rem;
  }
}

.btn-download {
  color: #667085;
  padding: 3px 7px;
  font-size: 1em;

  &:hover {
    background: rgb(102 112 133 / 20%);
    color: #667085;
    border-radius: 0.4rem;
  }
}

/*---- search Bar ---*/
.custom_search {
  text-align: right;
  width: -moz-fit-content;
  width: fit-content;
  margin: auto;
  position: relative;
  display: flex;

  &.cs-r {
    margin: 0 0 0 auto;
  }

  span.mdi {
    background: var(--background-main);
    ;
    border-radius: 1rem 0 0 1rem;
    padding: 10px;
    padding: 10px 15px;
    font-size: 20px;
    position: absolute;
  }

  input {
    border-radius: 1rem;
    min-height: 51px;
    min-width: 450px;
    padding: 10px 20px 10px 55px;
    font-size: 14px;
    background: var(--background-main);
    ;
    border: none !important;
    margin: 0 auto;
    display: block;

    &:focus {
      background: #f5f5f5 !important;
    }
  }
}

.body-header-button {
  display: flex;
  justify-content: flex-end;
}

.btn-upload {
  background: var(--background-secondary);
  border-radius: 1rem;
  font-size: 0.9em;
  color: var(--color-secondary);
  font-weight: 600;
  padding: 10px 20px;
  min-height: 48px;

  span.mdi {
    font-size: 20px;
    margin-top: -10px;
    display: inline-block;
    margin-bottom: -20px;
    margin-right: 5px;
    transform: translate(0px, 2px);
  }

  &:hover {
    background: var(--color-secondary);
    color: var(--color-white);
  }

  &.btn:disabled {
    background: var(--background-secondary);
    color: var(--font-secondary);
    border: none;
  }
}

.main_h {
  color: var(--font-main);
  font-size: 18px;
  font-weight: 600;
}

.qmig-card-body {
  padding: 1rem;
}

/*--- Custom Forms ---*/
.form-label {
  color: #777980;
  font-weight: 500;
  font-size: 0.87em;
  position: relative;

  &.d-required:after {
    color: red;
    content: '*';
    margin-left: 5px;
  }
}

.form-select {
  color: #858D9D;
}

.form-select,
.form-control {
  color: #858D9D;
  border-radius: 12px;
  border-radius: 0.8rem;
  border-color: var(--border-color);
  font-size: 0.85em;
  min-height: 45px;
  padding: 10px 15px;
}

.ng-select .ng-select-container {
  min-height: 45px;
  border-radius: 0.8rem;
  border-color: var(--border-color);
}

.form-group {
  margin-bottom: 1rem;
  position: relative;
}
span.password_show {
  position: absolute;
  right: 12px;
  bottom: 10px;
}

.custom-file-upload {
  position: relative;
  cursor: pointer;
  overflow: hidden;

  input#formFile {
    height: 180px;
    opacity: 0;
    position: relative;
    z-index: 2;
  }
}

.file-upload-mask {
  border-radius: 1rem;
  border: 1px dashed var(--border-color);
  border-width: 2px;
  position: absolute;
  top: 0px;
  cursor: pointer;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: #F9F9FC;
  text-align: center;
  padding: 20px;

  img {
    max-width: 45px;
    margin: auto;
  }

  p {
    font-weight: 500;
    margin: 15px 0px;
    color: #858D9D;
  }

  .btn-upload {
    padding: 10px 20px;
    min-height: 20px;
  }
}

.offcanvas-body {
  padding: 20px;
}

.offcanvas.offcanvas-end {
  margin: 10px;
  border-radius: 1rem;
}

/*--- custom accordion ---*/
.qmig-accordion {
  .accordion-button {
    &:hover {
      text-decoration: underline;
    }

    &:focus {
      border-color: var(--color-secondary);
      box-shadow: 0 0 0 .25rem rgba(121, 38, 232, .25);
      border: none;
      box-shadow: none;
    }

    &:not(.collapsed) {
      color: var(--color-secondary);
      background-color: var(--background-secondary);
    }
  }
}

/*--- Login page ---*/
section.login-page {
  padding: 1rem 0;
  background: #f6f3fd;
}

.login-image {
  height: calc(100vh - 2rem);
  background: var(--color-secondary);
  background: linear-gradient(45deg, rgb(121 38 232 / 60%), rgb(121 38 232 / 60%)), url(../images/sign.jpg);
  border-radius: 2.5rem;
  padding: 3rem;
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  background-size: cover;

  img {
    max-width: 200px;
    position: absolute;
    top: 4rem;
  }
}

.login-content {
  text-align: center;

  h2 {
    color: #fff;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  p {
    color: #fff;
    font-size: 14px;
  }
}

.login-footer {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translate(-50%, 0);
  width: max-content;

  a {
    color: rgb(255 255 255 / 80%);
    font-size: 12.5px;
    padding: 0px 10px;
  }
}

.login-form {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 2rem);
}

.login-from-content {
  min-width: 25rem;
  text-align: center;

  h2 {
    font-weight: 700;
    color: var(--font-main);
  }
}

.login-from-content p {
  font-size: 1em;
  margin-bottom: 2rem;
  color: var(--font-secondary);
}

.login-from-content .file-upload-mask p {
  font-size: 12px;
  margin-bottom: 1rem;
}

.login-from-content .file-upload-mask {
  background: #fff;
  padding: 25px 15px;

  .btn-upload {
    padding: 10px 20px;
    font-size: 14px;
    min-height: 40px;
  }
}

.login-from-content form p {
  font-size: 0.85em;
  margin: 0;
}

.login-from-content .form-group span {
  position: absolute;
  right: 10px;
  top: 12px;
  background: #aca8ae;
  background: #a9a9a9;
  color: #fff;
  border-radius: 50px;
  width: 20px;
  height: 20px;
  font-size: 12px;
  line-height: 20px;
  cursor: pointer;
  transition: all 0.5s;
}

.login-from-content .form-group span:hover {
  background: var(--color-secondary);
  transition: all 0.5s;
}

.login-from-content .form-group i {
    position: absolute;
    top: 0;
    background: #e0e2e7;
    left: 0;
    border-radius: 0.8rem 0 0 0.8rem;
    height: 40px;
    width: 40px;
    line-height: 40px;
    font-size: 18px;
}

.login-from-content .form-control {
  padding: 10px 30px 10px 50px;
}

.login-from-content .form-control:focus {
  border-color: #fff;
}

.form-control:focus {
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 .25rem rgb(121 38 232 / 30%);
}

.login-from-content .custom-file-upload {
  input#formFile {
    height: 170px;
  }
}

.btn-sign {
  border-radius: 1rem;
  font-size: 0.9em;
  background: var(--color-secondary);
  color: var(--color-white);
  font-weight: 600;
  padding: 12px 25px;

  span.mdi {
    font-size: 20px;
    margin-top: -10px;
    display: inline-block;
    margin-bottom: -20px;
    margin-right: 5px;
    transform: translate(0px, 2px);
  }

  &:hover {
    background: var(--background-secondary);
    color: var(--color-secondary);
    border: 1px solid;
  }

  &.btn:disabled {
    background: var(--color-secondary);
    color: #fff;
  }
}

/*--- Spinner --*/
.spinner {
  display: inline-block;
  margin-bottom: -3px;
  >div {
    height: 16px;
    width: 16px;
    color: #fff;
  }
}

@keyframes SpinnerSpin {
  0% {
    opacity: 1
  }

  100% {
    opacity: .15
  }
}

.spinner-div {
  height: 16px;
  width: 16px;
  color: var(--color-primary);
  position: relative;
  top: 50%;
  left: 50%;

  >div {
    animation: SpinnerSpin 1.2s linear infinite;
    background: var(--color-primary);
    border-radius: 5px;
    height: 8%;
    left: -10%;
    position: absolute;
    top: -3.9%;
    width: 24%;

    &:nth-child(1) {
      animation-delay: -1.2s;
      transform: rotate(0.0001deg) translate(146%);
    }

    &:nth-child(2) {
      animation-delay: -1.1s;
      transform: rotate(30deg) translate(146%);
    }

    &:nth-child(3) {
      animation-delay: -1s;
      transform: rotate(60deg) translate(146%);
    }

    &:nth-child(4) {
      animation-delay: -0.9s;
      transform: rotate(90deg) translate(146%);
    }

    &:nth-child(5) {
      animation-delay: -0.8s;
      transform: rotate(120deg) translate(146%);
    }

    &:nth-child(6) {
      animation-delay: -0.7s;
      transform: rotate(150deg) translate(146%);
    }

    &:nth-child(7) {
      animation-delay: -0.6s;
      transform: rotate(180deg) translate(146%);
    }

    &:nth-child(8) {
      animation-delay: -0.5s;
      transform: rotate(210deg) translate(146%);
    }

    &:nth-child(9) {
      animation-delay: -0.4s;
      transform: rotate(240deg) translate(146%);
    }

    &:nth-child(10) {
      animation-delay: -0.3s;
      transform: rotate(270deg) translate(146%);
    }

    &:nth-child(11) {
      animation-delay: -0.2s;
      transform: rotate(300deg) translate(146%);
    }

    &:nth-child(12) {
      animation-delay: -0.1s;
      transform: rotate(330deg) translate(146%);
    }
  }
}

.spinner-white .spinner-div {
  color: #fff;

  >div {
    background: #fff;
  }
}

.custom-file-upload:focus {
  outline: 0;
  box-shadow: 0 0 0 .25rem rgba(13, 110, 253, .25);
  border-radius: 1rem;
}

/*--- Custom pagination ---*/
.custom_pagination {
  text-align: right;
  margin-top: 15px;
  padding-bottom: 1px;
}

nav .ngx-pagination .current {
  padding: 3px 12px;
  background: var(--color-secondary);
  border-radius: 0.4rem;
}

nav .ngx-pagination a:hover,
nav .ngx-pagination button:hover {
  background: var(--background-secondary);
  color: var(--color-secondary) !important;
  border-radius: 0.4rem;
}

/*--- Custom Check box ---*/
.form-check-input:checked {
  background-color: var(--color-secondary);
  border-color: var(--color-secondary);
  transition: all 0.5s;
}

.form-check-input {
  border-color: var(--font-optional);
  border-color: #dbd2e3;
  width: 1em;
  height: 1em;
  transition: all 0.5s;
}

.form-check-input:focus,
.form-check-input:hover {
  box-shadow: 0 0 0 .5rem rgb(121 38 232 / 20%);
  transition: all 0.5s;
}

.qmig-Form .form-select:focus,
.qmig-Form .form-control:hover,
.form-select:focus,
.form-control:hover {
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 .25rem rgb(121 38 232 / 20%);
  transition: all 0.5s;
}

.login-form .qmig-Form .form-control:hover {
  border-color: #fff;
  box-shadow: 0 0 0 .25rem rgb(121 38 232 / 25%);
}

/* .form-check .form-check-input {
margin-left: -2em;
} */
.form-control::placeholder {
  color: #000;
  opacity: 0.5;
  /* Firefox */
}

/*manual conversion*/
.stmt_block .btn-sign.btn-cardAdd {
  padding: 0px 5px;
  font-size: 10px;
  transition: all 0.5s;
}

button.btn.btn-outline-primary.btn-icon-text {
  padding: 2px 5px;
  font-size: 10px;
}

.stmt_block .btn i {
  font-size: 12px;
}

.stmt_block {
  background: rgb(0 0 0 / 3%);
  border-radius: 10px;
}
.stmt_block .form-control {
  border: none;
  background: transparent;
  min-height: 30px;
  color: var(--font-secondary);
  &:hover{
    border: none;
    box-shadow: none;
  }
}
.stmt-right .form-control {
  border: 1px solid var(--border-color);
  background: #fff;
}
.row.statement-row:hover {
  background: #f4f4f5;
}
.row.statement-row.active {
  background: rgb(0 0 0 / 2%);
  margin: 5px 0px;
}
.row.statement-row {
  margin: 0;
  padding: 5px;
  border-radius: 10px;
  position: relative;
}
.btn-eye {
  border: none;
  color: var(--color-secondary);
  padding: 0px 10px;
  opacity: 0;
  transition: opacity 0.5s;
}
.delta-controls {
  position: absolute;
  display: flex;
  flex-wrap: wrap;
  width: 64px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.statement-row:hover .btn-eye, .statement-row.active .btn-eye{
  opacity: 1;
  transition: all 0.5s;
}
span.stmt_number {
  font-size: 13px;
  margin-bottom: 5px;
  display: block;
}
.border_right {
  border-right: 1px solid #eee;
}

.mt-22 {
  margin-top: -5px;
  margin-bottom: 3px;
}

.stmt_block p {
  font-size: 11.5px;
  margin-bottom: 7px;
  cursor: pointer;
}

.stmt_list {
  max-height: 600px;
  overflow: auto;
}

.stmt_list::-webkit-scrollbar-thumb {
  background-color: #ff8901;
  outline: 1px solid #ff8901;
}

.stmt_list::-webkit-scrollbar {
  width: 7px;
}

.stmt_block textarea.form-control {
  border-radius: 10px;
}

.border_left span.stmt_show .stmt_show_btn i.mdi.mdi-chevron-down {
  top: -16px;
  right: auto;
  left: -25px;
}

span.stmt_show .stmt_show_btn i.mdi.mdi-chevron-down {
  position: absolute;
  top: -12px;
  right: 50%;
  background: var(--color-secondary);
  color: #fff;
  border-radius: 50px;
  width: 25px;
  height: 25px;
  font-size: 24px;
  cursor: pointer;
  /* opacity: 0; */
  transition: 0.5s all ease;
  line-height: 27px;
}

span.stmt_show {
  position: relative;
  display: block;
}

/* span.stmt_show:hover i.mdi {
   opacity: 1 !important; 
   transition: 0.5s all ease; 
} */
.qmig_btn_group {
  position: absolute;
  top: 5px;
  right: 5px;
  display: none;
  .btn-sign {
    border-radius: 0.65rem;    
    padding: 2px 10px;
    line-height: 25px;
    font-size: 12px;
    /* background: var(--background-secondary);
    color: var(--color-secondary);
    border: none;
    &:hover{
      color: var(--background-secondary);
      background: var(--color-secondary);
    } */
  }
}
.row.statement-row:hover  .qmig_btn_group{
  display: block;
}


span.stmt_show .btn-sign {
  padding: 2px 10px;
  font-size: 10.7px;
}

.stmt_card {
  position: relative;
}
.stmt_merge i.mdi {
  background: #4472c4;
  color: #fff;
  border-radius: 50px;
  width: 25px;
  height: 25px;
  display: block;
  text-align: center;
  line-height: 25px;
  font-size: 20px;
}

.stmt_merge {
  position: absolute;
  top: 48%;
  right: -29px;
}

textarea {
  resize: auto;
  overflow-x: hidden;
}

.qmig_btn_group i.mdi {
  font-size: 16px;
  margin-top: -5px;
  display: inline-block;
  margin-bottom: -5px;
  transform: translate(0px, 2px);
}

.qmig_btn_group .btn-sign span {
  display: none;
  width: 0px;
  transition: display 0.5s;
  margin-left: 4px;
  font-weight: 500;
}

.qmig_btn_group .btn-mdown i.mdi {
  transform: rotate(180deg);
}

.qmig_btn_group .btn-sign:hover span {
  display: inline-block;
  width: fit-content;
  transition: width 0.5s;
}
.btn-stmt {
  background: #4472c4;
  color: #fff;
  font-size: 11px;
  font-weight: 400;
  border-radius: 7px;
  padding: 2px 10px;
}

span.stmt_move_rt {
  position: absolute;
  right: -24px;
  background: var(--color-secondary);
  color: #fff;
  border-radius: 100%;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  cursor: pointer;
}
.stmt_block.bg_active {
  background: rgb(146 195 83 / 20%);
}

.stmt_block.bg_active .form-control {
  background: #bbee7a;
}

.stmt_block.bg_modified {
  background: rgb(146 195 83 / 10%);
  background: rgb(70 195 95 / 10%);
}

.stmt_block.bg_modified .form-control {
  background: rgb(0 194 129 / 3%);
  border: none;
}

.stmt_block.bg_modified .form-control::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #bbee7a;
  opacity: 1;
  /* Firefox */
}

.bg_new .form-control {
  background: #eceeff;
  border-color: #eceeff;
}

.stmt_block.bg_new {
  background: rgb(204 207 235 / 40%);
}

.bg_deleted .form-control {
  background: #ffdede;
}

.bg_deleted .form-control::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  color: #ff6262;
  opacity: 1;
  /* Firefox */
}

.stmt_block.bg_deleted {
  background: #ffb6b6;
  background: rgb(255 98 98 / 10%);
}

hr.line {
  width: 100%;
  margin: 0;
}

textarea.form-control.cust-textarea {
  min-height: 150px !important;
}

.bg_only .form-control {
  background: #f2e3ff;
  border-color: #f2e3ff;
}

.bg_only .form-control::placeholder {
  color: #ff8901;
  opacity: 1;
  /* Firefox */
}

.bg_only {
  background: rgb(255 137 1 / 10%);
}

.bg_not .form-control {
  background: #ffe8b0;
  border-color: #ffe8b0;
}

.bg_not {
  background: #8e24aa;
  background: rgb(142 36 170 / 10%);
  background: rgb(255 205 78 / 40%);
}

.cc_Note {
  padding: 20px;
  background: #f9f9f9;
  border-radius: 1rem;
}

.cc_Note ul {
  margin: 0;
}

.cc_Note li {
  list-style: none;
  position: relative;
  font-size: 12.5px;
  line-height: 30px;
  width: 50%;
}

.cc_Note li span {
  width: 20px;
  height: 20px;
  display: inline-block;
  transform: translate(0px, 5px);
}

.cc_Note li span.bg_not {
  background: #8e24aa;
  background: #ffcc4e;
  background: #ffe8b0;
}

.cc_Note li span.bg_only {
  background: var(--color-secondary);
  background: #f2e3ff;
}

.cc_Note li span.bg_deleted {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  background: #ff6262;
  background: #ffdede;
}

.cc_Note li span.bg_modified {
  background: #bbee7a;
  background: #46c35f;
  background: #e5f7ec;
}

.cc_Note li span.bg_new {
  background: #cccfeb;
  background: #6372ff;
  background: #eceeff;
}

.col-md-25 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 20%;
  flex: 0 0 20%;
  max-width: 20%;
  padding: 0px 10px;
}
/*--- docuemnt ---*/
.documentCard {
  border: 1px solid var(--border-color);
  border-radius: 0.8rem;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  transition: all 0.5s;
  margin-bottom: 8px;
}

.documentCard:hover {
  transition: all 0.5s;
  background: #f8f8f8;
}

.docCicon {
  background: rgb(255 59 86 / 15%);
  padding: 10px;
  border-radius: 0.7rem;
  margin-right: 7px;
  font-size: 1.5em;
  max-width: 45px;
  height: 45px;
  line-height: 23px;
  color: #ff3b56;
}

.docCicon span.mdi {
  transform: translate(0px, 1px);
  display: block;
}

.docCContent h4 {
  font-size: 0.9rem;
  color: #000;
  margin: 0;
}

.docCContent p {
  margin: 0;
  font-size: 0.8em;
}

.docCContent {
  width: 53%;
  padding-top: 3px;
}

.docCAction {
  width: 26%;
}

.documentCard.xlsx,
.documentCard.\.csv {
  .docCicon {
    background: #dcf8e8;
    color: #13cd68;
  }
}

.documentCard.\.vtt {
  .docCicon {
    background: #e7e7e8;
    color: #373140;
  }
}

.close-sidebar {
  position: fixed;
  z-index: 100;
  background: var(--background-secondary);
  color: var(--color-secondary);
  font-size: 1.5rem;
  width: 55px;
  height: 55px;
  line-height: 50px;
  text-align: center;
  border-radius: 50rem;
  cursor: pointer;
  transition: all .5s;
  left: -25px;
  border: 3px solid #fff;
  padding: 0;
  margin-top: -15px;
  span.mdi {
    transform: translate(7px, 0px);
    display: block;
  }
}


/*--- Header Toggle ---*/
/* body.sidebar-icon-only img.logo, body.sidebar-icon-only .close-sidebar .mdi-chevron-left {
display: none;
transition: all 0.5s;
}
body.sidebar-icon-only img.d-none {
display: block !important;
width: 45px;
transition: all 0.5s;
} */


body.sidebar-icon-only .sideNav {
  width: 0;
  padding: 0 !important;
  transition: all 0.1s;
}

body.sidebar-icon-only .sideNav .nav-link {
  padding-left: 12px !important;
}

body.sidebar-icon-only .dropdown-toggle::after {
  display: none;
}

body.sidebar-icon-only span.ms-1 {
  display: none;
}

/* body.sidebar-icon-only li.nav-item.hover-open span.ms-1{
display: block;
} */
body.sidebar-icon-only .sideNav .close-sidebar {
  left: 73px;
}

body.sidebar-icon-only .main_body {
  padding-left: 1rem !important;
  transition: all 0.5s;
  /* margin-top: -52px; */
}

body.sidebar-icon-only .pageName {
  transition: all 0.5s;
  margin-left: 20px;
}

body.sidebar-icon-only .close-sidebar span.mdi::before {
  content: "\F0142";
}

/* body.sidebar-icon-only .main_body .qmig-card-body .col-xl-3 {
width: 20%;
transition: all 0.5s;
} */
.alert {
  padding: 0;
  margin: 0;
}

.login-logo {
  display: none;
}

.password_label {
  position: relative;
}

.password_label .eye_design {
  position: absolute;
  right: 15px;
  top: 35px;
  z-index: 9;
}

/*--- Help Center ---*/
.res-navLogo {
  display: none;
}

.navbar-expand-xl .navbar-collapse {
  flex-wrap: wrap;
}

.navbar-vertical-content {
  position: relative;
  z-index: 1;
  background: #f7f7fb;
}

.sideNav-flex {
  height: 79vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.help_center {
  /* padding: 10px; */
  position: relative;
  max-width: 100%;
  z-index: 0;
}

.help_cd {
  text-align: center;
  position: absolute;
  padding: 10px;
  top: 40%;

  /* left: 16%; */
  /* max-width: 80%; */
  p {
    color: #fff;
    font-size: 0.75em;
    letter-spacing: 0.2px;
  }

  .btn-upload {
    font-size: 0.8em;
    padding: 10px 20px;
    min-height: 40px;
    /* pointer-events: none; */
  }
}

.btn-sync {
  color: var(--color-secondary);
  background: var(--background-secondary);
  border-radius: 0.7rem;
  font-size: 14px;
  padding: 4px 10px;
  font-weight: 500;
  letter-spacing: 0.3px;
  margin-right: 5px;
  max-height: 40px;
  margin-top: 22px;

  &:hover {
    background: var(--color-secondary);
    color: var(--color-white);
  }
}

/*--- Custom ToolTip ---*/
.qmig-tooltip {
  margin-left: 5px;
  color: var(--font-optional);
  position: relative;
  display: inline-block;

  &:before {
    content: "\F1C6F";
    display: inline-block;
    font: normal normal normal 16px / 1 "Material Design Icons";
  }
}

.qmig-tooltip i {
  visibility: hidden;
  min-width: 200px;
  background-color: var(--font-secondary) !important;
  /* Change this to your desired color */
  color: #fff;
  /* Change text color if needed */
  border-radius: 0.5rem;
  text-align: center;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -100px;
  opacity: 0;
  transition: opacity 0.3s;
  font-style: normal;
}

.qmig-tooltip i::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #555 transparent transparent transparent;
}

.qmig-tooltip:hover i {
  visibility: visible;
  opacity: 1;
}
.res-d-block{
  display: none;
}

/*--- Dashboard ---*/
.welcome_strip {
  border: 1px solid #E3E3E3;
  border-radius: 1rem; 
  margin-top: 30px;
}
.ws_img {
  transform: translate(0px, -35px);
  margin-bottom: -35px;
  img {
    display: block;
    margin: 0 auto;
    max-width: 150px;
  }
}
.ws_content {
  padding: 30px;
  p {
    font-size: 1rem;
    font-weight: 500;
    color:var(--font-optional);
    margin: 15px 0px;
  }
  h3 {
    font-weight: 700;
    font-size: 2.1rem;
    color:var(--font-main)
  }
}
span.col1{
  color: var(--color-secondary);
}
.qmigTabs .nav {
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 0;
  .nav-item {
    margin: 4px;
  }
  .nav-link {
    border-radius: 0.7rem;
    font-size: 14px;
    padding: 7px 20px;
    font-weight: 500;
    color: var(--font-main);
    &.active {
      background: #f4f4f8;
  }
  }
}
.qmigTabs {
  box-shadow: 0px 5px 18.47px rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  &.QHT{    
    width: fit-content;
    float: right;
  }
}

select.form-select.form-small {
  padding: 5px;
  min-height: 30px;
  max-width: 150px;
  font-size: 0.75em;
  border-radius: 0.45rem;
  display: block;
  margin: 7px 7px 0 auto;
}
.dash-card h4 {
  font-weight: 400;
  font-size: 2.5rem;
  color: var(--font-optional);
    span {
      font-weight: 300;
      font-size: 1.25rem;
      color: #999;
    }
}
.bg-success {
  background: #13cd68 !important;
}
.bg-warning{
  background: #fba10d !important;
}
.dash-cItem:last-child span{
  background: #13cd68 !important;
}
.dash-content {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  background: #f4f4f8;
  border-radius: 1rem;
  padding: 10px;
  h3 {
    font-size: 1.25rem;
    color: var(--font-secondary);
    margin: 0px;
  }
  p{
    margin: 0;
    font-size: 0.7rem;
  }
}

.dash-cItem{
  width: 33%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  padding: 0px 8px;
  span {
    width: 27px;
    height: 27px;
    display: inline-block;
    background: var(--color-primary);
    font-size: 1.15rem;
    line-height: 27px;
    border-radius: 100px;
    color: #fff;
    text-align: center;
  }
  &:not(:last-child) {
    border-right: 1px solid #ddd;
    margin-left: -5px;
  }
}

.btn-small {
  min-height: 15px;
  font-size: 12px;
  padding: 7px 30px;
  border-radius: 0.7rem;
}
.qmigTabs.vacuum-tab .nav {
  justify-content: flex-start;
}

/*--- Error Page --*/
section.error_sec {
  height: 100vh;
  display: flex;
  align-items: center;
}
.error_content {
  text-align: center;

  h1 {
    color: #ecf0f1;
    font-size: 250px;
    font-weight: 600;

    img {
      display: inline-block;
      max-width: 300px;
      transform: translate(0px, -35px);
      margin: 0px 20px;
    }
  }

  h3 {
    font-size: 35px;
    color: var(--font-main);
    font-weight: 900;
  }

  p {
    font-size: 18px;
    color: var(--font-optional);
    max-width: 60%;
    display: block;
    margin: auto;
  }
}
.m-w100 {
  max-width: 100% !important;
  margin: 10px !important;
}
.dashBoard_count {
  display: flex;
  justify-content: end;
}
.dashBoardC1 {
  padding: 5px 20px;
  p {
    margin: 0;
    font-size: 0.8rem;
    color:var(--font-optional)
  }
  h3 {
    margin: 0;
    font-size: 1.4rem;
    color:var(--font-secondary)
  }
  &:not(:last-child){    
    border-right: 1px solid #ddd;
  }
}

/*--- help center ---*/
.help-center-header {
  h3 {
    font-weight: 800;
    font-size: 2.5rem;
  }
  p {
    font-size: 1.15rem;
    max-width: 70%;
    margin: auto;
  }
}
.help-card {
  border: 1px solid #e0e2e7;
  border-radius: .8rem;
  padding: 10px;
  transition: all .5s;
  margin-bottom: 8px;
  img {
    max-width: 30px;
    margin-bottom: 10px;
  }
  h5{
    font-weight: 600;
    font-size: 1.2rem;
  }
  p{
    color: var(--font-optional);
  }
}
.help-logout a {
  color: var(--font-main);
  text-decoration: underline;
  &:hover{
    text-decoration: none;
  }
}
.sideNav.helpNav{
    width: 19rem;
    margin-left: 13px;
    position: relative;
}
.helpCenter_desc {
  margin: 0.5rem 0;
}
.helpNav .dropdown-toggle::after {
  position: absolute;
  left: auto;
  right: 12px;
  top: 12px;
  transform: none;
  transition: 0.3s ease transform;
  display: inline-block;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border: none;
  width: 1rem;
  height: 1rem;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='none' stroke='%23212529' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M2 5L8 11L14 5'/%3e%3c/svg%3e");
}
.helpNav .dropdown-toggle[aria-expanded=true]::after {
  transform: rotate(178deg);
  transition: 0.3s ease transform;
}
.sideNav.helpNav .nav-link {
  padding-left: 0.75rem !important;
  padding-right: 1.5rem !important;
}
.sideNav.helpNav .nav-link.active::before, .sideNav.helpNav .nav-link:hover::before {
  display: none;
}
.sideNav.helpNav .parent-wrapper .nav-link::after {
  left: -2px;
}
.help-center-description {
  padding-left: 1rem;
}
.help-center-description ul {
  padding: 0;
  margin-bottom: 15px;
}
.help-center-description ul li {
  list-style-type: initial;
  display: list-item;
  font-size: 14px;
  margin-left: 30px;
  margin-bottom: 5px;
}
.v-pageName {
  background: #f2e3ff;
  color: #7926e8;
  font-size: 0.85rem;
  font-weight: 600;
  text-align: center;
  transition: all .5s;
  border: 3px solid #fff;
  writing-mode: vertical-lr;
  padding: 7px 2px;
  border-radius: 0.4rem;
  letter-spacing: .5px;
  transform: rotate(180deg);
  position: fixed;
  left: -6px;
  top: 140px;
}
.avatar {
  width: 30px;
  height: 30px;
  border-radius: 100%;
  text-align: center;
  line-height: 30px;
  color: #fff;
  text-transform: uppercase;
  font-size: 12.5px;
  letter-spacing: 1px;
  font-weight: 500;
}
.badge-csuccess {
  background: rgb(36 167 71 / 15%);
  font-weight: 400;
  letter-spacing: 0.2px;
  color: #24a747;
}
.badge-cwarning {
  background: rgb(247 200 40 / 20%);
  font-weight: 400;
  letter-spacing: 0.2px;
  color: #f7c828;
}
.badge-cdanger {
  background: rgb(255 0 0 / 20%);
  font-weight: 400;
  letter-spacing: 0.2px;
  color: #ff0000;
}

/*--- Dashboard ---*/
.custom-legend {
  display: flex;
  justify-content: center;
}
.custom-legend .legend-card {
  position: relative;
  font-size: 12px;
  margin: 0px 15px;
  padding-left: 40px;
}
.custom-legend .legend-card:before {
  content: '';
  position: absolute;
  width: 40px;
  height: 15px;
  background: #6200E8;
  left: -5px;
  top: 2.5px;
}
.custom-legend .legend-card:nth-child(2)::before {
  background: #e5e5e5;
}
.badge-clower {
  background: rgba(88, 92, 120, 0.2);
  font-weight: 400;
  letter-spacing: 0.2px;
  color: #1c1b1b;
}
/*--- Custom Confirm Box ---*/
.fadeIn {
    animation: fadeIn .5s
}
.zoomIn {
    animation: zoomIn .5s
}
.zoomOut {
    animation: zoomOut .5s
}
@keyframes zoomIn {
    0% {
        transform: scale(0)
    }
    100% {
        transform: scale(1)
    }
}
@keyframes zoomOut {
    0% {
        transform: scale(1)
    }
    100% {
        transform: scale(0)
    }
}
@keyframes fadeIn {
    0% {
        opacity: 0
    }
    100% {
        opacity: 1
    }
}

.dialog-ovelay {
    height: 100vh;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.50);
    z-index: 999999
}
.dialog-ovelay .dialog {
  position: relative;
  width: 30%;
  background-color: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, .2);
  border-radius: 0.7rem;
  overflow: hidden;
  top: 40%;
  left: 38%;
}
.dialog-ovelay .dialog .dialog_h {
    padding: 10px 8px;
    background-color: #f6f6f7;
    border-bottom: 1px solid #e5e5e5
}
.dialog-ovelay .dialog .dialog_h h3 {
    display: inline-block
}
.dialog-ovelay .dialog .dialog-msg {
    padding: 12px 10px
}
.dialog-ovelay .dialog .dialog-msg p{
    margin: 0;
    font-size: 15px;
    color: #333
}
.dialog-ovelay .dialog footer {
    border-top: 1px solid #e5e5e5;
    padding: 8px 10px
}
.dialog-ovelay .dialog footer .controls {
    direction: rtl
}
.dialog-ovelay .dialog footer .controls .button {
    padding: 5px 15px;
    border-radius: 3px
}
.customConfirm{
  display: none;
}

.q-tooltip {
  position: relative;
  display: inline-block;
  border-bottom: 1px dotted black;
}

.q-tooltip i {
visibility: hidden;
width: 160px;
background-color: rgb(11, 11, 11);
color: #fff;
text-align: center;
border-radius: 9px;
padding: 5px 0;

/* Position the tooltip */
position: absolute;
z-index: 1;
top: 140%;
left: 50%;
margin-left: -60px;
}

.q-tooltip i::after {
content: "";
position: absolute;
bottom: 100%; /* Position the arrow at the bottom of the tooltip */
left: 50%;
margin-left: -5px; /* Center the arrow horizontally */
border-width: 5px;
border-style: solid;
border-color: var(--font-secondary) transparent transparent transparent; /* Arrow color matches tooltip background */
}

.q-tooltip:hover i {
  visibility: visible;
  opacity: 1;
}

/* Delta Control */
  
.deltaComparison {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.comparison-block {
  width: 49%;
}

.cr-header {
  padding-top: 10px;
  color: #ad1f1f;
  display: flex;
  font-size: 14px;
  justify-content: space-between;
}
.cr-header div {
  display: ruby;
}
.cr-header span {
  color: #676f7e;
}
.ca-header {
  padding-top: 10px;
  color: #064;
}

.comparison-content {
  padding: 10px;
  background-color: #f8f8f8;
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 150px;
  overflow-y: auto;
  border-radius: 0.4rem;
}

.comparison-content[contenteditable="true"]:focus {
  outline: 2px solid #007bff;
}

.comparison-content div {
  padding: 5px;
}
.dash-card .main_h {
  font-size: 1.0rem;
}
hr.dash-dotted {
  border-top: 0.1rem dashed #e0e2e7;
}
body.sidebar-icon-only .main_body section.dashboard_reports .main_h.mt-2 {
  padding-left: 20px;
}
.dashCH h5 {
  font-size: 16px;
  font-weight: 600;
}
.dashCH h1 {
  margin: 35px 0;
  font-size: 50px;
}

.dashCH h2 {
    margin: 20px 0;
    font-size: 1.4rem;
}
.wd-animate{
  animation: fadeIn 0.5s ease-in-out;
}
.form-control.form-small {
    padding: 5px;
    min-height: 30px;
    max-width: 150px;
    font-size: 0.75em;
    border-radius: 0.45rem;
    display: block;
}
.multi-sel.ng-select .ng-select-container {
    min-height: 30px;
    border-radius: 0.45rem;
    margin-top: 10px;
}
.form-control.form-small.w-100{
  max-width: 100%;
}
/*--- Eswara for performance module end --*/
.perfChecks {
  display: flex;
  flex-wrap: wrap;
}
.perfChecks .form-check {
  width: 20%;
  font-size: 15px;
}
.offcanvas.offcanvas-end .file-upload-mask p {
  font-size: 10.7px;
}
.dropdown-toggle.active::after{  
  transform: rotate(0deg);
}
.pagination-controls {
  position: absolute;
  bottom: 10px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.pagination-controls button {
  padding: 1px 2px;
  cursor: pointer;
  background-color: #4c00b5;
  color: white;
  border: none;
  font-size: 16px;
  border-radius: 5px;
}

.pagination-controls .prev-button {
  position: absolute;
  left: 10px;
}

.pagination-controls .next-button {
  position: absolute;
  right: 10px;
}

.pagination-controls button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/*--- data Migration Dashboard ---*/
.DBSHead {
    display: flex;
    width: 100%;
    justify-content: space-between;
}
.DBSHead p, .DBSHead span {
    color: var(--color-secondary);
    font-weight: 500;
    letter-spacing: 0.5px;
}
.DBSHead span {
    font-size: 1.2rem;
    margin-top: -8px;
}
.DBSBody {
    position: relative;
    display: flex;
    justify-content: space-between;
    h3 {
        font-weight: 700;
        font-size: 1.55rem;
        margin: 0;
    }
    span {
        color: #777;
        font-size: 0.85rem;
    }
    .arrow-show {
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
    }
}
.DBSFooter {
  margin-top: 5px;
  p {
      font-weight: 500;
      margin-bottom: 5px;
  }
  .progress {
    height: 10px;
    background: var(--background-secondary);
    --bs-progress-bar-bg: var(--color-secondary);
  }
}
.cagent_status {
  p {
    margin-bottom: 5px;
    font-size: 0.8rem;
    span {
        font-weight: 500;
        font-size: 0.9rem;
    }
  }
  .form-check {
      font-size: 0.9rem;
      font-weight: 500;
      margin-left: 30px;
      margin-bottom: 20px;
  }
}
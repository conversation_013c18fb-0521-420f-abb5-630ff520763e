import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GetFilesFromExpath, deleteFile, documentsList } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './reports.component.html',
  styles: ``
})
export class ReportsComponent {

  /*-Search Filter-*/
  searchText: string = '';

  /*--- Airflow Reports Pagination   ---*/
  pageNumber: number = 1;
  pi: number = 10;
  /*--- Airflow Reports   ---*/
  airflowFiles: any=[];
  fileStatus: any;

  /*--- Airflow Reports Download  ---*/
  fileResponse: any
  spinDownload: boolean = false;
  pageName: string = ''
  constructor(private titleService: Title,private toast: HotToastService, private datamigrationservice: DataMigrationService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.pageName = this.route.snapshot.data['name'];
  }


  ngOnInit(): void {
this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.fetchAirflowFiles()
  }
  onKey() {
    this.pageNumber = 1;
  }
  /*--- Get Reports   ---*/
  fetchAirflowFiles() {
    const path = "Validation_Reports";// 
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      this.airflowFiles = data
      this.fetchAirflowFiles1()
    })
  }
  fetchAirflowFiles1() {
    var tempfiles: any = []
    const path = "Summary_Validation_Reports";// "Summary_validation/"
    this.datamigrationservice.GetFilesFromExpath(path).subscribe((data: any) => {
      tempfiles = data;
      if (tempfiles.length > 0) {
        tempfiles.forEach((itemk: any) => {
          this.airflowFiles.push(itemk)
        })
      }
      this.airflowFiles.sort((a: any, b: any) => {
        const dateA = new Date(a.created_dt).getTime();
        const dateB = new Date(b.created_dt).getTime();
        return dateB - dateA; // For descending order
      });
    })
  }
  /*--- Download file   ---*/
  downloadFile(fileInfo: any) {
    this.datamigrationservice.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spinDownload = false

    })
  }

  /*--- Delete Project Reports   ---*/

  deleteFiles(path: string) {
    this.datamigrationservice.deleteFile(path).subscribe({
      next: (data: deleteFile) => {
        //console.log(data)
        this.fileStatus = data.message
        this.fetchAirflowFiles();
        this.toast.success(this.fileStatus)
      },
      error: (error) => {
        this.toast.error(error.message)
      },
    });
  }

  /*--- Apply Filter   ---*/

  applyFilter(filterValue: string) {
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
    this.airflowFiles = this.airflowFiles.filter((el: any) => { return el.fileName.includes(filterValue) })
  }
}

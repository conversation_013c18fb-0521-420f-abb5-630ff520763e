import { Component, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GetObjectCode, GetObjectType, GetProjectTgtDeployStatusSelect, ObjectCode, ObjectSelect, ObjectType, ObjectsSelect, RedisCacheInsertion, conList, deleteFile, documentsList, fileStatus, manualSave, projectConRunTblInsert, redisCommand, schemalist } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { ActivatedRoute, RouterLink, RouterLinkActive } from '@angular/router';
import { Title } from '@angular/platform-browser';
declare let $: any;

@Component({
  selector: 'app-manual-conversion',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './manual-conversion.component.html',
  styles: ``
})
export class ManualConversionComponent {
  //Global Variables
  projectId: string;
  runNo: string = '';

  objectSelected: boolean = false;
  currentSourceConnection: any;
  objType: any;
  srcId: any;
  schemaName: string = '';
  //Target Connection Name
  targetList: any;
  currentTargetConnection: string = '';
  displayTargetConnection: boolean = false;

  // Get Run Number 
  runNumbersList: { iteration_id: string }[] = [];
  schemaList: any
  currentRunNumber: string = '';
  currentSchema: string = '';
  currentName: string = '';

  //object names
  objectNames: any;
  opt: string = ''
  //Grid 
  typeSle: boolean = false;
  typeSl: boolean = false;
  @ViewChild('div1', { static: false }) div1!: ElementRef;
  @ViewChild('div2', { static: false }) div2!: ElementRef;

  //show statement
  currentStmt: string = ''
  statementShow: boolean = false;
  currentObject: string = ''

  //conversion statement
  statementList2: any;
  statementList: any;
  stmtcV: number = 0;

  //object type
  objectTypeList: any;
  objtypeid: any

  //save button
  saveSpin: boolean = false;
  buttonDisable: boolean = true;
  ManualOutputCode: any = [];

  //deploy button
  deploySpin: boolean = false;
  deployButton: boolean = true;

  //customer alert
  snackMessage: any;

  //deploy time stamp
  TgtDeployData: any=[];
  statusMessages: any

  //deploy status
  deploystatuss: any;
  deployTime: any
  totaldeploy: any
  observations: any
  deploystatusSpin: boolean = false

  hidedata: boolean = false;

  deploymentForm: any
  pageName: string = ''
  manualForm: any;
  deploymentLog = this.formBuilder.group({
    timeStamp: [''],
    status: [{ value: '', disabled: true }],
    time: [{ value: '', disabled: true }],
    cumulativeTime: [{ value: '', disabled: true }],
    statusMessage: [''],
    observationTime: [''],
  });
  migtypeid: string = ""
  constructor(private titleService: Title, private toast: HotToastService, private codemigrationservice: CodeMigrationService, public formBuilder: FormBuilder, private el: ElementRef, private renderer: Renderer2, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getRunNumber()
    this.GetConsList()
    this.deploymentForm = this.formBuilder.group({
      conname: ['', [Validators.required]],
      time_taken_min: ['', [Validators.required]],
      observations: ['', [Validators.required]]
    });
    this.manualForm = this.formBuilder.group({
      runNumber: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      targetSchema: ['', [Validators.required]],
      objectType: ['', [Validators.required]],
      objectName: ['', [Validators.required]],
      objectStatus: ['', [Validators.required]]
    })
  }

  /*--Run Number*/
  getRunNumber() {
    this.codemigrationservice.RuninfoSelect().subscribe((data: any) => {
      this.runNumbersList = data['Table1']
    });
  }
  /*--fetchschema*/
  srcSchemaList: any = []
  tgtSchemaList1: any = []
  getSchema(value: string) {
    this.srcSchemaList = []
    this.tgtSchemaList1 = []
    this.schemaList = []
    this.objectTypeList = []
    this.objectNames = []
    this.statementList = []
    this.manualForm.get('schema')?.setValue('new value');
    this.manualForm.get('objectStatus')?.setValue('new value');
    this.currentRunNumber = value;
    this.objectSelected = false
    this.deploymentLog.reset()
    this.codemigrationservice.SchemaSelect(value).subscribe((data: schemalist) => {
      this.schemaList = data['Table1'];
      this.schemaList.forEach((item: any) => {
        let srcobj = {
          schema_name: item.src_schema_name,
          connection_id: item.src_connection_id,
          schema_id: item.src_schema_id
        }
        this.srcSchemaList.push(srcobj)
        let tgtobj = {
          schema_name: item.schema_name,
          connection_id: item.connection_id,
          schema_id: item.schema_id
        }
        this.tgtSchemaList1.push(tgtobj)
      })
    });
    if(this.migtypeid=="33")
      {
        this.getObjectName("");
      }
  }
  selectedSrcSchem: any
  srcConId: any
  selectSrcSchema(value: any) {
    this.objectTypeList = []
    this.objectNames = []
    this.statementList = []
    this.objectSelected = false;
    this.displayTargetConnection=false;
    this.deploymentLog.reset()
    var sch = this.srcSchemaList.filter((item: any) => {
      return item.schema_id == value
    })
    if (sch.length > 0) {
      this.srcConId = sch[0].connection_id
      this.selectedSrcSchem = sch[0].schema_name
    }
  }
  get f(): any {
    return this.deploymentForm.controls;
  }
  get ff(): any {
    return this.manualForm.controls;
  }
  /*--fetch object type */
  selectedtgtschemaname: any
  seltgtconid: any
  getObjectType(value: string) {
    var tgtsch = this.tgtSchemaList1.filter((item: any) => {
      return item.schema_id == value
    })

    if (tgtsch.length > 0) {
      this.selectedtgtschemaname = tgtsch[0].schema_name
      this.seltgtconid = tgtsch[0].connection_id
    }
    this.currentSourceConnection = this.schemaList.filter((item: any) => { return item.schema_id == value })
    this.objectTypeList = []
    this.objectNames = []
    this.statementList = []
    this.manualForm.get('objectStatus')?.setValue('new value');
    this.currentObject = value;
    const newObjectType: GetObjectType = {
      runno: this.currentRunNumber,
      schema: this.schemaName
    }
    this.codemigrationservice.GetObjectType(newObjectType).subscribe((data: ObjectType) => {
      this.objectTypeList = data['Table1']
      //console.log(this.objectTypeList)
    })

  }
  /*--object type */
  getobjtypeid(value: any) {
    this.objtypeid = value
    var objty = this.objectTypeList.filter((item: any) => {
      return item.object_type_id == value
    })

    if (objty.length > 0) {
      this.objType = objty[0].object_type
    }
  }

  /*--object status*/
  getObjectName(value: string) {
    this.hidedata = true;
    this.opt = value
    this.objectNames = []
    this.statementList = []
    this.currentSchema = value;
    const Obj: ObjectsSelect = {
      runno: this.currentRunNumber,
      schemaid: this.currentObject,
      ObjTypeId: this.objtypeid,
      deployed: value
    }
    if (this.migtypeid == "33") {
      Obj["schemaid"] = "null";
      Obj["ObjTypeId"] = "33"
      Obj["deployed"]="'f'"
    }
    if(value==""){

    }
    //console.log(Obj)
    this.codemigrationservice.ObjectsSelect(Obj).subscribe((data: ObjectSelect) => {
      this.objectNames = data['Table1']
    });
  }
  /*--object Names*/
  getObjectStatus(value: string) {
    var objData = this.objectNames.filter((item: any) => {
      return item.object_name == value
    })
    if (objData.length > 0) {
      this.srcId = objData[0].src_id
      this.GetDeployStatus()
    }
    this.statementList = []
    this.currentName = value;
  }
  getObjectCode() {
    this.objectSelected = true
    var opt = ""
    if (this.opt == "true") {
      opt = "D"
    }
    else {
      opt = "F"
    }
    const newObjectCode: GetObjectCode = {
      Runnid: this.currentRunNumber,
      ObjTypeId: this.objtypeid,
      Schemaid: this.currentObject,
      ObjectName: this.currentName,
      Opt: opt
    }
    if (this.migtypeid == "33") {
      newObjectCode["Schemaid"] = "null";
      newObjectCode["ObjTypeId"] = "33"
      newObjectCode["Opt"]="f"
    }
    this.codemigrationservice.GetObjectCode(newObjectCode).subscribe((data: ObjectCode) => {
      this.statementList = data['Table1'];
      this.statementList.filter((item: any) => { return item.updated_code == '' ? item.updated_code = item.tgt_code : item.updated_code = item.tgt_code })
      //console.log(this.statementList)
      this.setDivHeight();
    });

    this.typeSle = true
    this.typeSl = true

    this.getStatments(newObjectCode)
  }

  /*--Get statements*/

  getStatments(newObjectCode: GetObjectCode) {
    this.codemigrationservice.GetObjectCode(newObjectCode).subscribe((data: ObjectCode) => {
      this.statementList2 = JSON.parse(JSON.stringify(data['Table1']))
      this.statementList2.filter((item: any) => { return item.updated_code == '' ? item.updated_code = item.tgt_code : item.updated_code = item.updated_code })
      this.setDivHeight();
    });
  }
  /*--for source and target scroll alignment*/
  ngAfterContentChecked() {
    if (this.typeSl) {
      const vtextarea = this.el.nativeElement.getElementsByClassName("gb_text");
      for (const element of vtextarea) {
        element.style.height = element.scrollHeight + 5 + 'px';
        element.classList.remove('gb_text')
      }
      this.typeSle = false
    }
  }

  ngAfterViewChecked() {
    if (this.typeSle) {
      this.setDivHeight();
    }
  }

  setDivHeight() {
    const div1: any = document.getElementById('div1');
    const div2: any = document.getElementById('div2');

    if (div1.offsetHeight > div2.offsetHeight) {
      const height = `${div1.offsetHeight}px`;
      this.renderer.setStyle(div2, 'height', height);
    } else {
      const div2height = `${div2.offsetHeight}px`;
      this.renderer.setStyle(div1, 'height', div2height);
    }
  }

  autoGrowTextZone(e: any) {
    e.target.style.height = "0px";
    e.target.style.height = (e.target.scrollHeight + 25) + "px";
    this.buttonDisable = false
  }
  /*--convert code*/
  convertCode(data: any) {
    let convertStmt = '';
    this.statementList2.filter((ba: any) => {
      if (ba.tgt_id == data) {
        convertStmt = ba.src_code
      }
    })

    this.statementList.filter((el: any) => {
      if (el.tgt_id == data) {
        if (el.src_code != el.tgt_code) {
          el.src_code = el.tgt_code
        } else {
          el.src_code = convertStmt
        }
      }
    })
  }

  showStatement(value: any) {
    this.currentStmt = value;
    this.statementShow = false;
    if (value != this.stmtcV) {
      this.stmtcV = value
    } else {
      this.statementShow = true;
      this.stmtcV = 0
    }
  }
  /*--text area change*/
  textareaChanged(data: any, data1: any) {
    this.deployButton = true
    this.displayTargetConnection = false
    this.statementList.filter((el: any) => {
      if (el.tgt_id == data) {
        el.grid_changed = "yes"
        el.updated_code = data1
        const newObject = {
          projectId: this.projectId.toString(),
          tgtId: el.tgt_id,
          updatedcode: el.updated_code,
          Override: true,
          acl: '',
          option:true
        }
        this.ManualOutputCode.push(newObject)
      }
    })
  }
  /*--Custom alert*/
  // customAlert(message: any, color: any) {
  //   this.snackMessage = message;
  //   const alId = <HTMLInputElement>document.getElementById('snackbar')
  //   alId.className = "show";
  //   alId.style.backgroundColor = color
  //   setTimeout(function () { alId.className = alId.className.replace("show", ""); }, 5000);
  // }
  customAlert: any
  saveData() {
    this.saveSpin = true
    this.codemigrationservice.manualCodeUpdate(this.ManualOutputCode).subscribe((data: any) => {
      this.saveSpin = false
      this.customAlert = data['jsonResponseData']['Table1'][0].v_status, 'green';
      //console.log(this.customAlert)
      this.deployButton = false
      this.buttonDisable = true
      this.displayTargetConnection = true
      if (data.message.includes("does not exist")) {
        this.saveSpin = false
        this.toast.error(" Not Found")
      }
      else {
        this.saveSpin = false
        this.toast.success(data.message)
      }
    },
      () => {
        this.toast.error('Status not updated!');
      })
  }
  /*--get source and target connection*/
  GetConsList() {
    this.codemigrationservice.getConList(this.projectId.toString()).subscribe((data: conList) => {
      this.targetList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
    })
  }
  selTgtId(value: any) {
    this.currentTargetConnection = value
    this.getSchemasList(value)
  }
  /*--Deploy data*/
  deployData() {
    this.deploySpin = true
    var timetaken = (<HTMLInputElement>document.getElementById("timetaken")).value
    var tobservatons = (<HTMLInputElement>document.getElementById("deployeeText")).value
    var schData = this.schemaList.filter((item: any) => {
      return item.schema_id == this.currentObject
    })
    var schemaname = schData[0].schemaname
    const newObject: RedisCacheInsertion = {
      projectId: this.projectId.toString(),
      option: 6,
      iteration: this.currentRunNumber,
      schema: this.selectedSrcSchem,//schData[0].schemaname,//
      targetSchema: this.selectedtgtschemaname,// schData[0].schemaname,
      srcConId: this.currentSourceConnection[0].connection_id,
      tgtConId: this.currentTargetConnection,
      objectType: this.objType,
      objectName: this.currentName,
      jobName: "qmig-convs",
      timeTaken: timetaken,
      Observations: tobservatons,
      //targetSchema:this.selectedtgtScema
    }
    if (this.migtypeid == "30" || this.migtypeid == "20") {
      newObject["srcConId"] = this.srcConId;
      newObject["targetSchema"] = this.selectedtgtschemaname;
    }
    this.codemigrationservice.RedisCacheInsertion(newObject).subscribe((data: fileStatus) => {
      this.deploySpin = false
      this.toast.success(data.message)
      // data['message'] === 'Command Inserted' ? this.customAlert("Status " + data['message'], 'green') : this.customAlert("Status " + data['message'], 'red'
      // )
    })
    this.manualForm.reset();
    this.deploymentForm.reset();
  }
  /*--Reset*/
  Reset() {
    this.ManualOutputCode = [];
    this.statementList = this.statementList2;
  }
  /*--Deploy status*/
  GetDeployStatus() {
    let obj: GetProjectTgtDeployStatusSelect = {
      projectId: this.projectId.toString(),
      srcid: this.srcId.toString()
    }
    this.codemigrationservice.GetProjectTgtDeployStatusSelect(obj).subscribe((data: manualSave) => {
      this.TgtDeployData = data['Table1'];
    })
  }
  /*--Deploy data filter*/
  Time: any
  filterDeployData(value: any) {
    var res = this.TgtDeployData.filter((item: any) => {
      return item.created_dt == value
    })
    console.log(res)
    console.log(value)

    if (res.length > 0) {
      this.deploystatuss = res[0].is_deployed
      this.deployTime = res[0].fix_duration
      this.totaldeploy =0// res[0].obj_total_time
      this.statusMessages = res[0].error
      this.observations = res[0].observations
    }
  }
  tgtSchemaList: any = []
  //GetSchemasLiist
  getSchemasList(connectionId: any) {
    const obj = {
      projectId: this.projectId,
      connectionId: connectionId
    }
    this.codemigrationservice.SchemaListSelect(obj).subscribe((data: any) => {
      // this.schemaList = data['Table1'];
      this.tgtSchemaList = data['Table1'];
      this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => {
        return item.schemaname != "ALL"
      });
    })
  }
  selectedtgtScema: string = ""
  selectTgtSchema(value: any) {
    this.selectedtgtScema = value
  }
  ConversionResponseData: any = []
  ConversionCommand() {
    this.deploySpin = true
    var timetaken = (<HTMLInputElement>document.getElementById("timetaken")).value
    var tobservatons = (<HTMLInputElement>document.getElementById("deployeeText")).value
    var schData = this.schemaList.filter((item: any) => {
      return item.schema_id == this.currentObject
    })
    var schemaname = schData[0].schemaname
    let obj: any = {
      sourceConnectionId: this.srcConId,
      targetConnectionId:this.currentTargetConnection,
      projectId: this.projectId.toString(),
      task: "Manual_Conversion",
      iteration: this.currentRunNumber,
      objectType: this.objType,
      schema: this.selectedSrcSchem,
      targetSchema:this.selectedtgtschemaname,
      objectName:this.currentName,
      observations:'"'+tobservatons+'"',
      fixDuration:timetaken,
      processType:"",
      jobName: "qmig-convs",
    }

    this.codemigrationservice.ConversionCommand(obj).subscribe((data: any) => {
      this.deploySpin = false
      this.ConversionResponseData = data;
      this.toast.success("Storage Conversion Command Executed")
      this.deploymentForm.reset()
    },
      error => {
        this.toast.error('Something went wrong')
      })
  }

  /*--Download Dependent Files*/

  isDownloadDependent: boolean = false;
  downloadDependent():void{
    this.isDownloadDependent = true;
    const newObjectCode = {
      conid: this.srcConId,
      schemaname: this.selectedSrcSchem,
      objectname: this.currentName,
    }

    this.codemigrationservice.downloadFetchDependency(newObjectCode).subscribe((data: any) => {
      this.isDownloadDependent = false;
      if( data.ddlResponse == "No Dependent Objects Found") {
        this.toast.error("No Dependent Objects Found");
      }else{        
        const blob = new Blob([data.ddlResponse], {  type: 'text/plain'  });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.selectedSrcSchem}_${this.currentName}_Dependent_Objects.sql`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        this.toast.success("Dependent Files Downloaded Successfully");
      }
    });
  }
}

import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { environment } from '../environments/environment';
import { GetExceution, GetDBConnections, GetFilesFromExpath, DeleteSchema, GetIndividual, GetIndividualLogs, GetOperations, GetReqData, GetRunno, Operation, PrjSchemasListSelectData, ReportsFilterByTime, SchemaListSelect, UpdateConnection, UploadBinaryFileToShare, addPrjConnection, addconnection, addfiletype, assessmentsetRedisCache, conlist, deleteDbConnection, deleteFile, deleteFileCon, deleteFileShareFile, deleteTableData, deleteTabledata, deployBtn, documentsList, downloadDocs, executeBtn, fileStatus, getfiletype, migdetailData, operation, projectConRunTblInsert, projectDocumentsDetailSelect, projectMigdetails, redisCommand, req, reqData, reqObj, setRedisCache, setRedisCache1, srctgtConfInsert, testSourceConn, updatePrjConnection, updateconnection, uploadCommonBinaryFileToBlob, uploadDocs, GetConfigData } from '../models/interfaces/types';
import { assessmentAPIConstant } from '../constant/assessmentAPIConstant';
import { APIConstant } from '../constant/APIConstant';

@Injectable({
  providedIn: 'root'
})
export class AssessmentService {

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl + 'Assess/'

  /*--- Upload Documents ---*/
  docusURL = this.apiURL + assessmentAPIConstant.uploadDocuments
  uploadDocuments = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.docusURL, body);
  };

  /*--- Download Files ---*/

  downloadFileURL = this.apiURL + assessmentAPIConstant.fileDownload;
  downloadFiles = (body: string): Observable<downloadDocs> => {
    return this.apiService.get(this.downloadFileURL + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }

  /*--- Delete Files ---*/

  deleteFileURL = this.apiURL + APIConstant.common.deleteFile;
  deleteFile = (body: string): Observable<deleteFile> => {
    return this.apiService.get(this.deleteFileURL + body)
  }

  /*--- Get Files from path ---*/


  InsertSchemaCommand = this.apiURL + assessmentAPIConstant.InsertSchemaCommand;
  insertSchema = (body: redisCommand): Observable<fileStatus> => {
    return this.apiService.post(this.InsertSchemaCommand, body);
  }

  getFilesURL = this.apiURL + assessmentAPIConstant.getFiles;
  getFiles = (body: string): Observable<documentsList> => {
    return this.apiService.get(this.getFilesURL + body)
  }
  //getConList
  getConListURL = this.apiURL + assessmentAPIConstant.getConList;
  getConList = (body: string): Observable<conlist> => {
    return this.apiService.get(this.getConListURL + body)
  }



  //projectMigDetailSelect
  projectMigDetailSelectURL = this.apiURL + assessmentAPIConstant.projectMigDetailSelect;
  projectMigDetailSelect = (body: migdetailData): Observable<projectMigdetails> => {
    return this.apiService.post(this.projectMigDetailSelectURL, body);
  }
  //GetBlobFiles

  GetBlobFilesURL = this.apiURL + assessmentAPIConstant.GetBlobFiles;
  GetBlobFiles = (body: string): Observable<string> => {
    return this.apiService.post(this.GetBlobFilesURL, body);
  }

  //ExecuteDbQuery
  ExecuteDbQueryURL = this.apiURL + assessmentAPIConstant.ExeBtn;
  ExecuteDbQuery = (body: executeBtn): Observable<any> => {
    return this.apiService.post(this.ExecuteDbQueryURL, body);
  }

  //UpLoadCommonBinaryFileToBlob
  UpLoadCommonBinaryFileToBlobURL = this.apiURL + assessmentAPIConstant.UpLoadCommonBinaryFileToBlob;
  UpLoadCommonBinaryFileToBlob = (body: uploadCommonBinaryFileToBlob): Observable<any> => {
    return this.apiService.post(this.UpLoadCommonBinaryFileToBlobURL, body);
  }

  //UploadBinaryFileToShare
  UploadBinaryFileToShareURL = this.apiURL + assessmentAPIConstant.UpLoadCommonBinaryFileToBlob;
  UploadBinaryFileToShare = (body: UploadBinaryFileToShare): Observable<any> => {
    return this.apiService.post(this.UploadBinaryFileToShareURL, body);
  }

  //addPrjConnection
  addPrjConnectionURL = this.apiURL + assessmentAPIConstant.addPrjConnection;
  addPrjConnection = (body: addPrjConnection): Observable<UpdateConnection> => {
    return this.apiService.post(this.addPrjConnectionURL, body);
  }
  //updatePrjConnection
  updatePrjConnectionURL = this.apiURL + assessmentAPIConstant.updatePrjConnection;
  updatePrjConnection = (body: updatePrjConnection): Observable<UpdateConnection> => {
    return this.apiService.post(this.updatePrjConnectionURL, body);
  }

  //DeleteprjDbConnection
  DeleteprjDbConnectionURL = this.apiURL + assessmentAPIConstant.DeleteprjDbConnection;
  DeleteprjDbConnection = (body: string): Observable<fileStatus> => {
    return this.apiService.get(this.DeleteprjDbConnectionURL + body)
  }
  /*connection */
  ////selectmigtype
  selectmigtypeURL = this.apiURL + assessmentAPIConstant.selectmigtype;
  selectmigtype = (body: string): Observable<string> => {
    return this.apiService.get(this.selectmigtypeURL + body)
  }

  //addProjectObject
  addConnectionURL = this.apiURL + assessmentAPIConstant.addProjectObject;
  addConnection = (body: addconnection): Observable<any> => {
    return this.apiService.post(this.addConnectionURL, body);
  }
  //using in deploy screen//
  //PrjExeLogSelect
  PrjExeLogSelectURL = this.apiURL + assessmentAPIConstant.PrjExeLogSelect;
  PrjExeLogSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.addConnectionURL, body);
  }

  //GetDBConnections
  GetDBConnectionsURL = this.apiURL + assessmentAPIConstant.GetDBConnections;
  GetDBConnections = (body: string): Observable<GetDBConnections> => {
    return this.apiService.get(this.GetDBConnectionsURL + body)
  }
  //deleteDbConnection
  deleteDbConnectionURL = this.apiURL + assessmentAPIConstant.deleteDbConnection;
  deleteDbConnection = (body: deleteDbConnection): Observable<any> => {
    return this.apiService.get(this.deleteDbConnectionURL + body.projectId + '&id=' + body.id)
  }

  //UpdateConnection
  UpdateConnectionURL = this.apiURL + assessmentAPIConstant.UpdateConnection;
  UpdateConnection = (body: updateconnection): Observable<UpdateConnection> => {
    return this.apiService.post(this.UpdateConnectionURL, body);
  }

  //testSourceConn
  testSourceConnURL = environment.serviceUrl1 + assessmentAPIConstant.testSourceConn;
  testSourceConn = (body: testSourceConn): Observable<deleteFile> => {
    return this.apiService.post(this.testSourceConnURL, body);
  }

  //testTargetConn
  testTargetConnURL = this.apiURL + assessmentAPIConstant.testTargetConn;
  testTargetConn = (body: testSourceConn): Observable<deleteFile> => {
    return this.apiService.post(this.testTargetConnURL, body);
  }

  //addfiletype
  addfiletypeURL = this.apiURL + assessmentAPIConstant.addfiletype;
  addfiletype = (body: addfiletype): Observable<any> => {
    return this.apiService.post(this.addfiletypeURL, body);
  }

  //getfiletype
  getfiletypeURL = this.apiURL + assessmentAPIConstant.getfiletype;
  getfiletype = (body: string): Observable<getfiletype> => {
    return this.apiService.get(this.getfiletypeURL + body)
  }

  //GetFileConnections
  GetFileConnectionsURL = this.apiURL + assessmentAPIConstant.GetFileConnections;
  GetFileConnections = (body: string): Observable<any> => {
    return this.apiService.get(this.GetFileConnectionsURL + body)
  }

  //srctgtConfInsert
  srctgtConfInsertURL = this.apiURL + assessmentAPIConstant.srctgtConfInsert;
  srctgtConfInsert = (body: srctgtConfInsert): Observable<any> => {
    return this.apiService.post(this.srctgtConfInsertURL, body);
  }


  //deleteFileShareFile
  deleteFileShareFileURL = this.apiURL + assessmentAPIConstant.deleteFileShareFile;
  deleteFileShareFile = (body: deleteFileShareFile): Observable<any> => {
    return this.apiService.post(this.deleteFileShareFileURL, body);
  }

  //deleteFileCon
  deleteFileConURL = this.apiURL + assessmentAPIConstant.deleteFileCon;
  deleteFileCon = (body: deleteFileCon): Observable<any> => {
    return this.apiService.get(this.deleteFileConURL + body)
  }

  //code and assessment

  //GetReqData
  GetReqDataURL = this.apiURL + assessmentAPIConstant.GetReqData;
  GetReqData = (body: GetReqData): Observable<reqData> => {
    return this.apiService.get(this.GetReqDataURL + body.projectId + '&operationType=' + body.operationType);
  };
  //GetIndividualLogs
  GetIndividualLogsURL = this.apiURL + assessmentAPIConstant.GetIndividualLogs
  GetIndividualLogs = (body: GetIndividualLogs): Observable<GetIndividual> => {
    return this.apiService.post(this.GetIndividualLogsURL, body);
  };
  //GetRunno
  GetRunnoURL = this.apiURL + assessmentAPIConstant.GetRunno;
  GetRunno = (body: string): Observable<GetRunno> => {
    return this.apiService.get(this.GetRunnoURL + body);
  };
  // PrjExeLogSelectTask
  PrjExeLogSelectTaskURL = this.apiURL + assessmentAPIConstant.PrjExeLogSelectTask
  PrjExeLogSelectTask = (body: string): Observable<string> => {
    return this.apiService.post(this.PrjExeLogSelectTaskURL, body);
  };

  //PrjSchemasListSelectData
  PrjSchemasListSelectDataURL = this.apiURL + assessmentAPIConstant.PrjSchemasListSelectData;
  PrjSchemasListSelectData = (body: PrjSchemasListSelectData): Observable<any> => {
    return this.apiService.get(this.PrjSchemasListSelectDataURL + body.projectId + "&ConId=" + body.ConId,);
  };

  //GetOperations
  GetOperationsURL = this.apiURL + assessmentAPIConstant.GetOperations;
  GetOperations = (body: GetOperations): Observable<Operation> => {
    return this.apiService.get(this.GetOperationsURL + body.projectId + '&OperationType=' + body.OperationType);
  };

  //projectConRunTblInsert
  projectConRunTblInsertURL = this.apiURL + assessmentAPIConstant.projectConRunTblInsert
  projectConRunTblInsert = (body: projectConRunTblInsert): Observable<any> => {
    return this.apiService.post(this.projectConRunTblInsertURL, body);
  };
  //setRedisCache
  assessmentsetRedisCacheURL = this.apiURL + assessmentAPIConstant.assessmentsetRedisCache
  assessmentsetRedisCache = (body: assessmentsetRedisCache): Observable<any> => {
    return this.apiService.post(this.assessmentsetRedisCacheURL, body);
  };
  //getFileFromList
  getFileFromListURL = this.apiURL + assessmentAPIConstant.getFileFromList;
  getFileFromList = (body: string): Observable<string> => {
    return this.apiService.get(this.getFileFromListURL + body);
  };
  //ReportsFilterByTime
  ReportsFilterByTimeURL = this.apiURL + assessmentAPIConstant.ReportsFilterByTime
  ReportsFilterByTime = (body: ReportsFilterByTime): Observable<any> => {
    return this.apiService.post(this.ReportsFilterByTimeURL, body);
  };
  //deleteTableData
  deleteTableDataURL = this.apiURL + assessmentAPIConstant.deleteTableData;
  deleteTableData = (body: deleteTableData): Observable<deleteTabledata> => {
    return this.apiService.get(this.deleteTableDataURL + body.projectId + '&requestId=' + body.requestId);
  };

  //projectDocumentsDetailSelect
  projectDocumentsDetailSelectURL = this.apiURL + assessmentAPIConstant.projectDocumentsDetailSelect;
  projectDocumentsDetailSelect = (body: projectDocumentsDetailSelect): Observable<projectDocumentsDetailSelect> => {
    return this.apiService.get(this.projectDocumentsDetailSelectURL + body);
  };
  projectDocumentsDetailSelect1 = (body: req): Observable<any> => {
    return this.apiService.get(this.projectDocumentsDetailSelectURL + body);
  };
  //InfraSelect
  InfraSelectURL = this.apiURL + assessmentAPIConstant.InfraSelect;
  InfraSelect = (body: string): Observable<any> => {
    return this.apiService.get(this.InfraSelectURL + body)
  }

  //GetSchemasByRunId
  GetSchemasByRunIdURL = this.apiURL + assessmentAPIConstant.GetSchemasByRunId;
  GetSchemasByRunId = (body: string): Observable<any> => {
    return this.apiService.get(this.GetSchemasByRunIdURL + body)
  }

  //GetFilesFromDir
  GetFilesFromDirURL = this.apiURL + assessmentAPIConstant.GetFilesFromDir;
  GetFilesFromDir = (body: string): Observable<any> => {
    return this.apiService.get(this.GetFilesFromDirURL + body)
  }
  testSMySqlConnURL = environment.serviceUrl1 + assessmentAPIConstant.testMySqlConn;
  testMySqlConn = (body: testSourceConn): Observable<deleteFile> => {
    return this.apiService.post(this.testSMySqlConnURL, body);
  }
  testorclConnURL = environment.serviceUrl1 + assessmentAPIConstant.testorclConn;
  testorclConn = (body: testSourceConn): Observable<deleteFile> => {
    return this.apiService.post(this.testorclConnURL, body);
  }
  createschemaurl = environment.serviceUrl1 + assessmentAPIConstant.createschema
  createschema = (data: any): Observable<any> => {
    return this.apiService.post(this.createschemaurl, data);
  };
  dropschemaobjUrl = this.apiURL + assessmentAPIConstant.dropschemaobjs
  dropschemaobjs = (data: any): Observable<any> => {
    return this.apiService.post(this.dropschemaobjUrl, data);
  };
  dropsql2oraObjectsUrl = environment.serviceUrl1 + assessmentAPIConstant.dropsql2oraObjects
  dropsql2oraObjects = (data: any): Observable<any> => {
    return this.apiService.post(this.dropsql2oraObjectsUrl, data);
  };

  OracleSchemasURL = this.apiURL + assessmentAPIConstant.OracleSchemas
  OracleSchemas = (body: any): Observable<any> => {
    return this.apiService.get(this.OracleSchemasURL + body);
  }
  OraSchemasURL = environment.serviceUrl1 + assessmentAPIConstant.OraSchemas
  OraSchemas = (body: any): Observable<any> => {
    return this.apiService.get(this.OraSchemasURL + body);
  }

  //tgtSchemaSelect
  tgtSchemaSelectURL = this.apiURL + assessmentAPIConstant.tgtSchemaSelect;
  tgtSchemaSelect = (body: string): Observable<string> => {
    return this.apiService.get(this.tgtSchemaSelectURL + body)
  }
  //DeleteSchema
  DeleteSchemaURL = this.apiURL + assessmentAPIConstant.DeleteSchema;
  DeleteSchema = (body: DeleteSchema): Observable<DeleteSchema> => {
    return this.apiService.get(this.DeleteSchemaURL + body.schemaname + '&conId=' + body.conId)
  }
  getObjTypesurl = environment.serviceUrl1 + assessmentAPIConstant.getobjtype
  getObjTypes = (data: any): Observable<any> => {
    return this.apiService.post(this.getObjTypesurl, data);
  };
  getObjnameurl = environment.serviceUrl1 + assessmentAPIConstant.getobjname
  getObjNames = (data: any): Observable<any> => {
    return this.apiService.post(this.getObjnameurl, data);
  };

  /*----SchemaListSelect----*/

  SchemaListSelectURL = this.apiURL + assessmentAPIConstant.SchemaListSelect;
  SchemaListSelect = (body: SchemaListSelect): Observable<SchemaListSelect> => {
    return this.apiService.get(this.SchemaListSelectURL + body.projectid + '&ConId=' + body.connectioId)
  }

  /*-----GetCommonFilesFromDirectory----*/

  GetCommonFilesFromDirectoryURL = this.apiURL + assessmentAPIConstant.GetCommonFilesFromDirectory;
  GetCommonFilesFromDirectory = (body: any): Observable<any> => {
    return this.apiService.get(this.GetCommonFilesFromDirectoryURL + body)
  }

  //downloadLargeFiles

  downloadLargeFilesURL = this.apiURL + assessmentAPIConstant.downloadLargeFiles;
  downloadLargeFiles = (body: string): Observable<any> => {
    return this.apiService.get(this.downloadLargeFilesURL + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }

  //setRedisCache1
  setRedisCache1 = (body: setRedisCache1): Observable<fileStatus> => {
    return this.apiService.post(this.setRedisCacheURL, body);
  };
  //setRedisCache

  setRedisCacheURL = this.apiURL + assessmentAPIConstant.setRedisCache;
  setRedisCache = (body: setRedisCache): Observable<fileStatus> => {
    return this.apiService.post(this.setRedisCacheURL, body);
  };

  //GetFilesFromExpathURL

  GetFilesFromExpathURL = this.apiURL + assessmentAPIConstant.GetFilesFromExpath;
  GetFilesFromExpath = (body: string): Observable<GetFilesFromExpath> => {
    return this.apiService.get(this.GetFilesFromExpathURL + body)
  }

  //getInventoryDirs

  getInventoryDirsURL = this.apiURL + assessmentAPIConstant.GetInventoryFolders
  getInventoryDirs = (): Observable<any> => {
    return this.apiService.get(this.getInventoryDirsURL);
  };

  //getsqlServerKey

  getsqlServerKeyURL = this.apiURL + assessmentAPIConstant.getsqlServerKey
  getsqlServerKey = (): Observable<any> => {
    return this.apiService.get(this.getsqlServerKeyURL);
  };

  //downloadInventoryZipFiles

  downloadInventoryZipFilesURL = this.apiURL + assessmentAPIConstant.downloadInventoryZip
  downloadInventoryZipFiles = (body: any): Observable<any> => {
    return this.apiService.get(this.downloadInventoryZipFilesURL + body);
  };

  //triggerInventory

  triggerInventoryURL = this.apiURL + assessmentAPIConstant.InsertInventorDetalis
  triggerInventory = (body: any): Observable<any> => {
    return this.apiService.post(this.triggerInventoryURL, body);
  };
  //ScriptDownload

  ScriptDownloadURL = this.apiURL + assessmentAPIConstant.downloadScript
  ScriptDownload = (): Observable<any> => {
    return this.apiService.get(this.ScriptDownloadURL, { responseType: 'blob' as 'json' });
  };

  //getServers
  getServersURL = this.apiURL + assessmentAPIConstant.GetServerlist
  getServers = (): Observable<any> => {
    return this.apiService.get(this.getServersURL);
  };

  //PrjIterationSelect

  PrjIterationSelectURL = this.apiURL + assessmentAPIConstant.PrjSQLRuninfoIteration
  PrjIterationSelect = (body: any): Observable<any> => {
    return this.apiService.get(this.PrjIterationSelectURL + body.projectId);
  };

  //PrjSqlScriptFunctionSelect
  PrjSqlScriptFunctionSelectURL = this.apiURL + assessmentAPIConstant.PrjSqlScriptFunctionSelect;
  PrjSqlScriptFunctionSelect = (body: any): Observable<any> => {
    return this.apiService.get(this.PrjSqlScriptFunctionSelectURL + body.projectId + "&functionname=" + body.functionname, body)
  }

  //insertSqlMiCommand
  insertSqlMiCommandURL = this.apiURL + assessmentAPIConstant.InsertSQLMiCommand
  insertSqlMiCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.insertSqlMiCommandURL, body);
  };

  //PrjSqlQueryExecute

  PrjSqlQueryExecuteURL = this.apiURL + assessmentAPIConstant.PrjSqlQueryExecute
  PrjSqlQueryExecute = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjSqlQueryExecuteURL, body);
  };

  //PrjSqlScriptcycleSelect
  PrjSqlScriptcycleSelectURL = this.apiURL + assessmentAPIConstant.PrjSqlScriptcycleSelect
  PrjSqlScriptcycleSelect = (body: any): Observable<any> => {
    return this.apiService.get(this.PrjSqlScriptcycleSelectURL + body.projectId + "&functionId=" + body.functionId);
  };

  //PrjSqlScriptCategorySelect

  PrjSqlScriptCategorySelectURL = this.apiURL + assessmentAPIConstant.PrjSqlScriptCategorySelect
  PrjSqlScriptCategorySelect = (body: any): Observable<any> => {
    return this.apiService.get(this.PrjSqlScriptCategorySelectURL + body.projectId);
  };


  //GetExceution
  GetExceutionURL = this.apiURL + assessmentAPIConstant.GetExceution;
  GetExceution = (body: GetExceution): Observable<any> => {
    return this.apiService.post(this.GetExceutionURL, body)
  }

  /*--- Upload Documents ---*/
  uploadDocumentURL = this.apiURL + assessmentAPIConstant.uploadDocument
  uploadDocument = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.uploadDocumentURL, body);
  };

  /*--- Upload Documents ---*/
  uploadLargeDocumentURL = this.apiURL + assessmentAPIConstant.uploadLargeFiles
  uploadLargeDocument = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.uploadLargeDocumentURL, body);
  };


  CreatePGSchemaURL = this.apiURL + assessmentAPIConstant.CreatePGSchema
  CreatePGSchema = (body: any): Observable<uploadDocs> => {
    return this.apiService.get(this.CreatePGSchemaURL + "?conid=" + body.Conid + "&schemaname=" + body.schemaname);
  };

  getObjectTypesURL = this.apiURL + assessmentAPIConstant.getObjectTypes;
  getObjectTypes = (body: any): Observable<any> => {
    return this.apiService.get(this.getObjectTypesURL + body.projectId + '&objectgroupname=' + body.objectgroupname)
  }
  e2emigrationURL = this.apiURL + assessmentAPIConstant.e2emigration;
  TriggerE2eCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.e2emigrationURL, body)
  }
  mariae2emigrationURL = environment.serviceUrl1 + assessmentAPIConstant.mariae2emigrationURL;
  TriggerMariaE2eCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.mariae2emigrationURL, body)
  }
  InsertscriptURL = this.apiURL + assessmentAPIConstant.InsertScript;
  insertscript = (body: any): Observable<any> => {
    return this.apiService.post(this.InsertscriptURL, body)
  }
  SelectScriptByCategoryURL = this.apiURL + assessmentAPIConstant.SelectScriptByCategory;
  SelectScriptByCategory = (body: string): Observable<any> => {
    return this.apiService.get(this.SelectScriptByCategoryURL + body)
  }

  ////SQL APIs
  CreateSqlSchemaURL = environment.serviceUrl1 + assessmentAPIConstant.CreateSqlSchema
  CreateSqlSchema = (body: any): Observable<uploadDocs> => {
    return this.apiService.get(this.CreateSqlSchemaURL + body.schemaname + "&conid=" + body.Conid);
  };
  getSqlSchemaURL = environment.serviceUrl1 + assessmentAPIConstant.getSqlSchema
  getSqlSchema = (body: any): Observable<uploadDocs> => {
    return this.apiService.get(this.getSqlSchemaURL + body);
  };
  dropSqlSchemaURL = environment.serviceUrl1 + assessmentAPIConstant.dropsqlschemobjects
  dropSqlSchema = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.dropSqlSchemaURL, body);
  };
  getSqlObjectsURL = environment.serviceUrl1 + assessmentAPIConstant.getSqlObjects
  getSqlObjects = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.getSqlObjectsURL, body);
  };
  getSqlObjecttypesURL = environment.serviceUrl1 + assessmentAPIConstant.getSqlObjectTypes
  getSqlObjecttypes = (body: any): Observable<uploadDocs> => {
    return this.apiService.get(this.getSqlObjecttypesURL + body.Conid + "&schema=" + body.schemaname);
  };

  testsqlTargetConnURL = environment.serviceUrl1 + assessmentAPIConstant.testSqlConnection;
  testSqlTargetConn = (body: testSourceConn): Observable<deleteFile> => {
    return this.apiService.post(this.testsqlTargetConnURL, body);
  }
  ///////sql APIs end

  //GetStorageObjectsData
  GetStorageObjectsDataURL = this.apiURL + assessmentAPIConstant.getStorageObjects;
  GetStorageObjectsData = (body: string): Observable<any> => {
    return this.apiService.get(this.GetStorageObjectsDataURL + body)
  }

  //GetCodeObjectsData
  GetCodeObjectsDataURL = this.apiURL + assessmentAPIConstant.getCodeObjects;
  GetCodeObjectsData = (body: string): Observable<any> => {
    return this.apiService.get(this.GetCodeObjectsDataURL + body)
  }

  //GetSummaryReportData
  GetSummaryReportDataURL = this.apiURL + assessmentAPIConstant.getSummaryReport;
  GetSummaryReportData = (body: string): Observable<any> => {
    return this.apiService.get(this.GetSummaryReportDataURL + body)
  }

  /*--- E2e Screen ---*/
  e2emigrationAllURL = this.apiURL + assessmentAPIConstant.e2emigrationAll;
  TriggerE2eAllCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.e2emigrationAllURL, body)
  }
  insertTablesCommandURL = this.apiURL + APIConstant.common.insertTablesCommand
  insertTablesCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.insertTablesCommandURL, body);
  }
  GetTablesByschemaGGURL = this.apiURL + APIConstant.common.GetTablesByschemaGG;
  GetTablesByschemaGG = (body: any): Observable<any> => {
    return this.apiService.get(this.GetTablesByschemaGGURL + body.schema + '&srcId=' + body.srcId)
  }
  getConfigUri = environment.serviceUrl + assessmentAPIConstant.GetConfigMenu
  GetConfigMenu = (body: any): Observable<any> => {
    return this.apiService.get(this.getConfigUri + body);
  }
  DataMigrationCommandURL = environment.serviceUrl1 + assessmentAPIConstant.DataMigrationCommand;
  DataMigrationCommand = (body: any): Observable<GetConfigData> => {
    return this.apiService.post(this.DataMigrationCommandURL, body)
  }
  GetPgDatabaseURL = this.apiURL + assessmentAPIConstant.GetPgDatabase;
  GetPgDatabase = (body: any): Observable<any> => {
    return this.apiService.get(this.GetPgDatabaseURL + body)
  }
  GetMySqlTablesURL = environment.serviceUrl1 + assessmentAPIConstant.GetMySqlTables;
  GetMySqlTables = (body: any): Observable<any> => {
    return this.apiService.get(this.GetMySqlTablesURL + body.conid + "&dbname=" + body.dbname)
  }

  DropSqlTablesURL = environment.serviceUrl1 + assessmentAPIConstant.DropMySqlTables;
  DropMySqlTables = (body: any): Observable<any> => {
    return this.apiService.get(this.DropSqlTablesURL + body.conid + "&tableLis=" + body.tableLis)
  }
  AssessmentCommadURL = this.apiURL + assessmentAPIConstant.AssessmentCommand;
  AssessmentCommad = (body: any): Observable<any> => {
    return this.apiService.post(this.AssessmentCommadURL, body);
  }
  ConversionCommadURL = this.apiURL + assessmentAPIConstant.ConversionCommand;
  ConversionCommad = (body: any): Observable<any> => {
    return this.apiService.post(this.ConversionCommadURL, body);
  }
  getOracleTablesURL = environment.serviceUrl1 + assessmentAPIConstant.getOracleTables;
  getOracleTables = (data: any): Observable<any> => {
    return this.apiService.get(this.getOracleTablesURL + data.conid + "&schemaname=" + data.schemaname);
  };
  getDb2TablesURL = environment.serviceUrl1 + assessmentAPIConstant.getDb2Tables;
  getDb2Tables = (data: any): Observable<any> => {
    return this.apiService.get(this.getDb2TablesURL + data.conid + "&schemaname=" + data.schemaname);
  };
  e2emigrationNewURL = this.apiURL + assessmentAPIConstant.e2emigrationNew;
  TriggerE2eCommandNew = (body: any): Observable<any> => {
    return this.apiService.post(this.e2emigrationNewURL, body)
  }
  AssessmentCommandwithFileURL = this.apiURL + assessmentAPIConstant.ExtractionFile
  AssessmentCommandwithFile = (body: any): Observable<any> => {
    return this.apiService.post(this.AssessmentCommandwithFileURL, body);
  };
  GetPostgresTablesBySchemaURL = environment.serviceUrl1 + "Common/Migration/" + APIConstant.common.GetPostgresTablesBySchema;
  GetPostgresTablesBySchema = (body: any): Observable<any> => {
    return this.apiService.get(this.GetPostgresTablesBySchemaURL + body.conid + '&schema=' + body.schema)
  }
  //testTargetConn
  testRedisConnURL = this.apiURL + assessmentAPIConstant.testRedisConn;
  testRedisConn = (body: testSourceConn): Observable<deleteFile> => {
    return this.apiService.post(this.testRedisConnURL, body);
  }
}

import { CommonModule } from '@angular/common';
import { Component,OnInit } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HotToastService } from '@ngxpert/hot-toast';
import { SqlService } from '../../../../services/sqlService.service';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { Title } from '@angular/platform-browser';

declare let $: any;
@Component({
  selector: 'app-serverscan',
  standalone: true,
  imports: [FormsModule,SpinnerComponent,CommonModule,ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './serverscan.component.html',
  styles: ``
})
export class ServerscanComponent  implements OnInit{
  projectId: string = '';
  pageNumber1 = 1;
  pageNumber2 = 1;
  constructor(private titleService: Title,private toast: HotToastService, private project: SqlService, public formBuilder: FormBuilder) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
  }
  ngOnInit(): void {
//this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.scanServers();
    this.getInventoryFolders();
    this.fetchInventoryFiles();
    
  }
  serverResponse: any;
  scan_spin: boolean = false;
  datachange: any;
  datachanges: any;
  datachanges1:any;
  p: number = 1;
  isCheckBoxSel: boolean = false; 
  masterSelected: boolean = false;
  getIPfromCheck: any;
  showCheckStatus: boolean = false
  allIPs: any = []
  pageNumber: number = 1;
  searchText: string = '';
  pi: number = 10;
  userData: any = [];
  isTriggerCheckSelected: boolean = false;
  grid_active: boolean = false;
  not_grid: boolean = false;

  scanServers() {
    this.project.getServers().subscribe((data: any) => {
      if (data.message == "Please deploy powershell script") {
       // this.toast.error(data.message)
        this.scan_spin = false
      }
      else {
        this.serverResponse = data
        this.scan_spin = false
      }

    },
      () => {
        this.toast.error("Please deploy powershell script")
        this.scan_spin = false
      })
  }
  onKey() {
    this.pageNumber = 1;
    this.pageNumber1 = 1;
    this.pageNumber2 = 1;
    this.p = 1;
  }
  checkboxselect($event: any, value: any): void {
    this.getIPfromCheck = value;
    if ($event.target.checked) {
      this.isCheckBoxSel = true
      this.allIPs.push(value)
      //console.log(this.allIPs)
    } else {
      const index = this.allIPs.indexOf(value);
      const rem = this.allIPs.splice(index, 1);
      //console.log(rem)
      //console.log(this.allIPs)
      this.isCheckBoxSel = false
    }
  }
  selectAll($event: any) {
    this.showCheckStatus = $event.target.checked
    if ($event.target.checked) {
      this.allIPs = []
      for (const element of this.serverResponse) {
        element.isSelected = true;
        this.allIPs.push(element.ip)
      }
      //console.log(this.serverResponse)
      this.isCheckBoxSel = true
    } else {
      this.allIPs = []
      for (const element of this.serverResponse) {
        element.isSelected = false;
      }
      this.isCheckBoxSel = false
    }
    //console.log(this.allIPs)
  }
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all') {
      this.pi = this.userData.length;
      this.p = 1;
    }
    else if (value == '20') {
      this.p = 1;
    }
    else if (value == '50') {
      this.p = 1;
    }
    else if (value == '100') {
      this.p = 1;
    }
  }

  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }
  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }
  
 
  fileResponse: any
  dwnld_spin: boolean = false
  downloadScript() {
    this.dwnld_spin = true
    this.project.ScriptDownload().subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = "agent.zip";
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.dwnld_spin = false
      this.getSqlKey()

    })
  }
 
  triggerCheckSelect($event: any) {
    if ($event.target.checked) {
      this.isTriggerCheckSelected = true
    } else {
      this.isTriggerCheckSelected = false
    }
  }
  ConsList: any
  GetConsList() {
    const projectId = this.projectId.toString();
    this.project.getConList(projectId).subscribe((data: any) => {
      const condata = data['Table1'];
      this.ConsList = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
    });
  }
  // condetails: any
  // selectedCon(con: any) {
  //   this.ConsList.filter((item:any)=>{
  //     return item.Connection_ID == con
  //   })
  // }
  inventoryListRequest: any = []
  inventoryRequest: any = {}
  triggerJob() {
    let userid, pwd
    if (this.isTriggerCheckSelected == false) {
      userid = (<HTMLInputElement>document.getElementById("sqluser")).value
      pwd = (<HTMLInputElement>document.getElementById("sqlpwd")).value
    }
    for (let i = 0; i < this.allIPs.length; i++) {
      this.inventoryRequest.host = this.allIPs[i]
      this.inventoryRequest.winAuth = this.isTriggerCheckSelected
      if (this.isTriggerCheckSelected == false) {
        this.inventoryRequest.username = userid
        this.inventoryRequest.password = pwd
      }
      this.inventoryListRequest.push(this.inventoryRequest)
    }
    this.project.triggerInventory(this.inventoryListRequest).subscribe((data: any) => {
      $('#exampleModalCenter').modal('hide');
    })

    $('#exampleModalCenter').modal('hide');
  }
  reportSpin: boolean = false;
  downloadZip() {
    if (this.allIPs.length > 1 || this.allIPs.length == 0) {
      this.toast.error("Please select one server only")
    }
    else {
      this.reportSpin = true
      this.project.downloadInventoryZipFiles(this.allIPs[0]).subscribe((blob: any) => {
        if (blob.message) {
          this.toast.error(blob.message)
          this.reportSpin = false
        }
        else {
          this.fileResponse = blob;
          const url = window.URL.createObjectURL(blob);
          const downloadLink = document.createElement('a');
          const fileName = "Inventory(" + this.allIPs[0] + ").zip";
          downloadLink.href = url;
          document.body.appendChild(downloadLink);
          downloadLink.download = fileName;
          downloadLink.click();
          this.reportSpin = false
        }
      })

    }
  }

  InventoryFiles: any
  fetchInventoryFiles() {
    const path = "dbfiles/"
    this.project.GetFilesFromExpath(path).subscribe((data: any) => {
      this.InventoryFiles = data
    })
  }
  spin_dwld: any;
  downloadFile(fileInfo: any) {
    // title="DB_Reg.zip"
    this.project.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      //console.log('hi');
      //downloadLink.href = 'data:application/octet-stream;base64,' + this.fileResponse;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  key: any
  getSqlKey() {
    this.project.getsqlServerKey().subscribe((item: any) => {
      this.key = item.message;
      $('#exampleModalCenter1').modal('show');
    })
  }
  InvFolders: any
  getInventoryFolders() {
    this.project.getInventoryDirs().subscribe((data: any) => {
      this.InvFolders = data;
    })
  }
  invenFiles: any
  noFiles: any = ''
  getinvFiles(path: any) {
    this.invenFiles = []
    const pth=path.split("/")[3]+"/"
    this.project.GetFilesFromExpath(pth).subscribe((data: any) => {
      if (data.length == 0) {
        this.noFiles = "No Files Generated";
      }
      else {
        this.noFiles = ''
        this.invenFiles = data;
      }
    })
  }
  closeModal()
  {
    $('#exampleModalCenter1').modal('Hide');
    // this.exampleModalCenter1 = false;
  }
}

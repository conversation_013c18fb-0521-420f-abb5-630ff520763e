<delta-tabs icon="mdi-database-import"></delta-tabs>
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingOne">
                <button class="accordion-button " type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne"
                    aria-expanded="false" aria-controls="collapseOne">
                    Target Conversion
                </button>
            </h2>
            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                data-bs-parent="#accordionExample">
                <div class="body-main mt-3">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="targetconform">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Run No</label>
                                        <select formControlName="runnumber" class="form-select" #run
                                            (change)="selectIteration(run.value)">
                                            <option selected disabled>Select Run Number</option>
                                            @for(list of runNumbers;track list;){
                                            <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validate.runnumber.touched && validate.runnumber.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.runnumber.errors?.['required']) {Run Number is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Connection Type</label>
                                        <select class="form-select" formControlName="contype" #drpdwn
                                            (change)="selectedDropdown(drpdwn.value)">
                                            <option selected> Select Connection Type</option>
                                            <option value="1">Database</option>
                                        </select>
                                        <div class="alert">
                                            @if(validate.contype.touched && validate.contype.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.contype.errors?.['required']) {Connection Type is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label d-required">Source Connection</label>
                                        <select class="form-select" formControlName="sourcecon"
                                             aria-label="Default select example">
                                            <option selected disabled>Select Source Connection</option>
                                            @for(list of SourceCo;track list;){
                                            <option value="{{ list.Connection_ID }}">{{ list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validate.sourcecon.touched && validate.sourcecon.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.sourcecon.errors?.['required']) {Source Connection is required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label d-required">Reference Connection</label>
                                        <select class="form-select" formControlName="tgtcon" #src
                                            (change)="prjSchemaLists(src.value)" aria-label="Default select example">
                                            <option selected disabled>Select Reference Connection</option>
                                            @for(list of TargetConn;track list;){
                                            <option value="{{ list.Connection_ID }}">{{ list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validate.tgtcon.touched && validate.tgtcon.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.tgtcon.errors?.['required']) {Source Connection is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label d-required">Schema Name</label>
                                        <ng-select [placeholder]="'Select Schema Type'" [items]="schemaList"
                                            [multiple]="true" bindLabel="schemaname" groupBy="gender"
                                            [selectableGroup]="true" [closeOnSelect]="false" bindValue="schemaname"
                                            [(ngModel)]="selectedItems" formControlName="scheman"
                                            (change)="selectschema(selectedItems)">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                            <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.schemaname}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if(validate.scheman.touched && validate.scheman.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.scheman.errors?.['required']) {Schema Name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label d-required">Object Category</label>
                                        <select class="form-select" aria-label="Default select example"
                                            aria-placeholder="Select Object List" #object
                                            (click)="SelectObjectTypes(object.value)" formControlName="objectt">
                                            <option selected> Object Category</option>
                                            <option value="ALL">ALL</option>
                                            <option value="Storage_Objects">Storage_Objects</option>
                                            <option value="Code_Objects">Code_Objects</option>
                                        </select>
                                        <div class="alert">
                                            @if(validate.objectt.touched && validate.objectt.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.objectt.errors?.['required']) {Object Category is required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label d-required">Object Type</label>
                                        <select class="form-select" aria-label="Default select example"
                                            aria-placeholder="Select objectType List" #objecttype
                                            formControlName="objecttype">
                                            <option selected disabled>Select Object Type</option>
                                            @for(list of objectType;track list;){
                                            <option value="{{ list.values }}">{{ list.values}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validate.objecttype.touched && validate.objecttype.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.objecttype.errors?.['required']) {Object Type is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2 offset-3">
                                    <button type="button" class="btn btn-upload w-100 mt-4"
                                        (click)="DeltaCommand(targetconform.value)"
                                        [disabled]="targetconform.invalid"> <span class="mdi mdi-cached"></span>
                                        Conversion </button>
                                </div>

                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                    Source Delta Files
                </button>
            </h2>
        </div>
        <div id="collapseTwo" class="accordion-collapse collapse " aria-labelledby="headingTwo"
            data-bs-parent="#accordionExample">
            <div class="qmig-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h3 class="main_h pt-3"><span class="mdi mdi-database"></span> Source Delta Files</h3>
                    </div>
                    <div class="custom_search cs-r">
                        <span class="mdi mdi-magnify"></span>
                        <input type="text" placeholder="Search Files" aria-controls="example">
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover qmig-table" id="example" style="width: 100%">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Folder Name</th>
                            <th>File Name</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                1
                            </td>
                            <td> Code </td>
                            <td>Source_3_2_2023.zip</td>
                            <td>
                                <button class="btn btn-download">
                                    <span class="mdi mdi-cloud-download-outline"></span>
                                </button>
                                <button class="btn btn-delete">
                                    <span class="mdi mdi-delete"></span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                2
                            </td>
                            <td> Code </td>
                            <td>DB_16_08_reg_Backup_Till_June1st_extraction_code.zip</td>
                            <td>
                                <button class="btn btn-download">
                                    <span class="mdi mdi-cloud-download-outline"></span>
                                </button>
                                <button class="btn btn-delete">
                                    <span class="mdi mdi-delete"></span>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                    Target Conversion Files
                </button>
            </h2>
        </div>

        <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree"
            data-bs-parent="#accordionExample">
            <div class="qmig-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h3 class="main_h pt-3"><span class="mdi mdi-database-sync"></span> Target Conversion Files</h3>
                    </div>
                    <div class="custom_search cs-r">
                        <span class="mdi mdi-magnify"></span>
                        <input type="text" placeholder="Search Files" aria-controls="example">
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover qmig-table" id="example" style="width: 100%">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>Folder Name</th>
                            <th>File Name</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                1
                            </td>
                            <td> Code </td>
                            <td>Source_3_2_2023.zip</td>
                            <td>
                                <button class="btn btn-download">
                                    <span class="mdi mdi-cloud-download-outline"></span>
                                </button>
                                <button class="btn btn-delete">
                                    <span class="mdi mdi-delete"></span>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                2
                            </td>
                            <td> Code </td>
                            <td>DB_16_08_reg_Backup_Till_June1st_extraction_code.zip</td>
                            <td>
                                <button class="btn btn-download">
                                    <span class="mdi mdi-cloud-download-outline"></span>
                                </button>
                                <button class="btn btn-delete">
                                    <span class="mdi mdi-delete"></span>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
            <div class="accordion-item">
                <h3 class="accordion-header" id="flush-headingFour">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseFour" aria-expanded="false" aria-controls="flush-collapseFour">
                        Target Conversion Status
                    </button>
                </h3>
                <div id="flush-collapseFour" class="accordion-collapse collapse" aria-labelledby="flush-headingFour"
                    data-bs-parent="#accordionFlushExample">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 mt-3  text-end">
                        <button class="btn btn-sync " (click)="refresh()">
                            <i class="mdi mdi-refresh"></i>
                            @if(ref_spin){
                            <app-spinner />
                            }
                        </button>
                </div>
                    <div class="table-responsive mt-2">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>Run No</th>
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>Schema Name</th>
                                    <th>Object Type</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (con of RequestTableData |searchFilter: datachanges| paginate: { itemsPerPage: 10,
                                currentPage: p ,id:'second'} ;track con) {
                                <tr>
                                    <td>{{p*10+$index+1-10}}</td>
                                    <td>{{con.run_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_name }}</td>
                                    <td>{{ con.schemaname }}</td>
                                    <td>{{ con.objecttype }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        @switch (con.status) {
                                        @case ('I') {
                                        <span>Initialize</span>
                                        }
                                        @case ('P') {
                                        <span>Pending</span>
                                        }
                                        @default {
                                        <span>Completed</span>
                                        }
                                        }
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
            <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
                <!-- <div class="accordion-item">
                    <h2 class="accordion-header" id="headingFive">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                            Page-Activity Log
                        </button>
                    </h2>
                    <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive"
                        data-bs-parent="#accordionExample">
                        <div class="qmig-card">
                            <div class="qmig-card-body">
                                <div class="row">
                                    <div class="col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="Schema">Run No</label>
                                            <select class="form-select" (change)="getPrjExeLogSelectTask(runnum.value)"
                                                #runnum>
                                                <option selected disabled>Select Run Number</option>
                                                @for(list of runNumbersData;track list;){
                                                <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover qmig-table">
                                            <thead>
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>Run No</th>
                                                    <th>Date and Time</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for (con of prjLogData |searchFilter: datachanges1| paginate: {
                                                itemsPerPage:
                                                10,currentPage: page , id: 'first'} ;track con; ) {
                                                <tr>
                                                    <td>{{p*10+$index+1-10}}</td>
                                                    <td>{{ con.iteration }}</td>
                                                    <td>{{ con.activity_date }}</td>
                                                    <td>{{ con.migtask }}</td>
                                                </tr>
                                                } @empty {
                                                <tr>
                                                    <td colspan="4">
                                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                                    </td>
                                                </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="custom_pagination">
                                        <pagination-controls (pageChange)="page = $event" id="first">
                                        </pagination-controls>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="flush-headingSix">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                data-bs-target="#flush-collapseSix" aria-expanded="false"
                                aria-controls="flush-collapseSix">
                                Execution Logs
                            </button>
                        </h3>
                        <div id="flush-collapseSix" class="accordion-collapse collapse"
                            aria-labelledby="flush-headingSix" data-bs-parent="#accordionFlushExample">
                            <div class="qmig-card">
                                <div class="qmig-card-body">
                                    <div class="row">
                                        <div class="col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="Schema">Run No</label>
                                                <select class="form-select" (change)="selectIterForLogs(runnumb.value)"
                                                    #runnumb>
                                                    <option selected disabled>Select Run Number</option>
                                                    @for(list of runNumbersData;track list;){
                                                    <option value="{{ list.iteration }}">{{ list.iteration}}
                                                    </option>
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-hover qmig-table">
                                                <thead>
                                                    <tr>
                                                        <th>S.No</th>
                                                        <th>Run No</th>
                                                        <th>Date and Time</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @for (con of LogsData |searchFilter: datachanges2| paginate:{
                                                    itemsPerPage:10,currentPage: p2 ,id:'second'} ;track con) {
                                                    <tr>
                                                        <td>{{ page3*10+$index+1-10 }}</td>
                                                        <td>{{con.fileName }}</td>
                                                        <td>
                                                            <button class="btn btn-download"
                                                                (click)="downloadFile(con)">
                                                                <span class="mdi mdi-cloud-download-outline"></span>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    } @empty {
                                                    <tr>
                                                        <td colspan="4">
                                                            <p class="text-center m-0 w-100">Empty</p>
                                                        </td>
                                                    </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="custom_pagination">
                                            <pagination-controls (pageChange)="p2 = $event" id="second">
                                            </pagination-controls>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
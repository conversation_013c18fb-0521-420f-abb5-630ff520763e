import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { HotToastService } from '@ngxpert/hot-toast';
import { TestingService } from '../../../../services/testing.service';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { CommonModule } from '@angular/common';
import { NgSelectModule, NgOption } from '@ng-select/ng-select';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgxPaginationModule } from 'ngx-pagination';
import { RunInfo, RunInfoTable } from '../../../../models/interfaces/types';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-test-assign',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, NgSelectModule, SearchFilterPipe, NgxPaginationModule],
  templateUrl: './test-assign.component.html',
  styles: ``
})
export class TestAssignComponent {

  // Basic Project Details
  project_name: string = '';
  getRole: string;
  projectId: string;

  targetObjects = [
    { values: 'Original Target Code', option: 'Original Target Code' },
    { values: 'Original Target Code', option: 'Updated Target Code' },
  ];
  //estList: Number=0;
  estHours: Number = 0;
  spin_tgtupdate: boolean = false;
  flag_mapto: boolean = false;
  estimationTime: number = 0;
  temp: number = 0;
  deploy_spin = false;
  IterationNo: string = '';
  observationss: string = '';
  statusMessages: string = '';
  deploystatuss: string = '';
  hideTextBox: boolean = true;
  hideFeatureDropdown: boolean = false;
  dbschema: string = '';
  chckBox: boolean = false;
  src_id: string = '';
  feature_Id: string = '';
  pageNumber: number = 1;
  searchText: string = '';
  OverRideCB: boolean = false;
  TgtUpdateTgt: boolean = false;
  Map_spin: boolean = false;
  Assign_spin: boolean = false;
  ObjectSelected: boolean = false;
  objectname: any;
  slist: string = "";
  codeObjectsCountcdc: string = '';
  src_id_cdc: string = '';
  srcobjCodeCDC: string = '';
  extractedschema: string = '';
  sourceCode: string = '';
  leadName: string = '';
  objCount: string = '';
  resource_name: string = '';
  migDetailSelect: string = '';
  srcConName: string = '';
  featureCount: number = 0;
  observations: string = '';
  statusMessage: string = '';
  tgtobjUpdatedCode: string = '';
  tgtobjCode: string = '';
  featureInsertedId: string = '';
  featureTextBoxValue: string = '';
  selectcheck: boolean = false;
  assignFeaturesInsertResponse: any;
  featureId: string = '';
  filtered: string = '';
  tenantId: string = '';
  subscriptionId: string = '';
  objType: string = '';
  sid: string = '';
  runid: string = '';
  srcCode: string = '';
  grpId: string = '';
  Schema: string = '';
  iteration: string = '';
  estimateData: any;
  groupleadData: any;
  singleObjData: any;
  logDetails: any;
  prjSrcTgt: any = {};
  prjSrcTgtD: any = {};
  prjSrcFeature: any = {};
  runInfo: RunInfoTable["Table1"] = [];
  selectByProject: any = [];
  userGroupData: any = {};
  srcObjectsSelectData: any = [];
  SelectedObject: any;
  tgtObjectsSelectData: any = [];
  selectedValue: any = [];
  tgtObjectsSelectDataCdc: any = [];
  featureData: any;
  infraData: any = [];
  src_list: any = [];
  srcObjectsSelectDataCdc: any = [];
  SourceObjCDC: any = [];
  DevopsSelectData: any;
  TaskResponse: any;
  codeObjectsCount: any;
  singleObjectData: any;
  statusSelectData: any;
  feature: any;
  selectedCodeObject: any;
  caAssignForm: any;
  codeobjectname: any;
  objectName: any;
  estList: any;
  selectedObj: any;
  featureInsertResponse: any = {};
  objectfeatureInsertResponse: any;
  selectedGroup: any;

  pageName: string = ''


  constructor(private toast: HotToastService, public fb: FormBuilder, public testingService: TestingService, private route: ActivatedRoute) {
    this.project_name = JSON.parse((localStorage.getItem('project_name') as string));
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') || 'null');
    this.pageName = this.route.snapshot.data['name'];
  }


  ngOnInit(): void {
    this.caAssignForm = this.fb.group({
      featureTxtBox: [''],
      CdcCheckBox: [''],
      overRideCheckBox: [''],
      Leadname: ['', [Validators.required]],
      assignTo: ['', [Validators.required]],
    });
    this.getRunInfoIteration();
    //this.userGroup();
    this.GetSecUserSelectByProject();
    this.projectMigDetailSelect();
    this.selectcheck = false;
    this.OverRideCB = false;
  }


  checkboxselect($event: any): void {
    this.selectcheck = $event.target.checked;
    if (this.selectcheck) {
      this.PrjsrcObjectsSelectCDC(this.objectname);
      this.tgtObjectSelectCDC();
    } else {
      this.srcObjectSelect(this.objectname);
      this.tgtObjectSelect();
    }
  }
  fetchSelected(value: any) {
    this.runInfo.filter((item: any) => {
      return item.iter_schema_operation === value
    });
  }

  // Fetching RunNo Details
  selectedIteration(value: any) {
    this.selectedValue = this.runInfo.filter((item: any) => {
      return item.dbschema === value;
    });

    this.dbschema = value;
    this.iteration = this.selectedValue[0].iteration;
    this.Schema = this.selectedValue[0].schemaname;
    this.runid = this.selectedValue[0].run_id;
    this.showFtrDrpDwn();
    this.postPrjSrcfeaturesSelect();
    // this.PrjsrcObjectsSelectCDC('null');
    this.srcObjectSelect('null');
  }

  selectGroupId(value: any) {
    this.leadName = value;
    // if (value == "0") {
    //   value = ""
    // }
    // this.grpId = value;
    // let leadinfo = this.groupleadData.filter((item: any) => {
    //   return item.groupid == value;
    // // });
    // if (leadinfo.length != 0) {
    //   this.leadName = leadinfo[0].resource_nm;
    // }
  }
  featureSelection(value: any) {
    this.getSrcId()
    this.feature = value;
    let featureValue = this.featureData.filter((item: any) => {
      return item.feature_name == value;
    });
    this.feature_Id = featureValue[0].feature_id;
    this.groupLead();
    if (value == 'New') {
      this.hideTextBox = false;
      this.hideFeatureDropdown = true;
      this.featureCount = 0;
      this.estHours = 0;
    }
    else {
      this.estimatedHours();
    }
  }
  showFtrDrpDwn() {
    this.hideTextBox = true;
    this.hideFeatureDropdown = false;
  }


  onItemSelect(item: any) {
    //this.objectName.push(item.objectname);
    const esttime = this.srcObjectsSelectData.filter((item1: any) => {
      return item1.objectname == item.objectname;
    })
    this.estimationTime = Number(esttime[0].estimation_time) + Number(this.estimationTime);
  }

  /* Code Objects method start*/
  selectCodeObject(value: any) {
    this.ObjectSelected = true;
    //  this.selectedObject = this.srcObjectsSelectData.filter((item: any) => {
    //   return this.src_id=value[0].src_id
    // });
    this.src_id = value[0].src_id;
    this.objectName = value[0].objectname;
    this.ObjectSelected = true;
    this.selectedCodeObject = value[0];
    //this.extractedschema = this.selectedCodeObject['objectname'].toString().split('.')[0].toUpperCase()
    this.prjdeploystatusselect(this.SelectedObject);
    this.tgtobjCode = '';
    this.tgtobjUpdatedCode = '';
    this.GetExecutionLog(this.selectedCodeObject.objectname);
    if (this.selectcheck) {
      this.PrjsrcObjectsSelectCDC(this.selectedCodeObject.objectname);
      this.tgtObjectSelectCDC();
    } else {
      this.srcObjectSelect(this.src_id);
      this.tgtObjectSelect();

    }
    this.estimatedHours()
  }


  srcObjectSelect(objID: any = []) {
    let obj = {
      //projectId: this.projectId.toString(),
      runid: this.iteration,
      objectid: objID,
    };
    if (objID == 'null') {
      this.testingService.testPrjSrcObjectsSelect(obj).subscribe((data: any) => {
        const testcaseValues = data['Table1'];
        const key = 'objectname';
        this.srcObjectsSelectData = testcaseValues;
        //this.srcObjectsSelectData = [...new Map(testcaseValues.map((item: any) => [item[key], item])).values()]
        this.codeObjectsCount = this.srcObjectsSelectData.length;
        this.objCount = this.codeObjectsCount;
      });

    } else {
      this.testingService.testPrjSrcObjectsSelect(obj).subscribe((data: any) => {
        let objData = data['Table1'];

        this.codeObjectsCount = this.srcObjectsSelectData.length;
        let obj = objData.filter((item: any) => {
          return item.objectname == objID;
        });

        this.objType = obj[0].objecttype;
        this.srcCode = obj[0].objectcode;
        this.sourceCode = this.srcCode;
        if (objData.length != 0) {
          this.src_id = objData[0].src_id;
          this.objType = objData[0].objecttype;
          this.srcCode = objData[0].objectcode;
        }

        if (this.objectName == 'ALL') {
          this.estimationTime = this.singleObjData[0].estimation_time;

        } else {
          this.temp = objData[0].total_mins;

          this.estimationTime = Number(this.temp) + Number(this.estimationTime);
          this.estList = this.estimationTime
        }
      });
    }
  }

  prjdeploystatusselect(selectedObj: any) {
    let obj = {
      projectId: this.projectId.toString(),
      srcid: this.src_id
    };
    this.deploystatuss = "";
    this.statusMessages = "";
    this.observationss = "";
    this.deploy_spin = true
    this.testingService.testPrjTgtDeploystatusSelect(obj).subscribe((data: any) => {
      this.statusSelectData = data['Table1'];
      this.deploy_spin = false
    });
  }

  GetExecutionLog(value: any) {
    // let obj = {
    //   projectId: this.projectId.toString(),
    //   iteration: this.iteration,
    //   objectName: this.objectname.toString(),
    // }
    // this.testingService.getExecutionLog(obj).subscribe((data: any) => {
    //   this.logDetails = data['Table1'];
    // })
  }

  PrjsrcObjectsSelectCDC(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      objectName: value.toString(),
      schemaName: this.Schema,
      option: 'null',
    };
    if (value == 'null') {
      this.testingService.prjsrcObjectsSelectCDC(obj).subscribe((data: any) => {
        this.srcObjectsSelectDataCdc = data['jsonResponseData']['Table1'];
        this.srcobjCodeCDC = this.srcObjectsSelectDataCdc[0].objectcode;
        this.codeObjectsCountcdc = this.srcObjectsSelectDataCdc.length;
        let obje = this.srcObjectsSelectDataCdc.filter((item: any) => {
          return item.objectname == this.selectedCodeObject;
        });

      });
    } else {
      this.testingService.prjsrcObjectsSelectCDC(obj).subscribe((data: any) => {
        let objData = data['jsonResponseData']['Table1'];
        if (objData.length == 2) {
          this.SourceObjCDC = objData;
          this.singleObjectData = objData.filter((item: any) => {
            return item.objectname == value;
          });
          this.srcobjCodeCDC = this.singleObjectData[0].objectcode;
          var objec = this.SourceObjCDC.filter((item: any) => {
            return item.objectname == this.objectname;
          });
          this.src_id_cdc = objec[0].src_id;
          this.objType = objec[0].objecttype;
          this.srcCode = objec[0].objectcode;
          this.sourceCode = this.srcCode;
        } else {
          this.sourceCode =
            'Source Code is not Available for ' + this.objectname;
        }
        if (value == 'ALL') {
          this.estimationTime = this.singleObjData[0].estimation_time;
        } else {
          this.estimationTime = objec[0].total_mins;
        }
      });
    }
  }

  tgtObjectSelectCDC() {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      objectName: this.objectname.toString(),
      schemaName: this.Schema,
      option: 'null',
    };
    this.testingService.prjtgtObjectsSelectCdc(obj).subscribe((data: any) => {
      this.tgtObjectsSelectDataCdc = data['jsonResponseData']['Table1'];
      if (this.tgtObjectsSelectDataCdc != undefined) {
        obj
        let tgtobjcdc = this.tgtObjectsSelectDataCdc.filter((item: any) => {
          return item.objectname == this.objectname;
        });

        this.tgtobjCode = tgtobjcdc[0].objectcode;
        this.tgtobjUpdatedCode = tgtobjcdc[0].updated_code;
      } else {
        this.tgtobjCode =
          'Target Code is not Available for ' + this.selectedCodeObject;
      }
    });
  }
  /* Code Objects method End*/



  selectTimestamp(value: any) {
    let status = this.statusSelectData.filter((item: any) => {
      return item.activitytime == value;
    });
    this.statusMessage = status[0].statusmessage;
    this.observations = status[0].observations;
  }
  getRunInfoIteration() {
    this.testingService.getRunNumbers(this.projectId).subscribe((data: RunInfoTable) => {
      this.runInfo = data.Table1;
    });
  }

  getProjectSrctgtconfSelectD() {
    this.prjSrcTgtD.projectId = this.projectId;
    this.prjSrcTgtD.migsrcType = 'D';
    this.prjSrcTgtD.connectionName = null;
    // this.testingService.getProjectSrctgtconfSelect(this.prjSrcTgtD).subscribe((data) => {
    // });
  }

  getProjectSrctgtconfSelect() {
    this.prjSrcTgt.projectId = this.projectId;
    this.prjSrcTgt.migsrcType = 'F';
    this.prjSrcTgt.connectionName = null;
    // this.testingService.getProjectSrctgtconfSelect(this.prjSrcTgt).subscribe((data) => {
    // });
  }

  getSecUserProjectmenu() {
    this.testingService.getSecUserProjectMenu().subscribe((data) => {
    });
  }

  GetSecUserSelectByProject() {
    // this.testingService.secUserSelectByProject(this.projectId).subscribe((data) => {
    //   this.selectByProject = data['jsonResponseData']['Table1'];
    // });
    this.userGroup();
  }

  postPrjSrcfeaturesSelect() {
    this.prjSrcFeature.projectId = this.projectId.toString();
    this.prjSrcFeature.iteration = this.runid;
    this.prjSrcFeature.groupId = 'null';
    this.prjSrcFeature.runid = this.iteration;
    this.testingService.testPrjSrcfeaturesSelect(this.prjSrcFeature).subscribe((data: any) => {
      this.featureData = data['Table1'];
    });
  }
  userGroup() {
    let obj = {
      //projectId: this.projectId.toString(),
      opt: 'A',
      grpid: 'null',
      rm_name: 'null',
    };

    this.testingService.testPrjFeatureGroupSelect(obj).subscribe((data: any) => {
      let sampledata = data['Table1'];
      this.userGroupData = sampledata.filter(
        (item: any, i: any) =>
          sampledata.findIndex((t: any) => t.groupid === item.groupid) === i
      );
    });
  }



  selectTimestampp(value: any) {
    if (this.statusSelectData != undefined) {
      let TgtDeployStatusSelect = this.statusSelectData.filter((item: any) => {
        return item.activitytime == value;
      });
      this.statusMessages = TgtDeployStatusSelect[0]?.statusmessage;
      this.observationss = TgtDeployStatusSelect[0]?.observations;
      this.deploystatuss = TgtDeployStatusSelect[0]?.deploystatus;
    }
  }

  tgtObjectSelect() {
    let obj = {
      //projectId: this.projectId.toString(),
      //iteration: this.iteration,
      srcid: this.src_id,
      //schemaname: 'HRPAY',
      opt: 'null',
    };
    this.testingService.testPrjTgtObjectsSelect(obj).subscribe((data: any) => {
      this.tgtObjectsSelectData = data['Table1'];
      if (this.tgtObjectsSelectData != undefined) {
        this.tgtobjCode = this.tgtObjectsSelectData[0].objectcode;
        this.tgtobjUpdatedCode = this.tgtObjectsSelectData[0].updated_code;
      }
    });
  }


  srcfeaturesInsert(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      feature_name: value,
      resource_nm: 'null',
      acl: ' ',
    };
    this.Map_spin = true;
    this.testingService.testPrjSrcFeaturesInsert(obj).subscribe((data: any) => {
      this.featureInsertResponse = data['jsonResponseData']['Table1'];
      this.featureInsertedId = this.featureInsertResponse[0].id;
      if (this.feature == 'New') {
        if (
          this.featureTextBoxValue == '' &&
          this.selectedCodeObject == 'ALL'
        ) {
          this.prjObjectFeaturesInsert(this.featureInsertedId);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
        if (
          this.featureTextBoxValue == '' &&
          this.selectedCodeObject != 'ALL'
        ) {
          this.prjObjectFeaturesInsertNew(this.featureInsertedId);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
        if (
          this.featureTextBoxValue != '' &&
          this.selectedCodeObject == 'ALL'
        ) {
          this.prjObjectFeaturesInsert(this.featureInsertedId);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
        if (
          this.featureTextBoxValue != '' &&
          this.selectedCodeObject != 'ALL'
        ) {
          this.prjObjectFeaturesInsertNew(this.featureInsertedId);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
      }
      if (this.feature != 'New') {
        if (this.feature != 'New' && this.selectedCodeObject == 'ALL') {
          this.prjObjectFeaturesInsert(this.featureInsertResponse.Id);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
        if (this.feature != 'New' && this.selectedCodeObject != 'ALL') {
          this.prjObjectFeaturesInsertNew(this.featureInsertResponse.Id);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
      }

    });
  }
  srcfeaturesInsertForAssignTo(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      schemaName: 'null',
      feature_name: value,
      resource_nm: 'null',
      acl: ' ',
    };
    this.testingService.testPrjSrcFeaturesInsert(obj).subscribe((data: any) => {
      this.featureInsertResponse = data['jsonResponseData']['Table1'];
      this.featureInsertedId = this.featureInsertResponse[0].id;
      if (this.feature == 'New') {
        if (
          this.featureTextBoxValue == '' &&
          this.selectedCodeObject == 'ALL'
        ) {
          this.prjObjectFeaturesInsert(this.featureInsertedId);
          this.prjAssignFeaturesInsert(this.feature_Id);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
        if (
          this.featureTextBoxValue == '' &&
          this.selectedCodeObject != 'ALL'
        ) {
          this.prjObjectFeaturesInsertNew(this.featureInsertedId);
          this.prjAssignFeaturesInsert(this.feature_Id);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
        if (
          this.featureTextBoxValue != '' &&
          this.selectedCodeObject == 'ALL'
        ) {
          this.prjObjectFeaturesInsert(this.featureInsertedId);
          this.prjAssignFeaturesInsert(this.feature_Id);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
        if (
          this.featureTextBoxValue != '' &&
          this.selectedCodeObject != 'ALL'
        ) {
          this.prjAssignFeaturesInsert(this.featureInsertedId);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
      }
      if (this.feature != 'New') {
        if (this.feature != 'New' && this.selectedCodeObject == 'ALL') {
          this.prjObjectFeaturesInsert(this.featureInsertResponse.Id);
          this.prjAssignFeaturesInsert(this.feature_Id);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
        if (this.feature != 'New' && this.selectedCodeObject != 'ALL') {
          this.prjObjectFeaturesInsertNew(this.featureInsertResponse.Id);
          this.prjAssignFeaturesInsert(this.feature_Id);
          this.ObjectFeaturesSelect();
          this.postPrjSrcfeaturesSelect();
        }
      }
      if (this.selectedCodeObject == 'ALL') {
        this.prjAssignFeaturesInsert(this.feature_Id);
        this.postPrjSrcfeaturesSelect();
        this.ObjectFeaturesSelect();
      }
      if (this.selectedCodeObject != 'ALL') {
        this.prjAssignFeaturesInsert(this.feature_Id);
        this.postPrjSrcfeaturesSelect();
        this.ObjectFeaturesSelect();
      }
    });
  }
  prjObjectFeaturesInsert(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      feature_id: this.feature_Id, //this.featureInsertResponse.Id,
      // iteration: this.iteration,
      resourcename: value, //this.resource_name,
      src_id: this.src_id,
      // schemaName: this.dbschema,
      status: 'null',
      acl: ' ',
    };
    this.Map_spin = true;
    this.testingService.testPrjObjectFeaturesInsert(obj).subscribe((data: any) => {
      this.objectfeatureInsertResponse = data['jsonResponseData']['Table1'];
      let status = this.objectfeatureInsertResponse[0].v_status;
      this.Map_spin = false;
      this.toast.success(status);
    });
  }
  prjObjectFeaturesInsertNew(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      feature_id: value,
      src_id: this.slist,
      acl: ' ',
    };
    this.Map_spin = true;
    this.testingService.testPrjObjectFeatureInsertNew(obj).subscribe((data: any) => {
      this.objectfeatureInsertResponse = data['jsonResponseData']['Table1'];
      let status = this.objectfeatureInsertResponse[0].v_status;
      this.Map_spin = false;
      this.toast.success(status);
    });
  }
  objectSelectResponse: any = {};
  ObjectFeaturesSelect() {
    let obj = {
      projectId: this.projectId.toString(),
      feature_id: 'null',
      src_id: 'null',
    };
    this.testingService.testPrjObjectsfeaturesSelect(obj).subscribe((data: any) => {
      this.objectSelectResponse = data['Table1'];
    });
  }
  ObjectFeaturesSelectcdc() {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: this.iteration,
      assignId: 'null',
      objectName: 'null',
      schemaName: this.Schema,
    };
    this.testingService.GetProjectObjectFeaturesSelectCdc(obj).subscribe((data: any) => {
      this.objectSelectResponse = data['jsonResponseData']['Table1'];
    });
  }
  ObjectFeaturesSelectcdcNew() {
    let obj = {
      projectId: this.projectId.toString(),
      featureId: 'null',
      src_Id: 'null',
    };
    this.testingService.GetProjectObjectFeaturesSelectCdcNew(obj).subscribe((data: any) => {
      this.objectSelectResponse = data['Table1'];
    });
  }
  objectFeaturesInsertCdc(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      featureId: value,
      iteration: this.iteration,
      schemaName: this.dbschema,
      acl: ' ',
    };
    this.testingService.ObjectFeaturesInserCdc(obj).subscribe((data: any) => {
      this.objectfeatureInsertResponse = data['jsonResponseData']['Table1'];
    });
  }
  objectFeaturesInsertCdcNew(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      featureId: value,
      src_id: this.src_id_cdc,
      acl: ' ',
    };
    this.testingService.ObjectFeaturesInserCdcNew(obj).subscribe((data: any) => {
      this.objectfeatureInsertResponse = data['jsonResponseData']['Table1'];
    });
  }


  mapToFeatureBtn(formData: any) {

    this.flag_mapto = true
    if (formData.CdcCheckBox === '') {
      formData.CdcCheckBox = false;
    }
    this.featureTextBoxValue = formData.featureTxtBox;
    this.chckBox = formData.CdcCheckBox;

    if (this.feature == 'New') {
      if (this.featureInsertedId != undefined) {
        this.prjObjectFeaturesInsert(this.featureInsertedId);
        this.ObjectFeaturesSelect();
        this.postPrjSrcfeaturesSelect();
      }

      if (this.featureInsertedId == undefined) {
        if (formData.featureTxtBox == '' && this.codeObjectsCount == this.objectname.length) {
          this.srcfeaturesInsert(this.dbschema);
        }
        if (formData.featureTxtBox == '' && this.objectname.length == 1) {
          this.srcfeaturesInsert(this.selectedCodeObject.objectname);
        }
        if (formData.featureTxtBox != '' && this.objectname.length == 1) {
          this.srcfeaturesInsert(formData.featureTxtBox);
        }
        if (formData.featureTxtBox != '' && this.codeObjectsCount != this.objectname.length && this.objectname.length != 1) {
          this.srcfeaturesInsert(formData.featureTxtBox);
        }
        if (formData.featureTxtBox != '' && this.objectname.length != 1 && this.codeObjectsCount == this.objectname.length) {
          this.srcfeaturesInsert(formData.featureTxtBox);
        }
      }

    }
    if (this.feature != 'New') {
      if (this.featureInsertedId != undefined) {
        this.prjObjectFeaturesInsert(this.featureInsertedId);
        this.ObjectFeaturesSelect();
        this.postPrjSrcfeaturesSelect();
      }
      if (this.feature != 'New' && this.selectedCodeObject == 'ALL') {
        this.prjObjectFeaturesInsert(this.feature_Id);
        this.ObjectFeaturesSelect();
        this.postPrjSrcfeaturesSelect();
      }
      if (this.feature != 'New' && this.selectedCodeObject != 'ALL') {
        this.prjObjectFeaturesInsertNew(this.feature_Id);
        this.ObjectFeaturesSelect();
        this.postPrjSrcfeaturesSelect();
      }
    }
    //this.estimatedHours();
  }


  AssignToBtn(formData: any) {
    if (this.leadName == this.resource_name) {
      this.toast.error('Lead Name and Assign Name should not be Same');
    }
    if (this.leadName != this.resource_name) {
      let filterlead = this.groupleadData.filter((item: any) => {
        return item.resource_nm == this.resource_name;
      });
      if (filterlead.length == 0) {
        if (formData.CdcCheckBox === '') {
          formData.CdcCheckBox = false;
        }
        this.featureTextBoxValue = formData.featureTxtBox;
        this.chckBox = formData.CdcCheckBox;
        if (this.feature == 'New') {
          if (
            formData.featureTxtBox == '' &&
            this.objectName == 'ALL'
          ) {
            this.prjObjectFeaturesInsert(this.featureInsertedId);
            this.prjAssignFeaturesInsert(this.featureInsertedId);
            this.ObjectFeaturesSelect();
            this.postPrjSrcfeaturesSelect();
          }
          if (
            formData.featureTxtBox == '' &&
            this.objectName != 'ALL'
          ) {
            this.prjObjectFeaturesInsertNew(this.featureInsertedId);
            this.prjAssignFeaturesInsert(this.featureInsertedId);
            this.ObjectFeaturesSelect();
            this.postPrjSrcfeaturesSelect();
          }
          if (
            formData.featureTxtBox != '' &&
            this.objectName == 'ALL'
          ) {
            this.prjObjectFeaturesInsert(this.featureInsertedId);
            this.prjAssignFeaturesInsert(this.feature_Id);
            this.ObjectFeaturesSelect();
            this.postPrjSrcfeaturesSelect();
          }
          if (
            formData.featureTxtBox != '' &&
            this.objectName != 'ALL'
          ) {
            this.prjAssignFeaturesInsert(this.featureInsertedId);
            this.ObjectFeaturesSelect();
            this.postPrjSrcfeaturesSelect();
          }
        }
        if (this.feature != 'New') {
          if (this.feature != 'New' && this.objectName == 'ALL') {
            this.prjAssignFeaturesInsert(this.feature_Id);
            this.ObjectFeaturesSelect();
            this.postPrjSrcfeaturesSelect();
          }
          if (this.feature != 'New' && this.selectedCodeObject != 'ALL') {
            this.prjAssignFeaturesInsert(this.feature_Id);
            this.ObjectFeaturesSelect();
            this.postPrjSrcfeaturesSelect();
          }
        }

      } else {
        this.toast.error('Lead Already Assigned to this Feature');
      }
    }
  }

  prjAssignFeaturesInsert(feature_id: any) {
    if (this.grpId == '') {
      this.grpId = 'null';
    }
    // let user = this.selectByProject.filter((item: any) => {
    //   return item.resource_nm == this.resource_name;
    // });
    //let userid = user[0].userid;
    let userid = "706ae535-3a0f-4090-8ef1-85dac2d5b0a5";
    let obj = {
      projectId: this.projectId.toString(),
      feature_id: feature_id,
      status: "null",
      grpid: this.grpId,
      userid: userid,
      rm_name: this.resource_name,
      acl: null,
    };
    this.Assign_spin = true;
    this.testingService.testPrjSrcFeaturesAssignInsert(obj).subscribe((data: any) => {
      this.assignFeaturesInsertResponse = data['jsonResponseData']['Table1'];
      let status = this.assignFeaturesInsertResponse[0].v_status;
      this.Assign_spin = false;
      this.toast.success(status);
    });
  }
  selectrmName(value: any) {
    this.resource_name = value;
  }
  createTaskButton() {
    let workitemObj = {
      objectName: 'string',
      projectID: this.projectId,
      tagValue: 'Inprogress',
      schema: this.Schema,
      src_con_name: this.srcConName,
      clientID: 'MC',
      workItemType: 'Task',
      sourceobjectName: 'string',
      targetobjectName: 'string',
      iterationID: this.iteration,
      assign: 'string',
      cdc: this.chckBox,
    };
    this.testingService.createWorkItem(workitemObj).subscribe((data: any) => {
      this.TaskResponse = data;
      //this.PrjDevopstaskInsert();
      //this.PrjDevopstaskSelect();
      if (this.chckBox === true) {
        this.tgtObjectSelectCDC();
      } else {
        this.tgtObjectSelect();
      }
    });
  }


  projectMigDetailSelect() {
    let prjmigDetail;
    let obj = {
      projectId: this.projectId.toString(),
      migsrcType: 'D',
    };
    // this.project.projectMigDetailSelect(obj).subscribe((data: any) => {
    //   prjmigDetail = data['jsonResponseData']['Table1'];
    //   this.migDetailSelect = prjmigDetail.filter((item: any) => {
    //     return item.migsrctgt == 'S';
    //   });
    //   if (this.migDetailSelect == 'S') {
    //     this.srcConName = this.migDetailSelect[0].dbconname;
    //   }
    // });
  }
  PrjDevopstaskSelect() {
    // this.project.PrjDevopTask(this.projectId).subscribe((data: any) => {
    //   this.DevopsSelectData = data['Table1'];
    // });
  }
  PrjDevopstaskInsert() {
    let featureName;
    if (this.feature == 'New') {
      featureName = this.featureTextBoxValue;
    } else {
      featureName = this.feature;
    }
    let taskObj = {
      projectId: this.projectId,
      taskId: this.TaskResponse.taskId,
      iteration: this.iteration,
      codeObject: this.selectedCodeObject,
      feature: featureName,
      assignTo: this.resource_name,
      acl: ' ',
    };
    // this.project.PrjDevopTaskInsert(taskObj).subscribe((data: any) => {
    //   this.DevopsSelectData = data['Table1'];
    // });
  }
  TgtcodeUpdateCdc() {
    let tgtdata = this.tgtObjectsSelectDataCdc.filter((item: any) => {
      return item.objectname == this.selectedCodeObject;
    });
    let tgtId = tgtdata[0].tgt_id;
    let obj = {
      projectId: this.projectId,
      tgt_id: tgtId,
      Override: true,
      updatedcode: '',
    };
    // this.testingService.PrjTgtcodeUpdateCdc(obj).subscribe((data: any) => {
    //   this.DevopsSelectData = data['Table1'];
    //});
  }

  TgtcodeUpdate() {
    this.spin_tgtupdate = true;

    let tgtId = this.tgtObjectsSelectData[0].tgt_id;
    let updatedcode = (<HTMLInputElement>(
      document.getElementById('orginaltgtcode')
    )).value;
    let obj = {
      projectId: this.projectId.toString(),
      tgt_id: tgtId,
      overRide: true,
      updated_code: updatedcode,
      acl: null,
    };
    this.testingService.testTgtCodeUpdate(obj).subscribe((data: any) => {
      this.DevopsSelectData = data['jsonResponseData']['Table1'];
      this.spin_tgtupdate = false;
      if (this.DevopsSelectData[0].v_status == 'Successfully Updated') {
        this.toast.success('Successfully Saved');
      }
    });
    this.spin_tgtupdate = false;
  }
  overRideChkBox($event: any): void {
    this.OverRideCB = $event.target.checked;
  }
  saveBtn() {
    if (this.chckBox) {
      this.TgtcodeUpdateCdc();
      this.tgtObjectSelectCDC();
    } else {
      this.TgtcodeUpdate();
      this.tgtObjectSelect();
    }
    if (this.OverRideCB) {
      //this.executeCommandOnVM();
    }
  }

  getInfraSelect() {
    let id = this.projectId;
    // this.project.InfraSelect(id).subscribe((data) => {
    //   this.infraData = data['jsonResponseData']['Table1'];
    // });
  }


  groupLead() {
    let obj = {
      projectId: this.projectId.toString(),
      feature_id: this.feature_Id,
      iteration: this.iteration,
    };
    this.testingService.testPrjFeaturesGroupLeadSelect(obj).subscribe((data: any) => {
      this.groupleadData = data['Table1'];
      for (const element of this.groupleadData) {
        if (element.resource_nm == "" && element.groupid == "") {
          element.resource_nm = "New Lead";
          element.groupid = "0";
        }
      }
    });
  }

  estimatedHours() {
    let obj = {
      projectId: this.projectId.toString(),
      feature_id: this.feature_Id,
    };
    this.testingService.testPrjSrcFeaturesEstTimeCountSelect(obj).subscribe((data: any) => {
      this.estimateData = data['Table1'];
      this.featureCount = this.estimateData[0].mapped_objcnt;
      this.estHours = this.estimateData[0].feature_level_esttime;
      this.Map_spin = false;

    });
  }
  TgtCodeShow() {
    this.TgtUpdateTgt = true;
  }
  TgtCodeShows() {
    this.TgtUpdateTgt = false;
  }

  getSrcId() {
    this.src_list = [];
    for (const element of this.objectname) {
      for (const element2 of this.srcObjectsSelectData) {
        if (element == element2.objectname) {
          this.src_list.push(element2.src_id)
        }
      }
    }
    this.slist = this.src_list.toString()
  }

  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.objectName.push(dd.objectname);
    });
    this.estimationTime = 0;
    for (const element of this.srcObjectsSelectData) {
      this.estimationTime = Number(element.total_mins) + this.estimationTime
    }
  }

  onDeSelectAll(items: any) {
    this.objectName = [];
    this.estList = [];
    this.estimationTime = this.estList.reduce((a: any, b: any) => a + b);
  }

  abc: any
  abcarray: any = []
  onItemDeSelect(item: any) {
    let selobj = this.srcObjectsSelectData.filter((item1: any) => {
      return item1.objectname == item.objectname
    })
    this.estimationTime = this.estimationTime - Number(selobj[0].total_mins)
    const inde = this.objectName.indexOf(item.objectname);
    let rem = this.objectName.splice(inde, 1);
    const index = this.objectName.indexOf(item.objectname);
    let removed = this.estList.splice(index, 1);
    let rm = this.objectName.splice(index, 1);
    this.estimationTime = this.estList.reduce((a: any, b: any) => a + b);
  }
}

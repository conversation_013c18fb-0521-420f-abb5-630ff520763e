import { Component } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { FormsModule, NgModel, ReactiveFormsModule, Validators, FormBuilder } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { Observable } from 'rxjs/internal/Observable';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

declare let $: any;
@Component({
  selector: 'app-validation',
  standalone: true,
  imports: [NgSelectModule, FormsModule, CommonModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './validation.component.html',
  styles: ``
})
export class ValidationComponent {
  migrationSource = [{ values: 'D', option: 'Database' }];
  selectedCar: number | undefined;
  selectedPeople = [];
  selectedItems = [];
  data: any = [];

  people$: Observable<any> | undefined;
  //pagination
  searchText: string = '';
  searchText1: string = '';
  projectId: string = '';

  //getConsLIst
  schemaList: any;
  obj: any;
  ConsList: any;
  datachangeLogs: any
  //getschemaList
  tgtlist: any;
  userData: any;
  selectedConname: any;
  //connection
  conId: any;
  conName: any;
  tgtId: any;
  //pagination
  datachange: any;
  datachange1: any;
  datachange2: any;
  pageNumber: number = 1;
  p: number = 1;
  page: number = 1;
  page2: number = 1;
  p1: number = 1;
  p2: number = 1;
  r_id: any;
  //page activity
  logdata: any;
  disabledprevious: boolean = false;
  tgtValue: string = '';
  //object type
  objectType: any;
  project: any;
  objtype: any;
  //validation
  value: any;
  airflowResponse: any
  execProjectForm: any;
  prjSchmea: any = {};
  getRunSpin: boolean = false;
  //get run number
  runNoData: any;
  runnoForReports: any;
  ref_spin: boolean = false
  tabledata: any;
  z: any;
  //iteration
  deleteResponse: any
  iterationForLogs: any
  //obj
  selectedObjType: any
  depLogs: any = []
  //schema
  schemaName: any = [];
  assessmentFiles: any
  obtypevalue: any
  OpName: any;
  operation: any = [];
  //hrs
  hrs: any
  fileResponse: any;
  spin_dwld: any;
  runinfospin: boolean = false;
  runinfo: boolean = false;
  //open model details
  prjdatas: any
  opId: any
  iterationselected: any
  selectedschemas: string = ""
  selectedItemstgt: any
  migtypeid: string = ""
  pageName: string = ''
  schemalable: string = ""
  constructor(private titleService: Title, private FormBuilder: FormBuilder, private common: CodeMigrationService, private toast: HotToastService, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }

  }
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList()
    this.getOperationList();
    this.getPrjRuninfoSelectAll()
    // this.getrunno()
    this.getreqTableData();
    this.execProjectForm = this.FormBuilder.group({
      runNo: ['', [Validators.required]],
      connectionType: ['', [Validators.required]],
      targetConnection: ['', [Validators.required]],
      connectionName: [''],
      fileName: [''],
      operation: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      tgtschema: ['']
    });
    this.pageNumber = 1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page2 = 1;
    this.page = 1;
  }
  //for validations
  get f() {
    return this.execProjectForm.controls;

  }
  selectschema(data: any) {
    this.schemaName = data
    //console.log(data)
  }
  onKey() {
    this.pageNumber = 1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page2 = 1;
    this.page = 1;
  }
  redisResp: any

  currentRunNumber: string = '';
  getRunNumber(data: any) {

    this.schemaList=[]
    this.ConsList=[]
    this.currentRunNumber = data;
    this.runnoForReports.filter((item:any)=>{
      if(item.iteration==data){
        this.schemaList.push({schema_name:item.schemaname})
        this.ConsList.push({Connection_ID:item.connection_id,conname:item.conname})
      }
    })
    // this.currentRunNumber = data;
    // this.schemasbyiteration(data)
    //console.log(data)
  }

  schemasbyiteration(value: string) {
    this.common.GetSchemasByRunId(value).subscribe((data: any) => {
      this.schemaList = data['Table1'];
    })
  }
  get getControl() {
    return this.execProjectForm.controls;
  }
  tgtSchemaList: any = []
  //GetSchemasLiist
  getSchemasList(connectionId: any) {
    this.tgtSchemaList = []
    if (this.migtypeid == "31") {
      this.tgtlist.filter((item: any) => {
        if (item.Connection_ID == connectionId) {
          let ob = {
            schema_name: item.dbname
          }
          this.tgtSchemaList.push(ob);
        }
      })
    }
    else {
      const obj = {
        projectId: this.projectId,
        connectionId: connectionId
      }
      this.common.SchemaListSelect1(obj).subscribe((data: any) => {
        // this.schemaList = data['Table1'];
        this.tgtSchemaList = data['Table1'];
        this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => {
          return item.schemaname != "ALL"
        });
      })
    }
  }
  selectedtgtScema: string = ""
  selectTgtSchema(value: any) {
    this.selectedtgtScema = value
  }
  //GetconnectionList
  GetConsList() {
    this.common.getConList(this.projectId.toString()).subscribe((data: any) => {
      // this.ConsList = data['Table1'].filter((item: any) => {
      //   return item.migsrctgt == 'S' && item.conname != "";
      // })
      this.tgtlist = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      //console.log(this.tgtlist)
    })
  }
  //selected file type
  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;

  }
  //Get terget connection

  tgtConid: any
  tgtCon: any
  selectTgtId(value: any) {
    //console.log(value)
    var con = this.tgtlist.filter((item: any) => {
      return item.Connection_ID === value
    })

    //console.log(this.tgtlist)
    //console.log(con,"tgtValue")
    this.tgtConid = value
    //this.tgtId = con[0].Connection_ID;
    this.tgtCon = con[0].conname

  }

  selectObj(value: any) {
    this.objtype = value
    this.execProjectForm.controls.schema.setValidators([Validators.required])
    this.execProjectForm.controls.schema.updateValueAndValidity()
  }


  //get schema list
  prjSchemaList(sourceConection: any) {
    this.prjSchmea.projectId = this.projectId.toString();
    this.prjSchmea.dbConnection = 'null';
    this.prjSchmea.sourceConnection = sourceConection.toString();
    this.common.prjSchemaListSelect(this.prjSchmea).subscribe((data: any) => {
      this.schemaList = data['Table1'];
    });
  }


  getAssessmentLogs(value: any) {
  }
  //update run number
  getUpdatedRunNumber() {
    this.getRunSpin = true
    this.getPrjRuninfoSelectAll()
    this.getreqTableData()

  }


  //Get run number


  getPrjRuninfoSelectAll() {
    this.common.GetRunno(this.projectId).subscribe((data) => {
      this.getRunSpin = false
      this.runNoData = data['Table1'];
      this.runnoForReports = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoForReports= this.runnoForReports.filter((item: any) => {
        return item.iteration!=""
      })
     // this.runnoForReports = this.runnoForReports.filter((data: any) => { return ((data.operation_type == "Extraction") && data.iteration != '') })
      // this.runNoData = this.runNoData.filter((item: any) => {
      //   if (item.iteration == "") {
      //     item.dbschema = "ALL"
      //   }
      //   return (item.operation_type == "Validation") || item.iteration == ""
      // })
      this.runnoForReports = this.filterList(this.runnoForReports)
      this.runNoData = this.filterList(this.runNoData)
      //console.log(this.runNoData)

    });
  }
  //filter list
  filterList(listData: any) {
    let uniqueNames: any = []
    for (let k = 0; k < listData.length; k++) {
      if (uniqueNames.length == 0) {
        uniqueNames.push(listData[k])
      }
      else {
        var abc = uniqueNames.filter((item: any) => {
          return item.iteration === listData[k].iteration
        })
        if (abc.length == 0) {
          uniqueNames.push(listData[k])
        }
      }
    }
    return uniqueNames;
  }

  //Get request table data
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: 'Validation',
    };
    this.ref_spin = true
    this.common.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      //console.log(this.tabledata)
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        //console.log(this.tabledata)
        for (let k = 0; k < this.tabledata.length; k++) {
          if (this.tabledata[k].status == "C") {
            this.tabledata[k].statusfull = "Completed"
          }
          else if (this.tabledata[k].status == "I") {
            this.tabledata[k].statusfull = "Initialize"
          }
          else if (this.tabledata[k].status == "P") {
            this.tabledata[k].statusfull = "Pending"
          }
          else {

          }
          if (this.tabledata[k].objecttype == "ALL") {
            this.tabledata[k].objecttype = ""
          }
        }
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList?.length; i++) {
              if (
                this.tabledata[this.z].connection_id ==
                this.ConsList[i].Connection_ID
              ) {
                this.tabledata[this.z].conname = this.ConsList[i].conname;
              }
            }
          }
        }
        else {
          this.tabledata = []
        }
      }
    });
  }
  // delete request table data

  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.common.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData()
    })
  }
  //**package activity log*/
  GetIndividualLogs(action: any) {
    this.page = 1
    if (action == "null") {
      this.r_id = "null"
    }
    if (action == "previous") {
      this.r_id = this.logdata[0].exelog_id;
    }
    if (action == "next") {
      this.r_id = this.logdata[this.logdata.length - 1].exelog_id;
      this.disabledprevious = false;
    }
    if (action == '') {
      action = 'null'
    }
    const logobj = {
      projectId: this.projectId.toString(),
      operationType: "Validation",
      operationName: "null",
      action: 'null',
      row_id: this.r_id,
      runno: action
    }
    this.common.GetIndividualLogs(logobj).subscribe((data: any) => {
      this.logdata = data['Table1'];
    });
  }
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value
  }
  //select object type
  selectObjType(value: any) {
    this.selectedObjType = value
    this.fetchLogs()
  }
  //fetch logs details
  fetchLogs() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Deployment_Logs/" + this.selectedObjType + "/";
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.depLogs = data;
      if (this.depLogs.length == 0) {
        let ob = {
          fileName: "No Files Created"
        }
        this.depLogs.push(ob)
      }
      //console.log(this.depLogs)
    })
  }

  //select iteration
  selectIteration(value: any) {
    this.iterationselected = value
    this.fetchAssessmentFiles()
  }
  //fetch assessment files details
  fetchAssessmentFiles() {
    this.assessmentFiles = []
    const path = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Validation/"
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.assessmentFiles = data
    })
  }
  //hrs
  hours: any = [{
    option: "ALL", value: "0"
  },
  {
    option: "1 hrs", value: "1"
  }, {
    option: "5 hrs", value: "5"
  }, {
    option: "10 hrs", value: "10"
  }, {
    option: "1 Day", value: "24"
  },
  ]
  //ge hrs details
  gethrs(value: any) {
    this.hrs = value
    //console.log(this.hrs)
    this.filterReports()
  }
  //filter reports
  filterReports() {
    if (this.hrs == "0") {
      this.assessmentFiles = []
      this.fetchAssessmentFiles()
    }
    else {
      const obj = {
        path: "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Validation/",
        hrs: this.hrs,
        validationFlag: false
      }
      this.common.ReportsFilterByTime(obj).subscribe((data: any) => {
        this.assessmentFiles = data
      })
    }
  }
  //download files
  downloadFile(fileInfo: any) {
    this.common.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  //open model details

  openModal(value: any) {
    // for (let i = 0; i < this.schemaName.length; i++) {
    //   this.selectedschemas = this.selectedschemas + this.schemaName[i]
    //   if (i < this.schemaName.length - 1) {
    //     this.selectedschemas = this.selectedschemas + ","
    //   }
    // }
    this.selectedschemas = this.schemaName.toString()
    $('#demo').offcanvas('show');
  }
  //open popup
  openPopup() {
    this.schemaName = []
    this.selectedschemas = ''
    this.execProjectForm.reset();
  }
  prjdata: any;
  spin: boolean = false;
  projectConRunTblInsert(data: any, value: any) {
    if (value == true) {
      data.status = 'P';
    } else {
      data.status = 'I';
    }
    const tableinsesrt = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(data.operation),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: '',
      objectname: '',
      objecttype: '',
    };
    this.runinfospin = true;
    this.common.projectConRunTblInsert(tableinsesrt).subscribe(
      (data: any) => {
        this.prjdata = data['jsonResponseData']['Table1'];
        if (data.message == 'Success') {
          this.runinfospin = false;
          $('#updateOrgModal').modal('hide');
          this.toast.success('Successfully Inserted');
          this.getreqTableData();
        }
      },
      () => {
        $('#updateOrgModal').modal('hide');
        this.spin = false;
        this.toast.error('Something happened');
      }
    );
  }
  //project connection run table inserts
  projectConRunTblInserts(data: any, value: any) {
    this.runinfo = true;
    this.opId = data.operation
    if (value == true) {
      data.status = "P"
    }
    else {
      data.status = "I"
    }
    if (data.pattern != "") {
      var objName = data.pattern
    }
    else {
      objName = ""
    }
    if (data.objectType != "") {
      var objType = data.objectType
    }
    else {
      objType = ""
    }
    // const tableinsesrt = {
    //   projectId: this.projectId.toString(),
    //   connection_id: parseInt(this.conId),
    //   operationId: parseInt(this.operation[0].operation_id),
    //   schema: this.schemaName.toString(),
    //   status: data.status,
    //   remarks: "",
    //   objectname: objName,
    //   objecttype: objType
    // }
    this.selectedItems = [];

    if (this.tabledata.length != 0) {
      const res = this.tabledata.filter((item: any) => {
        return item.connection_id == this.conId && item.schemaname == this.schemaName.toString() && item.status == "I" && item.operation_name == "Code_Objects"
      })
      if (res.length >= 1) {
        this.runinfo = false;
        $('#demo').offcanvas('hide');
        this.toast.error("Request already Added")
      }
      if (res.length == 0) {

        this.RedisCommand()
        this.getreqTableData();
        //this.runinfo = false;
        // $('#updateOrgModal').modal('hide');
      }
    }
    if (this.tabledata.length == 0) {
      this.RedisCommand()
      this.getreqTableData();
      // this.runinfo = false;
      //$('#updateOrgModal').modal('hide');

    }

  }
  //redis command details
  runinfospins: boolean = false;
  RedisCommand() {
    this.runinfospins = true;
    const obj: any = {
      projectId: this.projectId.toString(),
      option: 2,
      schema: this.schemaName.toString(),
      connection: this.conName,
      Object: this.OpName,
      srcConId: this.conId,
      tgtConId: this.tgtConid,
      jobName: "qmig-convs",
      iteration: this.currentRunNumber,
      targetSchema: this.selectedtgtScema
    }
    //console.log(obj);
    this.common.setRedisCache(obj).subscribe((data: any) => {
      this.redisResp = data;
      this.runinfospins = false;
      this.schemaName = []
      this.selectedschemas = ''
      this.execProjectForm.reset();
      $('#demo').offcanvas('hide');
      this.toast.success("Triggered Successfully")
    })
  }
  
  //select operation
  selectOperation(id: any) {
    // const op = this.operation.filter((item: any) => {
    //   return item.operation_id == id;
    // });
    this.OpName = id
    this.execProjectForm.controls.schema.setValidators([Validators.required])
    this.execProjectForm.controls.schema.updateValueAndValidity()
  }


  //select object type
  SelectObjectTypes(value: any) {
    this.objectType = value;
    //console.log(this.objectType)
  }
  //get operation list
  getOperationList() {
    const obj = {
      projectId: this.projectId,
      OperationType: 'Assessment',
    };
    this.common.getoperation(obj).subscribe((data: any) => {
      this, (this.operation = data['Table1']);
    });
  }

  ConversionResponseData: any = []
  ConversionCommand() {
    let obj: any = {
      sourceConnectionId: this.conId,
      targetConnectionId: this.tgtConid,
      projectId: this.projectId.toString(),
      task: "Validation",
      iteration: this.currentRunNumber,
      objectCategory: this.OpName,
      schema: this.schemaName.toString(),
      targetSchema: this.selectedtgtScema,
      jobName: "qmig-convs",
    }
    this.common.ConversionCommand(obj).subscribe((data: any) => {
      this.ConversionResponseData = data;
      $('#demo').offcanvas('hide');
      this.toast.success("Validation Command Executed")
    },
      error => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong')
      })
  }
}


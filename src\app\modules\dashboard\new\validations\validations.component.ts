import { Component, ViewChild } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { DashboardService } from '../../../../services/dashboard.service';
import { AssessmentService } from '../../../../services/assessment.service';
import { Connection } from '../../../../models/interfaces/dashBoardTypes';
import { ChartConfiguration, ChartOptions } from 'chart.js';

@Component({
  selector: 'app-validations',
  standalone: true,
  imports: [TabsComponent, NgChartsModule,],
  templateUrl: './validations.component.html',
  styles: ``
})
export class ValidationsComponent {
  @ViewChild('chart1') chart1: BaseChartDirective | undefined;
  @ViewChild('chart2') chart2: BaseChartDirective | undefined;
  @ViewChild('chart3') chart3: BaseChartDirective | undefined;

  connectionsList: Array<String> = [];
  schemaList: any = [];
  iterationList: Array<String> = [];
  operationsList: any[] = [];
  operationsCopy: any[] = this.operationsList
  operationsTable: any[] = this.operationsList

  connectionID: string = '';
  connectionValue: string = '';
  schemaId: string = '';
  schemaName: string = '';
  iterationId: string = ''


  /*---- Search bar ---*/
  searchText: string = '';


  /*--- Long Running Queries Chart Data ---*/

  public longRunningChartLabels = [];
  public longRunningChartType = 'bar';
  public longRunningChartLegend = true;
  public longRunningChartData = [
    {
      data: [],
      label: ' Source row Count',
      borderColor: '#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [],
      label: ' Target row count',
      borderColor: '#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [],
      label: ' Difference count',
      borderColor: '#cf30fc',
      backgroundColor: '#f75989',
      hoverBackgroundColor: '#f5346e',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public longRunningChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          autoSkip: false,   // Show all labels
          maxRotation: 90,   // Rotate labels if needed
          minRotation: 45
        }
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };


  /*--- Long Running Queries Chart Data ---*/

  public sObjectChartLabels = [];
  public sObjectChartType = 'bar';
  public sObjectChartLegend = true;
  public sObjectChartData = [
    {
      data: [],
      label: ' Object Count',
      borderColor: '#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [],
      label: ' Match count',
      borderColor: '#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public sObjectChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        stacked: true,
        ticks: {
          autoSkip: false,   // Show all labels
          maxRotation: 90,   // Rotate labels if needed
          minRotation: 45
        }
      },
      y: {
        stacked: true
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };

  /*---- line Charts ----*/


  // Custom plugin for displaying percentage in the center
  centerTextPlugin = {
    id: 'centerTextPlugin',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'myDoughnutChart') {
        const { ctx, chartArea: { width, height } } = chart;

        ctx.save();
        // Get conversionPercentage from the component
        const percentage = ((this.convertedCount / this.totalCount) * 100).toFixed(2);

        // Define style for the text
        ctx.font = 'bold 25px Geist';
        ctx.fillStyle = '#333';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Set position to center of the chart
        const centerX = width / 2;
        const centerY = height / 2 + 20;

        // Draw the text in the center
        ctx.fillText(percentage, centerX, centerY);
        ctx.restore();
      }
    }
  };

  public lineChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'Source row Count',
      'Target row Count',
    ],
    datasets: [
      {
        data: [],
        label: 'Daily count of un used indexes',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
          '#8b36ff',
          '#f0f0f0'
        ],
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,

      }
    ]
  };
  public lineChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }

  };
  public lineChartLegend = false;
  public lineChartPlugins = [this.centerTextPlugin];

  /*--- source vs extraction % ---*/
  public sourceVSChartLabels = [];
  public sourceVSChartType = 'bar';
  public sourceVSChartLegend = true;
  public sourceVSChartData = [
    {
      data: [],
      label: ' Converted',
      borderColor: '#a25eff',
      backgroundColor: '#a25eff',
      hoverBackgroundColor: '#8b36ff',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
    {
      data: [],
      label: ' Pending',
      borderColor: '#4c00b5',
      backgroundColor: '#4c00b5',
      hoverBackgroundColor: '#4c00b5',
      barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public sourceVSChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
          label: (tooltipItem) => {
            const dataPoint = tooltipItem.formattedValue;
            // Return multiple labels for the tooltip
            return [
              " Count" + ': ' + dataPoint,  // Main dataset label
            ];
          }
        }
      }
    }
  };
  selectedConnectionId: string | null = null;
  totalCount: number = 0;
  connectionCounts: any[] = [];
  schemaCounts: any[] = [];
  convertedCount: number = 0;
  conversionPercentage: string = '';
  ConsList: any[] = [];
  filteredConsList: any[] = [];
  Schemadisable: boolean = false;
  // Pagination for sObjectChart
  public sObjectChartCurrentPage: number = 1;
  public sObjectChartItemsPerPage: number = 10;
  public sObjectChartTotalPages: number = 1;
  public sObjectChartPaginatedLabels: string[] = [];
  public sObjectChartPaginatedData: any[] = [];

  // Pagination for longRunningChart
  public longRunningChartCurrentPage: number = 1;
  public longRunningChartItemsPerPage: number = 10;
  public longRunningChartTotalPages: number = 1;
  public longRunningChartPaginatedLabels: string[] = [];
  public longRunningChartPaginatedData: any[] = [];


  differenceCount: number = 0;
  differencePercentage: number = 0;

  constructor(private dbService: DashboardService, private assessmentService: AssessmentService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
  }

  ngOnInit(): void {
    this.getOperationsList(null, null, null, null, null);
    this.GetConsList();
    this.getConValidationDashboardData();
    this.gettgtValidationDashboardData(null);
    this.getsrcSchemaData(null,null);
    this.gettgtSchemaData(null,null,null);
    this.gettablenameData(null,null,null,null)

  }

  percentage: any;
  /*--- Fetch Iteration ---*/
  getOperationsList(connectionName: string | null, tgtid: string | null, schemaname: string | null, tgtsch: string | null, objname: string | null) {
    const obj = {
      srcId: connectionName || "null",
      tgtId: tgtid || "null",
      srcSchema: schemaname || "null",
      tgtSchema: tgtsch || 'null',
      tableName: objname || "null"
    }
    this.dbService.getValidationCounts(obj).subscribe((data: any) => {
      this.operationsList = data;
      const total = data.totalCounts?.[0];
      if (total) {

        this.totalCount = parseInt(total.source_Row_Counts, 10);
        this.convertedCount = parseInt(total.target_Row_Counts, 10);
        this.differenceCount = parseInt(total.difference_Row_Counts, 10);
        // this.differencePercentage = parseFloat(total.difference_Percentage);

        const percentage = ((this.convertedCount / this.totalCount) * 100).toFixed(2);
        this.percentage = percentage;
        this.lineChartData = {
          labels: ['Source row Count', 'Target row Count'],
          datasets: [
            {
              data: [this.totalCount, this.convertedCount],
              label: 'Daily Count of Unused Indexes',
              circumference: 180,
              rotation: 270,
              backgroundColor: ['#8b36ff', '#f0f0f0'],
              hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
              borderWidth: 0,
            }
          ]
        };
      }
      const connectionData = data.connection_TotalCounts || [];
      this.sourceVSChartLabels = connectionData.map(
        (item: any) => `${item.source_Connection_Id} (${item.target_Connection_Id})`
      );
      this.sourceVSChartData[0].data = connectionData.map((item: any) => parseInt(item.source_Row_Counts, 10));
      this.sourceVSChartData[1].data = connectionData.map((item: any) => parseInt(item.target_Row_Counts, 10));

      const SchemaData = data.schema_Connection_TotalCounts || [];
      this.longRunningChartLabels = SchemaData.map(
        (item: any) => `${item.source_Schema} (${item.target_Schema})`
      );
      this.longRunningChartData[0].data = SchemaData.map((item: any) => parseInt(item.source_Row_Counts, 10));
      this.longRunningChartData[1].data = SchemaData.map((item: any) => parseInt(item.target_Row_Counts, 10));
      this.longRunningChartData[2].data = SchemaData.map((item: any) => parseInt(item.difference_Row_Counts, 10));

      const tablenamesData = data.schema_Connection_IndividualCounts || [];
      this.sObjectChartLabels = tablenamesData.map(
        (item: any) => `${item.table_Name}`
      );
      this.sObjectChartData[0].data = tablenamesData.map((item: any) => parseInt(item.source_Row_Counts, 10));
      this.sObjectChartData[1].data = tablenamesData.map((item: any) => parseInt(item.target_Row_Counts, 10));
    },
      (error) => {
        console.error("Error:", error);
      }
    );
  }

  tgtList: any;
  projectId: any
  GetConsList() {
    this.dbService.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.filteredConsList = [...this.ConsList];
      this.getConValidationDashboardData()
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      this.filterTargetId = [...this.tgtList];
     // this.gettgtValidationDashboardData(conid)
    });

  }
  getSchema(value: any) {
    this.dbService.getSchemalist(value).subscribe((data: any) => {
      this.schemaList = data
    })
  }
  conList: any[] = [];
  tgtSchemaList: any;
  selectedSchema: any
  getTgtSchema(tgt: string) {
    this.assessmentService.tgtSchemaSelect(tgt).subscribe((data: any) => {
      this.tgtSchemaList = data;
    });
  }
  //getTableCountBySchema

  TableCountBySchema: any
  getTableCountBySchema(value: string) {
    this.dbService.getTableCountBySchema(value).subscribe((data: any) => {
      this.TableCountBySchema = data;

    });
  }
  onTargetConnectionChange(conId: any) {
    const tgtname = conId.target.value;
    this.selectedTargetConId = tgtname;
    if (this.selectedConnectionId && tgtname) {
      this.getOperationsList(this.selectedConnectionId, tgtname, null, null, null);
      this.getsrcSchemaData(this.selectedConnectionId,tgtname)
    }else{
      this.getOperationsList(this.selectedConnectionId, tgtname, null, null, null);
    }
    
  }
  onConnectionChange(conId: any) {
    const selectedConId = conId.target.value;
    this.selectedConnectionId = selectedConId;
    this.filteredConsList = this.ConsList.filter(item => item.Connection_ID === selectedConId);
    this.getOperationsList(selectedConId, null, null, null, null);
    this.gettgtValidationDashboardData(this.selectedConnectionId);
   
  }
  selectedConId: string = '';
  ObjectNamesList: any[] = [];
  getTableDetails(conid: any, sch: any) {
    let obj = {
      Conid: conid,
      objectType: "TABLE",
      schemaname: sch
    }
    this.assessmentService.getObjNames(obj).subscribe((data: any) => {
      this.ObjectNamesList = data
    })
  }
  selectedTargetConId: any;
  onSchemaChange(schemaName: any) {
    const schema = schemaName.target.value;
    this.selectedSchema = schema;
    if (this.selectedConnectionId && this.selectedTargetConId && schema) {
      this.Schemadisable=true;
      this.getOperationsList(this.selectedConnectionId, this.selectedTargetConId, schema, null, null);
      this.gettgtSchemaData(this.selectedConnectionId, this.selectedTargetConId, schema);
    } else {
      this.getOperationsList(
        this.selectedConnectionId || null,
        this.selectedTargetConId || null,
        schema,
         null,
         null
      );
    }
  }
  //getValidationDashboardData
  filterConsList: any;
  getConValidationDashboardData() {
    let obj = {
      srcId: "null",
      tgtId: "null",
      srcSchema: "null",
      tgtSchema: "null",
      tableName: "null",
      option: "0"
    }
    this.dbService.getValidationDashboardData(obj).subscribe((data: any) => {
      this.filterConsList = this.conList.map((dropdownItem: any) => {
        const match = this.ConsList.find((consItem: any) => consItem.Connection_ID === dropdownItem.source_connection_id);
        return match ? { ...dropdownItem, conid: match.conname } : null;
      }).filter((item: any) => item !== null);
    })
  }
  filterTargetId: any;
  gettgtValidationDashboardData(conid:any) {
    let obj = {
      srcId: conid|| "null",
      tgtId: "null",
      srcSchema: "null",
      tgtSchema: "null",
      tableName: "null",
      option: "1"
    }
    this.dbService.getValidationDashboardData(obj).subscribe((data: any) => {
      this.filterTargetId = this.tgtList.map((dropdownItem: any) => {
        const match = this.tgtList.find((consItem: any) => consItem.Connection_ID === dropdownItem.source_connection_id);
        return match ? { ...dropdownItem, conid: match.conname } : null;
      }).filter((item: any) => item !== null);
    })
  }
  scrSchemaData: any
  getsrcSchemaData(conid: any,tgtId:any) {
    let obj = {
      srcId: conid || "null",
      tgtId: tgtId || "null",
      srcSchema: "null",
      tgtSchema: "null",
      tableName: "null",
      option: "2"
    }
    this.dbService.getValidationDashboardData(obj).subscribe((data: any) => {
      this.scrSchemaData = data['Table1'];
    })
  }
  tgtschemaData: any
  gettgtSchemaData(conid: any, tgtid: any, srcschema: any) {
    let obj = {
      srcId: conid || "null",
      tgtId: tgtid || "null",
      srcSchema: srcschema || "null",
      tgtSchema: "null",
      tableName: "null",
      option: "3"
    }
    this.dbService.getValidationDashboardData(obj).subscribe((data: any) => {
      this.tgtschemaData = data['Table1'];
    })
  }
  selectedtgtType:any
 ontgtschemaSelect(event:any){
  const objType = event.target.value;
  this.selectedtgtType = objType;

  if (this.selectedConnectionId && this.selectedTargetConId && this.selectedSchema && objType) {
    this.getOperationsList(this.selectedConnectionId, this.selectedTargetConId, this.selectedSchema, objType, null);
    this.gettablenameData(this.selectedConnectionId, this.selectedTargetConId, this.selectedSchema, objType);
  }else{
    this.getOperationsList(this.selectedConnectionId, this.selectedTargetConId, this.selectedSchema, objType, null);
  }
 }
  tablename: any
  gettablenameData(conid: any, tgtid: any, srcschema: any, tgtschema: any) {
    let obj = {
      srcId: conid || "null",
      tgtId: tgtid || "null",
      srcSchema: srcschema || "null",
      tgtSchema: tgtschema || "null",
      tableName: "null",
      option: "4"
    }
    this.dbService.getValidationDashboardData(obj).subscribe((data: any) => {
      this.tablename = data['Table1'];
    })
  }
  selectedtablename:any
  ontableSelect(event:any){
    const tabname = event.target.value;
    this.selectedtablename = tabname;
    if (
      this.selectedConnectionId && this.selectedTargetConId && this.selectedSchema && this.selectedtgtType && this.selectedtablename ) 
      {
      this.getOperationsList(this.selectedConnectionId,this.selectedTargetConId,
        this.selectedSchema,
        this.selectedtgtType,
        this.selectedtablename
      );
    }else{
      this.getOperationsList(this.selectedConnectionId,this.selectedTargetConId,this.selectedSchema,this.selectedtgtType, this.selectedtablename
      );
    }
  }
  updatePagination() {
    const sObjectStartIndex = (this.sObjectChartCurrentPage - 1) * this.sObjectChartItemsPerPage;
    const sObjectEndIndex = sObjectStartIndex + this.sObjectChartItemsPerPage;

    this.sObjectChartPaginatedLabels = this.sObjectChartLabels.slice(sObjectStartIndex, sObjectEndIndex);
    this.sObjectChartPaginatedData = this.sObjectChartData.map((dataset: any) => ({
      ...dataset,
      data: dataset.data.slice(sObjectStartIndex, sObjectEndIndex),
    }));

    const longRunningStartIndex = (this.longRunningChartCurrentPage - 1) * this.longRunningChartItemsPerPage;
    const longRunningEndIndex = longRunningStartIndex + this.longRunningChartItemsPerPage;

    this.longRunningChartPaginatedLabels = this.longRunningChartLabels.slice(longRunningStartIndex, longRunningEndIndex);
    this.longRunningChartPaginatedData = this.longRunningChartData.map((dataset: any) => ({
      ...dataset,
      data: dataset.data.slice(longRunningStartIndex, longRunningEndIndex),
    }));
  }
  prevPage() {
    if (this.sObjectChartCurrentPage > 1) {
      this.sObjectChartCurrentPage--;
      this.updatePagination();
    }
  }

  nextPage() {
    if (this.sObjectChartCurrentPage < this.sObjectChartTotalPages) {
      this.sObjectChartCurrentPage++;
      this.updatePagination();
    }
  }
  schprevPage() {
    if (this.longRunningChartCurrentPage > 1) {
      this.longRunningChartCurrentPage--;
      this.updatePagination();
    }
  }

  schnextPage() {
    if (this.longRunningChartCurrentPage < this.longRunningChartTotalPages) {
      this.longRunningChartCurrentPage++;
      this.updatePagination();
    }
  }
}

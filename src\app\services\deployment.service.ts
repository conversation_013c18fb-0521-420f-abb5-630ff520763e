import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { APIConstant } from '../constant/APIConstant'
import { environment } from '../environments/environment';
import { deploymentAPIConstant } from '../constant/deploymentAPIConstant';
import path from 'node:path';
import { deleteTabledata, deleteTableData, GetRunno, uploadDocs } from '../models/interfaces/types';

@Injectable({
  providedIn: 'root'
})
export class DeploymentService {

  apiURL = environment.serviceUrl+"Deployment/"
  constructor(
    private apiService: ApiService) { }

  GetRunNoForstmtsUrl = this.apiURL + deploymentAPIConstant.GetRunNoForstmts
  GetRunNoForstmts = (data: any): Observable<any> => {
    return this.apiService.get(this.GetRunNoForstmtsUrl + data.projectId + '&migsrcType=' + data.migsrcType);
  };

  getConListURL = this.apiURL + deploymentAPIConstant.GetConnectionList
  getConList = (body: any): Observable<any> => {
    return this.apiService.get(this.getConListURL + body);
  };

  getschemaListUrl = this.apiURL + deploymentAPIConstant.GetschemaList
  getschemaList = (data: any): Observable<any> => {
    return this.apiService.get(this.getschemaListUrl + data.projectId + '&conid=' + data.sourceConnection);
  };

  GetDeltacurrentFilesurl = this.apiURL + deploymentAPIConstant.getFiles
  getFiles(data: any) {
    return this.apiService.get(this.GetDeltacurrentFilesurl + data.path);
  }

  docusURL = this.apiURL + deploymentAPIConstant.uploadDocuments
  uploadDocuments = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.docusURL, body);
  };

  GetSchemaSelecturl = this.apiURL + deploymentAPIConstant.GetSchemaSelect
  GetSchemaSelect(data: any) {
    return this.apiService.get(this.GetSchemaSelecturl + data.projectId + '&runNo=' + data.runno);
  }

  getSourceFilePathsurl = this.apiURL + deploymentAPIConstant.GetSourceFilePath
  GetSourceFilePath(data: any) {
    return this.apiService.post(this.getSourceFilePathsurl, data);
  }

  RedisDelta = this.apiURL + deploymentAPIConstant.DeltaCommand
  DeltaCommand(data: any) {
    return this.apiService.post(this.RedisDelta, data);
  }
  GetPrjRuninfoSelectAllUrl = this.apiURL + deploymentAPIConstant.GetPrjRuninfoSelectAll
  GetPrjRuninfoSelectAll(data: any) {
    return this.apiService.get(this.GetPrjRuninfoSelectAllUrl + data.projectId);
  }

  // date time format
  formatDate(dateString: string): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    const date = new Date(dateString);
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  }

  PrjExelogSelectTaskUrl = this.apiURL + deploymentAPIConstant.PrjExelogSelectTask
  PrjExelogSelectTask(data: any) {
    return this.apiService.post(this.PrjExelogSelectTaskUrl, data);
  }

  GetRequestTableDataUrl = this.apiURL + deploymentAPIConstant.GetRequestTableData
  GetRequestTableData(data: any) {
    return this.apiService.get(this.GetRequestTableDataUrl + data.projectId + '&operationType=' + data.operationType);
  }

  deleteTableDataURL = this.apiURL + deploymentAPIConstant.deleteTableData;
  deleteTableData = (body: deleteTableData): Observable<deleteTabledata> => {
    return this.apiService.get(this.deleteTableDataURL + body.projectId + '&requestId=' + body.requestId);
  };

  //downloadLargeFiles
  downloadLargeFilesURL = this.apiURL + deploymentAPIConstant.downloadLargeFiles;
  downloadLargeFiles = (body: string): Observable<any> => {
    return this.apiService.get(this.downloadLargeFilesURL + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }

  //Get Objects Names
  getObjectNameURL = this.apiURL + deploymentAPIConstant.GetObjectNames;
  getObjectNames =  (body: any): Observable<any> => {
    return this.apiService.get(this.getObjectNameURL  + body.RunNo + '&schemaName=' + body.schemaName)
  }

  //Get Objects Names
  getObjectsURL = this.apiURL + deploymentAPIConstant.GetObjectCode;
  getObjects =  (body: any): Observable<any> => {
    return this.apiService.get(this.getObjectsURL  + body.RunNo + '&objName=' + body.objName)
  }

  
  getTargetFilePathsurl = this.apiURL + deploymentAPIConstant.TargetFilePathsForFileshare
  GetTargetFilePath(data: any) {
    return this.apiService.post(this.getTargetFilePathsurl, data);
  }

  UploadCloudFilesURL = this.apiURL + deploymentAPIConstant.UploadCloudFiles
  UploadCloudFiles = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.UploadCloudFilesURL, body);
  };

  //GetRunno
GetRunnoURL = this.apiURL + deploymentAPIConstant.GetRunno;
GetRunno = (body:any): Observable<GetRunno> => {
  return this.apiService.get(this.GetRunnoURL + + body.projectId + '&migsrcType=' + body.migsrcType);
};

  //GetSchemasByRunId
  GetSchemasByRunIdURL = this.apiURL + deploymentAPIConstant.GetSchemasByRunId;
  GetSchemasByRunId = (body:string): Observable<any> => {
   return this.apiService.get(this.GetSchemasByRunIdURL + body)
  }


  deltaGenerateURL = this.apiURL + deploymentAPIConstant.DeltaCommand
  deltaGenerate = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.deltaGenerateURL, body);
  };

  deltaUpdateURL = this.apiURL + deploymentAPIConstant.DeltaUpdate
  deltaUpdate = (body: any): Observable<any> => {
    return this.apiService.post(this.deltaUpdateURL, body);
  };

  changeObjStatusURL = this.apiURL + deploymentAPIConstant.changeObjectStatus
  changeObjStatus = (body: any): Observable<any> => {
    return this.apiService.post(this.changeObjStatusURL, body);
  };

  getDeploymentStatusURL = this.apiURL + deploymentAPIConstant.getdeltaDeploymentStatus
  getDeploymentStatus = (body: any): Observable<any> => {
    return this.apiService.post(this.getDeploymentStatusURL, body);
  };
}
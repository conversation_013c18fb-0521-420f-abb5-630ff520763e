import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
// import { deleteFile, documentsList, redisCommand} from '../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { Title } from '@angular/platform-browser';

declare let $: any;

@Component({
  selector: 'app-data-compare',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, RouterOutlet, NgSelectModule],
  templateUrl: './data-compare.component.html',
  styles: ``
})
export class DataCompareComponent {
  userData: any = [];
  infraData: any = [];
  validationreports: any
  Estimationreport: any
  datachange: any;
  datachangeLogs: any
  secUserData: any = [];
  prjSrcTgtData: any = [];
  reqD: any = [];
  reqF: any = [];
  p: number = 1;
  p1: number = 1;
  p2: number = 1;
  prjSrcTgt: any = {};
  prjSrcTgtD: any = {};
  ExeLog: any = {};
  prjSchmea: any = {};
  schemaList: any = [];
  connectionType: any = [
    { values: 'F', option: 'File' },
    { values: 'D', option: 'Database' },
  ];
  operation: any
  objectType: any
  projectId: any;
  getRole: any;
  term: any;
  tenantId: any;
  subscriptionId: any;
  resourceGroup: any;
  location: any;
  vmName: any;
  dbName: any;
  filtered: any;
  executeCommandObj: any = {};
  dbNameFiltered: any;
  spin: boolean = false;
  project_name: any;
  showSchema: boolean = false;
  runinfospin: boolean = false;
  runinfo: boolean = false;
  dropdownList = [];
  selectedItems: any = [];
  // dropdownSettings: IDropdownSettings = {};
  schemaName: any = [];
  r_id: any;
  logdata: any;
  disabledprevious: boolean = true;
  pi: number = 10;
  page1: number = 1;
  AssessmentLog: any = {};
  runNoData: any = [];
  prjLogData: any = [];
  page2: number = 1;
  piA: number = 10;
  piB: number = 10;
  datachanges: any;
  datachange1: any;
  datachange2: any;
  ref_spin: boolean = false
  tabledata: any
  delete_file: boolean = false
  searchText: any;
  pageNumber: number = 1;
  pageNumber1: number = 1;
  migtypeid: string = ""
  pageName: string = '';
  initialselected: boolean = false

  datacompareForm = this.formBuilder.group({
    connectionName: ['', Validators.required],
    method: ['', Validators.required],
    table: [''],
    schema: ['', Validators.required],
    tgtschema: [''],
    confiName: ['', Validators.required],
    targetConnection: ['', Validators.required],
  });
  ExecutionForm = this.formBuilder.group({
    // operation: ['', [Validators.required]],
    config: ['', [Validators.required]],

  })
  schemalable: string = ""
  initTypes: any = [{ option: "Normal", value: "default" },
  { option: "High Volume", value: "high_volume" }]
  constructor(private titleService: Title, private route: ActivatedRoute, private toast: HotToastService, private datamigrationservices: DataMigrationService, public formBuilder: FormBuilder) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }
  }
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList();
    this.fetchConfigFiles()
    this.pageNumber = 1;
    this.pageNumber1 = 1;
    // this.dropdownSettings = {
    //   singleSelection: false,
    //   idField: 'object_name',
    //   textField: 'object_name',
    //   selectAllText: 'Select All',
    //   unSelectAllText: 'UnSelect All',
    //   itemsShowLimit: 3,
    //   allowSearchFilter: true,
    // };
  }
  get getControl() {
    return this.datacompareForm.controls;
  }
  get forms(): any {
    return this.ExecutionForm.controls;
  }

  onItemSelect(item: any) {
    this.schemaName.push(item.object_name);
    //console.log(this.schemaName)
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.object_name);
    this.schemaName.splice(inde, 1);
    //console.log(this.schemaName)
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.object_name);
    });
    //console.log(this.schemaName)
  }
  onDeSelectAll(items: any) {
    this.schemaName = [];
    //console.log(this.schemaName)
  }

  ConsList: any;
  tgtList: any
  GetConsList() {
    const projectId = this.projectId.toString()
    this.datamigrationservices.getConList(projectId).subscribe((data: any) => {
      const condata = data['Table1'];
      this.ConsList = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = condata.filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.ref_spin = false
    })
  }
  selectedConname: any
  conName: any
  conId: any;
  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;
  }
  prjdata: any

  prjdatas: any
  opId: any

  //Method Compare
  sample_select: boolean = false
  methodSelect(value: any) {
    if (value == "Sample_Data") {
      this.sample_select = true
    }
    else {
      this.sample_select = false
    }
    this.selectlevel(this.sample_select)
  }

  //Chunk Percentage
  validatepercentage(value: any) {
    //console.log(value)
    if (value >= 1 && value <= 100) {

    }
    else {
      this.toast.error("Please Enter Value Between 1 to 100 only");
    }
  }
  tgtschemaList: any = []
  getSchemasList(ConnectionId: any, value: string) {
    if (this.migtypeid == "31") {
      if (value == "T") {
        this.tgtschemaList = []
        this.tgtList.filter((item: any) => {
          if (item.Connection_ID == ConnectionId) {
            this.tgtschemaList.push({ schemaname: item.dbname })
          }
        })
      } else if (value == "S") {
        this.schemaList = []
        this.ConsList.filter((item: any) => {
          if (item.Connection_ID == ConnectionId) {
            this.schemaList.push({ schemaname: item.dbname })
          }
        })
      }
    }
    else {
      const obj = {
        projectid: this.projectId,
        connectioId: ConnectionId
      }
      this.datamigrationservices.SchemaListSelect(obj).subscribe((data: any) => {
        if (value == "S") {
          this.schemaList = data['Table1'];
        }
        else {
          this.tgtschemaList = data['Table1'];
          this.tgtschemaList = this.tgtschemaList.filter((item: any) => {
            return item.schemaname != "ALL"
          });

        }
      })
    }
  }


  z: any;

  selectedOperation: any
  selectOpertion(id: any) {
    const op = this.operation.filter((item: any) => {
      return item.operation_id == id
    })
    this.selectedOperation = op[0].operation_name
  }

  runnoForReports: any;

  filterList(listData: any) {
    let uniqueNames: any = []
    for (let k = 0; k < listData.length; k++) {
      if (uniqueNames.length == 0) {
        uniqueNames.push(listData[k])
      }
      else {
        var abc = uniqueNames.filter((item: any) => {
          return item.iteration === listData[k].iteration
        })
        if (abc.length == 0) {
          uniqueNames.push(listData[k])
        }
      }
    }
    return uniqueNames;
  }





  objType: any = [];
  obtypevalue: any
  SelectObjectTypes(value: any) {
    this.objType = value;
    this.datacompareForm.controls.schema.setValidators([Validators.required])
    this.datacompareForm.controls.schema.updateValueAndValidity()
  }
  tgtId: any
  currentTarget: string = ''
  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.currentTarget = el.conname : '' })
  }



  fileResponse: any
  spin_dwld: any;

  //downloadfile
  downloadFile(fileInfo: any) {
    this.datamigrationservices.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }

  loadingSpinners: { [key: string]: boolean } = {};
  toggleSpinner(itemId: string): void {
    this.loadingSpinners[itemId] = !this.loadingSpinners[itemId];
  }
  //deletefiles

  deleteFiles(value: any) {
    this.datamigrationservices.deleteFiles(value).subscribe((data: any) => {
      this.fetchConfigFiles()
      this.toast.success(data.message)
    })
  }

  dcfileRespnse: any
  folder: any
  fetchDataCompareFiles(value: any) {
    this.folder = "";
    var path = "Data_Compare_Reports/";
    this.datamigrationservices.getFilesfromExPath(path).subscribe((data: any) => {
      var temp = data
      //= data
      this.dcfileRespnse = temp.filter((item: any) => {
        return item.fileName.includes("_~_" + value + ".")
      })
    })
  }

  //deleteFiles1
  loadingSpinners1: { [key: string]: boolean } = {};
  toggleSpinner1(itemId: string): void {
    this.loadingSpinners1[itemId] = !this.loadingSpinners1[itemId];
  }
  delete_file1: boolean = false
  deleteFiles1(value: any) {
    this.datamigrationservices.deleteFiles(value).subscribe((data: any) => {
      this.fetchDataCompareFiles(this.folder)
      this.toast.success(data.message)
    })
  }

  //changeFn1
  selectedTableName: any
  changeFn1(value: any) {
    this.schemaName = value;
   // this.selectedItems = null; 
    this.datacompareForm.get('tgtschema')?.setValue(null);
    // this.tableHide = true;
  }


  // level_schema: boolean = false
  // level_table: boolean = false
  // extraspace: boolean = true
  selectedLevel: any
  selectlevel(value: any) {
    this.selectedLevel = value
    if (value == true) {
      // this.level_schema = true
      // this.level_table = false
      // this.extraspace = false
      // this.schemaName = []
      this.datacompareForm.controls['connectionName'].setValidators([Validators.required])
      this.datacompareForm.controls['targetConnection'].setValidators([Validators.required])
      this.datacompareForm.controls['method'].setValidators([Validators.required])
      this.datacompareForm.controls['confiName'].setValidators([Validators.required])
      this.datacompareForm.controls['table'].clearValidators()
      this.datacompareForm.controls['schema'].setValidators([Validators.required])
      this.datacompareForm.controls['tgtschema'].clearValidators()

      // this.dropdownSettings.unSelectAllText
    }
    else if (value == false) {
      // this.level_schema = true
      // this.level_table = true
      // this.extraspace = false

      this.datacompareForm.controls['connectionName'].setValidators([Validators.required])
      this.datacompareForm.controls['targetConnection'].setValidators([Validators.required])

      this.datacompareForm.controls['method'].setValidators([Validators.required])
      this.datacompareForm.controls['confiName'].setValidators([Validators.required])
      this.datacompareForm.controls['table'].clearValidators()
      this.datacompareForm.controls['schema'].setValidators([Validators.required])
      this.datacompareForm.controls['tgtschema'].clearValidators()
    }
    this.datacompareForm.controls['connectionName'].updateValueAndValidity()
    this.datacompareForm.controls['targetConnection'].updateValueAndValidity()
    this.datacompareForm.controls['method'].updateValueAndValidity()
    this.datacompareForm.controls['confiName'].updateValueAndValidity()
    this.datacompareForm.controls['table'].updateValueAndValidity()
    this.datacompareForm.controls['schema'].updateValueAndValidity()
    //this.datacompareForm.controls['chunkSize'].updateValueAndValidity()
  }
  tgtschema: string = ""
  objtype: any
  tableHide: boolean = false
  selectedSchemas = []
  selectObj(value: any, type: string) {
    value = this.selectedSchemas
    this.tableHide = false
    if (type == "S") {
      if (value.length <= 1) {
        this.objtype = value
        this.tableHide = false;
        // this.getTables(value)
        if (this.migtypeid == "46") {
          this.getDb2Tables(this.selectedSchemas[0]);
        }
        else {
          this.getOracleTables(this.selectedSchemas[0]);
        }
      } else {
        // this.selectedSchemas=[]
        this.tableHide = true
      }
    }
    else {
      this.tgtschema = value
    }


  }
  tablesresponse: any
  getTables(value: any) {
    let obj = {
      schemaname: value,
      conId: this.conId
    }
    this.datamigrationservices.GetTablesByschema(obj).subscribe((data: any) => {
      this.tablesresponse = data['Table1']
    })
  }

  //create configuration
  insertDataComapre(formData: any) {
    var tables: any = []
    formData.confiName = "dc_" + formData.confiName
    // if (this.selectedLevel == "Schema") {
    //   this.schemaName = []
    //   tables = []
    // }
    // if (this.selectedLevel == 'Table') {
    for (let k = 0; k < this.schemaName.length; k++) {
      tables[k] = this.schemaName[k]
    }
    // }
    const obj: any = {
      projectId: this.projectId.toString(),
      operation: "Datacompare",
      srcId: this.conId,
      tgtId: this.tgtId,
      schema: formData.schema,
      tgtSchema: formData.tgtschema,
      chunkPercentage: formData.percentcompare.toString(),
      filePercentage: formData.percentfile,
      chunkSize: formData.chunkSize.toString(),
      level: formData.level,
      largeTableConcurrency: formData.largeTable,
      tablename: tables.toString(),
      method: formData.method,
      fileName: formData.confiName,
    }

    //console.log(obj);
    this.datamigrationservices.insertTablesCommand1(obj).subscribe((data: any) => {
      this.toast.success("Executed")
    })

  }
  configFiles: any
  files_spin: boolean = false
  fetchConfigFiles() {
    this.files_spin = true
    const path = "Config_Files/Data_Compare";//"AIRFLOW_FILES/Data_Compare_Reports/"
    this.datamigrationservices.GetFilesFromExpath(path).subscribe((data: any) => {
      this.configFiles = data
      this.files_spin = false
    })
  }

  fileSrc: any
  configFilesExe: any
  fetchSourceConfigFiles(conn: any, op: any) {
    this.files_spin = true
    this.fileSrc = conn
    const path = conn + "/Config_Files/Data_Compare";
    this.datamigrationservices.GetFilesFromExpath(path).subscribe((data: any) => {
      if (op == "0") {
        this.configFiles = data
      }
      else {
        this.configFilesExe = data
      }
      this.files_spin = false
    })
  }
  fetchTargetConfigFiles(conn: any, op: any) {
    this.targetConn = conn
    const path = this.fileSrc + "/" +  this.targetConn + "/Config_Files/Data_Compare";
    this.datamigrationservices.GetFilesFromExpath(path).subscribe((data: any) => {
      if (op == "0") {
        this.configFiles = data
      }
      else {
        this.configFilesExe = data
      }
      this.files_spin = false
    })
  }
  page3: number = 1
  trigger_spin: boolean = false
  onkey() {
    this.pageNumber = 1;
    this.pageNumber1 = 1;
  }
  onKey() {
    var sercachbar = (<HTMLInputElement>document.getElementById("searchdag")).value
    this.p1 = 1;
    this.page2 = 1;
    this.p2 = 1;
    this.page3 = 1;
    this.p = 1;
    //console.log(sercachbar, "bar")
    if (sercachbar.length > 0) {
      this.isDisableAllCheck = true
    }
    else {
      this.isDisableAllCheck = false
    }
  }
  DataMigrationCommand(formData: any) {
    var tables: any = []
    let reqMem = formData.request_Memory_measure == '' ? 'Mi' : formData.request_Memory_measure
    let limitMem = formData.limit_Memory_measure == '' ? 'Mi' : formData.limit_Memory_measure
    for (let k = 0; k < this.schemaName.length; k++) {
      tables[k] = this.schemaName[k]
    }
    let obj = {
      task: "Data_Compare",
      projectId: this.projectId.toString(),
      srcId: this.conId,
      tgtId: this.tgtId,
      schema: formData.schema,
      tgtschema: formData.tgtschema,
      tableList: tables.toString(),
      chunkSize: formData.chunkSize.toString(),
      concurrency: formData.largeTable,
      chunkPercentage: formData.percentcompare.toString(),
      outputrows: formData.percentfile,
      cdcoption: formData.method,
      fileName: formData.confiName,
      podConfig: formData.podConfig,
    }
    if (obj['cdcoption'] == 'Sample_Data') {
      obj['chunkSize'] = ""
      obj['chunkPercentage'] = ""
      obj['outputrows'] = ""

    }
    this.datamigrationservices.DataMigrationCommand(obj).subscribe((data: any) => {
      this.datacompareForm.reset();
      this.toast.success(data.message);
    })
  }
  //Execution Section

  reff_spin: boolean = false
  isDisableAllCheck: boolean = false
  selectedPath: any
  dagsInfo: any
  allIPs: any = []
  srcCon: any
  scheduleSelect: boolean = false
  dagData: any

  filteredConfigfiles: any
  initType: any
  selecttype(value: any) {
    this.initType = value
  }

  getConfigDags(path: any) {
    this.showCheckStatus = false
    this.dagsInfo = []
    this.allIPs = []
    this.selectedPath = path
    //  path="C:\\Users\\<USER>\\Downloads\\catchup_config.xlsx"
    this.p = 1
    // if (this.selectedOperation == 'Partition_Initial_Data_Load' || this.selectedOperation == 'GG_Initial_Data_Load' || this.selectedOperation == "Initial_Data_Load" || this.selectedOperation == 'E2E_Data_Load') {
    // this.datamigrationservices.getggpdDags(path).subscribe((data: any) => {
    //   this.dagsInfo = data.dagList
    //   this.srcCon = data.sourceConnectionId
    //   this.dagsInfo.filter((item: any) => {
    //     item.isSelected = false
    //     return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
    //   })
    // })
    //  } else {
    this.datamigrationservices.getExcelDags(path).subscribe((data: any) => {
      this.dagsInfo = data.dagList
      this.srcCon = data.sourceConnectionId
      this.dagsInfo.filter((item: any) => {
        item.isSelected = false
        return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
      })
    })
    // }
  }
  refreshdags() {
    if (this.selectedPath == undefined) {
      this.toast.error("Please select Config File")
    } else {
      this.dagsInfo = []
      this.allIPs = []
      this.p = 1
      this.reff_spin = true
      // if (this.selectedOperation == 'Partition_Initial_Data_Load' || this.selectedOperation == 'GG_Initial_Data_Load' || this.selectedOperation == "Initial_Data_Load") {
      //   this.datamigrationservices.getggpdDags(this.selectedPath).subscribe((data: any) => {
      //     this.dagsInfo = data.dagList
      //     this.reff_spin = false
      //     this.srcCon = data.sourceConnectionId
      //     this.dagsInfo.filter((item: any) => {
      //       item.isSelected = false
      //       return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
      //     })
      //   })
      // } else {
      this.datamigrationservices.getExcelDags(this.selectedPath).subscribe((data: any) => {
        this.reff_spin = false
        this.dagsInfo = data.dagList
        this.srcCon = data.sourceConnectionId
        this.dagsInfo.filter((item: any) => {
          item.isSelected = false
          return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
        })
      })
      // }
    }
  }

  count: any = []
  openPopup() {
    this.count = this.allIPs.filter((item: any) => {
      return item.isSelected == true
    })
  }
  scheduleResponse: any
  schedule_spin: boolean = false
  executeSpin: boolean = false
  scheduleDags() {
    this.schedule_spin = true
    var datestring = (<HTMLInputElement>document.getElementById("schDate")).value
    var dags: any = []
    let obj1: any = {}
    this.allIPs.forEach((item: any) => {
      obj1 = {}
      if (item.isSelected == true) {
        obj1['dag_id'] = item.dag_id
        dags.push(obj1);
      }
    })
    var date = new Date(datestring)
    var utc = date.toUTCString()
    var utcdate = new Date(utc);
    const year = utcdate.getUTCFullYear();
    const month = this.padZero(date.getUTCMonth() + 1); // Months are zero-based
    const day = this.padZero(date.getUTCDate());
    const hours = this.padZero(date.getUTCHours());
    const minutes = this.padZero(date.getUTCMinutes());
    const seconds = this.padZero(date.getUTCSeconds());

    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
    let obj = {
      dagsListreqs: dags,
      scheduleDate: formattedDate
    }
    //console.log(obj);
    this.datamigrationservices.ScheduleDags(obj).subscribe((data: any) => {
      this.schedule_spin = false
      this.scheduleResponse = data.message
      if (data.message.includes("Dags scheduled")) {
        this.toast.success(this.scheduleResponse)
      } else {
        this.toast.error(this.scheduleResponse)
      }
      (<HTMLInputElement>document.getElementById("schDate")).value = "";
      $('#updateOrgModal').offcanvas('hide');
    })
  }
  padZero(num: number): string {
    return num < 10 ? '0' + num : num.toString();
  }
  popup(value: any) {
    if (value == 0) {
      this.scheduleSelect = true
    }
    else {
      this.scheduleSelect = false
    }
  }
  configData: any
  searchText1: any
  dagSelected: boolean = false
  selectedDag: any
  selectedDagName: string = ''
  cdcSelected: boolean = false
  selectDag(value: any) {
    this.dagSelected = true
    this.selectedDag = value
    this.selectedDagName = value.split('/').pop()
    this.selectedDagName == 'CDC_AF_DEMO_504_PM.xlsx' ? this.cdcSelected = true : this.cdcSelected = false;
    //console.log(value.split('/').pop())
  }
  getSCN() {
    this.executeSpin = true
    if (this.migtypeid == "30" || this.migtypeid == "24" || this.migtypeid == "35" || this.migtypeid == "34" || this.migtypeid == "31" || this.migtypeid == "28") {
      this.triggerDag()
    }
    else {
      if (this.srcCon == "" || this.selectedOperation == "Reverse_CDC") {
        this.allIPs = this.allIPs.filter((itemy: any) => {
          return itemy.isSelected == true
        })
        this.triggerDag()
      } else {
        this.datamigrationservices.getScnNumber(this.srcCon).subscribe((data: any) => {
          this.executeSpin = false
          if (data != undefined) {
            this.scnValue = data.scn
            if (this.scnValue != "") {
              this.allIPs = this.allIPs.filter((itemk: any) => {
                return itemk.isSelected == true
              })
              //console.log(this.allIPs)
              $('#updateOrgModal').offcanvas('hide');
              this.triggerDag()
            }
          }
          else {
            $('#updateOrgModal').offcanvas('hide');
            this.toast.error("Failed TO get SCN Number")
          }
        })
      }
    }
  }
  showCheckStatus: any
  isCheckBoxSel: boolean = false
  selectAll($event: any) {
    this.showCheckStatus = $event.target.checked
    if ($event.target.checked) {
      this.allIPs = []
      this.dagsInfo.filter((item: any) => {
        if (item.isDagAvailable !== false) {
          item.isSelected = true
          this.allIPs.push(item)
        }
      })
      this.isCheckBoxSel = true
    } else {
      this.allIPs = []
      for (const element of this.dagsInfo) {
        element.isSelected = false
      }
      this.isCheckBoxSel = false
    }
    //console.log(this.allIPs)
  }
  masterSelected: boolean = false;
  getIPfromCheck: any;
  checkboxselect($event: any, value: any): void {
    //console.log(value)
    this.getIPfromCheck = value;
    if ($event.target.checked) {
      this.isCheckBoxSel = true
      this.allIPs.length === null ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
      //console.log(this.allIPs.length)
      this.showCheckStatus = false
      const abc = this.dagsInfo.filter((item: any) => {
        return item.dag_id == value.toString()
      })
      //console.log(abc)

      var def = this.allIPs.filter((itemz: any) => {
        return itemz.dag_id == value.toString()
      })
      if (def.length == 0) {
        if (abc[0].isSelected == false) {
          abc[0].isSelected = true
        }
        this.allIPs.push(abc[0])
      }
      else {
        this.allIPs.filter((itema: any) => {
          if (itema.dag_id == value.toString()) {
            itema.isSelected = true
          }
        })
      }
      var check = this.allIPs.filter((itemz: any) => {
        return itemz.isSelected == true
      })
      if (check.length == this.dagsInfo.length) {
        this.showCheckStatus = true
      }
      //console.log(this.allIPs)
    } else {
      //let dag_index = this.dagsInfo.indexOf(value);
      // let i = 0
      this.dagsInfo.filter((item: any) => {
        if (item.dag_id == value) {
          item.isSelected = false
        }
        // i++
      })
      // let index = this.allIPs.indexOf(value);
      // this.dagsInfo[dag_index].isSelected = false
      // let y=0
      this.allIPs.filter((item1: any) => {
        if (item1.dag_id == value) {
          item1.isSelected = false
        }

        // y++
      })
      var check1 = this.allIPs.filter((itemz: any) => {
        return itemz.isSelected == true
      })
      if (check1.length == this.dagsInfo.length) {
        this.showCheckStatus = true
      }
      //this.allIPs[index].isSelected = false
      //const rem = this.allIPs.splice(index, 1);
      //console.log(rem)
      //console.log(this.allIPs)
      this.showCheckStatus = false
      this.allIPs.length === 0 ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
    }
  }
  getDagFormValue(value: any) {
    //console.log(value)
  }
  dagRes: any
  scnValue: any
  triggerDag() {
    this.trigger_spin = true
    const obj = {
      dagList: this.allIPs,
      scn: this.scnValue,
      type: this.initType
    }
    this.datamigrationservices.TriggerMultiDagsWithCDC(obj).subscribe((data: any) => {
      this.dagRes = data;
      this.trigger_spin = false
      this.allIPs = []
      this.showCheckStatus = false
      this.dagsInfo.filter((item: any) => {
        if (item.isSelected == true) {
          item.isSelected = false
        }
      })
      $('#updateOrgModal').offcanvas('hide');
      this.toast.success(data.message)
    },
      error => {
        this.trigger_spin = false
        $('#updateOrgModal').offcanvas('hide');
        this.toast.error('Dag Trigger Failed ');
      })
  }
  setRequiredValidators(controls: string[]) {
    controls.forEach(control => {
      (this.datacompareForm.controls[control as keyof typeof this.datacompareForm.controls] as FormControl).setValidators([Validators.required]);
    });
  }
  clearValidators(controls: string[]) {
    controls.forEach(control => {
      (this.datacompareForm.controls[control as keyof typeof this.datacompareForm.controls] as FormControl).clearValidators();
    });
  }
  updateAllControls() {
    Object.keys(this.datacompareForm.controls).forEach(control => {
      (this.datacompareForm.controls[control as keyof typeof this.datacompareForm.controls] as FormControl).updateValueAndValidity();
    });
  }
  updatePodconfigValidation(value: any) {
    if (value == "Custom") {
      this.setRequiredValidators(['request_Memory', 'limit_Memory']);
      this.updateAllControls()
    }
  }
  DataMigrationNewCommand(fd: any) {
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnectionId: fd.connectionName,
      targetConnectionId: fd.targetConnection,
      schema: fd.schema.toString(),
      targetSchema: fd.tgtschema,
      task: "Data_Compare",
      tablename: this.selectedItems.toString(),//fd.table,
      compareType: fd.method,
      fileName: fd.confiName,
      jobName: "qmig-migrt",
      codeoption:true
    }
    this.datamigrationservices.DataMigrationNewCommand(obj).subscribe((data: any) => {
      this.datacompareForm.reset();
      this.toast.success(data.message);
    })
  }

  getOracleTables(value: any) {
    let obj = {
      conid: this.conId,
      schemaname: value
    }
    this.datamigrationservices.getOracleTables(obj).subscribe((data: any) => {
      this.tablesresponse = data
      this.tablesresponse.forEach((item: any) => {
        item.table_name = item.tablename
      })

      // this.tablesresponse.forEach((item: any) => {
      //   item.type = "ALL"
      // })
    })
  }

  getDb2Tables(value: any) {
    let obj = {
      conid: this.conId,
      schemaname: value
    }
    this.datamigrationservices.getDb2Tables(obj).subscribe((data: any) => {
      this.tablesresponse = data
      this.tablesresponse.forEach((item: any) => {
        item.table_name = item.tablename
      })

      // this.tablesresponse.forEach((item: any) => {
      //   item.type = "ALL"
      // })
    })
  }
  srcIdforRepoerts: any
  selectSrcForReports(value: any) {
    this.srcIdforRepoerts = value;
  }
  DataCompareFolders: any = []
  tgtidForReports: any
  GetDataCompareReports(value: any) {
    this.tgtidForReports = value
    var path = this.srcIdforRepoerts + "/" + value + "/Data_Compare_Reports"
    this.datamigrationservices.getFilesfromExPath(path).subscribe((data: any) => {
      this.DataCompareReports = data;
    }
    )
  }

  DataCompareReports: any = []

  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }
  getRunSpin1: boolean = false;
  targetConn:any
  getUpdatedRunNumber1() {
    this.getRunSpin1 = false;
    this.fetchConfigFiles();
    this.fetchSourceConfigFiles(this.fileSrc,'0')
    this.fetchTargetConfigFiles(this.targetConn,this.fileSrc);
  }
  getRunSpin:boolean = false;
  getUpdatedRunNumber(){
this.GetDataCompareReports(this.tgtidForReports)
  }
  readDataString: any = []
  selectedSourceConnection: string = '';
  selectedTargetConnection: string = '';
  onEditConfigClick() {
    this.openModal();
  }
  openModal() {
  $('#demo').offcanvas('show');
  }
  onSourceChange(value: any) {
    this.selectedSourceConnection = value;
  }
  onTargetChange(value: string) {
    this.selectedTargetConnection = value;
  }
  readData: any
  ReadLocalJson() {
    const path = "/mnt/extra/" + this.conId + "/Config_Files/chunk_configuration.json"
    this.updatePath = path;
    this.datamigrationservices.ReadLocalJson(path).subscribe((data: any) => {
      this.readData = data;
      this.readDataString = JSON.stringify(data, null, 0);
    })
  }
  ReadLocalJson1() {
    const path = "/mnt/extra/" + this.conId + "/" + this.tgtId + "/Config_Files/chunk_configuration.json"
    this.updatePath = path;
    this.datamigrationservices.ReadLocalJson(path).subscribe((data: any) => {
      this.readData = data;
      this.readDataString = JSON.stringify(data, null, 0);
    })
  }
  //EditLocalJson
  editData: any
  updatePath: string = '';
  uploadSpin: boolean = false;
  Update() {
    const parsedData = this.readDataString.replace(/[\r\n\/]/g, '')// JSON.stringify(this.readDataString);
    this.uploadSpin = true;
    let obj = {
      path: this.updatePath,
      jsonData: parsedData//.replace(/[\r\n\/]/g, '')
    }
    this.datamigrationservices.EditLocalJson(obj).subscribe({
      next: () => {
        this.uploadSpin = false;
        this.toast.success("Updated Successfully");
        $('#demo').offcanvas('hide');
      },
      error: (err) => {
        console.error('API Error:', err);
        this.uploadSpin = false;
        this.toast.error('Update failed. Please try again.');
      }
    });
  }
}

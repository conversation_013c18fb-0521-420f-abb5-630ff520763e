import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { SchemaListSelect1, SchemaSelect } from '../../../../models/interfaces/types';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

declare let $: any;
@Component({
  selector: 'app-code-object',
  standalone: true,
  imports: [NgSelectModule, FormsModule, CommonModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './code-object.component.html',
  styles: ``
})
export class CodeObjectComponent {
  migrationSource = [{ values: 'D', option: 'Database' }];
  selectedCar: number | undefined;
  selectedPeople = [];
  selectedItems = [];
  data: any = [];
  ref_spin2: boolean = false;
  ref_spin1: boolean = false;
  people$: Observable<any> | undefined;
  //pagination
  searchText: string = '';
  searchText1: string = '';
  projectId: string = '';
  //getConsLIst
  schemaList: any;
  obj: any;
  conList: any;
  datachangeLogs: any
  //getschemaList
  tgtList: any;
  userData: any;
  selectedConname: any;
  //connection
  conId: any;
  conName: any;
  tgtId: any;
  //pagination
  datachange: any;
  datachange1: any;
  datachange2: any;
  page3: number = 1;
  pageNumber: number = 1;
  pageNumber1: number = 1;
  datachangeLogs1: any
  p: number = 1;
  page1: number = 1;
  page: number = 1;
  page2: number = 1;
  p1: number = 1;
  p2: number = 1;
  r_id: any;
  //page activity
  logdata: any;
  disabledprevious: boolean = false;
  tgtValue: string = '';
  //object type
  objectType: any;
  project: any;
  objtype: any;
  //validation
  value: any;
  airflowResponse: any
  execProjectForm: any;
  prjSchmea: any = {};
  getRunSpin: boolean = false;
  //get run number
  runNoData: any;
  runnoForReports: any;
  ref_spin: boolean = false
  tabledata: any;
  z: any;
  //iteration
  deleteResponse: any
  iterationForLogs: any
  //obj
  selectedObjType: any
  depLogs: any = []
  //schema
  schemaName: any = [];
  assessmentFiles: any
  //onkey2
  q: any
  q1: any
  q2: any
  ExecututionFiles: any;
  objType: any = [];
  obtypevalue: any
  selectedOperation: any
  spin: boolean = false;
  operation: any;
  prjdata: any
  ExeLog: any = {};
  prjSrcTgt: any = {};
  prjSrcTgtD: any = {};
  //hrs
  hrs: any
  fileResponse: any;
  spin_dwld: any;
  runinfospin: boolean = false;
  runinfo: boolean = false;
  //open model details
  prjdatas: any
  opId: any
  //iteration details
  iterationselected: any
  selectedschemas: string = ""
  pageName: string = ''
  migtypeid: string = ""
  schemalable: any
  constructor(private titleService: Title, private FormBuilder: FormBuilder, private common: CodeMigrationService, private toast: HotToastService, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }
  }
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList()
    this.getObjectTypes();
    this.getPrjRuninfoSelectAll()
    this.getreqTableData();
    this.pageNumber = 1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page = 1;
    this.page1 = 1;
    this.page2 = 1;
    // this.getrunno()
    this.execProjectForm = this.FormBuilder.group({
      connectionName: ['', Validators.required],
      targetConnection: [''],
      schema: ['', Validators.required],
      objectType: ['', Validators.required],
      objectName: [''],
      runNo: ['', [Validators.required]],
      targetSchema: ['', Validators.required],
      refreshFlag: [''],
      deployConnection: ['', [Validators.required]]
    });
    if (this.migtypeid === '48') {
      this.execProjectForm.get('connectionName').clearValidators();
      this.execProjectForm.controls['connectionName'].updateValueAndValidity()
      this.execProjectForm.get('objectType').clearValidators();
      this.execProjectForm.controls['objectType'].updateValueAndValidity()
      this.execProjectForm.get('schema').clearValidators();
      this.execProjectForm.controls['schema'].updateValueAndValidity()
      this.execProjectForm.get('deployConnection').clearValidators();
      this.execProjectForm.controls['deployConnection'].updateValueAndValidity()
      this.execProjectForm.get('targetSchema').clearValidators();
      this.execProjectForm.controls['targetSchema'].updateValueAndValidity()
      this.execProjectForm.get('runNo').clearValidators();
      this.execProjectForm.controls['runNo'].updateValueAndValidity()
    } 
    this.execProjectForm.updateValueAndValidity();
  }

  //for validations
  get f() {
    return this.execProjectForm.controls;

  }
  tgtschema: string = ""
  selecttgtschema(value: any) {
    this.tgtschema = value
  }
  //schema list
  selectschema(data: any) {
    this.schemaName = data.toString();
    this.showTargetSchemaAndTables = data.length === 1;
    this.isAll = data.toString();
    this.selectedsrcschema = [];
    this.tgtschema = "";
    const targetSchemaControl = this.execProjectForm.get('targetSchema');
    this.tgtschema = targetSchemaControl?.value;
    if (data === 'ALL' || (Array.isArray(data) && data.includes('ALL'))) {
      this.schemaList.forEach((item: any) => {
        if (this.migtypeid == "31") {
          this.selectedsrcschema.push(item.schemaname)
        }
        else {
          this.selectedsrcschema.push(item.schema_name);
        }
      });
    } else if (Array.isArray(data)) {
      this.selectedsrcschema = data.filter((schema: any) => schema !== 'ALL');
    } else {
      this.selectedsrcschema = [data];
    }

    console.log("Selected Schemas →", this.selectedsrcschema);
  }
  ObjectsName:any;
  selectedobjectNames:any;
  selectpipelineobjects(data: any){
    this.ObjectsName = data.toString();
    this.selectedobjectNames = [];
    if (data === 'ALL' || (Array.isArray(data) && data.includes('ALL'))) {
      this.PipelineData.forEach((item: any) => {
          this.selectedobjectNames.push(item.pipline_name);
      });
    } else if (Array.isArray(data)) {
      this.selectedobjectNames = data.filter((schema: any) => schema !== 'ALL');
    } else {
      this.selectedobjectNames = [data];
    }
  }
  //onkey event
  onKey() {
    this.pageNumber = 1;
    this.page2 = 1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page1 = 1
    this.page3 = 1;

  }//on key event 2
  onKey2() {
    this.q = 1;
    this.q1 = 1;
    this.q2 = 1;
  }
  //redis
  redisResp: any
  ObjectCategory: any = "Code_Objects"
  currentRunNumber: string = '';

  //get run number
  getRunNumber(data: any) {
    this.currentRunNumber = data;

    this.schemaList = []
    this.conList = []
    this.currentRunNumber = data;
    this.totalRunnumbers.filter((item: any) => {
      if (item.iteration == data) {
        this.schemaList.push({ schema_name: item.schemaname })
        this.schemaList = this.schemaList.filter((item: any) => item.schema_name !== 'ALL');
        this.schemaList.forEach((item: any) => {
          item.type = "ALL"
        })
        if (this.conList.length == 0) {
          this.conList.push({ Connection_ID: item.connection_id, conname: item.conname })
        }
      }
    })
    // this.schemasbyiteration(data);
    //console.log(data)
  }

  schemasbyiteration(value: string) {
    this.common.GetSchemasByRunId(value).subscribe((data: any) => {
      this.schemaList = data['Table1'];
    })
  }
  get getControl() {
    return this.execProjectForm.controls;
  }
  //GetSchemasLiist
  tgtSchemaList: any = [];
  getSchemasList(connectionId: any) {
    this.tgtSchemaList = []
    if (this.migtypeid == "31") {
      this.tgtList.filter((item: any) => {
        if (item.Connection_ID == connectionId) {
          let ob = {
            schema_name: item.dbname
          }
          this.tgtSchemaList.push(ob);
        }
      })
    }
    else {
      const obj: SchemaListSelect1 = {
        projectId: this.projectId,
        connectionId: connectionId
      }
      this.common.SchemaListSelect1(obj).subscribe((data: SchemaSelect) => {
        // this.schemaList = data['Table1'];
        this.tgtSchemaList = data['Table1'];
        this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => {
          return item.schemaname != "ALL"
        });
      })
    }
  }
  //GetconnectionList
  GetConsList() {
    const projectId = this.projectId.toString()
    this.common.getConList(projectId).subscribe((data: any) => {
      const condata = data['Table1'];
      if (this.migtypeid == '48') {
        this.conList = condata.filter((item: any) => {
          return item.migsrctgt == 'S' && item.conname != "";
        })
      }
      this.tgtList = condata.filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.getreqTableData()
      this.ref_spin = false
    })
  }
  //selected file type
  selectedfiletype(value: any) {
    const selectedconname = this.conList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;

  }
  //Get terget connection

  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
  }
  //obj list details
  selectedobjecttypes:any;
  selectObj(value: any) {
    this.objtype = value;
    if (value === "Pipeline"&& this.migtypeid === '48') {
      this.execProjectForm.controls.schema.clearValidators();
      this.execProjectForm.controls.schema.updateValueAndValidity();
    }else{
      this.execProjectForm.controls.schema.setValidators([Validators.required])
      this.execProjectForm.controls.schema.updateValueAndValidity()
    }
    
  }
  dep_objectType: any
  //Get object types
  getObjectTypes() {
    const obj = {
      projectId: this.projectId,
      objectgroupname: "Code_Objects"
    }
    this.common.getObjectTypes(obj).subscribe((data: any) => {
      this.objectType = data['Table1'];
      this.dep_objectType = this.objectType.filter((item: any) => {
        return item.object_category != "ALL"
      })
      const itemsToRemove = ["Package_Function", "Package_Procedure"];
      if (this.migtypeid == "20") {
        this.objectType = this.objectType.filter((el: any) => {
          return !itemsToRemove.includes(el.objecttype_name)
        })
        this.dep_objectType = this.dep_objectType.filter((el: any) => {
          return el.objecttype_name != "Package"
        })
      }
      var ob = {
        object_type: "ALL", object_category: "Code_Objects",
        object_type_id: "0"
      }
      this.objectType.unshift(ob)
    })
  }
  //get schema list
  prjSchemaList(sourceConection: any) {
    this.prjSchmea.projectId = this.projectId.toString();
    this.prjSchmea.dbConnection = 'null';
    this.prjSchmea.sourceConnection = sourceConection.toString();
    this.common.prjSchemaListSelect(this.prjSchmea).subscribe((data: any) => {
      this.schemaList = data['Table1'];
      this.schemaList = this.schemaList.filter((item: any) => item.schema_name !== 'ALL');
      this.schemaList.forEach((item: any) => {
        item.type = "ALL"
      })
    });
  }

  //update run number
  getUpdatedRunNumber() {
    this.getRunSpin = true
    this.getPrjRuninfoSelectAll()
    this.getreqTableData()

  }
  //get run numbers
  // getrunno() {
  //   this.common.GetRunno(this.projectId).subscribe((data) => {
  //     this.runNoData = data['Table1'];
  //     this.runNoData = this.runNoData.filter((item: any) => {
  //       if (item.iteration == "") {
  //         item.dbschema = "ALL"
  //       }
  //       return (item.operation_type == "Extraction") 
  //     })
  //     this.runNoData=this.filterList(this.runNoData)
  //   });
  // }
  //Get run number
  totalRunnumbers: any = []
  getPrjRuninfoSelectAll() {
    this.common.GetRunno(this.projectId).subscribe((data: any) => {
      this.getRunSpin = false
      this.totalRunnumbers = data['Table1']
      this.runNoData = data['Table1'];
      this.runnoForReports = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoForReports = this.runnoForReports.filter((item: any) => {
        return item.iteration != ""
      })
      // this.runnoForReports = this.runnoForReports.filter((data: any) => { return ((data.operation_type == "Extraction") && data.iteration != '') })
      // this.runNoData = this.runNoData.filter((item: any) => {
      //   if (item.iteration == "") {
      //     item.dbschema = "ALL"
      //   }
      //   return (item.operation_name == "Code_Objects" && item.operation_type == "Conversion") || item.iteration == ""
      // })
      this.runnoForReports = this.filterList(this.runnoForReports)
      this.runNoData = this.filterList(this.runNoData)
    });
  }
  //filter list
  filterList(listData: any) {
    let uniqueNames: any = []
    for (const element of listData) {
      if (uniqueNames.length == 0) {
        uniqueNames.push(element)
      }
      else {
        var abc = uniqueNames.filter((item: any) => {
          return item.iteration === element.iteration
        })
        if (abc.length == 0) {
          uniqueNames.push(element)
        }
      }
    }
    return uniqueNames;
  }

  //Get request table data
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.common.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      this.tabledata = this.tabledata.filter((item: any) => {
        return item.operation_category === "Code_Objects"
      })
      this.ref_spin = false
    })
  }
  // delete request table data

  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.common.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData()
    })
  }
  //**package activity log*/
  GetIndividualLogs(action: any) {
    this.page = 1
    if (action == "null") {
      this.r_id = "null"
    }
    if (action == "previous") {
      this.r_id = this.logdata[0].exelog_id;
    }
    if (action == "next") {
      this.r_id = this.logdata[this.logdata.length - 1].exelog_id;
      this.disabledprevious = false;
    }
    if (action == '') {
      action = 'null'
    }
    const logobj = {
      projectId: this.projectId.toString(),
      operationType: "Conversion",
      operationName: "Code_Objects",
      action: 'null',
      row_id: this.r_id,
      runno: action
    }
    this.common.GetIndividualLogs(logobj).subscribe((data: any) => {
      this.logdata = data['Table1'];
    });
  }
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value
  }
  //select object type
  selectObjType(value: any) {
    this.selectedObjType = value
    this.fetchLogs()
  }
  //fetch logs details
  fetchLogs() {
    this.depLogs = []
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Deployment_Logs/" + this.selectedObjType + "/";
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.depLogs = data;
      if (this.depLogs.length == 0) {
        let ob = {
          fileName: "No Files Created"
        }
        this.depLogs.push(ob)
      }
      //console.log(this.depLogs)
    })
  }

  //select iteration
  selectIteration(value: any) {
    this.iterationselected = value
    this.fetchAssessmentFiles()
  }
  //fetch assessment files details
  fetchAssessmentFiles() {
    this.assessmentFiles = []
    const path = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Validation/"
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.assessmentFiles = data.filter((item: any) => {
        return item.fileName.includes("_Code_Objects_Validation")
      })
    })
  }
  //hrs
  hours: any = [{
    option: "ALL", value: "0"
  },
  {
    option: "1 hrs", value: "1"
  }, {
    option: "5 hrs", value: "5"
  }, {
    option: "10 hrs", value: "10"
  }, {
    option: "1 Day", value: "24"
  },
  ]
  //ge hrs details
  gethrs(value: any) {
    this.hrs = value
    //console.log(this.hrs)
    this.filterReports()
  }
  //filter reports
  filterReports() {
    if (this.hrs == "0") {
      this.assessmentFiles = []
      this.fetchAssessmentFiles()
    }
    else {
      const obj = {
        path: "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Validation_Reports/",
        hrs: this.hrs,
        validationFlag: false
      }
      this.common.ReportsFilterByTime(obj).subscribe((data: any) => {
        this.assessmentFiles = data
      })
    }
  }
  //download files
  downloadFile(fileInfo: any) {
    this.common.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  //open model details

  openModal(value: any) {
    for (let i = 0; i < this.schemaName.length; i++) {
      this.selectedschemas = this.selectedschemas + this.schemaName[i]
      if (i < this.schemaName.length - 1) {
        this.selectedschemas = this.selectedschemas + ","
      }
    }
    $('demo').offcanvas('show');
  }
  //open popup
  openPopup() {
    this.schemaName = []
    this.selectedschemas = ''
    this.execProjectForm.reset();
  }

  //project connection run table inserts
  projectConRunTblInserts(data: any, value: any) {
    this.runinfo = true;
    this.opId = data.operation
    if (value == true) {
      data.status = "P"
    }
    else {
      data.status = "I"
    }
    if (data.pattern != "") {
      var objName = data.pattern
    }
    else {
      objName = ""
    }
    if (data.objectType != "") {
      var objType = data.objectType
    }
    else {
      objType = ""
    }
    // const tableinsesrt = {
    //   projectId: this.projectId.toString(),
    //   connection_id: parseInt(this.conId),
    //   operationId:parseInt(this.operation[0].operation_id),// parseInt(data.operation),
    //   schema: this.schemaName.toString(),
    //   status: data.status,
    //   remarks: "",
    //   objectname: objName,
    //   objecttype: objType
    // }
    if (this.tabledata.length != 0) {
      const res = this.tabledata.filter((item: any) => {
        return item.connection_id == this.conId && item.schemaname == this.schemaName.toString() && item.status == "I" && item.operation_name == "Code_Objects"
      })
      if (res.length >= 1) {
        this.runinfo = false;
        $('#demo').offcanvas('hide');
        this.toast.error("Request already Added")
      }
      if (res.length == 0) {

        this.RedisCommand()
        this.getreqTableData();
        //this.runinfo = false;
        //$('#updateOrgModal').modal('hide');
      }
    }
    if (this.tabledata.length == 0) {
      this.RedisCommand()
      this.getreqTableData();
      // this.runinfo = false;
      //$('#updateOrgModal').modal('hide');

    }
    // this.runinfo = true;
    // this.project.projectConRunTblInsert(tableinsesrt).subscribe((data: any) => {
    //   this.prjdatas = data['jsonResponseData']['Table1'];
    //   if (data.message == "Success") {
    //     this.RedisCommand()
    //     this.runinfo = false;
    //     $('#updateOrgModal').modal('hide');
    //     this.alertService.success('Successfully Inserted');
    //     this.getreqTableData()
    //   }
    // },
    //   () => {
    //     this.spin = false;
    //     this.alertService.danger('Something happened');
    //   });
  }
  //redis command details
  RedisCommand() {
    this.runinfo = true;
    const obj = {
      projectId: this.projectId.toString(),
      option: 3,
      schema: this.schemaName.toString(),
      connection: this.conName,
      Object: this.ObjectCategory,
      srcConId: this.conId,
      tgtConId: this.tgtId,
      objectType: this.migtypeid == "20" ? (this.objtype == 'Package' ? 'Package_Function,Package_Procedure' : this.objtype) : this.objtype,
      jobName: "qmig-convs",
      iteration: this.currentRunNumber,
      targetSchema: this.tgtschema
    }
    this.common.setRedisCache(obj).subscribe((data: any) => {
      this.redisResp = data;
      this.runinfo = false;
      this.schemaName = []
      this.selectedschemas = ''
      this.execProjectForm.reset();
      $('#demo').offcanvas('hide');
      this.toast.success("Executed Successfully")
    })
  }
  //get operation details
  getProjectSrctgtconfSelectD(migSrcType: any) {
    this.prjSrcTgtD.projectId = this.projectId.toString();
    this.prjSrcTgtD.migsrcType = migSrcType;
    this.prjSrcTgtD.connectionName = 'null';
    // this.project.ProjectSrctgtconfSelect(this.prjSrcTgtD).subscribe((data) => {
    //   this.prjSrcTgtData = data['Table1'];
    //   this.reqF = this.prjSrcTgtData.filter((item: any) => {
    //     return item.migsrctype == 'F' && item.migsrctgt == 'S';
    //   });
    //   this.reqD = this.prjSrcTgtData.filter((item: any) => {
    //     return item.migsrctype == 'D' && item.migsrctgt == 'S';
    //   });
    // });
  }
  //get project source target details
  getProjectSrctgtconfSelect() {
    this.prjSrcTgt.projectId = this.projectId.toString();
    this.prjSrcTgt.migsrcType = 'F';
    this.prjSrcTgt.connectionName = null;
    // this.project.ProjectSrctgtconfSelect(this.prjSrcTgt).subscribe((data) => {
    //   this.userData = data['Table1'];
    // });
  }
  //get project documents details
  getprojectDocumentsDetail() {
    const requestObj = {
      projectID: this.projectId.toString(),
      containerName: 'qmigratorfiles' + this.projectId,
      folderName: 'ProjectDocs',
    };
    this.common.projectDocumentsDetailSelect(requestObj).subscribe((data) => {
      this.userData = data;
    });
  }
  //post user access select
  postUserAccessSelect() {
    const obj = {};
    this.common.UserAccessSelect(obj).subscribe(() => {
    });
  }
  //get operation details
  getPrjExeLogSelect() {
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operation = 'TASK';
    this.ExeLog.pageNo = '1';
    this.common.PrjExeLogSelect(this.ExeLog).subscribe((data) => {
      this.userData = data['Table1'];
    });
  }
  //project connection run table inserts

  projectConRunTblInsert(data: any, value: any) {
    if (value == true) {
      data.status = "P"
    }
    else {
      data.status = "I"
    }
    if (data.pattern != "") {
      var objName = data.pattern
    }
    else {
      objName = ""
    }
    if (data.objectType != "") {
      var objType = data.objectType
    }
    else {
      objType = ""
    }

    const tableinsesrt = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(this.operation[0].operation_id),// parseInt(data.operation),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: "",
      objectname: objName,
      objecttype: objType
    }

    this.runinfospin = true;
    this.project.projectConRunTblInsert(tableinsesrt).subscribe((data: any) => {
      this.prjdata = data['jsonResponseData']['Table1'];
      if (data.message == "Success") {
        this.runinfospin = false;
        $('#demo').offcanvas('hide');
        this.toast.success('Successfully Inserted');
        this.getreqTableData()
      }
    },
      () => {
        this.spin = false;
        this.toast.error('Something happened');
      });
  }
  //se;ect operation
  selectOpertion(id: any) {
    const op = this.operation.filter((item: any) => {
      return item.operation_id == id
    })
    this.selectedOperation = op[0].operation_name
  }
  //select obj types
  SelectObjectTypes(value: any) {
    this.objType = value;
    this.execProjectForm.controls.schema.setValidators([Validators.required])
    this.execProjectForm.controls.schema.updateValueAndValidity()
  }
  //selectiterationforlogs
  selectIterforlogs(value: any) {
    this.iterationForLogs = value
    this.filterExecutionReports()
  }
  //get execution reports
  filterExecutionReports() {
    this.ExecututionFiles = []
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Execution_Logs/Conversion/"
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
      this.ExecututionFiles = this.ExecututionFiles.filter((item: any) => {
        return item.fileName.includes("_Code_Objects")
      })
    })
  }
  ConversionResponseData: any = []
  ConversionCommand() {
    let obj: any = {
      sourceConnectionId: this.conId,
      targetConnectionId: this.tgtId,
      projectId: this.projectId.toString(),
      task: this.migtypeid == '48' ? "Deploy_Pipeline" : "Conversion",
      iteration: this.migtypeid == '48' ? "null" : this.currentRunNumber,
      objectCategory: this.migtypeid == '48' ? "Pipeline" : this.ObjectCategory,
      objectType: this.migtypeid == "20" ? (this.objtype == 'Package' ? 'Package_Function,Package_Procedure' : this.objtype) : this.objtype,
      schema: this.migtypeid == '48' ? "null" : this.selectedsrcschema.join(','),
      objectName: this.migtypeid == '48' ? this.selectedobjectNames.join(',') : "null",
      targetSchema: this.migtypeid == '48' ? "null" : this.tgtschema || this.selectedsrcschema.toString(),
      deployConnectionId: this.migtypeid == '48' ? "null" : this.deployCon,
      processType: "",
      restartFlag: this.refreshChecked == true ? "True" : "False",
      jobName: "qmig-convs",
    }
    console.log(obj)
    this.common.ConversionCommand(obj).subscribe((data: any) => {
      this.ConversionResponseData = data;
      // this.runinfospins = true;
      $('#demo').offcanvas('hide');
      this.toast.success("Code Object Conversion Command Executed")
    },
      error => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong')
      })
  }
  refreshChecked: boolean = false
  getCheckValue($event: any): void {
    //console.log($event.target.checked)
    if ($event.target.checked == true) {
      this.refreshChecked = true
    }
    else {
      this.refreshChecked = false
    }
  }
  deployCon:any;
  drconName:any;
  drconId:any;
  selectDeployCon(value:any){
    // if(this.tgtId == value){
    //   this.toast.warning("Target and Deploy Connection Can not be Same ");
    // }
    // else{
      const selectedconname = this.tgtList.filter((item: any) => {
        return item.Connection_ID === value;
      });
      this.deployCon = value;
      this.drconId = selectedconname[0].Connection_ID;
      this.drconName = selectedconname[0].conname;
     // this.deployCon=value
    // }
  }
  selectedsrcschema: any = [];
  selectedtgtschema: any
  schemavalue: any
  showschemaa: boolean = false
  isAll: any;
  showTargetSchemaAndTables: boolean = true;
  validationstable() {
    const tgtschemaControl = this.execProjectForm.get('targetSchema');
    if(this.migtypeid == '48'){
      tgtschemaControl?.clearValidators();
      tgtschemaControl?.setValue(null);
    }else{
      if (this.isAll === "ALL") {
        tgtschemaControl?.clearValidators();
        tgtschemaControl?.setValue(null);
      }
      else if (Array.isArray(this.selectedItems) && this.selectedItems?.length === 1) {
        tgtschemaControl?.clearValidators();
        tgtschemaControl?.setValue(null);
      }
      else {
        tgtschemaControl?.clearValidators();
        tgtschemaControl?.setValue(null);
      }
      tgtschemaControl?.updateValueAndValidity();
    }
    
  }
  depLogs1: any
  fetchLogs1() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs1 + "/Failed_Objects/Code_Objects/";
    this.depLogs1 = []
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.depLogs1 = data;
    })
  }
  iterationForLogs1: any;
  selectIterForLogs1(value: any) {
    console.log('Selected iteration:', value);
    this.iterationForLogs1 = value;
    this.fetchLogs1();
  }
  selectObjType1(value: any) {
    this.selectedObjType = value;
   
  }
  //getPipelineList
  PipelineData: any;
  selectedlines: string = "";
  pipeline: any
  getPipelineList(conid: any) {
    this.pipeline = conid
    this.common.getPipelineList(this.pipeline).subscribe((data: any) => {
      this.PipelineData = data['Table1']
      this.PipelineData = this.PipelineData.filter((item: any) => item.schema_name !== 'ALL');
      this.PipelineData.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
}

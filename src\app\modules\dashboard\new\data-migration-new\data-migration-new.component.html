<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-12">
            <dash-tabs></dash-tabs>
        </div>
    </div>

    <!--- Main Content --->
    <div class="qmigTabs mt-3">
        <div class="row">
            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2 pe-1 mt-1">
                <div class="form-group">
                    <select class="form-select form-small m-w100" #sc (change)="onSourceConnectionChange(sc.value);">
                        <option selected disabled>Source Connection</option>
                        @for ( src of ConsList; track src) {
                        <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2 px-1 mt-1">
                <div class="form-group">
                    <select class="form-select form-small m-w100" #tgtsc
                        (change)="ontargetConnectionChange(tgtsc.value)">
                        <option selected disabled>Target Connection</option>
                        @for(tgt of tgtList;track tgt; ){
                        <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-1 mt-3">
                <button class="btn btn-sync" (click)="getUpdatedRunNumber1()">
                    @if(getRunSpin1){
                    <app-spinner />
                    }@else{
                    <span class="mdi mdi-refresh"></span>
                    }
                </button>
            </div>
        </div>
    </div>

    @if(isSourceConnectionSelected){
    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-6">
                    <h3 style=" color: var(--color-secondary);font-size: 1.20em;" class="mt-3">Database Snapshot
                        <button class="btn btn-sync" (click)="getsnapshotDetails()">
                            @if(getRunSpin2){
                            <app-spinner />
                            }@else{
                            <span class="mdi mdi-refresh"></span>
                            }
                        </button>
                    </h3>
                </div>
                @if(migtypeid== '31'){
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2 px-1"></div>
                }@else {
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2 px-1">
                    <select class="form-select form-small m-w100" #schemaSelect
                        (change)="onSchemaChange(schemaSelect.value)">
                        <!-- @if(migtypeid== '31'){
                                <option selected disabled>Source Database </option>
                            }@else { -->
                        <option selected disabled>Source Schema Name</option>
                        <!-- } -->
                        @for(sc of schemaList; track sc;){
                        <!-- @if(this.migtypeid == '31'){
                        <option value="{{sc.schema_name}}">{{sc.schema_name}}</option>
                        }@else{ -->
                        <option value="{{sc.schemaname}}">{{sc.schemaname}}</option>
                        <!-- } -->
                        }
                    </select>
                </div>
                }
                @if(migtypeid== '31'){
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2 px-1"></div>
                }@else{
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2 px-1">
                    <select class="form-select form-small m-w100" #tgtsch (change)="ontgtSchemaChange(tgtsch.value)">
                        <!-- @if(migtypeid== '31'){
                            <option selected disabled>Target Database </option>
                            }@else { -->
                        <option selected disabled>Target Schema Name</option>
                        <!-- } -->
                        @for ( sche of tgtSchemaList; track sche) {
                        <!-- @if(this.migtypeid == '31'){
                            <option value="{{sche.schema_name}}">{{sche.schema_name}}</option>
                            }@else{ -->
                        <option value="{{sche.schemaname}}">{{sche.schemaname}}</option>
                        <!-- } -->
                        }
                    </select>
                </div>
                }
                @if(this.migtypeid=='38' ){
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                    <ng-select (change)="onTableChange(selectedTable)" [items]="ObjectNamesList" [multiple]="true"
                        bindLabel="table_name" [closeOnSelect]="false" bindValue="table_name"
                        [placeholder]="'Table Names'" [(ngModel)]="selectedTable" class="multi-sel">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                [ngModelOptions]="{ standalone : true }" /> {{item.table_name}}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items">
                            @for(item of slicedData(items);track item; ){
                            <div class="ng-value">
                                {{item.table_name}}
                            </div>
                            }
                            @if(items.length > 3)
                            {
                            <div class="ng-value">
                                <span class="ng-value-label">{{items.length - 3}} more...</span>
                            </div>
                            }
                        </ng-template>
                    </ng-select>
                </div>
                }@else if(this.migtypeid=='31'){
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
                    <ng-select (change)="onTableChange(selectedTable)" [items]="ObjectNamesList" [multiple]="true"
                        bindLabel="tableName" [closeOnSelect]="false" bindValue="tableName"
                        [placeholder]="'Table Names'" [(ngModel)]="selectedTable" class="multi-sel">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                [ngModelOptions]="{ standalone : true }" /> {{item.tableName}}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items">
                            @for(item of slicedData(items);track item; ){
                            <div class="ng-value">
                                {{item.tableName}}
                            </div>
                            }
                            @if(items.length > 3)
                            {
                            <div class="ng-value">
                                <span class="ng-value-label">{{items.length - 3}} more...</span>
                            </div>
                            }
                        </ng-template>
                    </ng-select>
                </div>
                }@else{
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-2">
                    <ng-select (change)="onTableChange(selectedTable)" [items]="ObjectNamesList" [multiple]="true"
                        bindLabel="objectName" [closeOnSelect]="false" bindValue="objectName"
                        [placeholder]="'Table Names'" [(ngModel)]="selectedTable" class="multi-sel">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                [ngModelOptions]="{ standalone : true }" /> {{item.objectName}}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items">
                            @for(item of slicedData(items);track item; ){
                            <div class="ng-value">
                                {{item.objectName}}
                            </div>
                            }
                            @if(items.length > 3)
                            {
                            <div class="ng-value">
                                <span class="ng-value-label">{{items.length - 3}} more...</span>
                            </div>
                            }
                        </ng-template>
                    </ng-select>
                </div>
                }

            </div>
        </div>
    </div>
    @if(gb != null || Tgtgb != null){
    <div class="row mt-3">
        <div class="col-xl-4 col-md-4 col-sm-6">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="DBSHead">
                        <p>Storage Size</p>
                        <span class="mdi mdi-harddisk"></span>
                    </div>
                    <div class="DBSBody">
                        <div class="DBSS">
                            <h3>{{gb}} GB</h3>
                            <span>Source</span>
                        </div>
                        <div class="DBST">
                            <h3>{{Tgtgb}} GB</h3>
                            <span>Target</span>
                        </div>
                        <div class="arrow-show">
                            <span class="mdi mdi-arrow-down"></span>
                        </div>
                    </div>
                    <div class="DBSFooter">
                        <p [ngClass]="getTrendInfo(gb, Tgtgb).textColor">
                            <span class="mdi" [ngClass]="getTrendInfo(gb, Tgtgb).icon"></span>
                            {{ getTrendInfo(gb, Tgtgb).label }}
                        </p>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" [ngClass]="getTrendInfo(gb, Tgtgb).color"
                                [style.width.%]="getTrendInfo(gb, Tgtgb).width"
                                [attr.aria-valuenow]="getTrendInfo(gb, Tgtgb).width" aria-valuemin="0"
                                aria-valuemax="100">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-4 col-sm-6">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="DBSHead">
                        <p>Row Count</p>
                        <span class="mdi mdi-database"></span>
                    </div>
                    <div class="DBSBody">
                        <div class="DBSS">
                            <h3>{{numRows | numberSuffix}} </h3>
                            <span>Source</span>
                        </div>
                        <div class="DBST">
                            <h3>{{TGtnumRows| numberSuffix}} </h3>
                            <span>Target</span>
                        </div>
                        <div class="arrow-show">
                            <span class="mdi mdi-arrow-down"></span>
                        </div>
                    </div>
                    <div class="DBSFooter">
                        <p [ngClass]="getTrendInfo(gb, Tgtgb).textColor">
                            <span class="mdi" [ngClass]="getTrendInfo(numRows, TGtnumRows).icon"></span>
                            {{ getTrendInfo(numRows, TGtnumRows).label }}
                        </p>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar"
                                [ngClass]="getTrendInfo(numRows, TGtnumRows).color"
                                [style.width.%]="getTrendInfo(numRows, TGtnumRows).width"
                                [attr.aria-valuenow]="getTrendInfo(numRows, TGtnumRows).width" aria-valuemin="0"
                                aria-valuemax="100">
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="col-xl-4 col-md-4 col-sm-6">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="DBSHead">
                        <p>Table Count</p>
                        <span class="mdi mdi-timetable"></span>
                    </div>
                    <div class="DBSBody">
                        <div class="DBSS">
                            <h3>{{totalTablesCount | numberSuffix}} </h3>
                            <span>Source</span>
                        </div>
                        <div class="DBST">
                            <h3>{{extractedTablesCount| numberSuffix}} </h3>
                            <span>Target</span>
                        </div>
                        <div class="arrow-show">
                            <span class="mdi mdi-arrow-down"></span>
                        </div>
                    </div>
                    <div class="DBSFooter">
                        <p [ngClass]="getTrendInfo(gb, Tgtgb).textColor">
                            <span class="mdi"
                                [ngClass]="getTrendInfo(totalTablesCount, extractedTablesCount).icon"></span>
                            {{ getTrendInfo(totalTablesCount, extractedTablesCount).label }}
                        </p>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar"
                                [ngClass]="getTrendInfo(totalTablesCount, extractedTablesCount).color"
                                [style.width.%]="getTrendInfo(totalTablesCount, extractedTablesCount).width"
                                [attr.aria-valuenow]="getTrendInfo(totalTablesCount, extractedTablesCount).width"
                                aria-valuemin="0" aria-valuemax="100">
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    }@else {
    <div class="pb-3 text-center"><app-spinner /></div>
    }

    <div class="mt-3">
        <div class="wd-animate">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
                            <h3 style="color: var(--color-secondary);font-size: 1.20em;" class="mt-4">Migration
                                Performance Stats
                            </h3>
                        </div>
                        <div class="col-12 col-sm-12 col-md-8 col-lg-8 col-xl-8">
                            <div class="row">
                                <div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
                                    <div class="">
                                        <label class="form-label w-auto me-3" for="name">Start Date</label>
                                        <input type="datetime-local" class="form-control form-small w-100" id="fromData"
                                            #fromDateInput>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
                                    <div class="">
                                        <label class="form-label w-auto me-3" for="name">End Date</label>
                                        <input type="datetime-local" class="form-control form-small w-100" id="toData"
                                            #toDateInput>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
                                    <div class="">
                                        <div class="body-header-button pt-2 mt-4">
                                            <button type="button" class="btn btn-upload btn-small w-100 me-1"
                                                (click)="executeCPUData(fromDateInput.value, toDateInput.value)">
                                                <span></span> Execute
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @if(CpuDetails && CpuDetails.length > 0){
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Source Details</th>
                                <th>Target Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(con of CpuDetails;
                            track con;) {
                            <tr>
                                <td>Total CPU(cores)</td>
                                <td>{{ isValueNaN(totalCpuCores) ? 'NA' : totalCpuCores }}</td>
                                <td>NA</td>
                            </tr>
                            <tr>
                                <td>CPU Utilization Percentage</td>
                                <td>{{ isValueNaN(utilizedCpu) ? 'NA' : utilizedCpu.toFixed(2) }}%</td>
                                <td>NA</td>
                            </tr>
                            <tr>
                                <td>Total Memory(GB)</td>
                                <td>{{isValueNaN(totalMemoryGb) ? 'NA' : totalMemoryGb}}</td>
                                <td>NA</td>
                            </tr>
                            <tr>
                                <td>Memory Utilization Percentage</td>
                                <td>{{ isValueNaN(memoryUtilizationGb) ? 'NA' : memoryUtilizationGb.toFixed(2) }}%</td>
                                <td>NA</td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                }
                @if(troughput && troughput.length > 0){
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Max TP</th>
                                <th>Min TP</th>
                                <th>Average TP</th>
                                <th>Max RPS</th>
                                <th>Min RPS</th>
                                <th>Avg RPS</th>
                                <!-- <th>Dag Max Records per second </th>
                                        <th>Dag Min Records per second</th> -->

                            </tr>
                        </thead>
                        @for(con of troughput;
                        track con;) {
                        <tbody>
                            <!-- <tr>
                                        <td>Lob</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                    </tr>
                                    <tr>
                                        <td>Non-Lob</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                        <td>0</td>
                                    </tr> -->
                            <tr>
                                <td>IDL</td>
                                <td>{{con.max_throughput}}</td>
                                <td>{{con.min_throughput}}</td>
                                <td>{{con.avg_throughput}}</td>
                                <td>{{con.max_records_per_second}}</td>
                                <td>{{con.min_records_per_second}}</td>
                                <td>{{con.avg_records_per_second}}</td>
                            </tr>
                        </tbody>
                        }
                    </table>
                </div>
                <p class="text-danger ms-3 pb-3">Note : TP = Thoughput(MB/s), RPS = Records per second, IDL = Initial
                    Data Load</p>
                }@else {
                <p class="text-center pb-3 text-danger">Please select Start and End data's.</p>
                }
            </div>
        </div>
    </div>
    <h3 style="color: var(--color-secondary);font-size: 1.40em;" class="mt-3 ms-2"> Validation Stats
        <button class="btn btn-sync" (click)="getvalidationDetails()">
            @if(getRunSpin3){
            <app-spinner />
            }@else{
            <span class="mdi mdi-refresh"></span>
            }
        </button>
    </h3>

    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="row">
                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                    <h3 style="font-size: 1.20em;" class="mt-3"> As per Initial Data Load</h3>
                </div>
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2 offset-md-2 offset-lg-2 offset-xl-2">
                    <select class="form-select form-small m-w100" [(ngModel)]="selectedSchema2"
                        (change)="onSchemaChange2(selectedSchema2)">
                        @if(migtypeid== '31'){
                        <option [ngValue]="null" selected disabled>Source Database </option>
                        }@else {
                        <option [ngValue]="null" selected disabled>Source Schema</option>
                        }
                        @for( src of sourceSchemas;track src){
                        <option [value]="src">{{ src }}</option>
                        }
                    </select>
                </div>
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
                    <select class="form-select form-small m-w100" [(ngModel)]="selectedtarget2"
                        (change)="ontargetChange2(selectedtarget2)">
                        @if(migtypeid== '31'){
                        <option [ngValue]="null" selected disabled>Target Database </option>
                        }@else {
                        <option [ngValue]="null" selected disabled>Target Schema</option>
                        }

                        <!-- <option *ngFor="let tgt of targetSchemas" [value]="tgt">{{ tgt }}</option> -->
                        @for( tgt of targetSchemas;track tgt){
                        <option [value]="tgt">{{ tgt }}</option>
                        }
                    </select>
                </div>
            </div>
        </div>

        @if(this.allData?.length > 0){
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Source</th>
                        <th>Target</th>
                    </tr>
                </thead>
                <!-- @for(conn of connectionWithCount;track conn){ -->
                <tbody>
                    <tr>
                        <td>Total Table Count</td>
                        <td>{{ filteredCount }}</td>
                        <td>{{ filteredCount }}</td>
                    </tr>
                    <tr>
                        <td>Row Count</td>
                        <td>{{ filteredSum| number }}</td>
                        <td>{{ filteredSum | number}}</td>
                    </tr>
                </tbody>
                <!-- } -->
            </table>
        </div>
        }@else {
        <!-- <div class="pb-3 text-center"><app-spinner /></div> -->
        <p class="text-center pb-3 text-danger">No Data for selected connections .</p>
        }
        <div class="qmig-card-body py-0">
            <div class="row">
                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                    <h3 style="font-size: 1.20em;" class="mt-3"> As per Latest
                        Validation</h3>
                </div>
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2 offset-md-2 offset-lg-2 offset-xl-2">
                    <select class="form-select form-small m-w100" [(ngModel)]="selectedSchema3"
                        (change)="onSchemaChange3(selectedSchema3)">
                        @if(migtypeid== '31'){
                        <option [ngValue]="null" selected disabled>Source Database </option>
                        }@else {
                        <option [ngValue]="null" selected disabled>Source Schema Name</option>
                        }
                        @for( loadsrc of latestsourceSchemas;track loadsrc){
                        <option [value]="loadsrc">{{ loadsrc }}</option>
                        }
                    </select>
                </div>
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
                    <select class="form-select form-small m-w100" [(ngModel)]="selectedtarget3"
                        (change)="ontargetChange3(selectedtarget3)">
                        @if(migtypeid== '31'){
                        <option [ngValue]="null" selected disabled>Target Database </option>
                        }@else {
                        <option [ngValue]="null" selected disabled>Target Schema Name</option>
                        }
                        @for( loadtgt of latesttargetSchemas;track loadtgt){
                        <option [value]="loadtgt">{{ loadtgt }}</option>
                        }
                    </select>
                </div>
            </div>
        </div>
        @if(this.tableData?.length > 0){
        <div class="table-responsive mt-3">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Source</th>
                        <th>Target</th>
                    </tr>
                </thead>
                <!-- @for(conn of connectionWithCount;track conn){ -->
                <tbody>
                    <tr>
                        <td>Total Table Count</td>
                        <td>{{ totalTableCount }}</td>
                        <td>{{ migratedTableCount }}</td>
                    </tr>
                    <tr>
                        <td>Row Count</td>
                        <td>{{ sourceRowCount | number}}</td>
                        <td>{{ targetRowCount | number}}</td>
                    </tr>
                </tbody>
                <!-- } -->
            </table>
        </div>
        }@else {
        <!-- <div class="pb-3 text-center"><app-spinner /></div> -->
        <p class="text-center pb-3 text-danger">No Data for selected connections .</p>
        }
       
        <div class="qmig-card-body">
            <div class="row">
                <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6">
                    <h3 style="font-size: 1.20em;" class="mt-2">Live Validation
                        <button class="btn btn-sync" (click)="getlivevalidationDetails()">
                            @if(getRunSpin4){
                            <app-spinner />
                            }@else{
                            <span class="mdi mdi-refresh"></span>
                            }
                        </button>
                    </h3>
                </div>
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
                    <select class="form-select form-small m-w100" [(ngModel)]="selectedSchema4"
                        (change)="onSchemaChange4(selectedSchema4)">
                        @if(migtypeid== '31'){
                        <option selected disabled>Source Database </option>
                        }@else {
                        <option selected disabled>Source Schema Name</option>
                        }
                        @for(sc1 of schemaList; track sc1;){
                        @if(this.migtypeid == '31'){
                        <option value="{{sc1.schema_name}}">{{sc1.schema_name}}</option>
                        }@else {
                        <option value="{{ sc1.schemaname }}"> {{sc1.schemaname }}</option>
                        }
                        }
                    </select>
                </div>
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
                    <select class="form-select form-small m-w100" [(ngModel)]="selectedtarget4"
                        (change)="ontgtSchemaChange4(selectedtarget4)">
                        @if(migtypeid== '31'){
                        <option selected disabled>Target Database </option>
                        }@else {
                        <option selected disabled>Target Schema Name</option>
                        }
                        @for ( sche1 of tgtSchemaList; track sche1) {
                        @if(this.migtypeid == '31'){
                        <option value="{{sche1.schema_name}}">{{sche1.schema_name}}</option>
                        }@else {
                        <option value="{{ sche1.schemaname }}"> {{sche1.schemaname }}</option>
                        }
                        }
                    </select>
                </div>
                @if(this.migtypeid=='38'){
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
                    <ng-select (change)="onTableChangelive(selectedTables)" [items]="liveObjectNamesList"
                        [multiple]="true" bindLabel="table_name" [closeOnSelect]="false" bindValue="table_name"
                        [placeholder]="'Table Names'" [(ngModel)]="selectedTables" class="multi-sel">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                [ngModelOptions]="{ standalone : true }" /> {{item.table_name}}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items">
                            @for(item of slicedData(items);track item; ){
                            <div class="ng-value">
                                {{item.table_name}}
                            </div>
                            }
                            @if(items.length > 3)
                            {
                            <div class="ng-value">
                                <span class="ng-value-label">{{items.length - 3}} more...</span>
                            </div>
                            }
                        </ng-template>
                    </ng-select>
                </div>
                }@else if(this.migtypeid=='31'){
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
                    <ng-select (change)="onTableChangelive(selectedTables)" [items]="liveObjectNamesList"
                        [multiple]="true" bindLabel="tableName" [closeOnSelect]="false" bindValue="tableName"
                        [placeholder]="'Table Names'" [(ngModel)]="selectedTables" class="multi-sel">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                [ngModelOptions]="{ standalone : true }" /> {{item.tableName}}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items">
                            @for(item of slicedData(items);track item; ){
                            <div class="ng-value">
                                {{item.tableName}}
                            </div>
                            }
                            @if(items.length > 3)
                            {
                            <div class="ng-value">
                                <span class="ng-value-label">{{items.length - 3}} more...</span>
                            </div>
                            }
                        </ng-template>
                    </ng-select>
                </div>
                } @else {
                <div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
                    <ng-select (change)="onTableChangelive(selectedTables)" [items]="liveObjectNamesList"
                        [multiple]="true" bindLabel="objectName" [closeOnSelect]="false" bindValue="objectName"
                        [placeholder]="'Table Names'" [(ngModel)]="selectedTables" class="multi-sel">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                [ngModelOptions]="{ standalone : true }" /> {{item.objectName}}
                        </ng-template>
                        <ng-template ng-multi-label-tmp let-items="items">
                            @for(item of slicedData(items);track item; ){
                            <div class="ng-value">
                                {{item.objectName}}
                            </div>
                            }
                            @if(items.length > 3)
                            {
                            <div class="ng-value">
                                <span class="ng-value-label">{{items.length - 3}} more...</span>
                            </div>
                            }
                        </ng-template>
                    </ng-select>
                </div>
                }

            </div>
        </div>
        <!-- <div class="row mt-3">
            <div class="col-xl-6 col-md-6 col-sm-6">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="DBSHead">
                            <p>Row Count</p>
                            <span class="mdi mdi-database"></span>
                        </div>
                        <div class="DBSBody">
                            <div class="DBSS">
                                <h3>{{ SourceRowCounts !== null && SourceRowCounts !== undefined ? (SourceRowCounts | numberSuffix) : 0 }} </h3>
                                <span>Source</span>
                            </div>
                            <div class="DBST">
                                <h3>{{TargetRowCount !== null && TargetRowCount !== undefined ? (TargetRowCount | numberSuffix) : 0 }} </h3>
                                <span>Target</span>
                            </div>
                            <div class="arrow-show">
                                <span class="mdi mdi-arrow-down"></span>
                            </div>
                        </div>
                        <div class="DBSFooter">
                            <p [ngClass]="getTrendInfo(gb, Tgtgb).textColor">
                                <span class="mdi" [ngClass]="getTrendInfo(SourceRowCounts, TargetRowCount).icon"></span>
                                {{ getTrendInfo(SourceRowCounts, TargetRowCount).label }}
                            </p>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar"
                                    [ngClass]="getTrendInfo(SourceRowCounts, TargetRowCount).color"
                                    [style.width.%]="getTrendInfo(SourceRowCounts, TargetRowCount).width"
                                    [attr.aria-valuenow]="getTrendInfo(SourceRowCounts, TargetRowCount).width" aria-valuemin="0"
                                    aria-valuemax="100">
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-xl-6 col-md-6 col-sm-6">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="DBSHead">
                            <p>Table Count</p>
                            <span class="mdi mdi-timetable"></span>
                        </div>
                        <div class="DBSBody">
                            <div class="DBSS">
                                <h3>{{totalTablesCount1 | numberSuffix}} </h3>
                                <span>Source</span>
                            </div>
                            <div class="DBST">
                                <h3>{{extractedTablesCount1| numberSuffix}} </h3>
                                <span>Target</span>
                            </div>
                            <div class="arrow-show">
                                <span class="mdi mdi-arrow-down"></span>
                            </div>
                        </div>
                        <div class="DBSFooter">
                            <p [ngClass]="getTrendInfo(gb, Tgtgb).textColor">
                                <span class="mdi"
                                    [ngClass]="getTrendInfo(totalTablesCount1, extractedTablesCount1).icon"></span>
                                {{ getTrendInfo(totalTablesCount1, extractedTablesCount1).label }}
                            </p>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar"
                                    [ngClass]="getTrendInfo(totalTablesCount1, extractedTablesCount1).color"
                                    [style.width.%]="getTrendInfo(totalTablesCount1, extractedTablesCount1).width"
                                    [attr.aria-valuenow]="getTrendInfo(totalTablesCount1, extractedTablesCount1).width"
                                    aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>
    
                    </div>
                </div>
            </div>
        </div> -->
        @if(totalTablesCount1 > 0 || extractedTablesCount1 > 0 ||SourceRowCounts > 0 || TargetRowCount > 0){
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th rowspan="2">#</th>
                        <th colspan="2" style="text-align: center;padding-right: 90px;">As per Database
                        </th>
                    </tr>
                    <tr>
                        <th>Source</th>
                        <th>Target</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Total Table Count</td>
                        <td>{{totalTablesCount1}}</td>
                        <td>{{extractedTablesCount1}}</td>

                    </tr>
                    <tr>
                        <td>Row Count</td>
                        <td>{{SourceRowCounts| number}}</td>
                        <td>{{TargetRowCount| number}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        }@else {
        <p class="text-center pb-3 text-danger">Please select source and target schema with table name.</p>
        <div class="pb-3 text-center">@if(isLoading){<app-spinner /> }</div>
        }
    </div>
    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="row">
                <div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
                    <h3 style="color: var(--color-secondary);font-size: 1.20em;" class="mt-4">CDC Stats
                    </h3>
                </div>
                <div class="col-12 col-sm-12 col-md-8 col-lg-8 col-xl-8">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
                            <div class="">
                                <label class="form-label w-auto me-3" for="name">Start Date</label>
                                <input type="datetime-local" class="form-control form-small w-100" id="fromData"
                                    #CPUfromDate>
                            </div>
                        </div>
                        <div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
                            <div class="">
                                <label class="form-label w-auto me-3" for="name">End Date</label>
                                <input type="datetime-local" class="form-control form-small w-100" id="toData"
                                    #CPUtoDate>
                            </div>
                        </div>
                        <div class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
                            <div class="">
                                <div class="body-header-button pt-2 mt-4">
                                    <button type="button" class="btn btn-upload btn-small w-100 me-1"
                                        (click)="executeCDCData(CPUfromDate.value, CPUtoDate.value)">
                                        <span></span> Execute
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @if(CDCDetails != null && CDCDetails != null){
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Extracted</th>
                        <th>Loaded</th>
                    </tr>
                </thead>
                @for(conn of CDCDetails;track conn){
                <tbody>
                    <tr>
                        <td>No. of Batches</td>
                        <td>{{conn.number_of_batchs}}</td>
                        <td>{{conn.number_of_batchs}}</td>
                    </tr>
                    <tr>
                        <td>Transaction_Count</td>
                        <td>{{conn.batch_length|number}}</td>
                        <td>{{conn.batch_length|number}}</td>
                    </tr>
                    <tr>
                        <td>Total_Transaction_Size(MB)</td>
                        <td>{{conn.batch_size_mb}}</td>
                        <td>{{conn.batch_size_mb}}</td>
                    </tr>
                    <tr>
                        <td>Thoughput(total rows/s)</td>
                        <td>{{conn.rows_per_sec|number}}</td>
                        <td>{{conn.rows_per_sec|number}}</td>
                    </tr>
                    <tr>
                        <td>MaxThoughput(total MB/s)</td>
                        <td>{{conn.max_throughput}}</td>
                        <td>{{conn.max_throughput}}</td>
                    </tr>
                    <tr>
                        <td>MinThroughput(MB/s)</td>
                        <td>{{conn.min_throughput}}</td>
                        <td>{{conn.min_throughput}}</td>
                    </tr>
                    <tr>
                        <td>LAG</td>
                        <td>{{formatLag(conn.extract_lag)}}</td>
                        <td>{{formatLag(conn.load_lag)}}</td>
                    </tr>
                    <tr>
                        <td>ErrorTranCount</td>
                        <td>0</td>
                        <td>0</td>
                    </tr>
                </tbody>
                }
            </table>
        </div>
        }@else {
        <p class="text-center pb-3 text-danger">Please select Start and End data's.</p>
        }

    </div>
    } @else {
    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <p class="text-center mt-2 text-danger">Please select source connection to view data.</p>
        </div>
    </div>
    }
</section>
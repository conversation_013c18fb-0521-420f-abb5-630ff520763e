import { CommonModule } from '@angular/common';
import { Component, Renderer2 } from '@angular/core';
import { Router, RouterLink, RouterLinkActive } from '@angular/router';
import { CommonService } from '../../../services/common.service';
import { AssessmentService } from '../../../services/assessment.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [RouterLink, RouterLinkActive, CommonModule],
  templateUrl: './header.component.html',
  styles: ``
})
export class HeaderComponent {
  username: any;
  showThis: boolean = false;
  projectIdMenu: Array<any> = [];
  projectName: Array<any> = [];

  bgColor: string = ''
  avatar: string = ''
  migtypeid: string = ''
  projectId: any
  constructor(
    private router: Router,
    private renderer: Renderer2, private common: CommonService, private assessmentService: AssessmentService,
  ) {
    this.username = localStorage.getItem('Email')
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
  }
  ngOnInit(): void {
    this.getProjectMenu();
    this.getmigType() 
    this.username = localStorage.getItem('Email')
    const getHashOfString = (str: string) => {
      let hash = 0;
      for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
      }
      hash = Math.abs(hash);
      return hash;
    };
    const normalizeHash = (hash: number, min: number, max: number) => {
      return Math.floor((hash % (max - min)) + min);
    };

    const generateHSL = (name: any) => {
      const hash = getHashOfString(name);
      const h = normalizeHash(hash, 0, 360);
      const s = normalizeHash(hash, 3, 255);
      const l = normalizeHash(hash, 9, 255);
      return [h, s, l];
    };
    this.bgColor = generateHSL(this.username).toString()
    var afterDot = this.username.substr(this.username.indexOf('.'));
    this.avatar = this.username.charAt(0) + '' + afterDot.charAt(1)
    //console.log(this.bgColor)
  }
  logout() {
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['/login']);
  }
  toggle() {
    if (document.body.classList.contains("sidebar-icon-only")) {
      this.renderer.removeClass(document.body, "sidebar-icon-only")
    }
    else {
      this.renderer.addClass(document.body, "sidebar-icon-only")
    }
  }
  migtype: any
  hideawr: boolean = false
  getmigType() {
    this,this.hideawr=false
    this.assessmentService.selectmigtype(this.migtypeid).subscribe((data: any) => {
      this.migtype = data['Table1'][0].migtype
      //console.log(this.projectId)
      if (this.projectId == "1243") {
        this.migtype = "Oracle_AlloyDB"
      }
      localStorage.setItem("migrationType", this.migtype);
      if (!this.migtype.includes("Oracle")) {
        this.hideawr = true
      }
      //console.log(this.migtype)
    })
  }
  getProjectMenu() {
    const ProjectId = JSON.parse(localStorage.getItem('project_id') || 'null');
    this.projectIdMenu.length = 0;
    this.common.getProjectMenu(ProjectId).subscribe((res) => {
      this.projectName = res['jsonResponseData']['Table1'];
      this.projectName.forEach((element: any) => {
        this.projectIdMenu.push(element.menu_id);
        //console.log(this.projectIdMenu)
      });
    });
  }
  selectedscreen: any;
  checked(menuscreen: any) {
    localStorage.setItem("menuscreenname", menuscreen);
  }

}

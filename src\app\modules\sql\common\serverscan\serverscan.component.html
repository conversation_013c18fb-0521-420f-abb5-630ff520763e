<!--- Bread Crumb --->
<div class="body-header">
    <div class="row">
        <div class="col-md-2 col-xl-2">
            <div class="pageName">
                <i class="mdi mdi-database-search"></i>
                <span>Server Scan</span>
            </div>
        </div>
        <div class="col-md-6 col-xl-6">
            <div class="custom_search ">
                <span class="mdi mdi-magnify"></span>
                <input type="text" placeholder="Search Document" class="form-control" [(ngModel)]="datachange"
                    (keyup)="onKey()">
            </div>
        </div>
        <div class="col-md-4 col-xl-4 text-right">
            <div class="body-header-button">
                <button type="button" class="btn btn-sign me-1" data-bs-toggle="offcanvas" data-bs-target="#demo1"
                    (click)="downloadZip()" [hidden]="!isCheckBoxSel"> Start Scan @if(reportSpin){<app-spinner />}</button>
                <button class="btn btn-upload" (click)="downloadScript()" data-bs-toggle="offcanvas"
                    data-bs-target="#demo"><span class="mdi mdi-cloud-download"></span>
                    &nbsp; Download Agent @if(dwnld_spin){<app-spinner />}
                </button>
            </div>
        </div>
    </div>
</div>

<div class="qmig-card">
    <h3 class="main_h px-3 pt-3">Scan Information</h3>
    <div class="table-responsive">
        <table class="table table-hover qmig-table" id="example" style="width:100%">
            <thead>
                <tr>
                    <th>
                        <div class="form-check">
                            <input type="checkbox" id="inlineCheckbox1" value="option1" (click)="selectAll($event)"
                                [(ngModel)]="masterSelected" (keyup)="onKey()" class="form-check-input"
                                [checked]="showCheckStatus">
                        </div>
                    </th>
                    <th>Domain</th>
                    <th>ComputerName</th>
                    <th>Core</th>
                    <th>Memory</th>
                    <th>Os</th>
                    <th>Disk</th>
                    <th>IP</th>
                    <th>Instance</th>
                    <th>Edition</th>
                    <th>Version</th>
                    <th>SqlType</th>
                </tr>
            </thead>
            <tbody>
                @for(user of serverResponse| searchFilter:datachange | paginate: { itemsPerPage: 10, currentPage:
                pageNumber} ; track user) {
                <tr>
                    <td>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="inlineCheckbox1" value="option1" #chck
                                (click)="checkboxselect($event, chck.value)" value="{{user.ip}}"
                                [checked]='user.isSelected'>
                        </div>
                    </td>
                    <td>{{user.domain}}</td>
                    <td>{{user.computerName}}</td>
                    <td>{{user.core}}</td>
                    <td>{{user.memory}}</td>
                    <td>{{user.os}}</td>
                    <td>{{user.disk}}</td>
                    <td>{{user.ip}}</td>
                    <td>{{user.instance}}</td>
                    <td>{{user.edition}}</td>
                    <td>{{user.version}}</td>
                    <td>{{user.sqlType}}</td>
                </tr>
                }
            </tbody>
        </table>
    </div>
    <div class="custom_pagination">
        <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
    </div>
</div>

<!-- Report & Logs -->
<div class="qmig-card mt-4">
    <h3 class="main_h px-3 pt-3">Reports & Logs</h3>
    <div class="accordion accordion-flush qmig-accordion" id="accordionFlushExample">
        <!-- Assessment Logs -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Inventory Reports
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="custom_search cs-r">
                        <span class="mdi mdi-magnify"></span>
                        <input type="text" placeholder="Search Reports" class="form-control" [(ngModel)]="datachanges"
                            (keyup)="onKey()">
                    </div>
                </div>
                <!-- downloadFile -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>File Name</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(validrepo of InventoryFiles |searchFilter: datachanges|paginate:{
                            itemsPerPage: 10, currentPage: pageNumber, id:'third'};
                            track validrepo;)
                            {
                            <a data-toggle="tooltip" data-placement="bottom" download="" id="validrepo"
                                class="btn btn-download table-data-fixed" (click)="downloadFile(validrepo)"
                                title="{{ validrepo.fileName }}"></a>
                            <tr>
                                <td>{{p*pi+$index+1-pi}}</td>
                                <td>{{validrepo.fileName }}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(validrepo)"
                                        title="{{ validrepo.fileName }}">{{
                                        validrepo.fileName }}
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty list of Reports</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                </div>
            </div>
        </div>

        <!-- Page-Activity Log -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    InventoryFiles
                </button>
            </h2>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <!-- Run No -->
                        <div class="col-sm-6 col-md-4 col-xl-2">
                            <div class="form-group ">
                                <select class="form-select" (change)="getinvFiles(Myselect.value)" #Myselect>
                                    <option selected value="">Select Connection Type</option>
                                    @for( list of InvFolders;track list;){
                                    <option value="{{ list.dirPath }}">
                                        {{ list.dirName }}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-8 col-xl-10">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Reports" class="form-control"
                                    [(ngModel)]="datachanges1" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(validrepo of invenFiles |searchFilter: datachanges1|paginate:{
                                itemsPerPage: 10, currentPage: pageNumber1, id:'first'};
                                track validrepo;)
                                {
                                <a data-toggle="tooltip" data-placement="bottom" download="" id="validrepo"
                                    class="btn btn-download table-data-fixed" (click)="downloadFile(validrepo)"
                                    title="{{ validrepo.fileName }}"></a>
                                <tr>
                                    <td>{{p*pi+$index+1-pi}}</td>
                                    <td>{{validrepo.fileName }}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(validrepo)"
                                            title="{{ validrepo.fileName }}">{{
                                            validrepo.fileName }}
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <!--- Pagination --->
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber1 = $event" id="first"></pagination-controls>
                    </div>
                </div>
                <div class="col-md-6 col-xl-6">
                    <div class="custom_search mb-2">
                        <span class="mdi mdi-magnify"></span>
                        <input type="text" placeholder="Search Files" class="form-control" [(ngModel)]="datachanges1"
                            (keyup)="onKey()">
                    </div>
                </div>
                <!-- downloadFile -->
                <div class="body-main mt-4">
                    <div class="qmig-card">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(validrepo of invenFiles |searchFilter: datachanges1|paginate:{
                                itemsPerPage: 10, currentPage: pageNumber2, id:'third'};
                                track validrepo;)
                                {
                                <a data-toggle="tooltip" data-placement="bottom" download="" id="validrepo"
                                    class="btn btn-download table-data-fixed" (click)="downloadFile(validrepo)"
                                    title="{{ validrepo.fileName }}"></a>
                                <tr>
                                    <td>{{p*pi+$index+1-pi}}</td>
                                    <td>{{validrepo.fileName }}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(validrepo)"
                                            title="{{ validrepo.fileName }}">{{
                                            validrepo.fileName }}
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>

                    </div>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="pageNumber2 = $event" id="third"></pagination-controls>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="offcanvas offcanvas-end" tabindex="-1" id="demo1">
    <div class="offcanvas-header">
        <h4 class="main_h">Server Scan</h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <!--- server Name --->
    <div class="offcanvas-body">
        <div class="form-group">
            <form class="form add_form" autocomplete="off">
                <div class="row">
                    <!-- <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="d-block" for="ipaddress">SQL Server IP</label>
                                                <input class="form-control" placeholder="IP Address"
                                                    [value]='getIPfromCheck'>
                                            </div>
                                        </div>  -->
                    <div class="col-md-12">
                        <div class="form-group">
                            <div class="form-check">
                                <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input"
                                        (click)="triggerCheckSelect($event)"> <i class="input-helper"></i> Windows Auth
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
    <div class="offcanvas-header">
        <h4 class="main_h">Key Details </h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <!--- Key Name --->
    <div class="offcanvas-body">
        <div class="form-group">
            <form class="form add_form" autocomplete="off">
                <div class="row">

                    <div class="col-md-12 mt-1" *ngIf="isTriggerCheckSelected==false">
                        <div class="form-group">
                            <label class="d-block" for="username">Key</label>
                            <input class="form-control" id="sqluser" type="text" value={{key}}>
                        </div>
                    </div>

                    <div class="col-md-6" *ngIf="isTriggerCheckSelected==false">
                        <div class="form-group">
                            <label class="d-block" for="password">SQL Password</label>
                            <input class="form-control" id="sqlpwd" type="password" placeholder="Password">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="text-right">
                            <button class="btn btn-sign btn-formAdd" data-bs-toggle="offcanvas" data-bs-target="#demo1"
                                (click)="triggerJob()">Start @if(isTriggerCheckSelected){<app-spinner />}</button>
                        </div>
                    </div>

                </div>
            </form>
        </div>

        <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
            <div class="offcanvas-header">
                <h4 class="main_h">Server Scan</h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
            </div>
            <!--- Key Name --->
            <div class="offcanvas-body">
                <div class="form-group">
                    <h4 class="card-title"> Key Details </h4>
                    <hr>
                    <form class="form add_form" autocomplete="off">
                        <div class="row">

                            <div class="col-md-12 mt-1" *ngIf="isTriggerCheckSelected==false">
                                <div class="form-group">
                                    <label class="d-block" for="username">Key</label>
                                    <input class="form-control" id="sqluser" type="text" value={{key}}>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-12">
            <dash-tabs></dash-tabs>
        </div>
    </div>


    <!--- Main Content --->
    <div class="qmigTabs mt-3">
        <div class="row">
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" (change)="onConnectionChange($event)">
                        <option selected disabled>Select Source Connection</option>
                        @for ( src of ConsList; track src) {
                        <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <select class="form-select form-small m-w100"  (change)="onTargetConnectionChange($event)">
                    <option value="" selected>Select Target Connection</option>
                    @for(tgt of tgtList;track tgt; ){
                    <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                    }
                </select>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <select class="form-select form-small m-w100" #tabname (change)="onSchemaChange($event)">
                    <option value="" selected>Select Source Schema</option>
                    @for(scma of scrSchemaData; track scma;){
                    <option value="{{scma.schema}}">{{scma.schema}}</option>
                    }
                </select>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" (change)="ontgtschemaSelect($event)">
                        <option selected disabled>Select Target Schema Name</option>
                        @for ( sche of tgtschemaData; track sche) {
                        <option value="{{ sche.target_schema }}"> {{sche.target_schema }}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <select class="form-select form-small m-w100" (change)="ontableSelect($event)">
                    <option value="" selected>Select Tables </option>
                    @for (obj of tablename; track obj) {
                        <option [value]="obj.table_name">{{ obj.table_name }}</option>
                      }
                </select>
            </div>
        </div>
    </div>
    <div class="row mt-3 dashCH">
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total Source Count</h5>
                    <h1><span class="mdi mdi-database"></span> {{ totalCount }}</h1>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total Target Count</h5>
                    <h1><span class="mdi mdi-database-sync"></span> {{ convertedCount }}</h1>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total Progress</h5>
                    <div style="display: block; width: 100%; overflow-x: auto;">
                        <canvas id="myDoughnutChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="lineChartData" [options]="lineChartOptions"
                            [legend]="lineChartLegend" [plugins]="lineChartPlugins">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Data Migration vs Pending by Connection</h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart [datasets]="sourceVSChartData"
                            [labels]="sourceVSChartLabels" [options]="sourceVSChartOptions"
                            [legend]="sourceVSChartLegend">
                        </canvas>
                    </div>
                    <!-- @if(sourceVSChartLabels.length > 0){
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart [datasets]="sourceVSChartData"
                            [labels]="sourceVSChartLabels" [options]="sourceVSChartOptions"
                            [legend]="sourceVSChartLegend">
                        </canvas>
                    </div>
                    }@else {
                    <p class="text-center m-0 w-100">No data available for the selected connection</p>
                    } -->
                </div>
            </div>
        </div>
        <div class="col-md-6 mt-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5> Match and Missing Count by Schema</h5>
                        </div>
                        <!-- <div class="col-md-4">
                            <div class="form-group" [hidden]="Schemadisable">
                                <select class="form-select form-small mt-0 me-0" (change)="onSchemaChartChange($event)">
                                    <option value="" selected>Select Schema</option>
                                    @for(schm of SchemaList; track schm;) {
                                    <option [value]="schm.schema_name">{{ schm.schema_name }}</option>
                                    }
                                </select>
                            </div>
                        </div> -->
                    </div>
                    <div style="display: block; width: 100%; overflow-x: auto;">
                        <canvas width="400" height="200" baseChart #chart1="base-chart"
                            [datasets]="longRunningChartData" [labels]="longRunningChartLabels"
                            [options]="longRunningChartOptions" [legend]="longRunningChartLegend">
                        </canvas>
                    </div>
                    <!-- @if(longRunningChartLabels.length > 0){
                   
                    <div style="position: relative; display: block; width: 100%; overflow-x: auto;">
                        <canvas style="max-width: 100%; height: auto;" baseChart #chart1="base-chart"
                            [datasets]="longRunningChartPaginatedData" [labels]="longRunningChartPaginatedLabels"
                            [options]="longRunningChartOptions" [legend]="longRunningChartLegend">
                        </canvas>
                        <div class="pagination-controls">
                            <button class="prev-button" (click)="schprevPage()"
                                [disabled]="longRunningChartCurrentPage === 1">&lt;</button>
                            <button class="next-button" (click)="schnextPage()"
                                [disabled]="longRunningChartCurrentPage === longRunningChartTotalPages">&gt;</button>
                        </div>
                    </div>
                    } @else {
                    <p class="text-center m-0 w-100">No data available for the selected connection</p>
                    } -->
                </div>
            </div>
        </div>
        <div class="col-md-6 mt-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Object, Match and Missing Count by Table Type</h5>
                    <div style="display: block; width: 100%; overflow-x: auto;">
                        <canvas width="400" height="200" baseChart #chart1="base-chart" [datasets]="sObjectChartData"
                            [labels]="sObjectChartLabels" [options]="sObjectChartOptions" [legend]="sObjectChartLegend">
                        </canvas>
                    </div>
                    <!-- @if(sObjectChartPaginatedLabels.length > 0){
                    <div style="position: relative; display: block; width: 100%; overflow-x: auto;">
                        <canvas style="max-width: 100%; height: auto;" baseChart #chart1="base-chart"
                            [datasets]="sObjectChartPaginatedData" [labels]="sObjectChartPaginatedLabels"
                            [options]="sObjectChartOptions" [legend]="sObjectChartLegend">
                        </canvas>
                        <div class="pagination-controls">
                            <button class="prev-button" (click)="prevPage()"
                                [disabled]="sObjectChartCurrentPage === 1">&lt;</button>
                            <button class="next-button" (click)="nextPage()"
                                [disabled]="sObjectChartCurrentPage === sObjectChartTotalPages">&gt;</button>
                        </div>
                    </div>
                    }@else {
                    <p class="text-center m-0 w-100">No data available for the selected connection</p>
                    } -->
                </div>
            </div>
        </div>
    </div>
</section>
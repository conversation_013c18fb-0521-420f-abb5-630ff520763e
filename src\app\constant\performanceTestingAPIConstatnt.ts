
export const performanceAPIConstant = {   

    getConList: 'GetConnectionList?projectId=',
    GetReqData: 'GetRequestTableData?projectId=',
    SchemaListSelect: 'GetSchemaList?projectId=',
    Execute: 'PerfTestCommand',
    selectedfileName: 'GetFilesFromDirectory?path=',
    GetFilesFromDir: 'GetFilesFromDirectory?path=',
    deleteFile: 'DeleteFiles?path=',
    fileDownload: 'DownloadLargeFile?filePath=',
    GetDuplicateIndexData: 'GetDuplicateIndexData',
    GetPerfIndexMonitorData: 'GetPerfIndexMonitorData',
    GetperfSchemas: 'GetperfSchemas?conid=',
    GetTablePartition:'GetTablePartition?Conid=',
    SubmitShowLogs:'GetPlanLogsData',
    getLongRunningQueries:'getLongRunningQueries',
    GetSuggestedIndex:'GetSuggestedIndex?Conid=',
    UploadCloudFiles:'UploadCloudFiles',
    getFiles: 'GetFilesFromDirectory?path=',
    GetUnusedIndexes:'UnusedIndexes',
    GetUsedIndexes:'UsedIndexes',
    BloatedIndexes:'BloatedIndexes',
    GetRatioIndexes:'RatioIndexes?Conid=',
    Perflogstatus:'GetPerflogstatus',
    GetPerflogFiles:'GetPerflogFiles',
    QueryInsightBasedoncolumns:'QueryInsightCoulumn',
    RecomondedIndexCount:'RecomondedIndexCount?Conid=',
    RecomondedIndexTable:'RecomondedIndexTable?Conid=',
    QueryInsightCoulumnByDate:'QueryInsightCoulumnByDate',
    PerfIndexStatusUpdate:'PerfIndexStatusUpdate',
    PerfSchemanamewithAll:'PerfSchemanamewithAll?Conid=',
    BloatedIndexExcel:'BloatedIndexesExcel',
    IndexeExcelDownLoad:'IndexeExcelDownLoad',
    IndexeTimeStamp:'IndexeTimeStamp?Conid=',
    DroppedIndexCount:'DroppedIndexCount?Conid=',
    DroppedIndexUpdate:'DroppedIndexUpdate',
    FailedIndexes:'FailedIndexes?Conid=',
    IndexStatementExecute:'IndexStatementExecute',
    RecomondedPartions:'RecomondedPartions',
    IndexTuningFiledlog:'IndexTuningFiledlog?Conid=',
    uploadLargeFiles: 'UploadCloudFiles',
    downloadLargeFiles: 'DownloadLargeFile?filePath=',
    DuplicateIndexSchema:'DuplicateIndexSchema',
    PerfPartitionLogstatus: 'PerformenencePartitionStatus',
    FetchAllProcedures: 'FetchAllProcedures?conid=',
    GetPerfLogs:'GetPartitionLogs',
}

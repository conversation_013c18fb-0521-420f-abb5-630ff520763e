import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GetDBConnections, UpdateConnection, addPrjConnection, addconnection, addfiletype, deleteDbConnection, deleteFile, documentsList, getfiletype, redisCommand, reqObj, srctgtConfInsert, testSourceConn, updatePrjConnection } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { Title } from '@angular/platform-browser';


declare let $: any;
@Component({
  selector: 'app-connections',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, RouterOutlet],
  templateUrl: './connections.component.html',
  styles: ``
})
export class ConnectionsComponent {
  /*--Globle Variables--*/
  projectId: string;

  /*--Add button form group--*/

  connectionForm: FormGroup = new FormGroup({});
  addupdateDetails: boolean = false;

  /*--For selecting files--*/
  selectedfiletypeName: any
  selectedFile: any;
  fileTypeId: any;
  userData: any = [];

  /*--For connections --*/
  FileConnections: any;
  DatabaseType: any;
  condata: any;
  prjSrcTgt: any = {};
  DBType: any = [
    { values: 'Oracle', option: 'Oracle' },
    { values: 'PostgreSQL', option: 'PostgreSQL' },
    { values: 'MySQL', option: 'MySQL' },
    { values: 'SQL', option: 'DataSQLbase' },
    { values: 'CosmosDB', option: 'CosmosDB' },
  ];

  databases: any = [
    { value: 'S', option: 'Source' },
    { value: 'T', option: 'Target' }
  ];
  ConData: any;
  token: any;
  sourceButton: boolean = true;
  targetButton: boolean = true;
  auditButton: boolean = true;
  datachange: any;
  srctgtconfInsertrequest: any;


  /*--For Delete file connections --*/
  deleteFileResponse: any;
  fileConId: any;

  /*--For File selection --*/
  posterTitle = 'No file selected';
  fileResponse: any;
  requiredFile: any = [];


  /*--Get Roles --*/
  rolesData: any = [];

  /*-- getPrjExeLogSelect--*/
  exeLogData: any = [];
  ExeLog: any = {};

  /*--For Paginations--*/
  pageNumber: number = 1;

  //File type

  addFileForm: FormGroup = new FormGroup({});
  filespin: boolean = false;
  fileName: string = 'No file selected';
  fileResult: any;
  fileNameget: any;
  // Migration Source

  migrationSource = [
    { values: 'D', option: 'Database' },
  ];
  migsrcTypevalue: any;
  Hidelable: boolean = false;
  dbTypeHide: boolean = true;
  FileTable: boolean = false;
  common: any = [];
  filetypelist: any;
  prjSrcTgtD: any = {};
  requiredDatabase: any = [];
  connectionPrj: boolean = true;
  name: any;

  //Source connection

  migsrctgtValue: any = '';
  setFolderName: any;
  sourceSevice: boolean = false;

  //for upload button
  uploadSpin: boolean = false;

  //test connection button
  testconnectionSpin: boolean = false;

  //Add button
  addfiletypelist: any;
  passwordtextCheck: boolean = true;//password validation
  usernametextCheck: boolean = true;
  testRes: string = '';
  downloadUrl: any;// download

  ProjectSrctgtconfData: any;
  infraSelectData: any = [];
  migtypeid: any;
  migtype: any;

  pageName: string = ''

  constructor(private titleService: Title, private toast: HotToastService, private assessmentService: AssessmentService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit() {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.migtypeid = localStorage.getItem("migtypeid")
    this.getmigType()
    this.connectionForm = this.formBuilder.group({
      dbconname: ['', [Validators.required]],
      port: [''],
      dbname: ['', [Validators.required]],
      dbschema: ['', [Validators.required]],
      dbhost: ['', [Validators.required]],
      dbuserid: ['', [Validators.required]],
      dbpassword: ['', [Validators.required]],
      serviceName: [''],
      parallelprocess: ['', [Validators.required]],
      id: [''],
      Username: [''],
      migenv: [''],
      dynamoUser: [''],
      dynamoPassword: ['']
    });
    this.addFileForm = this.formBuilder.group({
      filetypename: ['', [Validators.required]],
    });
    // if (this.migtypeid == "20") {
    //   let obj =
    //     { value: 'K', option: 'Kafka' };
    //   this.databases.push(obj)
    // }
    this.getDBConnections();
    this.Getfiletype();
  }

  /*-- connection Details --*/
  getmigType() {
    this.assessmentService.selectmigtype(this.migtypeid).subscribe((data: any) => {
      this.migtype = data['Table1'][0].migtype
      if (this.projectId == "1243") {
        this.migtype = "Oracle_AlloyDB"
      }
    })
  }
  getDBConnections() {
    this.assessmentService.GetDBConnections(this.projectId.toString()).subscribe((data: GetDBConnections) => {
      this.ProjectSrctgtconfData = data['Table1'];
      console.log(this.ProjectSrctgtconfData)
      this.migsrctgtValue != '' ? this.filterData(this.migsrctgtValue) : ''
    })
  }
  Getfiletype() {
    this.assessmentService.getfiletype(this.projectId.toString()).subscribe((data: getfiletype) => {
      this.filetypelist = data['Table1'];
    })
  }
  /*-- Add Button --*/
  addProjectConnection(Data: any) {
    this.uploadSpin = true;
    let connectionType = localStorage.getItem('connectionType')
    const addProjectObject: any = {
      token: this.token,
      project_id: Number(this.projectId),
      migsrctgt: this.migsrctgtValue,
      migsrctype: this.migsrcTypevalue,
      migenv: this.DatabaseType,
      filename: 'null',
      dbconname:Data.dbconname,
      dbhost: this.migtypeid =="47"?"null":Data.dbhost,
      dbname: Data.dbname,
      schema:this.migtypeid =="49"?"null": Data.dbschema,
      port: Data.port===null?'0000':Data.port,
      dbuserid:  this.migtypeid =="47"?"null":Data.dbuserid,
      dbpassword:  this.migtypeid =="47"?"null":Data.dbpassword,
      parallel: Data.parallelprocess === null ? '00' : Data.parallelprocess.toString(),
      json: 'service_name=' + Data.serviceName,
    };
    if (this.migtypeid == "35" || this.migtypeid == "34") {
      addProjectObject['dynamoUsername'] = Data.dynamoUser
      addProjectObject['dynamoPassword'] = Data.dynamoPassword
      addProjectObject['isDynamo'] = true
    }else if(this.migtypeid == '31'){
      addProjectObject['target_mapping'] = ""
    }
    this.uploadSpin = true;
    this.assessmentService.addConnection(addProjectObject).subscribe((data: any) => {
      this.condata = data['jsonResponseData']['Table1'];
      if (this.condata[0].v_status != "Error = duplicate key value violates unique constraint \"idx_unique\"") {
        this.uploadSpin = false;
        $('#demo').offcanvas('hide');
        this.toast.success('Connection Added Successfully');
        this.getDBConnections();
      }
      else if (this.condata[0].v_status == "Error = duplicate key value violates unique constraint \"idx_unique\"") {
        this.uploadSpin = false;
        $('#demo').offcanvas('hide');
        this.toast.error('Connection Already Added');
        this.getDBConnections();
      }
      // if (data.Status == "Please Create Infrastructure before adding Connection") {
      //   this.uploadSpin = false;
      //   $('#demo').offcanvas('hide');

      //   this.toast.error(data.Status)
      // }
      // else {
      //   this.condata = data['jsonResponseData']['Table1'];
      //   if (this.condata[0].v_status != "Error = duplicate key value violates unique constraint \"idx_unique\"") {
      //     this.uploadSpin = false;
      //     this.filterData(this.migsrctgtValue);
      //     $('#demo').offcanvas('hide');
      //     this.toast.success('Connection Added Successfully');
      //   }
      //   if (this.condata[0].v_status == "Error = duplicate key value violates unique constraint \"idx_unique\"") {
      //     this.uploadSpin = false;
      //     $('#demo').offcanvas('hide');
      //     this.toast.error('Connection Already Added');
      //   }
      // }

    }, () => {
      $('#demo').offcanvas('hide');
      this.toast.error('Something Went Wrong!');
      this.uploadSpin = false;
    });

  }
  /*-- For PopUPs --*/
  openPopup() {
    this.connectionForm.reset();
    this.addupdateDetails = true;
  }
  openPopups() {
    this.addFileForm.reset();
    this.addupdateDetails = true;
  }

  /*-- Form Validation --*/
  get f(): any {
    return this.connectionForm.controls;
  }


  /*-- Migration Source --*/
  selectFilesAndDatabase(value: any) {
    this.migsrcTypevalue = value;
    this.Hidelable = true
    if (value == 'F') {
      this.dbTypeHide = true;
      this.FileTable = true
    }
    if (value == 'D') {
      this.dbTypeHide = false;
      this.FileTable = false
    }
    if (value == 'F') {
      this.prjSrcTgtD = {
        value: 'F',
      };
      this.common = this.filetypelist;
      this.requiredFile = this.common.filter((item: any) => {
        return item.value == 'F';
      });
    } else {
      this.prjSrcTgtD = {
        value: 'D',
      };
      this.common = this.databases;
      this.requiredDatabase = this.common.filter((item: any) => {
        return item.value == 'D';
      });
    }
    this.connectionPrj = false;
  }
  /*-- File Types --*/
  getProjectSrctgtconfSelectD(value: any) {
    this.migsrctgtValue = value;
    if (value == "S") {
      this.DatabaseType = (this.migtype || "").split("_")[0];
      this.sourceButton = false;
      this.targetButton = true;
      this.auditButton = true
      this.sourceSevice = true;
      if (this.migtypeid == "31") {
        if (this.DatabaseType == "Mariadb") {
          this.DatabaseType = this.migtype.split("_")[0].replace(/[\d\/]/g, "")
        }
      }
      this.connectionForm = this.formBuilder.group({
        dbconname: ['', [Validators.required]],
        port: ['', [Validators.required]],
        dbname: ['', [Validators.required]],
        dbschema: ['', [Validators.required]],
        dbhost: ['', [Validators.required]],
        dbuserid: ['', [Validators.required]],
        dbpassword: ['', [Validators.required]],
        serviceName: ['', [Validators.required]],
        parallelprocess: ['', [Validators.required]],
        id: [''],
        migenv: [''],
        dynamoUser: [''],
        dynamoPassword: ['']
      });
      if (this.migtypeid == "31") {
        this.clearValidators(['dbschema'])
        this.updateAllControls()
      }
      else if(this.migtypeid == "49"){
        this.clearValidators(['parallelprocess','dbschema','serviceName'])
        this.updateAllControls()
      }
      else {
        this.setRequiredValidators(['dbschema'])
        this.updateAllControls()
      }
    }
    else if (value == "T") {
      this.DatabaseType = (this.migtype || "").split("_")[1]?.replace(/[\d\/]/g, "") || "";
      this.sourceButton = true;
      this.targetButton = false;
      this.auditButton = true
      this.sourceSevice = false;
      this.connectionForm = this.formBuilder.group({
        dbconname: ['', [Validators.required]],
        port: ['',],
        dbname: ['', [Validators.required]],
        dbschema: ['', [Validators.required]],
        dbhost: ['', [Validators.required]],
        dbuserid: ['', [Validators.required]],
        dbpassword: ['', [Validators.required]],
        parallelprocess: ['',],
        serviceName: ['',],
        id: [''],
        migenv: [''],
        dynamoUser: [''],
        dynamoPassword: [''],
        target_mapping:['']
      });
      if (this.migtypeid == "40" || this.migtypeid == "48" || this.migtypeid == "46") {
        this.clearValidators(['parallelprocess', 'port', 'dbschema'])
        this.updateAllControls()
      }
      else if(this.migtypeid == "49"){
        this.clearValidators(['parallelprocess','dbschema'])
        this.updateAllControls()
      }
      else  if(this.migtypeid == "31" ){
        this.clearValidators(['dbschema'])
        this.updateAllControls()
      }
      else if (this.migtypeid == "47") {
        this.clearValidators(['dbhost', 'dbuserid', 'dbpassword'])
        this.updateAllControls()
      }
      else {
        this.setRequiredValidators(['parallelprocess', 'port', 'dbschema'])
        this.updateAllControls()
      }
    }
    else {
      this.DatabaseType = this.migtype.split("_")[1].replace(/[\d\/]/g, "")
      this.sourceButton = true;
      this.targetButton = true;
      this.auditButton = false
      this.sourceSevice = false;
      this.connectionForm = this.formBuilder.group({
        dbconname: ['', [Validators.required]],
        port: ['', [Validators.required]],
        dbname: ['', [Validators.required]],
        dbschema: ['', [Validators.required]],
        dbhost: ['', [Validators.required]],
        dbuserid: ['', [Validators.required]],
        dbpassword: ['', [Validators.required]],
        parallelprocess: ['', [Validators.required]],
        serviceName: ['',],
        id: [''],
        migenv: ['']
      });
    }
    this.prjSrcTgtD.projectId = this.projectId.toString();
    this.prjSrcTgtD.migsrcType = 'D';
    this.prjSrcTgtD.connectionName = 'null';
    if (value == 'S') {
      this.sourceButton = false;
      this.targetButton = true;
      this.auditButton = true;
      this.name = 'Source';
    }
    if (value == 'T') {
      this.targetButton = false;
      this.sourceButton = true;
      this.auditButton = true;
      this.name = 'Target';
    }
    if (value == 'A') {
      this.targetButton = true;
      this.sourceButton = true;
      this.auditButton = false;
      this.name = 'Audit';
    }

    this.connectionPrj = true;

    if (this.migtypeid == "31") {

    }
  }

  onFileSelected(event: any) {
    if (event.target.files.length > 0) {
      this.posterTitle = event.target.files[0].name;
    }
  }

  sendValue($event: any): void {
    this.readThis($event.target);
  }

  readThis(inputValue: any): void {
    this.fileName = inputValue.files[0].name;
    const myReader: FileReader = new FileReader();
    myReader.onloadend = () => {
      this.fileResult = myReader.result;
    };
    myReader.readAsDataURL(inputValue.files[0]);
  }


  /*--Update Button in Table --*/
  updateRecord(Data: any) {
    this.testRes = ''
    this.addupdateDetails = false;
    this.connectionForm.patchValue({
      dbconname: Data.conname,
      dbhost: Data.dbhost,
      dbname: Data.dbname,
      dbschema: Data.dbschema,
      port: Data.dbport,
      dbuserid: Data.dbuserid,
      dbpassword: Data.dbpassword,
      serviceName: Data.service_name,
      migenv: Data.migenv,
      parallelprocess: Data.parallelprocess.toString(),
      id: parseInt(Data.Connection_ID)
    });
    $('#exampleModalCenter').modal('show');
  }
  /*--Update Button  --*/
  update(data: any) {
    this.uploadSpin = true;
    data.project_id = this.projectId;
    data.schema = data.dbschema;
    data.json = 'service_name=' + data.serviceName,
      data.migsrctype = this.migsrcTypevalue;
    data.migsrctgt = this.migsrctgtValue;
    data.parallel = data.parallelprocess.toString()
    data.fileName = 'null';
    this.assessmentService.UpdateConnection(data).subscribe(() => {
      this.getDBConnections();
      this.uploadSpin = false;
      $('#demo').offcanvas('hide');
      this.toast.success('Updated Successfully');
    });
    this.getDBConnections();
  }
  /*--source and target connection insert  --*/
  srctgtConfInsert() {
    const obj: srctgtConfInsert = {
      projectId: this.projectId.toString(),
      migsrctype: "F",
      migsrctgt: "S",
      migenv: "PRD",
      filename: this.fileName,
      filetypeId: this.fileTypeId,
      dbconname: "null",
      dbhost: "null",
      dbname: "null",
      dbschema: "null",
      dbport: "null",
      dbuserid: "null",
      dbpassword: "null"
    }
    this.assessmentService.srctgtConfInsert(obj).subscribe((data) => {
      this.srctgtconfInsertrequest = data;
    })
  }
  /*--Add File */
  Addfiletype(data: any) {
    const fileinsertobj: addfiletype = {
      projectId: this.projectId.toString(),
      fileType: data.filetypename,
    }
    this.filespin = true;
    this.assessmentService.addfiletype(fileinsertobj).subscribe((data: any) => {
      this.addfiletypelist = data['jsonResponseData']['Table1'];
      if (data.message == "Success")
        this.filespin = false;
      this.Getfiletype();
      $('#demo').offcanvas('hide');
      this.toast.success('Successfully Inserted');
    })
    this.Getfiletype();
  }
  /*--selectedfile --*/
  selectedfiletype(value: any) {
    this.fileTypeId = value;
    const selectedfile = this.filetypelist.filter((item: any) => {
      return item.file_type_id === value;
    });
    this.userData = [];
    this.selectedfiletypeName = selectedfile[0]?.filetype;
  }
  /*--FIleConnections --*/
  getFIleConnections() {
    this.assessmentService.GetFileConnections(this.projectId).subscribe((data: any) => {
      this.FileConnections = data['Table1'];
      let i, j;
      if (this.FileConnections != undefined) {
        for (i = 0; i < this.FileConnections.length; i++) {
          for (j = 0; j < this.userData.length; j++) {
            if (this.userData[j].fileName == this.FileConnections[i].filename) {
              this.FileConnections[i].downloadUrl = this.userData[j].fileUrl;
            }
          }
        }
      }
    })
  }
  /*--deleteFile --*/
  deleteFileShareFile(fileName: any) {
    const obj = {
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Source ZIP File",
      fileName: fileName,
      projectId: this.projectId.toString()
    }
    this.assessmentService.deleteFileShareFile(obj).subscribe((data: any) => {
      this.deleteFileResponse = data['Table1'];
      this.deleteFileConnections(this.fileConId);
    })
  }

  deleteFileConnections(connectionId: any) {
    const obj = {
      connectionId: connectionId,
      projectId: this.projectId
    }
    this.assessmentService.deleteFileCon(obj).subscribe((data: any) => {
      this.deleteFileResponse = data['Table1'];
      this.getFIleConnections()
    })
  }

  deleteFilCon(conId: any) {
    this.fileConId = conId;
    const fileinfo = this.FileConnections.filter((item: any) => {
      return item.Connection_ID = conId
    })
    this.deleteFileShareFile(fileinfo[0].filename);
  }
  /*--for files --*/
  setupFolder(data: any) {
    if (data == 'Source ZIP File') {
      this.setFolderName = 'Zip_Files';
    } else if (data == 'AWR File') {
      this.setFolderName = 'awr_report';
    } else if (data == 'Config File') {
      this.setFolderName = 'Zip_Files';
    } else if (data == 'Infrastructure File') {
      this.setFolderName = 'Infrastructure';
    } else if (data == 'Data Masking Reference File') {
      this.setFolderName = 'datamasking';
    } else if (data == 'Work Load File') {
      this.setFolderName = 'workload';
    }

  }

  sendEye() {
    this.passwordtextCheck = !this.passwordtextCheck;
  }
  sendEyeuser() {
    this.usernametextCheck = !this.usernametextCheck;
  }
  
  onFileSelected1(event: any) {
    const file: File = event.target.files[0];
    this.selectedFile = file
  }
  /*---Database Connection Details Table delete--*/
  deleteDbConnection(id: any) {
    const obj: deleteDbConnection = {
      projectId: this.projectId,
      id: id
    }
    this.assessmentService.deleteDbConnection(obj).subscribe((data: any) => {
      this.getDBConnections();
    })
  }
  /*---filterData--*/
  filterData(con: any) {
    localStorage.setItem('connectionType', con)
    if (con == "S") {
      this.ConData = []
      this.ConData = this.ProjectSrctgtconfData.filter((item: any) => {
        return item.migsrctgt == "S"
      })
    }
    if (con == "T") {
      this.ConData = []
      this.ConData = this.ProjectSrctgtconfData.filter((item: any) => {
        return item.migsrctgt == "T"
      })
    }
    if (con == "K") {
      this.ConData = []
      this.ConData = this.ProjectSrctgtconfData.filter((item: any) => {
        return item.migsrctgt == "K"
      })
    }
  }
  /*---test connection--*/
  TestConnection(formData: any) {
    this.testconnectionSpin = true
    const obj: testSourceConn =
    {
      dbHost: formData.dbhost,
      dbName: formData.dbname,
      dbPort: formData.port,
      dbUserid: formData.dbuserid,
      dbPassword: formData.dbpassword
    }
    if (this.migtypeid == "49") {
      this.assessmentService.testRedisConn(obj).subscribe((data: any) => {
        this.testconnectionSpin = false;
        if (data.message.includes(':')) {
          this.testRes = data.message.split(':')[1];
          this.toast.success(data.message)
        } else {
          this.testRes = data.message;
          this.toast.success(this.testRes)
        }
      })
    }
    else {
      if (this.name == 'Source') {
        if (this.migtypeid == "30") {
          this.assessmentService.testMySqlConn(obj).subscribe((data: deleteFile) => {
            if (data.message.includes(':')) {
              this.testRes = data.message.split(':')[1];
              this.testconnectionSpin = false
              this.toast.success(data.message)
              // $('#demo').offcanvas('hide');
            }
            else {
              this.testRes = data.message;
              this.testconnectionSpin = false;
              this.toast.success(this.testRes)
              //  $('#demo').offcanvas('hide');
            }
          }, error => {
            this.testconnectionSpin = false
            this.toast.error(this.testRes)
            this.openPopup()
            // $('#demo').offcanvas('hide');
          })

        }
        else {
          this.assessmentService.testSourceConn(obj).subscribe((data: deleteFile) => {
            if (data.message.includes(':')) {
              this.testRes = data.message.split(':')[1];
              this.testconnectionSpin = false
              this.toast.success(data.message)
              //$('#demo').offcanvas('hide');
            }
            else {
              this.testRes = data.message;
              this.testconnectionSpin = false;
              this.toast.success(data.message)
              // $('#demo').offcanvas('hide');
            }
          }, error => {
            this.testconnectionSpin = false
            this.toast.error(this.testRes)
            this.openPopup()
            // $('#demo').offcanvas('hide');
          })
        }
      }
      else {
        if (this.migtypeid == "30") {
          this.assessmentService.testorclConn(obj).subscribe((data: deleteFile) => {
            if (data.message.includes(':')) {
              this.testRes = data.message.split(':')[1];
              this.testconnectionSpin = false
              this.toast.success(data.message)
              // $('#demo').offcanvas('hide');
            }
            else {
              this.testRes = data.message;
              this.testconnectionSpin = false;
              this.toast.success(data.message)
              //  $('#demo').offcanvas('hide');
            }
          }, error => {
            this.testconnectionSpin = false
            this.toast.error('Something went wrong')
            this.openPopup()
            // $('#demo').offcanvas('hide');
          })
        }
        else if (this.migtypeid == "28") {
          this.assessmentService.testSqlTargetConn(obj).subscribe((data: deleteFile) => {
            if (data.message.includes(':')) {
              this.testRes = data.message.split(':')[1];
              this.testconnectionSpin = false
              this.toast.success(data.message)
              // $('#demo').offcanvas('hide');
            }
            else {
              this.testRes = data.message;
              this.testconnectionSpin = false;
              this.toast.success(data.message)
              //$('#demo').offcanvas('hide');
            }
          }, error => {
            this.testconnectionSpin = false
            this.toast.error('Something went wrong')
            this.openPopup()
            // $('#demo').offcanvas('hide');
          })
        }
        else {
          this.assessmentService.testTargetConn(obj).subscribe((data: deleteFile) => {
            this.testconnectionSpin = false
            this.testRes = data.message
            this.toast.success(data.message)
            // $('#demo').offcanvas('hide');
          })
        }
      }
    }
  }
  setRequiredValidators(controls: string[]) {
    controls.forEach(control => {
      (this.connectionForm.controls[control as keyof typeof this.connectionForm.controls] as FormControl).setValidators([Validators.required]);
    });
  }
  clearValidators(controls: string[]) {
    controls.forEach(control => {
      (this.connectionForm.controls[control as keyof typeof this.connectionForm.controls] as FormControl).clearValidators();
    });
  }
  updateAllControls() {
    Object.keys(this.connectionForm.controls).forEach(control => {
      (this.connectionForm.controls[control as keyof typeof this.connectionForm.controls] as FormControl).updateValueAndValidity();
    });
  }
}

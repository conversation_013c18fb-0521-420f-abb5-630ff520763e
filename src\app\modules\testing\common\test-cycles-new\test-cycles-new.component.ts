import { Component } from '@angular/core';
import { HotToastService } from '@ngxpert/hot-toast';
import { AnyCatcher } from 'rxjs/internal/AnyCatcher';
import { TestingService } from '../../../../services/testing.service';
import { Form<PERSON>uilder, FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import html2canvas from 'html2canvas';
import jspdf from 'jspdf';
import * as XLSX from 'xlsx';

declare let $: any;

@Component({
  selector: 'app-test-cycles-new',
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule, FormsModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './test-cycles-new.component.html',
  styles: ``
})
export class TestCyclesNewComponent {
  cyclesForm: any
  TestCaseForm: any
  operation: any = [
    {
      option: "Cycle2", value: "Cycle2"
    },
    {
      option: "Cycle3", value: "Cycle3"
    },
    {
      option: "Cycle3 Debug", value: "Cycle3_Debug"
    },
  ]
  project_name: any
  projectId: any
  pageName: any
  ConsList: any = []
  tgtList: any = []
  kafkaList: any = []
  selectedCycle: any
  masterSelected: any
  disable: boolean = true
  exeoption: any = [{
    option: "Break", value: "Break"
  }, {
    option: "Continue", value: "Continue"
  }]
  constructor(private toast: HotToastService,
    private testingService: TestingService,
    private fb: FormBuilder,
    private route: ActivatedRoute
  ) {
    this.project_name = localStorage.getItem('project_name');
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }
  ngOnInit(): void {
    this.GetConsList();
    this.GetIterations();
    this.getreqTableData()
    this.cyclesForm = this.fb.group({
      operation: ['', [Validators.required]],
      iteration: ['', [Validators.required]],
      srcCon: [''],
      tgtCon: ['', [Validators.required]],
      exeOption: [''],
      exeTimeLimit: ['', [Validators.required]],
      kafkaCon: ['']
    })
    this.TestCaseForm = this.fb.group({
      objectname: ['', [Validators.required]],
      tgt_test_id: ['', [Validators.required]],
      test_id: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      objecttype_name: [''],
      exec_time_limit: [''],
      error_log: ['',],
      initial_error_log: [''],
      exec_time: ['',],
    });
  }
  get f() {
    return this.cyclesForm.controls;
  }
  //Get ConsList
  GetConsList() {
    this.testingService.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      this.kafkaList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'K' && item.conname != '';
      });
    });
  }
  iterationList: any = []
  GetIterations() {
    this.testingService.getTestCaseBatches(null).subscribe((data: any) => {
      this.iterationList = data['Table1'];
      for (const element of this.iterationList) {
        if (element.iteration == "") {
          element.iteration = "ALL"
        }
      }
    })
  }
  cycleHide: boolean = false
  selectedCycleTestCases: any = []
  selectCycles(cycle: any) {
    this.selectedCycle = cycle
    var temp = this.testcaseData
    if (cycle == "Cycle2") {
      this.selectedCycleTestCases = temp.filter((item: any) => {
        return item.operation_name == "Cycle_2"
      })
      this.testcaseData = this.testcaseData
      this.cycleHide = false
      this.clearValidators(['srcCon', 'kafkaCon', 'exeOption'])
    } else if (cycle == "Cycle3") {
      this.cycleHide = true
      this.selectedCycleTestCases = temp.filter((item: any) => {
        return item.operation_name == "Cycle_3"
      })
      this.setRequiredValidators(['srcCon', 'kafkaCon', 'exeOption'])
    }
  }
  setRequiredValidators(controls: string[]) {
    controls.forEach(control => {
      (this.cyclesForm.controls[control as keyof typeof this.cyclesForm.controls] as FormControl).setValidators([Validators.required]);
    });
  }
  clearValidators(controls: string[]) {
    controls.forEach(control => {
      (this.cyclesForm.controls[control as keyof typeof this.cyclesForm.controls] as FormControl).clearValidators();
    });
  }
  selectedIteration: any
  SelectIteration(iter: any) {
    this.selectedIteration = iter
    this.selectTestCases()
  }
  testcaseData: any = []
  datachange1: any;
  pi: number = 10;
  p: number = 1;
  selectTestCases() {
    let obj = {
      //projectId: this.projectId.toString(),
      tgt_test_id: this.selectedIteration,
      userTestCase: null
    }
    this.testingService.PrjTgttestcasehdrSelect(obj).subscribe((data: any) => {
      this.testcaseData = data["Table1"];
      this.testcaseData.forEach((item: any) => {
        item.isSelected = false;
      })
    })
  }
  checkUncheckAll($event: any) {
    if ($event.target.checked) {
      this.masterSelected = true
      this.testcaseData.forEach((item: any) => {
        item.isSelected = true;
      })
    } else {
      this.masterSelected = false
      this.testcaseData.forEach((item: any) => {
        item.isSelected = false;
      })
    }
  }
  onChange(tctId: any, $event: any) {
    if ($event.target.checked) {
      this.testcaseData.forEach((item: any) => {
        if (item.testcase_id == tctId) {
          item.isSelected = true;
        }
      })
    } else {
      this.testcaseData.forEach((item: any) => {
        if (item.testcase_id == tctId) {
          item.isSelected = false;
        }
      })
    }
  }
  TriggerWebTestCommand(formData: any) {
    var selectedtctids: any = []
    this.testcaseData.forEach((item: any) => {
      if (item.isSelected == true) {
        selectedtctids.push(item.testcase_id)
      }
    })
    if (selectedtctids.length != 0) {
      if (this.selectedCycle == "Cycle2") {
        formData.exeOption = "Continue";
      }
      let obj = {
        task: this.selectedCycle,
        iteration: this.selectedIteration,
        SrcConId: formData.srcCon,
        TgtConId: formData.tgtCon,
        testcaseIds: selectedtctids.toString(),
        option: formData.exeOption,
        timelimit: formData.exeTimeLimit.toString(),
        kafkaConId: formData.kafkaCon,
        projectId: this.projectId.toString()
      }
      this.testingService.webTestCommand(obj).subscribe((data: any) => {
        this.toast.success(data.message);
      })
    }
    else {
      this.toast.error("Please Select Atleast One Test Case");
    }
  }
  updateRecord(list: any) {
    this.TestCaseForm.patchValue({
      objectname: list.statement,
      tgt_test_id: list.testcase_id,
      test_id: list.test_id,
      execution_time: list.execution_time_limit,
      exec_time: list.exec_time_limit,
      global_variable: list.global_variable,
      object_definition: list.object_definition,
      project_id: list.project_id,
      objecttype_name: list.objecttype_name,
      test_group: list.test_group,
      error_log: list.error,
      initial_error_log: list.processed_error_log
    });
    $('#test').offcanvas('show');

  }
  SaveBtnObjData: any
  testCaseId: any
  Spin: boolean = false
  saveBtn(data: any) {
    if (data.test_id != undefined) {
      this.testCaseId = data.test_id;
    }
    let SaveObj = {
      //projectId: this.projectId.toString(),
      testCaseId: data.tgt_test_id,
      objectSignature: data.objectname,
      //timeLimit: data.exec_time,
      //error_log: data.error_log,
      // acl: ' ',
    };
    this.Spin = true;
    this.testingService.PrjTgtTestCaseUpdate(SaveObj).subscribe(
      (data: any) => {
        this.SaveBtnObjData = data;

        $('#demo').offcanvas('show');
        this.Spin = false;

        if (data['message'] == 'Success') {
          this.toast.success(data.message);

        }
      },
      (error) => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong!');
        this.Spin = false;
      }
    );
  }
  openPopup() {
    this.TestCaseForm.reset();
  }
  pdfspin: boolean = false
  generatePDF() {
    let html1 = document.getElementById('tblexample') as HTMLCanvasElement;
    let divHeight = $('#tblexample').height();
    let divWidth = $('#tblexample').width();
    let ratio = divHeight / divWidth;
    this.pdfspin = true;
    html2canvas(html1).then(canvas => {
      let pdf = new jspdf('p', 'pt', [canvas.width, canvas.height]);
      let imgData = canvas.toDataURL("image/jpeg", 1.0);
      let width = pdf.internal.pageSize.getWidth();
      let height = pdf.internal.pageSize.getHeight();
      height = ratio * width;
      pdf.addImage(imgData, 'JPEG', 0, 0, width - 20, height - 10);
      let docname = "TestCycleExecutedData_" + this.projectId.toString();
      pdf.save(docname + '.pdf');
      if (html1) {
        this.pdfspin = false;
      }
    },
      (error) => {
        this.pdfspin = false;
      });

  }
  fileName = 'TestCycle.xlsx';
  exportexcel(): void {
    /* pass here the table id */
    let element = document.getElementById('tblexample');
    const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);

    /* generate workbook and add the worksheet */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    /* save to file */
    XLSX.writeFile(wb, this.fileName);
  }
  ref_spin: boolean = false
  tabledata: any = []
  z: any
  datachange2: any
  piA: number = 10
  p1: number = 1
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: 'Cycle',
    };
    this.ref_spin = true
    this.testingService.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        for (let k = 0; k < this.tabledata.length; k++) {
          if (this.tabledata[k].status == "C") {
            this.tabledata[k].statusfull = "Completed"
          }
          else if (this.tabledata[k].status == "I") {
            this.tabledata[k].statusfull = "Initialize"
          }
          else if (this.tabledata[k].status == "P") {
            this.tabledata[k].statusfull = "Pending"
          }
          else {

          }

          if (this.tabledata[k].objecttype == "ALL") {
            this.tabledata[k].objecttype = ""
          }
        }
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList?.length; i++) {
              if (
                this.tabledata[this.z].connection_id ==
                this.ConsList[i].Connection_ID
              ) {
                this.tabledata[this.z].conname = this.ConsList[i].conname;
              }
            }
          }
        }
        else {
          this.tabledata = []
        }
      }
    });
  }
  deleteResponse: any;
  deleteTableDatas(request_id: any) {
    const obj: any = {
      projectId: this.projectId,
      requestId: request_id,
    };
    this.testingService.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData();
    });
  }
  onKey() {
    this.p1 = 1;
    this.p = 1;
  }
  CycleReport: any = []
  datachange4: any
  p3: number = 1
  CycleReports() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterReports + "/Reports/Testing/" + this.selectedOpRepo
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.CycleReport = data
    })
  }
  iterReports: any
  SelectIterationForReports(value: any) {
    this.iterReports = value;
  }
  selectedOpRepo: any
  operationReports(value: any) {
    this.selectedOpRepo = value
    this.CycleReports()
  }
  //download files details
  fileResponse: any
  downloadFile(fileInfo: any) {
    this.testingService.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();

    })
  }
  iterationForLogs: any
  ExecututionFiles: any = []
  iterationForDepLogs: any
  selectedOpLog: any
  DepLogFiles: any = []
  datachange: any
  datachangeLogs: any
  page3: number = 1
  fetchAssessmentFiles() {
    const path = "PRJ" + this.projectId + "SRC/" + this.iterationForDepLogs + "/Deployment_Logs/Testing/" + this.selectedOpDep
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.DepLogFiles = data
    })
  }
  // filter execution reports
  filterExecutionReports() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Execution_Logs/Testing/" + this.selectedOpLog
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
    })
  }
  //select iteration for Deployment logs

  selectIterforlDepogs(value: any) {
    this.iterationForDepLogs = value
  }
  //select iteration for Execution logs
  selectIterforlogs(value: any) {
    this.iterationForLogs = value
  }

  operationLogs(value: any) {
    this.selectedOpLog = value
    this.filterExecutionReports()
  }
  selectedOpDep: any
  operationDepLogs(value: any) {
    this.selectedOpDep = value
    this.fetchAssessmentFiles()
  }

}

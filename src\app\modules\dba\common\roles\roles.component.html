<div class="v-pageName">{{pageName}}</div>
   

    <!--- connection details --->
    <div class="body-main mt-4">
       
        <div class="qmig-card">
            <div class="qmig-card-body">
                <form class="form qmig-Form" [formGroup]="roleForm">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Connection Name</label>
                                <select class="form-select" formControlName="connection" #ser
                                    (change)="Getdbname(ser.value)" formControlName="connection">
                                    <option selected value="">Select a connection name</option>
                                    @for(ConsList1 of ConsList;track ConsList1; ){
                                    <option value="{{ ConsList1.Connection_ID }}"> {{ ConsList1.conname }} </option>
                                    }
                                </select>
                                @if ( f.connection.touched && f.connection.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.connection.errors.required) {Connection name is required }
                                </p>
                                }
                            </div>
                        </div>
                        <!-- Database details -->
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="Schema">Database</label>
                                <select class="form-select" formControlName="db" #db (change)="Getroles(db.value)"
                                    formControlName="db">
                                    <option selected value="">Select a database</option>
                                    @for(dbs of dblist;track dbs; ){
                                    <option value="{{ dbs.dbname }}"> {{ dbs.dbname }} </option>
                                    }
                                </select>
                                @if ( f.db.touched && f.db.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.db.errors.required) {Database is required }
                                </p>
                                }
                            </div>
                        </div>

                        <!-- Roles details -->
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="Schema">Role Name</label>
                                <ng-select [items]="rolelist" [multiple]="true" bindLabel="rolename" groupBy="type"
                                    [selectableGroup]="true" formControlName="rolename"
                                    (change)="selectRole(selectedItems)" [closeOnSelect]="false" bindValue="rolename"
                                    [(ngModel)]="selectedItems">
                                    <ng-template ng-optgroup-tmp let-item="item" let-item$="item$" let-index="index">
                                        <input id="item-{{index}}" type="checkbox" [ngModel]="item$.selected" />
                                        {{item.type | uppercase}}
                                    </ng-template>
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                            [ngModelOptions]="{ standalone : true }" /> {{item.rolename}}
                                    </ng-template>

                                </ng-select>
                                @if ( f.rolename.touched && f.rolename.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.rolename.errors.required) {Rolename is required }
                                </p>
                                }

                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                            <div class="form-group mt-1">
                                <button class="btn btn-upload w-100 mt-4" type="button" [disabled]="roleForm.invalid"
                                    (click)="GetSearchRolesInfo()"> <span class="mdi mdi-database-search"></span>
                                    Fetch @if(spinner){<app-spinner />} </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="body-main mt-4" [hidden]="!tableHide">
        <div class="qmig-card">
            <div class="qmig-card-body">
                <div class="row">
                    <div class="col-md-3 col-xl-3 offset-md-9">
                        <button class="btn btn-upload w-100" (click)="exportexcel()">
                            <i class="mdi mdi-download "></i> Download <i *ngIf="excelSpin"
                                class="fa fa-spinner fa-spin"></i> </button>
                    </div>
                    
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover qmig-table">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>RoleName</th>
                            <th>Memberof</th>

                        </tr>
                    </thead>
                    <tbody>
                        @for (documents of searchRoleList | searchFilter: searchText1 |paginate:
                        {itemsPerPage:50,currentPage: p2,id:'First'} ; track documents; let i = $index) {
                        <tr>
                            
                            <!-- <td>{{ pi * (page1 - 1) + i + 1 }}</td> -->
                            <td>{{documents.sno}}</td>
                            <td>{{documents.roleName}}</td>
                            <td>{{documents.memberof[0] }}</td>
                        
                        </tr>
                        } @empty {
                        <tr>
                            <td colspan="4">
                                <p class="text-center m-0 w-100">No Results</p>
                            </td>
                        </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="custom_pagination">
                <pagination-controls (pageChange)="p2 = $event" id="First"></pagination-controls>
            </div>
        </div>
    </div>
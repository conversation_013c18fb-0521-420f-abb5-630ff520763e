import { Component } from '@angular/core';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { HotToastService } from '@ngxpert/hot-toast';
import {FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { TestingService } from '../../../../services/testing.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import * as XLSX from 'xlsx';
import html2canvas from 'html2canvas';
import jspdf from 'jspdf';
import { CommonModule } from '@angular/common';
import { TestcycleReqObj } from '../../../../models/interfaces/types';
import { ActivatedRoute } from '@angular/router';

declare let $: any;

@Component({
  selector: 'app-sit',
  standalone: true,
  templateUrl: './sit.component.html',
  styles: ``,
  imports: [ReactiveFormsModule, CommonModule, FormsModule, SpinnerComponent,NgSelectModule,NgxPaginationModule,SearchFilterPipe],

  
})
export class SitComponent {

  /*--- Login form ---*/
  cycleForm = this.fb.group({
    cycle: ['', [Validators.required]],
    Connection: [''],
    TargetConnection:['', [Validators.required]],
    objectType:[''],
    valuee:[''],
    schema:[''],
    objectName:[''],
    objectCategory:[''],

  })
/*--- Related to exportexcel  ---*/ 
   ServiceNames: any = [];
 
  // /*---- Excel ---*/
  fileName = 'TestCycle.xlsx';
  
/*--- Related to exportexcel End ---*/ 

pageName:string=''

  constructor(private toast: HotToastService, 
    private testingService:TestingService, 
    private fb: FormBuilder,
    private route: ActivatedRoute
   )
    {
    this.project_name = localStorage.getItem('project_name');
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.schemaCheck = "break";
    this.pageName = this.route.snapshot.data['name'];

    
  }
  executeForm = this.fb.group({
    file: ['', [Validators.required]],    
  })


  datachange1: string = '';
  Add_Spin: boolean = false;
  sourceData: any = [];
  FunctionalTestingTgtTableData: any = [];
  pi: number = 10;
  p: number = 1;
  projectId: any;
  batchId:any;
  FileNameGets: any = [];
  TestCaseForm: any;
  sourcecon: any;
  selectedConname: any;
  conId: any;
  conName: any;
  userData:any;
  targetData: any = [];
  NoDataHide: boolean = false;
  selectedInfra: any;
  infraSelectData: any;
  masterSelected:boolean =false;
  checkedList:any=[];
  showCallStmt: boolean = false;
  showCheckStatus: boolean = false;
  showTarget: boolean = true;
  showSource: boolean = true; 
  showDebugRow: boolean=true;
  selectedcycle: any;
  showPerfRow: boolean = true;
  tgtconnection: any;
  // selectcheck: boolean = false;
  selectcheck :string ="false";
  schemaCheck: any;
  temptestcase: any;
  dataByBatchId:any;
  objectType: any = [];
  obtypevalue: any;
  checkValue: any;
  s_Map_Data: any = []
  t_Map_Data: any = []
  save_Spin: boolean = false;
  targetMapData: any = [];
  mappingForm: any;
  testCaseId: any;
  Spin: boolean = false;
  SaveBtnObjData: any;
  tgttestCasedataResponse: any
  noRecords: any
  isNoRecords: boolean = false;
  chooseExport: boolean = false;
  choosePdf: boolean = false;
  currentBatchId: any;
  sourceMapData: any = [];
  temptest: any = [];
  aa: any = [];
  temp_flag: any;
  temp_t_flag: any;
  temp_s_data: any = [];
  temp_t_data: any = [];
   project_name: any;
  output: any = "";
  funcationItems: any = [];
  pdfspin: boolean = false;
  filteredCases: any = [];
  ConnectionType:any;
  TargetConnectionType:any;
  usertestCases: any;
  TgtConId:any
  target_connection_id:any
  exe_option:any;
   task:any;
   runinfo: boolean = false;
   prjSrcTgt: any = {};
   FunctionalTestingSrcTableData: any;
   ConnectionNameData: any;
   testcasesList:any=[];
   batchids: any
  ids: any = [];
  xyz:any=[];
  batchId_service: any = [];
  btndis: boolean = true;
  DbconName:any;
  ConnectionName:any;
  FileNameGet:any;
  sourceHide: boolean = true;
  targetHide: boolean = true;
  tempTestcases: any = [];

  ngOnInit(): void {
    this.schemaCheck = "break";
    this.TestCaseForm = this.fb.group({
      objectname: ['', [Validators.required]],
      tgt_test_id: ['', [Validators.required]],
      test_id: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      objecttype_name:[''],
      exec_time_limit:[''],
      error_log: ['',],
      initial_error_log:[''],
      exec_time: ['',],
    });
    
    this.mappingForm = this.fb.group({
      sconnection: ['',],
      tconnection: ['',],
    })
    
    this.GetConsList();
    this.GetBatchIds();

    // this.TestCaseForm.controls['execution_time'].disable();
    // this.TestCaseForm.controls['global_variable'].disable();
    // this.TestCaseForm.controls['object_definition'].disable();
    this.TestCaseForm.controls['objecttype_name'].disable();
    // this.TestCaseForm.controls['project_id'].disable();
    // this.TestCaseForm.controls['test_group'].disable();
    

   
  }

  /*--- Login form controls ---*/
  get f():any { return this.cycleForm.controls; }
  
  
  onKey() {
    this.p = 1;
  }

  /*--- SourceConnection List    ---*/ 

  selectsourceconnection(value: any) {
    this.sourcecon = value
  }
  testCaseSelection(data: any) {
    this.btndis = false;
    this.DbconName = data;
    this.ConnectionName = this.ConnectionNameData[0].migsrctgt;
    if (this.ConnectionName == 'S') {
      this.FileNameGet = [];
      this.NoDataHide = true;
      this.sourceHide = false;
    }
    if (this.ConnectionNameData[1].migsrctgt == 'T') {
      this.FileNameGets = [];
      this.NoDataHide = true;
      this.targetHide = false;
    }
  }


  
  /*--- fetch button     ---*/ 

  selectRange() {
    const start = (<HTMLInputElement>(document.getElementById("start"))).value;
    const end = (<HTMLInputElement>(document.getElementById("end"))).value
    if (start != "" && end != "") {
      const obj = {
        projectId: this.projectId.toString(),
        begin_testid: start,
        end_testid: end
      }
      this.testingService.selecttestcaseRange(obj).subscribe((data: any) => {
        this.FunctionalTestingTgtTableData = data["Table1"];
      })
    }
    else {
      this.toast.error("Please enter start and  End Value");
    }
  }


  /*--- Call Statement Checkbox    ---*/ 
 
  withCallStmt($event: any): void {
    this.showCheckStatus = $event.target.checked
    if ($event.target.checked)
      this.showCallStmt = true
    else
      this.showCallStmt = false
  }
  

  
  /*--- Select Cycle dropdown    ---*/ 

  selectCycle(value: any) {
    this.selectedcycle = value;
    if (value == "Cycle2") {
      this.showPerfRow = true
      this.showTarget = false;
      this.showSource = true
      this.showDebugRow=true
      this.showCallStmt = false
      this.showCheckStatus = false
      this.cycleForm.controls['Connection'].clearValidators()
      this.cycleForm.controls['objectType'].clearValidators()
      this.cycleForm.controls['valuee'].clearValidators()
      this.cycleForm.controls['objectName'].clearValidators()
      this.cycleForm.controls['schema'].clearValidators()
      this.cycleForm.controls['objectCategory'].clearValidators()
      this.cycleForm.controls['cycle'].setValidators([Validators.required])
      this.cycleForm.controls['TargetConnection'].setValidators([Validators.required])
     
    }
   else if (value == "Cycle3" || value == "Cycle3_Debug" || value == "Cycle3_Debug_Dynamic_Rules") {
      this.showPerfRow = true
      this.showTarget = false;
      this.showSource = false
      //this.showDebugRow=false
      this.showCallStmt = false
      this.showCheckStatus = false
      this.cycleForm.controls['Connection'].setValidators([Validators.required])
      this.cycleForm.controls['objectType'].clearValidators()
      this.cycleForm.controls['valuee'].setValidators([Validators.required])
      this.cycleForm.controls['objectName'].clearValidators()
      this.cycleForm.controls['schema'].clearValidators()
      this.cycleForm.controls['objectCategory'].clearValidators()
      this.cycleForm.controls['cycle'].setValidators([Validators.required])
      this.cycleForm.controls['TargetConnection'].setValidators([Validators.required])
    }
   else if (value == "Perf_debug") {
      this.showPerfRow = false
      this.showTarget = true;
      this.showSource = true
      this.showDebugRow=true
      this.showCheckStatus = false
      this.cycleForm.controls['Connection'].setValidators([Validators.required])
      this.cycleForm.controls['objectType'].setValidators([Validators.required])
      this.cycleForm.controls['valuee'].clearValidators()
      this.cycleForm.controls['objectName'].setValidators([Validators.required])
      this.cycleForm.controls['schema'].setValidators([Validators.required])
      this.cycleForm.controls['objectCategory'].setValidators([Validators.required])
      this.cycleForm.controls['cycle'].setValidators([Validators.required])
      this.cycleForm.controls['TargetConnection'].setValidators([Validators.required])
    }
    this.cycleForm.controls['Connection'].updateValueAndValidity()
    this.cycleForm.controls['objectType'].updateValueAndValidity()
    this.cycleForm.controls['valuee'].updateValueAndValidity()
    this.cycleForm.controls['objectName'].updateValueAndValidity()
    this.cycleForm.controls['schema'].updateValueAndValidity()
    this.cycleForm.controls['objectCategory'].updateValueAndValidity()
    this.cycleForm.controls['cycle'].updateValueAndValidity()
    this.cycleForm.controls['TargetConnection'].updateValueAndValidity()
  }
 
  /*--- TargetConnection List    ---*/ 
  
  testCaseSelections(data: any) {
    this.tgtconnection = data;
  }

   /*--- Continue checkboxselect   ---*/ 

  checkboxselect($event: any): void {
    this.selectcheck = $event.target.checked;
    if (this.selectcheck == "true") {
      this.schemaCheck = 'continue'
    }
    if (this.selectcheck == "false") {
      this.schemaCheck = 'break'
    }
  }
 
    /*--- Target Table    ---*/ 

    FunctionalTestingTgtTable(batchId: any) {
      this.currentBatchId = batchId;
      this.FunctionalTestingTgtTableData = []
    }
    

    /*--- BatchID List    ---*/ 

  GetDataByBatchId(bid:any)
  {
    this.p = 1;
    this.FunctionalTestingTgtTableData = this.temptestcase
    
      this.FunctionalTestingTgtTableData = this.FunctionalTestingTgtTableData.filter( (item:any) => {
          return item.iteration == bid;
    })
    this.dataByBatchId=this.FunctionalTestingTgtTableData;
  }
  /*--- Filterstatus List    ---*/ 

  
  filterStatus(status:any){
    if(this.batchId!= undefined)
    {
      if(status != 'All'){
        this.FunctionalTestingTgtTableData = this.dataByBatchId.filter( (item:any) => {
            return item.testcase_status == status;
        })
      }else{
        this.FunctionalTestingTgtTableData = this.dataByBatchId;
      }
    }
    else{
      this.FunctionalTestingTgtTableData = this.temptestcase
      if(status != 'All'){
        this.FunctionalTestingTgtTableData = this.FunctionalTestingTgtTableData.filter( (item:any) => {
            return item.testcase_status == status;
        })
      }else{
        this.FunctionalTestingTgtTableData = this.FunctionalTestingTgtTableData;
      }
    }
    
  }

    /*--- SourceConnection List    ---*/ 

    onChange(document: any, id: any, $event: any) {
      const isChecked = $event.target.checked;
      document.isSelected = isChecked;
  
      if (isChecked) {
          this.testcasesList.push(document);
          this.FileNameGets.push(id);
          this.tgt_test_id = id;
      } else {
          this.testcasesList = this.testcasesList.filter((item: any) => item.tgt_test_id !== id);
          this.FileNameGets = (this.FileNameGets as any[]).filter((existingId: any) => existingId !== id);
      }
  
      this.masterSelected = this.FunctionalTestingTgtTableData.every((item: any) => item.isSelected);
      this.getCheckedItemList();
  }
  
    

    /*--- Record Update    ---*/ 

  updateRecord(list: any) {
    this.TestCaseForm.patchValue({
       objectname: list.object_signature,
       tgt_test_id: list.tgt_test_id,
       test_id: list.test_id,
       execution_time: list.execution_time,
      exec_time: list.exec_time_limit,
      global_variable: list.global_variable,
      object_definition: list.object_definition,
      project_id: list.project_id,
      objecttype_name: list.objecttype_name,
      test_group: list.test_group,
      error_log: list.error_log,
      initial_error_log:list.processed_error_log
    });
    $('#test').offcanvas('show');
   
  }
  objCategory: any = [
    // { values: '1', option: 'ALL' },
    // { values: '2', option: 'StorageObjects' },
    { values: '3', option: 'CodeObjects' }
  ]
  
    /*--- ObjectTypes List    ---*/ 

  SelectObjectTypes(value: any) {
    let abc = this.objCategory.filter((item: any) => {
      return item.values == value;
    })
    this.obtypevalue = abc[0].option;
    if (value == "1") {
      this.objectType = [];
      this.objectType = [
        { values: 'ALL', option: 'ALL' }
      ];
 
    }
    if (value == "2") {
      this.objectType = [];
      this.objectType = [
        { values: 'ALL', option: 'ALL' },
        { values: 'TABLES', option: 'TABLES' },
        { values: 'VIEWS', option: 'VIEWS' },
        { values: 'MATERIALIZED_VIEWS', option: 'MATERIALIZED_VIEWS' },
        { values: 'SEQUENCES', option: 'SEQUENCES' },
        { values: 'SYNONYMS', option: 'SYNONYMS' },
        { values: 'TYPES', option: 'TYPES' },
      ];
    }
    if (value == "3") {
      this.objectType = [];
      this.objectType = [
        // { values: 'ALL', option: 'ALL' },
        { values: 'PROCEDURES', option: 'PROCEDURES' },
        { values: 'FUNCTIONS', option: 'FUNCTIONS' },
        { values: 'PACKAGES', option: 'PACKAGES' },
        { values: 'TRIGGERS', option: 'TRIGGERS' },
      ];
    }
  }


    /*--- Mapping Button List    ---*/ 

  OpenMap() {
    $('#demo').offcanvas('show');
  }

   /*--- Values List    ---*/ 

  selectValue(pdata: any) {
    this.checkValue = pdata
  }

    /*--- Connection List    ---*/ 

  GetConsList() {
    this.testingService.getConList(this.projectId.toString()).subscribe((data: any) => {
      const condata = data['Table1'];
      this.sourceData = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.targetData = condata.filter((item:any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }

    /*---  CheckUncheck   ---*/ 

  // checkUncheckAll() {
  //   for (let i = 0; i < this.FunctionalTestingTgtTableData.length; i++) {
  //     this.FunctionalTestingTgtTableData[i].isSelected = this.masterSelected;
  //   }
  //   this.getCheckedItemList();
  // }
  checkUncheckAll($event: any) {
    const isChecked = $event.target.checked;
  
    this.masterSelected = isChecked;
    this.FunctionalTestingTgtTableData.forEach((item: any) => {
      item.isSelected = isChecked;
    });
  
    if (isChecked) {
      // Flatten the array to avoid nested arrays and include all selected items
      this.testcasesList = this.FunctionalTestingTgtTableData.map((item: any) => item);
    } else {
      this.testcasesList = [];
    }
  
    this.getCheckedItemList();
  }
   
  
  getCheckedItemList() {
    this.checkedList = [];
    this.batchids = this.testcasesList.filter(
      (item: any, i: any) =>
        this.testcasesList.findIndex((t: any) => t.iteration === item.iteration) === i
    );
    this.output = ""
    for (let j = 0; j < this.batchids.length; j++) {
      let abc = this.testcasesList.filter((item: any) => {
        return item.iteration == this.batchids[j].iteration;
      })
      for (const element of abc) {
        this.xyz.push(element.tgt_test_id);

      }

      if (j < this.batchids.length - 1) {
        this.output = this.output + this.batchids[j].iteration + "-[" + this.xyz + "]/";
        this.xyz = []
      }
      if (j == this.batchids.length - 1) {
        this.output = this.output + this.batchids[j].iteration + "-[" + this.xyz + "]";
        this.xyz = []
      }

    }
    this.ServiceNames = []
    this.batchId_service = this.testcasesList
    let temp_snames1 = this.batchId_service.filter(
      (item: any, i: any) =>
        this.batchId_service.findIndex((t: any) => t.service_name === item.service_name) === i
    );
    for (const element of temp_snames1) {
      this.ServiceNames.push(element.service_name)
    }
    for (const element of this.testcasesList) {
      if (element.isSelected) {
        this.checkedList.push(element);
        if (element.tgt_test_id)
          this.FileNameGets.push(element.tgt_test_id);
      }

    }
    this.checkedList = JSON.stringify(this.checkedList);
  }
  
  
  /*--- Batch ID List    ---*/ 
  
  GetTgtDataByBatchId()
  {
    const start = (<HTMLInputElement>(document.getElementById("start"))).value;
    const end = (<HTMLInputElement>(document.getElementById("end"))).value
    if (start != "" && end != "") {
      const obj = {
        projectId: this.projectId.toString(),
        begin_Iteration: start,
        end_Iteration: end
      }
      this.testingService.GetDataByBatchId(obj).subscribe((data: any) => {
        this.FunctionalTestingTgtTableData = data["Table1"];
      })
    }
    else {
      this.toast.error("Please enter start and  End Value");
    }
  }

  //*-- Execute Button for perf debug--*//

  executePerf() {
    this.selectedInfra = this.infraSelectData.filter((item: any) => {
      return item.active === 'True';
    });
    let pattern = (<HTMLInputElement>(document.getElementById("pdClstmt"))).value
    let modfPattern = pattern.replace(/ /g, "@").split('"').join('#').split("'").join('&')
    let obj = {
      projectID: this.projectId.toString(),
      command: 'wrk_ld_perf_log.sh',
      id: this.projectId.toString(),
      ObjectType: this.sourcecon,
      Schema: this.tgtconnection,
      ConnectionName: (<HTMLInputElement>(document.getElementById("pdschema"))).value,
      DbName: (<HTMLInputElement>(document.getElementById("pdobjName"))).value,
      Operation: (<HTMLInputElement>(document.getElementById("pdobjType"))).value,
      Pattern: '"' + modfPattern + '"'
    }
    this.Add_Spin = true;
    // this.testingService.ExecuteCommandOnVM(obj).subscribe(
    //   (data: any) => {
    //     this.ExecuteBtnObjData = data;
    //     this.Add_Spin = false;
    //     if (data.message == 'Command Executed Successfully') {
    //       this.toast.success(data.message);
    //       if (obj.Pattern != '') {
    //         window.location.reload();
    //       }
    //     }
    //     if (data.message == 'VM is not in Running State') {
    //       this.toast.error(data.message);
    //     }
    //   },
    //   (error) => {
    //     this.toast.danger('Something went wrong!');
    //     this.Add_Spin = false;
    //   }
    // );
 
  }
    /*--- Export Button   ---*/
    /*--- Commented because while importing getting error   ---*/

    exportexcel(): void {
      /* pass here the table id */
      let element = document.getElementById('example');
      const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);
   
      /* generate workbook and add the worksheet */
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
   
      /* save to file */
      XLSX.writeFile(wb, this.fileName);
    }


  /*--- Record Update    ---*/ 
 
  selectSourceMapValues(data: any, value: any) {
    if (this.sourceMapData.length == 0) {
      this.sourceMapData.push(data + ":" + value)
      this.temp_s_data.push(data + ":" + value)
    }
    else {
 
      for (let i = 0; i < this.temp_s_data.length; i++) {
        if (this.sourceMapData[i].split(":")[0] == data) {
          this.sourceMapData.splice(i, 1, data + ":" + value)
          this.temp_flag = true
        }
 
      }
      if (!this.temp_flag) {
        this.sourceMapData.push(data + ":" + value)
        this.temp_s_data.push(data + ":" + value)
      }
    }
 
 
  }

  /*--- Record Update    ---*/ 
 
  Connection_ID:any;
  selectTargetMapValues(data: any, value: any) {
    this.Connection_ID=value;
    if (this.targetMapData.length == 0) {
      this.targetMapData.push(data + ":" + value)
      this.temp_t_data.push(data + ":" + value)
    }
    else {
 
      for (let i = 0; i < this.temp_t_data.length; i++) {
        if (this.targetMapData[i].split(":")[0] == data) {
          this.targetMapData.splice(i, 1, data + ":" + value)
          this.temp_t_flag = true
        }
 
      }
      if (!this.temp_t_flag) {
        this.targetMapData.push(data + ":" + value)
        this.temp_t_data.push(data + ":" + value)
      }
    }
 
  }
  FunctionalTestingSrcTable() {
    let ConnectionName = {
      projectId: this.projectId.toString(),
      testCaseName: 'null',
    };
    this.testingService.GetProjectsrctestcasehdrSelect(ConnectionName).subscribe((data: any) => {
        this.FunctionalTestingSrcTableData = data['Table1'];
      });
  }
 
  /*--- Record Update    ---*/ 
  OnSave() {
    this.s_Map_Data = []
    this.t_Map_Data = []
    this.save_Spin = true
    this.s_Map_Data = this.sourceMapData
    this.t_Map_Data = this.targetMapData
    this.save_Spin = false
    this.toast.success("Saved");
    $('#demo').offcanvas('hide');
 
  }
  /*--- Record Update    ---*/ 
  onCancel() {
    this.s_Map_Data = []
    this.t_Map_Data = []
    this.mappingForm.reset();
    $('#demo').offcanvas('hide');

  }
/*--- Record Update    ---*/ 
  GetBatchIds() {
    this.testingService.getTestCaseBatches(null).subscribe((data: any) => {
      this.batchId = data['Table1'];
      for (const element of this.batchId) {
        if (element.iteration == "") {
          element.iteration = "ALL"
        }
      }
    })
  }
 
  /*--- Record Update    ---*/ 
  openPopup() {
    this.TestCaseForm.reset();
  }

  /*--- Record Update    ---*/ 
  saveBtn(data: any) {
    if (data.test_id != undefined) {
      this.testCaseId = data.test_id;
    }
    let SaveObj = {
      projectId: this.projectId.toString(),
      testCaseId: data.tgt_test_id,
      objectSignature: data.objectname,
      timeLimit: data.exec_time,
      error_log: data.error_log,
      acl: ' ',
    };
    this.Spin = true;
    this.testingService.PrjTgtTestCaseUpdate(SaveObj).subscribe(
      (data: any) => {
        this.SaveBtnObjData = data;
 
        $('#demo').offcanvas('show');
        this.Spin = false;
 
        if (data['message'] == 'Success') {
          this.toast.success(data.message);
 
        }
      },
      (error) => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong!');
        this.Spin = false;
      }
    );
  }
  /*--- UserTestCase Dopdown   ---*/ 
 
  PrjTgttestcasehdrSelect(sel: any) {
    let obj = {
      //projectId: this.projectId.toString(),
      tgt_test_id: this.currentBatchId,
      userTestCase: sel
    }
    this.testingService.PrjTgttestcasehdrSelect(obj).subscribe((data: any) => {
      this.FunctionalTestingTgtTableData = data["Table1"];
      if (this.FunctionalTestingTgtTableData == undefined) {
        this.isNoRecords = true;
        this.noRecords = "No Records"
        this.chooseExport = false
      }
      else {
        this.isNoRecords = false;
        this.chooseExport = true
      }
    })
  }

  /*--- Record Update    ---*/ 
  usertestCaseselect() {
    this.usertestCases = [
      { values: 'all', option: 'All' },
      { values: 'notnull', option: 'TC_ID' },
      { values: 'distinct_objects', option: 'Distinct' },
    ]
  }

  ExecuteBtn() {
    this.selectedInfra = this.infraSelectData.filter((item: any) => {
      return item.active === 'True';
    });
    if (this.selectedcycle == 'Cycle2') {
      this.s_Map_Data = "''";
      this.t_Map_Data = "'" + this.t_Map_Data.toString() + "'";
    }
    else {
      this.s_Map_Data = "'" + this.s_Map_Data.toString() + "'";
      this.t_Map_Data = "'" + this.t_Map_Data.toString() + "'";
    }


    let ExecuteBtnObj = {
      projectID: this.projectId.toString(),
      tenantID: this.selectedInfra[0].tenant,
      subscriptionID: this.selectedInfra[0].subscription,
      resourceGroup: this.selectedInfra[0].resourcegroup,
      vmName: this.selectedInfra[0].vmname,
      location: this.selectedInfra[0].infralocation,
      command: 'wrk_ld_debug_new.sh',
      objectType: this.tgtconnection,//this.ConnectionName,
      id: this.projectId.toString(),
      connection: this.selectedcycle,
      connectionName: this.sourcecon,
      pattern: this.schemaCheck,
      schema: this.s_Map_Data,
      DbName: this.t_Map_Data,
      fileName: "'" + this.output + "'",
      migtype: this.checkValue,
    };
    this.Add_Spin = true;
  //   this.project.ExecuteCommandOnVM(ExecuteBtnObj).subscribe(
  //     (data: any) => {
  //       this.ExecuteBtnObjData = data;
  //       this.Add_Spin = false;
  //       if (data.message == 'Command Executed Successfully') {
  //         this.alertService.success(data.message);
  //         document.querySelectorAll('input[type=checkbox]').forEach((element: any) => { element.checked = false });

  //       }
  //       if (data.message == 'VM is not in Running State') {
  //         this.alertService.danger(data.message);


  //       }
  //     },
  //     (error) => {
  //       this.alertService.danger('Something went wrong!');
  //       this.Add_Spin = false;
  //     }
  //   );
   }
   generatePDF() {
    let html1 = document.getElementById('example') as HTMLCanvasElement;
    let divHeight = $('#example').height();
    let divWidth = $('#example').width();
    let ratio = divHeight / divWidth;
    this.pdfspin = true;
    html2canvas(html1).then(canvas => {
      let pdf = new jspdf('p', 'pt', [canvas.width, canvas.height]);
      let imgData = canvas.toDataURL("image/jpeg", 1.0);
      let width = pdf.internal.pageSize.getWidth();
      let height = pdf.internal.pageSize.getHeight();
      height = ratio * width;
      pdf.addImage(imgData, 'JPEG', 0, 0, width - 20, height - 10);
      let docname = "TestCycleExecutedData_" + this.projectId.toString();
      pdf.save(docname + '.pdf');
      if (html1) {
        this.pdfspin = false;
      }
    },
      (error) => {
        this.pdfspin = false;
      });

  }
  selectedTestcases() {
    this.FunctionalTestingTgtTableData = []
    let start = (<HTMLInputElement>(document.getElementById("start"))).value;
    let end = (<HTMLInputElement>(document.getElementById("end"))).value;

    for (let i = 0; i <= parseInt(end); i++) {
      for (let j = parseInt(start); j <= parseInt(end); j++) {
        if (parseInt(this.temptestcase[i].tgt_test_id) == j) {
          this.filteredCases.push(this.temptestcase[i]);
        }
      }
      this.FunctionalTestingTgtTableData = this.filteredCases;
      this.filteredCases = []
    }
    if (end == "") {
      this.FunctionalTestingTgtTableData = this.temptestcase
    }
  }

  tgt_test_id:any
  SrcConId:any;
  testcycleexecute() {
    const tgtTestIds = this.testcasesList.map((item: any) => item.tgt_test_id);
    const formattedTestIds = tgtTestIds.join(',');
    const batchId = `${this.currentBatchId}-[${formattedTestIds}]`;
  
    const obj: TestcycleReqObj = {
      projectId: this.projectId.toString(),
      TgtConId: this.tgtconnection,
      BatchId: batchId,
      SrcConId: this.sourcecon,
      option: this.selectcheck.toString(),
      task: this.selectedcycle,
      flag: this.selectcheck.toString()
    };
  
  
    this.testingService.TestCycleCmd(obj).subscribe((data: any) => {
      
        this.toast.success("Command Executed Successfully");
      },
      (error: any) => {
        console.error('Error executing command', error);
        this.toast.error("Failed to execute command");
      }
    );
  }
  
}


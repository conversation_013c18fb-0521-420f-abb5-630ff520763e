# 'Allow scripts to access the OAuth token' was selected in pipeline.  Add the following YAML to any steps requiring access:
#       env:
#           MY_ACCESS_TOKEN: $(System.AccessToken)
# Variable 'clientId' was defined in the Variables tab
# Variable 'clientName' was defined in the Variables tab
# Variable 'mig_path' was defined in the Variables tab
# Variable 'migration_id' was defined in the Variables tab
# Variable 'module_name' was defined in the Variables tab
# Variable 'projectName' was defined in the Variables tab
# Variable 'token' was defined in the Variables tab
# Variable 'projectId' was defined in the Variables tab

trigger: none
name: $(mig_path)-$(projectId)-$(Build.BuildId)
resources:
  repositories:
  - repository: self
    type: git
    ref: feature-1

jobs:
- job: Job_1
  displayName: Agent job 1
  pool:
    vmImage: ubuntu-22.04
  steps:
  - checkout: self
    fetchDepth: 1
    persistCredentials: True
  - task: Bash@3
    displayName: Setting Docker Image name
    inputs:
      targetType: inline
      script: >-
        DOCKER_IMAGE_NAME="$(mig_path)-$(projectId)-$(Build.BuildId)"

        clean=${DOCKER_IMAGE_NAME//[^[:alnum:]-]/}

        echo "##vso[task.setvariable variable=DOCKER_IMAGE_NAME]$clean"
  - task: Docker@2
    displayName: build
    inputs:
      containerRegistry: qmigtest
      repository: qubeappv3
      command: build
      Dockerfile: Dockerfile
      tags: $(DOCKER_IMAGE_NAME)
      arguments: --build-arg DEPLOY=production --build-arg category=$(mig_path)
      # --build-arg MODULES="$(module_name)" --build-arg MIGRATION_PATH="$(mig_path)"
  - task: AquaSecurityOfficial.trivy-official.custom-build-release-task.trivy@1
    displayName: Image Scan
    # enabled: False
    inputs:
      image: qmigtest.azurecr.io/qubeappv3:$(DOCKER_IMAGE_NAME)
      # severities: CRITICAL,HIGH,MEDIUM,LOW
      # loginDockerConfig: true
      ignoreUnfixed: true
      options: '--scanners vuln'
      exitCode: '0'
  - task: Docker@2
    displayName: Push
    inputs:
      containerRegistry: qmigtest
      repository: qubeappv3
      command: push
      Dockerfile: Dockerfile
      tags: $(DOCKER_IMAGE_NAME)
      addPipelineData: true
      addBaseImageData: true
  - task: Bash@3
    displayName: Bash Script
    inputs:
      targetType: inline
      script: >-
        git tag $(DOCKER_IMAGE_NAME)

        git push origin $(DOCKER_IMAGE_NAME)
    env:
      MY_ACCESS_TOKEN: $(System.AccessToken)
  - task: Bash@3
    displayName: Insert the values into database
    inputs:
      targetType: inline
      script: 'curl -X POST "https://qmigapiv2.azurewebsites.net/api/v1/RepoAccess/addImageDetails" -H  "Authorization: Bearer $(token)" -H  "Content-Type: application/json" -d "{\"migration_id\":\"$(migration_id)\",\"mig_path\":\"$(mig_path)\",\"clientId\":\"$(clientId)\",\"module_id\":\"1\",\"project_id\":\"1\",\"tag\":\"$(DOCKER_IMAGE_NAME)\"}"'

import { Component, Query } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { PerofmanceTestingService } from '../../../../services/performanceTesting.service';
import { ActivatedRoute } from '@angular/router';
import * as XLSX from 'xlsx';


declare let $: any;
@Component({
  selector: 'app-duplicate-index',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],

  templateUrl: './duplicate-index.component.html',
  styles: ``
})

export class DuplicateIndexComponent {
  perfForm:any = this.formBuilder.group({
     tgtconnection: ['', Validators.required],
     schemaname: ['', Validators.required],
    // connectionType: ['', Validators.required],
  });
  hidedata: boolean = true;
  data: boolean = false;
  projectId: string;
  ref_spin: boolean = true;
  tabledata: any;
  schemaName: any = [];
  selectedschemas: any = [];
  fileName: string = '';
  pageNumber: number = 1;
  exe: any;
  contype: string = ""
  dup: string = ""
  mon: string = ""
  tgtId: any
  tgtValue: string = ''
  z: any;
  i: any;
  conName: any
  conId: any;
  ConsList: any;
  tgtList: any
  hideD: boolean = false
  value: any;
  selectedDuplicate: string = "";
  perSchema: any;
  perSchemas: any;
  TableData: boolean = false;
  Exespinner: boolean = false;
  Updatespinner: boolean = false;
  excelSpin: boolean = false;
  pageName: string = '';
  testing: any;
  uploadForm: any;
  ConId: any;
  selectedObjItems1 = [];
 source_count1 = 0;
  
  get f(): any {
    return this.uploadForm.controls;
  }


  constructor(private toast: HotToastService, private performanceservices: PerofmanceTestingService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    
  }

  /*--- Page Title ---*/
  pageTitle: string = "Documents"
  pageIcon: string = "assets/images/documents.svg"


  ngOnInit(): void {
    this.GetConsList();
    this.pageName = this.route.snapshot.data['name'];
  }
  

  
  /*--- Validation ---*/
  get validate() {
    return this.perfForm.controls;
  }

  /*--- Schema   ---*/
  GetperfSchemas() {
    const obj = {
      Conid: this.tgtId,
    }
    this.performanceservices.PerfSchemanamewithAll(obj).subscribe((data: any) => {
      this.perSchemas = data;
      // this.perSchema = this.perSchemas.sort((a:any, b:any) => a.schemaname.localeCompare(b.schemaname));
      this.perSchema = this.perSchemas.sort((a: any, b: any) => {
        // If 'a' or 'b' is 'all', place it at the beginning
        if (a.schemaname === 'all') return -1; // 'all' should come first
        if (b.schemaname === 'all') return 1;  // 'all' should come first

        // Otherwise, perform alphabetical sorting
        return a.schemaname.localeCompare(b.schemaname);
      });
    })
  }
  suggestedindexes:any;
  DuplicateCount:any;
  OriginalCount:any;
  CurrentTime:any;
  CurrentTimes:any;
  RecomndedIndex() {
    const obj = {
      Conid: this.tgtId,
    }
    this.performanceservices.GetSuggestedIndex(obj).subscribe((data: any) => {
      this.suggestedindexes = data;
      this.DuplicateCount =data[0].total_duplicate_count;
      this.OriginalCount =data[0].total_original_count;
      this.CurrentTimes =data[0].current_time;
      this.CurrentTime =this.removeMilliseconds(this.CurrentTimes);
    })
    
  }

  removeMilliseconds(dateTime: string): string {
    // Match and remove everything after the seconds (including milliseconds)
    const result = dateTime.replace(/\.\d+$/, '');
    return result;
  }

  /*--- SelectContype   ---*/
  selectContype(value: string) {
    this.contype = value;
    if (value == "0") {
      this.hidedata = true;
      this.data = false;
    }
    else {
      this.hidedata = false;
      this.data = true;
    }
  }
 
  /*--- Get Operation List   ---*/
  selTgtId(value: any) {
    this.tgtId = value;
    this.tgtList.filter((el: any) => { return el.filename == value ? this.tgtValue = el.conname : '' });
    this.GetperfSchemas();
    this.RecomndedIndex();
    this.FailedDuplicateIndex();
  }

 

  /*--- GetReqTableData   ---*/
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.performanceservices.GetReqData(obj)?.subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.ConsList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_name == "Storage_Objects"
        })
        //console.log(this.tabledata)
      }
    })
  }
  
  ftsFiles: any
  /*--- GetConsList ---*/
  GetConsList() {
    this.performanceservices.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.getreqTableData()
      this.ref_spin = false
    })
  }

  /*--- selectDupliacte ---*/
  selectDuplicate(value: string) {
    this.selectedDuplicate = value
    if (value == "1") {
      this.hideD = true
    }
    else {
      this.hideD = false;
    }
  }

  selectSchema(value: any) {
    this.selectedschemas = value;
  }


  /*--- GetDuplicate Index ---*/
  duplicateIndexData: any
  GetDuplicateIndexData(value:any) {
     this.Exespinner = true;
    this.TableData = true;
   
      const obj = {
        conId: this.tgtId.toString(),
        Query:value.schemaname.toString(),
      }
    //this.spinner=false
    this.performanceservices.DuplicateIndexSchema(obj).subscribe((data: any) => {
      this.duplicateIndexData = data;
      this.Exespinner = false;
    })

    //console.log(this.perSchema)
  }

 
  GetDuplicateIndexDatas() {
    this.TableData = true;
    var obj = {}
    if (parseInt(this.selectedDuplicate) == 0) {
      obj = {
        conId: this.tgtId,
        option: parseInt(this.selectedDuplicate),
      }
    }
    else {
      var schms: any = []
      if (this.selectedschemas.length > 0) {
        this.selectedschemas.filter((item: any) => {
          schms.push("'" + item + "'")
        })
        schms = schms.toString()
      }
      obj = {
        conId: this.tgtId.toString(),
        schemas: schms,
        option: parseInt(this.selectedDuplicate),
      }
    }
    //this.spinner=false
    this.performanceservices.GetDuplicateIndexData(obj).subscribe((data: any) => {
      this.duplicateIndexData = data;
      this.Exespinner = false;
    })

    //console.log(this.perSchema)
  }
  /*--- exportExcelAnalyze ---*/
  fileNamee = 'DuplicateIndexes.xlsx';
  testingg: any = []
  excelSpinn: any = false;
  exportexcelAnalyze(): void {
    this.testingg = []
    this.excelSpinn = true;
    var test = this.duplicateIndexData
    for (var el of test) {
      var newEle: any = {};
      newEle.Schemaname = el.schemaname;
      newEle.Tablename = el.tablename;
      //newEle.IndexColumns=el.indexColumns;
      newEle.Indexname = el.indexname;
      newEle.IndexType = el.indexType;
      newEle.DropScript = el.dropScripts;
      this.testingg.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testingg);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileNamee);
    this.excelSpinn = false;
  }
  

  onCheckboxChange(con: any) {
    if (con.isSelected) {
      // Add selected data to the selectedData array (add data.sno)
      if (!this.selectedDatas.includes(con.dropScripts)) {
        this.selectedData.push(con.indexType);
        this.selectedDatas.push(con.dropScripts);
      }
    } else {
      // Remove unselected data from the selectedData array (remove data.sno)
      const indexs = this.selectedData.findIndex(item => item === con.indexType);
      const index = this.selectedDatas.indexOf(con.dropScripts);
      if (indexs !== -1) {
        this.selectedData.splice(indexs, 1);
          this.selectedDatas.splice(index, 1);
          this.selectedDatas=[];
          this.selectedData=[];
      }
    }
    // Update selectAll checkbox based on child checkboxes state
    this.updateSelectAllState();
}




  // Method to update the state of the 'Select All' checkbox
updateSelectAllState() {
  this.selectAll = this.duplicateIndexData.every((con: { isSelected: any; }) => con.isSelected);
}

toggleAllSelection() {
  // Set all rows' isSelected based on the state of selectAll checkbox
  this.duplicateIndexData.forEach((con: { isSelected: boolean; }) => {
    con.isSelected = this.selectAll;
  });

  // Update the selectedData array to reflect all selected or none selected
  if (this.selectAll) {
    this.selectedData = this.duplicateIndexData.map((con: { indexType: any; }) => con.indexType);
    this.selectedDatas = this.duplicateIndexData.map((con: { dropScripts: any; }) => con.dropScripts);
    // Add all sno values
  } else {
    // Clear selected data
    this.selectedDatas = [];
    this.selectedData = [];
  }
}

replaceAndCleanContent(content: string): string {
  // Step 1: Replace all occurrences of `;,,` with `;`
  content = content.replace(/;,,/g, ";");
  content = content.replace(/;,/g, ";");
  // Step 2: If the content starts with a comma, remove the first comma
  if (content.startsWith(",")) {
    content = content.substring(1);  // Remove the first character (the comma)
  }
  return content;
}


selectedData: any[] = [];
selectedDatas:any[]=[]; // This will hold the selected data for API request
selectedSnos: number[] = [];
selectAll: boolean = false;
selectedDatass:string='';
updateBtn: boolean = false;
currentlyCheked(isChecked: any) {
  if (isChecked.target.checked) {
     this.updateBtn = true;
  } else {
    this.updateBtn = false;
  }
}

PerfDuplicateindex:any;
PerfDuplicateindexUpdate()
{
let obj={
  Conid:this.tgtId,
  Query:this.replaceAndCleanContent(this.selectedDatas.toString()),
}
this.PerfDuplicateindex=[];
this.Updatespinner=true;
this.performanceservices.DroppedIndexUpdate(obj).subscribe((data: any) => 
{
  this.PerfDuplicateindex=data[0];
  this.selectedDatas=[];
  this.RecomndedIndex();
  this.GetDuplicateIndexDatas();
  this.Updatespinner=false;
  this.updateBtn = false;
  this.toast.success("Dropped for selected checkboxes- Success : "+this.PerfDuplicateindex.successCount +" failed :  "+this.PerfDuplicateindex.failedCount);
  this.PerfDuplicateindex.successCount = 0;
this.PerfDuplicateindex.failedCount = 0;
 
});

}

   fileNamees = 'FailedDuplicateIndexes.xlsx';
   FailedDuplicateIndexes: any = [];
   FailedDuplicate: any = false;
   FailedDuplicateIndexExport(): void {
    this.FailedDuplicateIndexes = []
    this.FailedDuplicate = true;
    var test = this.FailedDuplicateIndexData;
    for (var el of test) {
      var newEle: any = {};
      newEle.Id = el.id;
      newEle.Index_name = el.indexname;
      newEle.Drop_time = el.droptime;
      newEle.Status = el.status;
      newEle.Message = el.message;
      newEle.Index_script = el.indexscript;
      this.FailedDuplicateIndexes.push(newEle);
    }
    this.FailedDuplicate = false;
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.FailedDuplicateIndexes);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileNamees);
  }

  FailedDuplicateIndexData:any;
  FailedDuplicateIndex() {
    const obj = {
      Conid: this.tgtId,
    }
    this.performanceservices.FailedIndexes(obj).subscribe((data: any) => {
      this.FailedDuplicateIndexData = data;
    })
    
  }

}

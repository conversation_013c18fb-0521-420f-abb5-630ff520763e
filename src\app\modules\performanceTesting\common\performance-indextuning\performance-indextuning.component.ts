import { Component, ElementRef, ViewChild } from '@angular/core';
import { HotToastService } from '@ngxpert/hot-toast';
import { PerofmanceTestingService } from '../../../../services/performanceTesting.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { interval } from 'rxjs';
import * as XLSX from 'xlsx';

declare let $: any;
@Component({
  selector: 'app-performance-indextuning',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './performance-indextuning.component.html',
  styles: ``
})
export class PerformanceIndextuningComponent {
  hidedata: boolean = true;
  data: boolean = false;
  projectId: string;
  ref_spin: boolean = true;
  ref_spin2: boolean = false;
  ref_spin3: boolean = false;
  ref_spin1: boolean = false;
  ref_spin4: boolean = false;
  PerflogstatusData: any;
  PerflogFile: any;
  tabledata: any;
  schemaName: any = [];
  selectFile: any;
  fileName: string = '';
  FileNameUpload: string = '';
  uploadfileSpin: boolean = false;
  fileAdd: boolean = false;
  projectDocuments: any;
  projectDocumentFilter: any;
  pageNumber: number = 1;
  pageNumber1: number = 1;
  pageNumber2: number = 1;
  searchText: string = '';
  exe: any;
  contype: string = ""
  contypes: string = ""
  dup: string = ""
  mon: string = ""
  tgtId: any;
  ustgtId: any;
  BltgtId:any;
  untgtId: any;
  filenameup: any;
  tgtIds: any;
  tgtIdd: any;
  tgtValue: string = ''
  fileValue: string = ''
  z: any;
  i: any;
  selectedConname: any
  conName: any
  conId: any;
  ConsList: any;
  tgtList: any
  selectedCategory: string = ""
  hideDrpdwn: boolean = false
  hideD: boolean = false
  value: any;
  selectedDuplicate: string = "";
  selectedMonitor: string = "";
  P2: number = 1;
  P3: number = 1;
  perSchema: any;
  perSchemas: any;
  IndexCount: any;
  IndexTimeStamp: any;
  DroppedIndexData: any;
  selectedItems = [];
  selectedObjItems1 = [];
  perSchema1: any;
  exe1: any;
  p: number = 1;
  p1: number = 1;
  TableData: boolean = false;
  analyzesearchlist: any;
  Exespinner: boolean = false;
  Monspinner: boolean = false;
  excelSpin: boolean = false;
  hideMon: boolean = false;
  updateBtn: boolean = false;

  pageName: string = ''
  indexMonitor: any;
  Monitor: boolean = false;
  testing: any;
  hideLog: boolean = false;
  uploadForm: any;
  Executionform: any;
  perfForm: any;
  Logform: any;
  ExcelForm: any;
  UsedIndexForm: any;
  BloatedForm:any;
  unUsedIndexForm: any;
  hideTable: boolean = false;
  Callstm: any;
  task: any;
  ParTable: any;
  ConId: any;
  stat: boolean = false;
  IndexTuningTableData: any;
  IndexeExceldata: any;
  hideQuery: boolean = false;
  selectedLog: string = "";
  hideL: boolean = false;
  hideS: boolean = false;
  hidedate: boolean = false;
  filesName: string = "";
  excelSpinn: boolean = false;
  subscription: any;
  files: string = "";
  selectedObjItems2 = [];
  selectedObjItems4 = [];
  selectedObjItems3 = [];
  // isAllSelected = false;

  get f(): any {
    return this.uploadForm.controls;
  }
  constructor(private toast: HotToastService,
    private performanceservices: PerofmanceTestingService,
    public formBuilder: FormBuilder,
    private route: ActivatedRoute) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
  }

  /*--- Page Title ---*/
  pageTitle: string = "Documents"
  pageIcon: string = "assets/images/documents.svg"


  ngOnInit(): void {
    this.GetConsList();
    this.getFiles();
    this.pageName = this.route.snapshot.data['name'];
    this.uploadForm = this.formBuilder.group({
      file1: ['', [Validators.required]],
    });
    this.Executionform = this.formBuilder.group({
      filenamee: ['', Validators.required],
      tgtconn: ['', Validators.required],
    });
    this.perfForm = this.formBuilder.group({
      tgtconnection: ['', Validators.required],
    });
    this.Logform = this.formBuilder.group({
      filenamee1: ['', Validators.required],
    });

    this.ExcelForm = this.formBuilder.group({
      fromDate: ['', Validators.required],
      toDate: ['', Validators.required],
      tgtconnect: ['', Validators.required],
    });

    this.UsedIndexForm = this.formBuilder.group({
      ustgtconn: ['', Validators.required],
      number1: ['', Validators.required],
      schemanames: ['', Validators.required],
    });

    this.BloatedForm = this.formBuilder.group({
      bltgtconn: ['', Validators.required],
      schemanamebl: ['', Validators.required],
    });

    this.unUsedIndexForm = this.formBuilder.group({
      untgtconn: ['', Validators.required],
      number2: ['', Validators.required],
      schemanamess: ['', Validators.required],
    });

    // this.subscription = interval(3000).subscribe(() => {
    //   this.GetPerflogFilestatus();
    // });

  }

  /*--- Validation ---*/
  get validate() {
    return this.perfForm.controls;
  }

  get valdate() {
    return this.ExcelForm.controls;
  }
  get usvaldate() {
    return this.UsedIndexForm.controls;
  }

  get blvaldate() {
    return this.BloatedForm.controls;
  }
  get unvaldate() {
    return this.unUsedIndexForm.controls;
  }

  get validatee() {
    return this.Executionform.controls;
  }

  get validates() {
    return this.uploadForm.controls;
  }

  /*--- Schema   ---*/
  GetperfSchemas() {
    const obj = {
      conId: this.tgtId,
    }
    this.performanceservices.PerfSchemanamewithAll(obj).subscribe((data: any) => {
      this.perSchemas = data;
      // this.perSchema = this.perSchemas.sort((a:any, b:any) => a.schemaname.localeCompare(b.schemaname));
      this.perSchema = this.perSchemas.sort((a: any, b: any) => {
        // If 'a' or 'b' is 'all', place it at the beginning
        if (a.schemaname === 'all') return -1; // 'all' should come first
        if (b.schemaname === 'all') return 1;  // 'all' should come first

        // Otherwise, perform alphabetical sorting
        return a.schemaname.localeCompare(b.schemaname);
      });
    })
  }

  RecomondedIndexCount() {
    const obj = {
      Conid: this.tgtId,
    }
    this.IndexCount = [];
    this.performanceservices.RecomondedIndexCount(obj).subscribe((data: any) => {
      this.IndexCount = data[0].count;
    })
  }

  RecomondedIndexTimeStamp() {
    const obj = {
      Conid: this.tgtId,
    }
    this.IndexTimeStamp = [];
    this.performanceservices.IndexeTimeStamp(obj).subscribe((data: any) => {
      this.IndexTimeStamp = data[0].latestTimestamp;
    })
  }

  DroppedIndex() {
    const obj = {
      Conid: this.tgtId,
    }
    this.DroppedIndexData = [];
    this.performanceservices.DroppedIndexCount(obj).subscribe((data: any) => {
      this.DroppedIndexData = data[0].total;
    })
  }



  /*--- SelectContype   ---*/
  selectContype(value: string) {
    this.contype = value;
    if (value == "0") {
      this.hidedata = true;
      this.data = false;
    }
    else {
      this.hidedata = false;
      this.data = true;
    }
  }

  selectContypes(value: string) {
    this.contype = value;
  }

  /*--- Get Operation List   ---*/
  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.filename == value ? this.tgtValue = el.conname : '' });
    this.RecomondedIndexCount();
    this.RecomondedIndexTable();
    this.RecomondedIndexTimeStamp();
    this.DroppedIndex();
  }

  GetperfSchemasu() {
    const obj = {
      Conid: this.ustgtId,
    }
    this.performanceservices.PerfSchemanamewithAll(obj).subscribe((data: any) => {
      this.perSchemas = data;
      this.perSchema = this.perSchemas.sort((a: any, b: any) => {
        if (a.schemaname === 'all') return -1; // 'all' should come first
        if (b.schemaname === 'all') return 1;  // 'all' should come first
        return a.schemaname.localeCompare(b.schemaname);
      });
    })
  }

  GetperfSchemaBloat() {
    const obj = {
      Conid: this.BltgtId,
    }
    this.performanceservices.PerfSchemanamewithAll(obj).subscribe((data: any) => {
      this.perSchemas = data;
      this.perSchema = this.perSchemas.sort((a: any, b: any) => {
        if (a.schemaname === 'all') return -1; // 'all' should come first
        if (b.schemaname === 'all') return 1;  // 'all' should come first
        return a.schemaname.localeCompare(b.schemaname);
      });
    })
  }

  GetperfSchemasun() {
    const obj = {
      Conid: this.untgtId,
    }
    this.performanceservices.PerfSchemanamewithAll(obj).subscribe((data: any) => {
      this.perSchemas = data;
      this.perSchema = this.perSchemas.sort((a: any, b: any) => {
        if (a.schemaname === 'all') return -1; // 'all' should come first
        if (b.schemaname === 'all') return 1;  // 'all' should come first
        return a.schemaname.localeCompare(b.schemaname);
      });
    })
  }
  selTgtused(value: any) {
    this.ustgtId = value;
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
    this.GetperfSchemasu();
  }
  selTgtbloat(value: any) {
    this.BltgtId = value;
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
    this.GetperfSchemaBloat();
  }
  selTgtunused(value: any) {
    this.untgtId = value;
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
    this.GetperfSchemasun();
  }

  selTgtIds(value: any) {
    this.tgtIds = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
  }
  selTgt(value: any) {
    this.tgtIdd = value
    this.tgtList.filter((el: any) => { return el.filename == value ? this.tgtValue = el.conname : '' });
  }
  selectFilenames(value: any) {
    this.filenameup = value;
    this.PerflogstatusData.filter((el: any) => { return el.filename == value ? this.fileValue = el.conname : '' });
  }





  /*--- GetReqTableData   ---*/
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.performanceservices.GetReqData(obj)?.subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.ConsList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_name == "Storage_Objects"
        })
      }
    })
  }

  ftsFiles: any
  /*--- GetConsList ---*/
  GetConsList() {
    this.performanceservices.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.getreqTableData()
      this.ref_spin = false
    })
  }

  /*--- selectDupliacte ---*/
  selectDuplicate(value: string) {
    this.selectedDuplicate = value
    if (value == "1") {
      this.hideD = true
    }
    else {
      this.hideD = false;
    }
  }

  RecomondedIndexTable() {
    const obj = {
      Conid: this.tgtId,
    }
    this.performanceservices.RecomondedIndexTable(obj).subscribe((data: any) => {
      this.IndexTuningTableData = data;
      this.IndexTuningTableDatas = data;
    })
  }
  excelspind:boolean=false;
  IndexeExcelDownLoad(value: any) {
    let obj = {
      Conid: this.tgtIdd,
      fromdate: value.fromDate,
      todate: value.toDate
    }
    this.excelspind=true;
    this.performanceservices.IndexeExcelDownLoad(obj).subscribe((data: any) => {
      this.IndexeExceldata = data;
      this.exportexcelIndexTuningExcel();
      this.RecomondedIndexTable();
      this.excelspind=false;
    });
  }
  /*--- exportExcelTablePartition ---*/
  fileName2 = 'IndexTuning.xlsx';
  IndexTuning: any = []
  exportexcelIndexTuning(): void {
    this.IndexTuning = []
    this.excelSpinn = true
    var test = this.IndexTuningTableData
    for (var el of test) {
      var newEle: any = {};
      newEle.Sno = el.sno;
      newEle.ColumnName = el.columnName;
      newEle.Date = el.date;
      newEle.Indexsuggested = el.indexSugg;
      newEle.SchemaName = el.schemaName;
      newEle.Status = el.status;
      newEle.TableName = el.tableName;
      this.IndexTuning.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.IndexTuning);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName2);
  }


  IndexTuningExcel: any = []
  exportexcelIndexTuningExcel(): void {
    this.generateFileName();
    this.IndexTuningExcel = [];
    this.excelSpinn = true
    var test = this.IndexeExceldata;
    for (var el of test) {
      var newEle: any = {};
      newEle.datname = el.datname;
      newEle.query_id = el.queryid;
      newEle.query_type = el.querytype;
      newEle.cpu_pctg = el.cpupctg;
      newEle.total_calls = el.totalcalls;
      newEle.total_time = el.totaltime;
      newEle.avg_time_sec = el.avgtimesec;
      newEle.avg_rows = el.avgrows;
      newEle.seq_scan_table = el.seqscantable;
      newEle.filter_columns = el.filtercolumns;
      newEle.plan_text = el.plantext;
      newEle.query_sql_text = el.querysqltext;
      this.IndexTuningExcel.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.IndexTuningExcel);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.files);
  }

  generateFileName() {
    const date = new Date();

    // Convert to Indian Standard Time (IST) (UTC +5:30)
    const indianTime = new Date(date.getTime() + (5.5 * 60 * 60 * 1000)); // Adding 5 hours and 30 minutes to UTC time

    // Format the IST date as YYYY-MM-DD_HH-MM-SS
    const formattedDate = indianTime.toISOString()
      .replace(/T/, '_')        // Replace 'T' with '_'
      .replace(/\..+/, '')      // Remove milliseconds
      .replace(/:/g, '-');      // Replace ':' with '-'

    // Generate the filename with the date and time in IST
    this.files = `IndexTuningInsertion_${formattedDate}.csv`;
  }

  /*--- UploadFile   ---*/

  uploadFile() {
    this.uploadfileSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', "PRJ1167SRC/Performance_Tuning/Index_Tuning/Reference_Files");
    this.performanceservices.UploadCloudFiles(formData).subscribe(
      (response: any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        // this.getDocuments();
        this.getFiles();
        this.toast.success(response.message)
        $('#demo').offcanvas('hide');
      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.toast.error('Something went wrong')
        $('#demo').offcanvas('hide');
      })
  }
  spin_process: any;
  uploadedData: any = [];
  getFiles() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Performance_Tuning/Index_Tuning/Reference_Files"
    };
    this.performanceservices.getFiles(requestObj).subscribe((data) => {
      this.uploadedData = data;
      // this.uploadedData = this.uploadedData.reverse();
    });
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.FileNameUpload = event.target.files[0].name;
  }
  tablespin:boolean=false;
  TablePartitionInsert(value: any) {
    const obj = {
      task: "Index_Tuning",
      projectId: this.projectId.toString(),
      operation: "File",
      option: 1,
      target_connection_id: this.tgtIds,
      fileName: this.removeFileExtension(value.filenamee),
      tgtId: this.tgtIds,
    }
    this.tablespin=true;
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.Callstm = data;
      this.toast.success(data.message);
      this.tablespin=false;
    })
  }

  GetPerflogFiles() {
    // const obj = {
    //   Conid: this.tgtId,
    // }
    this.ref_spin2 = true;
    this.performanceservices.GetPerflogFiles().subscribe((data: any) => {
      this.PerflogFile = data;
      this.ref_spin2 = false;
    })
  }

  removeFileExtension(filename: string): string {
    // Using regular expression to remove any extension
    return filename.replace(/\.[^/.]+$/, '');
  }
  GetPerflogFilestatus() {
    const obj = {
      Conid: this.tgtId,
      file_name: this.removeFileExtension(this.filenameup),
    }
    this.ref_spin3 = true;
    this.performanceservices.GetPerflogstatus(obj).subscribe((data: any) => {
      this.PerflogstatusData = data;
      this.ref_spin3 = false;
    })
  }


  // ngOnDestroy(): void {
  //   // Unsubscribe to prevent memory leaks
  //   this.subscription.unsubscribe();
  // }

  selectedData: any[] = [];
  selectedDatas: any[] = []; // This will hold the selected data for API request
  selectedSnos: number[] = [];
  selectAll: boolean = false;
  IndexTuningTableDatas: any;
  // Method to handle checkbox changes
  // Method to handle checkbox changes
  onCheckboxChange(con: any) {
    if (con.isSelected) {
      // Add selected data to the selectedData array (add data.sno)
      this.selectedData.push(con.sno);
      this.selectedDatas.push(con.indexSugg);
    } else {
      // Remove unselected data from the selectedData array (remove data.sno)
      const index = this.selectedData.findIndex(item => item === con.sno);
      const indexs = this.selectedDatas.indexOf(con.indexSugg);
      if (index !== -1) {
        this.selectedData.splice(index, 1);
        this.selectedDatas.splice(indexs, 1);
        this.selectedDatas=[];
          this.selectedData=[];
      }
    }

    // Update selectAll checkbox based on child checkboxes state
    this.updateSelectAllState();
  }
  // Method to update the state of the 'Select All' checkbox
  updateSelectAllState() {
    this.selectAll = this.IndexTuningTableDatas.every((con: { isSelected: any; }) => con.isSelected);
  }

  // Method to handle Select All checkbox changes
  toggleAllSelection() {
    // Set all rows' isSelected based on the state of selectAll checkbox
    this.IndexTuningTableDatas.forEach((con: { isSelected: boolean; }) => {
      con.isSelected = this.selectAll;
    });

    // Update the selectedData array to reflect all selected or none selected
    if (this.selectAll) {
      this.selectedData = this.IndexTuningTableDatas.map((con: { sno: any; }) => con.sno);
      this.selectedDatas = this.IndexTuningTableDatas.map((con: { indexSugg: any; }) => con.indexSugg);
      // Add all sno values
    } else {
      this.selectedData = [];  // Clear selected data
      this.selectedDatas = [];
    }

  }

  currentlyCheked(isChecked: any) {
    if (isChecked.target.checked) {
      this.updateBtn = true;
    } else {
      this.updateBtn = false;
    }
  }
  PerfStatusindex: any;
  PerfIndexUpdate() {
    let obj = {
      Conid: this.tgtId,
      Column: this.selectedData.toString(),
    }
    this.updateBtn=true;
    this.PerfStatusindex = [];
    this.performanceservices.PerfIndexStatusUpdate(obj).subscribe((data: any) => {
      this.PerfStatusindex = data[0];
      this.updateBtn=false;
      this.RecomondedIndexCount();
      this.RecomondedIndexTable();
      this.RecomondedIndexTimeStamp();
      this.DroppedIndex();
      this.updateBtn = false;
    });
    this.RecomondedIndexTable();
  }

  Perfindexstate: any;
  indexupda:boolean=false;
  PerfIndexExecution() {
    let obj = {
      Conid: this.tgtId,
      Query: this.replaceAndCleanContent(this.selectedDatas.toString()),
    }
    this.indexupda=true;
    this.Perfindexstate = [];
    this.performanceservices.IndexStatementExecute(obj).subscribe((data: any) => {
      this.Perfindexstate = data[0];
      this.selectedDatas=[];
      this.indexupda = false;
      this.toast.success("Created Index for selected checkboxes- Success : " + this.Perfindexstate.successCount + " failed :  " + this.Perfindexstate.failedCount);
      this.Perfindexstate.successCount = 0;
      this.Perfindexstate.failedCount = 0;
    });
    this.RecomondedIndexCount();
    this.DroppedIndex();
  }

  replaceAndCleanContent(content: string): string {
    // Step 1: Replace all occurrences of `;,,` with `;`
    content = content.replace(/;,,/g, ";");
    content = content.replace(/;,/g, ";");
    // Step 2: If the content starts with a comma, remove the first comma
    if (content.startsWith(",")) {
      content = content.substring(1);  // Remove the first character (the comma)
    }
    return content;
  }

  UsedIndexesData: any;
  UnUsedIndexesData: any;
  BloatIndexesData: any;
  BloatIndexesExcelData: any;
  RatioIndexesData: any;
  GetUsedIndexes(value: any) {
    const obj = {
      Conid: this.ustgtId,
      schemaname: value.schemanames.toString(),
      Days: value.number1.toString(),
    };
    this.ref_spin1 = true;
    this.UsedIndexesData = [];
    this.performanceservices.GetUsedIndexes(obj).subscribe((data: any) => {
      this.UsedIndexesData = data;
      this.ref_spin1 = false;
    })
  }
  schemanam = ['null'];
  GetUnUsedIndexes(value: any) {
    const obj = {
      Conid: this.untgtId,
      schemaname: value.schemanamess.toString(),
      Days: value.number2.toString(),
    };
    this.ref_spin2 = true;
    this.UnUsedIndexesData = [];
    this.performanceservices.GetUnusedIndexes(obj).subscribe((data: any) => {
      this.UnUsedIndexesData = data;
      this.ref_spin2 = false;
    })
  }

  selectSchema1(value: any) {


  }

  selectSchema2(value: any) {

  }

  excelSpinn1: any = false;
  fileName1 = 'UsedIndexes.xlsx';
  UsedIndexes: any = []
  exportexcelusedIndex(): void {
    this.UsedIndexes = [];
    this.excelSpinn1 = true;
    var test = this.UsedIndexesData;
   for (var el of test) {
     var newEle: any = {};
     newEle.IndexName = el.indexname;
     newEle.Schema = el.schemaname;
     newEle.TableName = el.tablename;
     newEle.IndexSize = el.index_size;
     newEle.TotalScans = el.totalscans;
     newEle.Totalfetch = el.totalfetch;
     this.UsedIndexes.push(newEle);
   }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.UsedIndexes);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName1);
  }
  fileNamee2 = 'UnUsedIndexes.xlsx';
UnUsedIndexes: any = []
   excelSpinn2: any = false;
   exportexcelUnUsedIndex(): void {
    this.UnUsedIndexes = [];
    this.excelSpinn2 = true;
    var test = this.UnUsedIndexesData;
    for (var el of test) {
      var newEle: any = {};
      newEle.IndexName = el.indexname;
      newEle.SchemaName = el.schemaname;
      newEle.TableName = el.tablename;
      newEle.IndexSize = el.index_size;
      newEle.TotalScans = el.totalscans;
      newEle.TotalFetch = el.totalfetch;
      this.UnUsedIndexes.push(newEle);
    }
     const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.UnUsedIndexes);
     const wb: XLSX.WorkBook = XLSX.utils.book_new();
     XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
     XLSX.writeFile(wb, this.fileNamee2);
   }

   GetBloatedIndexExcel(value:any) {
    const obj = {
      Conid:this.BltgtId,
      schemaname:value.schemanamebl.toString(),
    }   
    this.performanceservices.BloatedIndexExcel(obj).subscribe((data: any) => {
    this.BloatIndexesExcelData = data;  
    })
  }

  fileNamee3 = 'BloatIndex.xlsx';
  BloatIndex: any = []
  excelSpinn3: any = false;
  exportexcelShowlogsBloat(): void {
    this.BloatIndex = [];
    this.excelSpinn3 = true;
    var test = this.BloatIndexesExcelData;
    for (var el of test) {
      var newEle: any = {};
       //  newEle.IndexName = el.indexname;
     //  newEle.TableName = el.tablename;
     //  newEle.Schema = el.schemaname;
     //  newEle.IndexSize = el.indexsize;
         newEle.VacumTable = el.vacumtable;
     //  newEle.Reindex = el.reindex;
      this.BloatIndex.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.BloatIndex);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileNamee3);
  }

  dataFetched:boolean = true
GetBloatedIndexes(value:any) {
  this.dataFetched = false
  const obj = {
    Conid: this.BltgtId,
      schemaname: value.schemanamebl.toString(),
  }   
  this.ref_spin3=true;
  this.BloatIndexesData=[];
  this.performanceservices.BloatedIndexes(obj).subscribe((data: any) => {
  this.BloatIndexesData = data;  
  this.ref_spin3=false;
  })
}
IndexTuningFiledlogData:any;
IndexTuningFiledlog()
{
  const obj = {
    Conid: this.tgtId,
  }   
  this.performanceservices.IndexTuningFiledlog(obj).subscribe((data: any) => {
    this.IndexTuningFiledlogData = data;  
    this.IndexTuningFiledlogs();
})
}

 /*--- exportExcelTablePartition ---*/
 IndexTuningFail = 'IndexTuningFailed.xlsx';
 IndexTuningFiled: any = [];
 IndexTuningF:boolean=false;
 IndexTuningFiledlogs(): void {
   this.IndexTuningFiled = []
   this.IndexTuningF = true;
   var test = this.IndexTuningFiledlogData
   for (var el of test) {
     var newEle: any = {};
     newEle.Sno = el.id;
     newEle.ColumnName = el.indexname;
     newEle.Date = el.creationdate;
     newEle.Status = el.status;
     newEle.Messege = el.messege;
     this.IndexTuningFiled.push(newEle);
   }
   const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.IndexTuningFiled);
   const wb: XLSX.WorkBook = XLSX.utils.book_new();
   XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
   XLSX.writeFile(wb, this.IndexTuningFail);
   this.IndexTuningF = true;
 }

}

import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
@Component({
  selector: 'app-upload-dag-file',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './upload-dag-file.component.html',
  styles: ``
})
export class UploadDagFileComponent {

  /*--- Page Title ---*/

  pageTitle: string = "Documents"
  pageIcon: string = "assets/images/documents.svg"

  /*--- Dag File Form ---*/

  getForm: FormGroup = this.formBuilder.group({
    document: ['', [Validators.required]],
  })
  getSpin: boolean = false;
  selFile: any;
  fileName: string = ''
 pageName:string = ''
  constructor(private titleService: Title,private toast: HotToastService, private datamigrationservice: DataMigrationService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.pageName = this.route.snapshot.data['name'];
   }

  ngOnInit(): void {
this.titleService.setTitle(`QMigrator - ${this.pageName}`);
  }

  /*--- Validation Form ---*/
  get f() {
    return this.getForm.controls;
  }
  /*--- Select Dag File  ---*/

  onFileSelected1(event: Event) {
    const File = event.target as HTMLInputElement;

    if (!File.files?.length) {
      return;
    }

    const file = File.files[0];
    this.selFile = file
    this.fileName = File.files[0].name;
    //console.log(file);
    // const file: File = event.target.files[0];
    // this.selFile = file
    // this.fileName = event.target.files[0].name;
    //console.log(this.selFile)
    //console.log(this.selFile.name)
  }

  /*--- openPopup ---*/
  openPopup() {
    this.getForm.reset();
    this.fileName = ''
  }

  /*--- Upload File ---*/
  uploadFile() {
    this.getSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selFile, this.selFile.name);
    formData.append('path', "");
    //console.log(formData)
    this.datamigrationservice.UploadDagFiles(formData).subscribe(
      response => {
        this.getSpin = false
        this.toast.success("File uploaded successfully")
        //console.log('File uploaded successfully', response);
      },
      error => {
        this.getSpin = false
        this.toast.warning("Error uploading file")
        console.error('Error uploading file', error);
      }
    );
  }
}

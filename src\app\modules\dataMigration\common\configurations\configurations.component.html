<div class="v-pageName">{{pageName}}</div>

<!---Reports & Logs---->
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Create New Configurations
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="getForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Operation </label>
                                        <select class="form-select" formControlName="operation"
                                            (change)="getOP(opvalue.value)" #opvalue>
                                            <option>Select Operation</option>
                                            @for(list of configData;track list; ){
                                            <option value="{{list.configvalue}}">{{list.configtype}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.operation.touched && f.operation.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.operation.errors?.['required']) { operation is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <!-- source connection list -->
                                @if(!incrementSelected || cdcSelected || initialLoadGGSelected || reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Source Connection </label>
                                        <select class="form-select" #org
                                            (change)="getDb(org.value);getSchemasList(org.value,'S')"
                                            formControlName="sourceConnection">
                                            <option>Select Source Connection</option>
                                            @for(list of ConsList;track list; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.sourceConnection.touched && f.sourceConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.sourceConnection.errors?.['required']) { Source Connection is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- target list -->
                                @if(!incrementSelected || cdcSelected || initialLoadGGSelected || reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Target Connection </label>
                                        <select class="form-select" #tgtcon
                                            (change)="getTgtId(tgtcon.value);getSchemasList(tgtcon.value,'T')"
                                            formControlName="targetConnection">
                                            <option>Select Target Connection</option>
                                            @for(list of tgtlist;track list; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.targetConnection.touched && f.targetConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.targetConnection.errors?.['required']) { Target Connection is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- level list -->
                                @if(logminer && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">CDC Level</label>
                                        <select class="form-select" #lvl (change)="selectLevel(lvl.value)"
                                            formControlName="level">
                                            <option>Select CDC Level</option>
                                            @for(le of Level;track le; ){
                                            <option value="{{le.value }}">{{le.option}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.level.touched && f.level.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.level.errors?.['required']) { CDC Level is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- schema list -->
                                @if(!incrementSelected || showschema || initialLoadGGSelected || cdcSelected)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Source Schema </label>
                                        <select class="form-select" #sc (change)="onItemSelect(sc.value)"
                                            formControlName="schemas">
                                            <option>Select Source Schema</option>
                                            @for(sch of schemaList;track sch; ){
                                            <option value="{{sch.schemaname}}">{{sch.schemaname}}</option>
                                            }
                                        </select>
                                        <!-- <ng-select [placeholder]="'Select Schema Name'" formControlName="schemas"
                                            [items]="schemaList" [multiple]="true" bindLabel="schemaname" [closeOnSelect]="false"
                                            bindValue="schemaname" [(ngModel)]="selectedItems" (change)="onItemSelect(selectedItems)">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                        </ng-select> -->
                                        <div class="alert">
                                            @if ( f.schemas.touched && f.schemas.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.schemas.errors?.['required']) { Schemas required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- tableslist -->
                                @if(!incrementSelected && !cdcSelected && !logminer && !showschemaa && !reverseCdc &&
                                !sql_oracletype && !hasMultipleSchemas || migtypeid=="30" || migtypeid=="20"|| migtypeid=="31")
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Tables </label>
                                        <ng-select [placeholder]="'Select Schema Name'" formControlName="tables"
                                            groupBy="type" [selectableGroup]="true" [items]="tablesList"
                                            (change)="tablesSelect(selectedTable)" [multiple]="true"
                                            bindLabel="table_name" [closeOnSelect]="false" bindValue="table_name"
                                            clearAllText="Clear" [clearSearchOnAdd]="true" [(ngModel)]="selectedTable">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }"
                                                    value={{item.table_name}} />
                                                {{item.table_name}}
                                            </ng-template>
                                            <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.table_name}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if ( f.tables.touched && f.tables.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.tables.errors?.['required']) { Tables required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- Target schema list -->
                                @if(!incrementSelected || showschema || initialLoadGGSelected || cdcSelected)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Target Schema </label>
                                        <select class="form-select" #tgtsc (change)="selecttgtschema(tgtsc.value)"
                                            formControlName="tgtschemas">
                                            <option>Select Target Schema</option>
                                            @for(tgtsch of tgtSchemaList;track tgtsch; ){
                                            <option value="{{tgtsch.schemaname}}">{{tgtsch.schemaname}}</option>
                                            }
                                        </select>
                                        <!-- <ng-select [placeholder]="'Select Schema Name'" formControlName="tgtschemas"
                                            [items]="tgtSchemaList" [multiple]="true" bindLabel="schemaname" [closeOnSelect]="false"
                                            bindValue="schemaname" [(ngModel)]="selectedItemstgt" (change)="onItemSelect(selectedItems)">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                        </ng-select> -->
                                        <div class="alert">
                                            @if ( f.tgtschemas.touched && f.tgtschemas.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.tgtschemas.errors?.['required']) { Target Schema required }
                                            </p>
                                            }
                                        </div>
                                        <!-- <div class="alert">
                                            @if ( f.tgtschemas.touched && f.tgtschemas.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.tgtschemas.errors?.['required']) { Target Schema required }
                                            </p>
                                            }
                                        </div> -->
                                    </div>
                                </div>
                                }

                                <!-- list of tables -->
                                @if(showtable || reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Table List
                                            <span class="qmig-tooltip"><i>Comma seprated list of tables name e.g.
                                                    'payroll.employee','payroll.month'</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="TablesList"
                                            id="tbllist" placeholder="'Schema.TableName1','Schema.TableName2'" />
                                        <div class="alert">
                                            @if ( f.TablesList.touched && f.TablesList.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.TablesList.errors?.['required']) { TablesList required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- small table concurrency -->
                                @if(!incrementSelected && !cdcSelected && !reverseCdc && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Small Table Concurrency
                                            <span class="qmig-tooltip"><i>Parallel tasks for small table</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="smallTable" />
                                        <div class="alert">
                                            @if ( f.smallTable.touched && f.smallTable.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.smallTable.errors?.['required']) { SmallTable required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- large table concurrency -->
                                @if(!incrementSelected && !cdcSelected && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Large Table Concurrency
                                            <span class="qmig-tooltip"><i>Parallel tasks for large table</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="largeTable" />
                                        <div class="alert">
                                            @if ( f.largeTable.touched && f.largeTable.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.largeTable.errors?.['required']) { largeTable required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- chunk size -->
                                @if(!incrementSelected && !cdcSelected && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Chunk Size (MB)
                                            <span class="qmig-tooltip"><i>Chunk size for each task in MB</i></span>
                                        </label>
                                        <input class="form-control" type="text" formControlName="chunkSize" id="pattern"
                                            placeholder="" />
                                        <div class="alert">
                                            @if ( f.chunkSize.touched && f.chunkSize.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.chunkSize.errors?.['required']) { Chunk Size is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- configuration name  -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Configuration Name </label>
                                        <input type="text" class="form-control" formControlName="confiName"
                                            placeholder="CONFIG_FILE_DATE" maxlength="15" />
                                        <div class="alert">
                                            @if ( f.confiName.touched && f.confiName.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.confiName.errors?.['required']) { Configuration Name required }
                                                @if (f.confiName.errors?.['maxLength']) { Maximium Length is 15
                                                character's }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                 <!-- Replication Config name  -->
                                 <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Replication Slot Name </label>
                                        <input type="text" class="form-control" formControlName="replication"
                                            placeholder="Replication" maxlength="30" />
                                        <div class="alert">
                                            @if ( f.replication.touched && f.confiName.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.replication.errors?.['required']) { Replication Name required }
                                                @if (f.replication.errors?.['maxLength']) { Maximium Length is 30
                                                character's }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- POD configuration  -->
                                @if( getForm.value.operation == 'GG_Initial_Data_Load' || getForm.value.operation ==
                                'Initial_Data_Load' || getForm.value.operation == 'Partition_Initial_Data_Load'
                                ||getForm.value.operation == 'E2E_Data_Load' )
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Pod Configuration
                                            <span class="qmig-tooltip"><i>Migration pod memory config e.g. Default is
                                                    based on data size</i></span>
                                        </label>
                                        <select class="form-select" formControlName="podConfig">
                                            <option>Select Pod Configuration</option>
                                            <option value="Default">Default</option>
                                            <option value="Custom">Custom</option>
                                        </select>
                                        <div class="alert">
                                            @if ( f.podConfig.touched && f.podConfig.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.podConfig.errors?.['required']) { POD Configuration Name required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- Data Load Type  -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Data Load Type
                                            <span class="qmig-tooltip"><i>Tool/Process for migrating data</i></span>
                                        </label>
                                        <select class="form-select" formControlName="data_LoadType">
                                            <option>Select Data Load Type</option>
                                            <option value="Default">Default</option>
                                            @if(this.migtypeid=="20"){
                                            <option value="Ora2PG">Ora2PG</option>
                                            }
                                            @if(this.migtypeid=="30"){
                                            <option value="Sql_Loader">
                                                Sql Loader
                                            </option>
                                            }
                                            @if(this.migtypeid=="31"){
                                            <option value="Mydumper">
                                                Mydumper
                                            </option>
                                            }
                                            @if(this.migtypeid=="28"){
                                            <option value="Bulk_Copy">
                                                Bulk Copy
                                            </option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.data_LoadType.touched && f.data_LoadType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.data_LoadType.errors?.['required']) { Data Load Type required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- Dag Execute Type  -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Dag Execute Type
                                            <span class="qmig-tooltip"><i>How Dags should get run e.g Auto trigger once
                                                    created</i></span>
                                        </label>
                                        <select class="form-select" formControlName="dagExecute">
                                            <option>Select Dag Execute Type</option>
                                            <option value="Auto_Trigger">Auto Trigger </option>
                                            <option value="Manual">Manual</option>
                                        </select>
                                        <div class="alert">
                                            @if ( f.dagExecute.touched && f.dagExecute.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.dagExecute.errors?.['required']) { Dag Execute Type required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }

                                <!-- Topic name -->
                                <!-- @if(cdcSelected)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Topic Name </label>
                                        <input type="text" class="form-control" formControlName="topicName" />
                                        <div class="alert">
                                        
                                        </div>
                                    </div>
                                </div>
                                } -->
                                <!-- bootstrap server -->
                                <!-- @if(!incrementSelected && !cdcSelected && !initialLoadGGSelected && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Kafka Bootstrap
                                            <span class="qmig-tooltip"><i>Host/IP of Kafka bootstrap for CDC</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="serverIP" />
                                        <div class="alert">
                                            @if ( f.serverIP.touched && f.serverIP.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.serverIP.errors?.['required']) { Server IP is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                } -->
                                <!-- data load -->
                                @if(incrementSelected && !reverseCdc && !cdcSelected)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Data Load Type </label>
                                        <select class="form-select" formControlName="dataLoad" #dtl
                                            (change)="filterConfig(dtl.value)">
                                            <option>Select Data load</option>
                                            @for(file of dataLoadType;track file; ){
                                            <option value="{{file.value }}">{{file.option}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.dataLoad.touched && f.dataLoad.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.dataLoad.errors?.['required']) { Data Load is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- refrence file -->
                                @if(incrementSelected)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Reference Config File</label>
                                        <select class="form-select" formControlName="refFile" #tgtcon>
                                            <option>Select Reference Config File</option>
                                            @for(file of configFiles;track file; ){
                                            <option value="{{file.fileName }}">{{file.fileName}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.refFile.touched && f.refFile.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.refFile.errors?.['required']) { Reference Config File is required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- batch details -->
                                <!-- @if(!cdcSelected && !reverseCdc && !initialLoadGGSelected)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Batch Mode
                                            <span class="qmig-tooltip"><i>Run in Batch mode</i></span>
                                        </label>
                                        <select class="form-select" formControlName="batch">
                                            <option value="True">True</option>
                                            <option value="Flase">False</option>
                                        </select>
                                        <div class="alert">
                                            @if ( f.batch.touched && f.batch.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.batch.errors?.['required']) { Batch Mode is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                } -->
                                <!-- Sequence number -->
                                @if(logminer && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Sequence Number
                                            <span class="qmig-tooltip"><i>Start SCN number from Source</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="scn" />
                                        <div class="alert">
                                            @if ( f.scn.touched && f.scn.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.scn.errors?.['required']) { Sequence Number is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- Dag name -->
                                @if(logminer && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Dag Name
                                            <span class="qmig-tooltip"><i>Dag Name to show up in Airflow</i></span>
                                        </label>
                                        <input type="text" class="form-control " formControlName="dagname" />
                                        <div class="alert">
                                            @if ( f.dagname.touched && f.dagname.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.dagname.errors?.['required']) { Dag Name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- Batch size details -->
                                @if( migtypeid == "31" || logminer && !reverseCdc || cdcSelected )
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Batch Size
                                            <span class="qmig-tooltip"><i>Number of changes to be in Batch, required in
                                                    Batch
                                                    Mode</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="batchsize" />
                                        <div class="alert">
                                            @if ( f.batchsize.touched && f.batchsize.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.batchsize.errors?.['required']) { Batch Size is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- sleep time -->
                                <!-- @if(!cdcSelected && !logminer && !reverseCdc && !initialLoadGGSelected)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Sleep Time (mins)
                                            <span class="qmig-tooltip"><i>Sleep process in-between batch in
                                                    minutes</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="sleepTime" />
                                        <div class="alert">
                                            @if ( f.sleepTime.touched && f.sleepTime.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.sleepTime.errors?.['required']) { Sleep Time is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                } -->
                                <!-- request cpu details -->
                                @if(!incrementSelected && !cdcSelected && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Request CPU
                                            <span class="qmig-tooltip"><i>Min. CPU for Migration task 1Core =
                                                    1000m</i></span>
                                        </label>
                                        <div class="row">
                                            <div class="col-8 col-md-8 pe-md-1">
                                                <input type="text" class="form-control" formControlName="request_CPU" />
                                            </div>
                                            <div class="col-4 col-md-4 ps-md-1">
                                                <select class="form-select" formControlName="request_CPU_measure">
                                                    <option value="">Core(s)</option>
                                                    <option value="m">m</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="alert">
                                            @if ( f.request_CPU.touched && f.request_CPU.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.request_CPU.errors?.['required']) { Request CPU is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- request memoy details -->
                                @if(!incrementSelected && !cdcSelected && !reverseCdc && getForm.value.podConfig ==
                                'Custom')
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Request Memory
                                            <span class="qmig-tooltip"><i>Min. Memory for Migration task 1Gi =
                                                    1000Mi</i></span>
                                        </label>
                                        <div class="row">
                                            <div class="col-8 col-md-8 pe-md-1">
                                                <input type="text" class="form-control"
                                                    formControlName="request_Memory" />
                                            </div>
                                            <div class="col-4 col-md-4 ps-md-1">
                                                <select class="form-select" formControlName="request_Memory_measure">
                                                    <option value="" selected>Mi</option>
                                                    <option value="Gi">Gi</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="alert">
                                            @if ( f.request_Memory.touched && f.request_Memory.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.request_Memory.errors?.['required']) { Request Memory is required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- limit cpu details -->
                                @if(!incrementSelected && !cdcSelected && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Limit CPU
                                            <span class="qmig-tooltip"><i>Max CPU for Migration task 1Core =
                                                    1000mi</i></span>
                                        </label>
                                        <div class="row">
                                            <div class="col-8 col-md-8 pe-md-1">
                                                <input type="text" class="form-control" formControlName="limit_CPU" />
                                            </div>
                                            <div class="col-4 col-md-4 ps-md-1">
                                                <select class="form-select" formControlName="limit_CPU_measure">
                                                    <option value="" selected>Core(s)</option>
                                                    <option value="m">m</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="alert">
                                            @if ( f.limit_CPU.touched && f.limit_CPU.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.limit_CPU.errors?.['required']) { Limit CPU is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- limit memory details -->
                                @if(!incrementSelected && !cdcSelected && !reverseCdc && getForm.value.podConfig ==
                                'Custom')
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Limit Memory
                                            <span class="qmig-tooltip"><i>Max Memory for Migration task 1Gi =
                                                    1000Mi</i></span>
                                        </label>
                                        <div class="row">
                                            <div class="col-8 col-md-8 pe-md-1">
                                                <input type="text" class="form-control"
                                                    formControlName="limit_Memory" />
                                            </div>
                                            <div class="col-4 col-md-4 ps-md-1">
                                                <select class="form-select" formControlName="limit_Memory_measure">
                                                    <option value="" selected>Mi</option>
                                                    <option value="Gi">Gi</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="alert">
                                            @if ( f.limit_Memory.touched && f.limit_Memory.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.limit_Memory.errors?.['required']) { Limit Memory is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- Audit connection list -->
                                <!-- @if(auditChecked)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Audit Connection </label>
                                        <input type="text" class="form-control" value="{{auditList.conname}}" disabled />
                                        <select class="form-select" #audit (change)="getauditCon(audit.value)"
                                            formControlName="auditConnection">
                                            <option>Select Audit</option>
                                            @for( list of auditList;track list; ){
                                            <option value="{{list.Connection_ID }}">{{list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                        
                                        </div>
                                    </div>
                                </div>
                                } -->
                                @if(initialLoadGGSelected && !reverseCdc || migtypeid =="31")
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">CDC Type </label>
                                        <select class="form-select" formControlName="cdcType" #cdcv
                                            (change)="cdcValidator(cdcv.value)">
                                            <option selected>Select CDC Type</option>
                                            <option value="Default"> Default</option>
                                            @if(migtypeid !="31"){
                                            <option value="Golden_Gate"> Golden Gate</option>
                                            }
                                        </select>

                                        <div class="alert">
                                            @if ( f.cdcType.touched && f.cdcType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.cdcType.errors?.['required']) { CDC Type is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- GoldenGate Pod details -->

                                @if(initialLoadGGSelected && !reverseCdc && getForm.value.cdcType !== 'Default')
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">GoldenGate Pod
                                            <span class="qmig-tooltip"><i>GoldenGate pods Deployed on cluster</i></span>
                                        </label>
                                        <select class="form-select" formControlName="podName"
                                            (change)="callShellScript(podVal.value)" #podVal>
                                            <option>Select GoldenGate Pod</option>
                                            @for( list of podList;track list; ){
                                            <option value="{{list }}">{{list}}</option>
                                            }
                                        </select>

                                        <div class="alert">
                                            @if ( f.podName.touched && f.podName.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.podName.errors?.['required']) { GoldenGate Pod is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- GoldenGate Home details -->
                                @if(initialLoadGGSelected && !reverseCdc && getForm.value.cdcType !== 'Default')
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">GoldenGate Home
                                            <span class="qmig-tooltip"><i>Home path location of GoldenGate</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="ggHome" />
                                        <div class="alert">
                                            @if ( f.ggHome.touched && f.ggHome.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.ggHome.errors?.['required']) { GoldenGate Home is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- shell script details -->
                                @if(initialLoadGGSelected && !reverseCdc && getForm.value.cdcType !== 'Default')
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Shell Script
                                            <span class="qmig-tooltip"><i>Run script to start GoldenGate
                                                    replicate</i></span>
                                        </label>
                                        <select class="form-select" formControlName="shellScript">
                                            <option>Select Shell Script </option>
                                            @for(list of shellScriptList;track list; ){
                                            <option value="{{ list.filePath }}">{{ list.fileName}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.shellScript.touched && f.shellScript.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.shellScript.errors?.['required']) { Shell Script is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                @if(reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Slot Name </label>
                                        <input type="text" class="form-control" formControlName="slotname" />

                                        <div class="alert">
                                            @if ( f.slotname.touched && f.slotname.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.slotname.errors?.['required']) { Slot Name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- @if(sql_oracletype)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Method </label>
                                        <select class="form-select" formControlName="method">
                                            <option selected disabled> Select Method </option>
                                            <option value="Python">
                                                Python
                                            </option>
                                            <option value="Sql_Loader">
                                                Sql Load
                                            </option>
                                        </select>
                                        <div class="alert">
                                            @if ( f.shellScript.touched && f.shellScript.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.shellScript.errors?.['required']) { Shell Script is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                } -->
                                @if(sql_oracletype)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Read Size </label>
                                        <input type="text" class="form-control" formControlName="readsize" />

                                        <div class="alert">
                                            @if ( f.slotname.touched && f.slotname.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.slotname.errors?.['required']) { Slot Name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                @if(sql_oracletype)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Batch Size </label>
                                        <input type="text" class="form-control" formControlName="rows" />

                                        <div class="alert">
                                            @if ( f.rows.touched && f.rows.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.rows.errors?.['required']) { Batch Size is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- GG trail path details -->
                                @if(initialLoadGGSelected && !reverseCdc && getForm.value.cdcType !== 'Default')
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">GG Trail Path
                                            <span class="qmig-tooltip"><i>Location of captured change files in
                                                    GoldenGate</i></span>
                                        </label>
                                        <input type="text" class="form-control" formControlName="trailPath" />

                                        <div class="alert">
                                            @if ( f.trailPath.touched && f.trailPath.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.trailPath.errors?.['required']) { GG Trail Path is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                @if(getForm.value.operation == 'CDC_Load' && migtypeid!="31" )
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">CDC Load Type
                                        </label>
                                        <select class="form-select" formControlName="cdcLoadType">
                                            <option selected value="">Select CDC Load Type </option>
                                            @for(cdcL of cdcLoadData;track cdcL; ){
                                            <option value="{{ cdcL.value }}">{{ cdcL.name}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.cdcLoadType.touched && f.cdcLoadType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.cdcLoadType.errors?.['required']) { CDC Load Type is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- audit logs -->
                                @if(!incrementSelected && !cdcSelected && !reverseCdc)
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4">
                                    <div class="form-group">
                                        <div class="form-check form-check-flat form-check-primary mt-3">
                                            <label class="form-check-label form-label">
                                                <input type="checkbox" class="form-check-input"
                                                    (click)="getCheckValue($event)" formControlName="auditLogs">
                                                <i class="input-helper"></i> Audit Logs
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- configuration details button -->
                                <!-- <div [ngClass]="incrementSelected && !cdcSelected? 'col-md-9' : ''"></div>
                                    <div [ngClass]="logminer && !cdcSelected? 'col-md-9' : ''"></div>
                                    <div [ngClass]="cdcSelected? 'col-md-0' : ''"></div> -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div [ngClass]="incrementSelected? 'mt-0 pt-1' : 'mt-0 pt-3'">
                                        <button class="btn btn-upload w-100" [disabled]="!getForm.valid"
                                            (click)="InserttablesCommand(getForm.value)"> <span
                                                class="mdi mdi-file-cog-outline btn-icon-prepend"></span>Create Config
                                            @if(executed){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Extract Table
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="extractForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source Connection
                                            Name</label>
                                        <select class="form-select" #org (change)="getSchemasList1(org.value)"
                                            formControlName="sourceConnection">
                                            <option disabled>Select a Source Connection</option>
                                            @for ( list of ConsList; track list) {
                                            <option value="{{ list.Connection_ID }}">{{ list.conname }}</option>
                                            }
                                        </select>
                                        @if ( fs.sourceConnection.touched && fs.sourceConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (fs.sourceConnection.errors.required) {Source Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection"> Schema
                                            Name</label>
                                        <select class="form-select" #org (change)="getSchemasList1(org.value)"
                                            formControlName="schema">
                                            <option disabled>Select a Schema </option>
                                            @for ( list of schemaList1; track list) {
                                            <option value="{{ list.schemaname }}">{{ list.schemaname }}</option>
                                            }
                                        </select>
                                        @if ( fs.schema.touched && fs.schema.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (fs.schema.errors.required) {Schema is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100 " (click)="extractValue(extractForm.value)"
                                            [disabled]="extractForm.invalid"> <span class="mdi mdi-cog-play"></span>
                                            Extract
                                            Tables
                                            @if(spin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        @if(migtypeid=="20"){ <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#cpuMemoryUtil" aria-expanded="false" aria-controls="flush-collapse">
                    CPU Memory Utilization
                </button>
            </h2>
            <div id="cpuMemoryUtil" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="CPUForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source Connection
                                            Name</label>
                                        <select class="form-select" #cpuSC (change)="getCPUSchema(cpuSC.value)"
                                            formControlName="sourceConnection">
                                            <option disabled>Select a Source Connection</option>
                                            @for ( list of ConsList; track list) {
                                            <option value="{{ list.Connection_ID }}">{{ list.conname }}</option>
                                            }
                                        </select>
                                        @if ( cf.sourceConnection.touched && cf.sourceConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (cf.sourceConnection.errors.required) {Source Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100 " (click)="extractCPUValue()"
                                            [disabled]="CPUForm.invalid"> <span class="mdi mdi-cog-play"></span>
                                            Start Process
                                            @if(cpu_spin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        }
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Configuration File List
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-6">
                            <h3 class="main_h py-4 px-3">
                                Config File List
                                <button class="btn btn-sync" (click)="fetchConfigFiles()">
                                    @if(ref_spin){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </h3>
                        </div>
                        <!-- search status of storage obj migration -->
                        <div class="col-12 col-sm-6 col-md-6">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Config File" aria-controls="example"
                                    class="form-control" [(ngModel)]="datachange" (keyup)="onKey()" />
                            </div>
                        </div>
                    </div>
                    <!-- for download file -->
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>File Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(docs of confFiles |searchFilter: datachange|paginate:{
                                itemsPerPage: 10, currentPage: p, id:'third'};
                                track docs){
                                <tr>
                                    <td>{{ p*10+$index+1-10 }}</td>
                                    <td>{{docs.fileName }}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(docs)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                        <button (click)="toggleSpinner(docs.fileName);deleteAirflowFiles(docs.fileName)"
                                            class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <!-- pagination -->
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>

        <!---Page-Activity Log-->
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    Upload Configuration File
                </button>
            </h3>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form">
                            <div class="form-group">
                                <label for="formFile" class="form-label d-required">Upload File</label>
                                <div class="custom-file-upload">
                                    <input class="form-control" type="file" id="formFile"
                                        (change)="onFileSelected1($event)">
                                    <div class="file-upload-mask">
                                        @if (fileName == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drag and drop deployment file here or click add deployment file </p>
                                        <button class="btn btn-upload"> Add File </button>
                                        } @else{
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ fileName }} </p>
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-9"></div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100" (click)="uploadFile('configFile')">
                                            <span class="mdi mdi-file-plus"></span> Upload
                                            @if(uploadSpin1){<app-spinner />}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>

        @if(migtypeid=="20")
        {
        <!---Page-Activity Log-->
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingFour">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseFour" aria-expanded="false" aria-controls="flush-collapseFour">
                    Upload Ora2PG Configuration Template
                </button>
            </h3>
            <div id="flush-collapseFour" class="accordion-collapse collapse" aria-labelledby="flush-headingFour"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form">
                            <div class="form-group">
                                <label for="formFile" class="form-label d-required">Upload File</label>
                                <div class="custom-file-upload">
                                    <input class="form-control" type="file" id="formFile"
                                        (change)="onFileSelected1($event)">
                                    <div class="file-upload-mask">
                                        @if (fileName == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drag and drop deployment file here or click add deployment file </p>
                                        <button class="btn btn-upload"> Add File </button>
                                        } @else{
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ fileName }} </p>
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-9"></div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100" (click)="uploadFile('uploadConfigTemp')">
                                            <span class="mdi mdi-file-plus"></span> Upload
                                            @if(uploadConfigTempSpin){<app-spinner />}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>
        }

        <!---Page-Activity Log-->
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Upload Table MetaData
                </button>
            </h3>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form">
                            <div class="form-group">
                                <label for="formFile" class="form-label d-required">Upload File</label>
                                <div class="custom-file-upload">
                                    <input class="form-control" type="file" id="formFile"
                                        (change)="onFileSelected($event)">
                                    <div class="file-upload-mask">
                                        @if (TableMetaFileName == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drag and drop deployment file here or click add deployment file </p>
                                        <button class="btn btn-upload"> Add File </button>
                                        } @else{
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ TableMetaFileName }} </p>
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-9"></div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100" (click)="uploadFile1()">
                                            <span class="mdi mdi-file-plus"></span> Upload
                                            @if(uploadSpin){<app-spinner />}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </div>
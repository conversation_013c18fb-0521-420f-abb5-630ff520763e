<!--- Reports section -->
<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-3">
            <h3 class="main_h mt-2">Migration Summary Reports </h3>
        </div>
        <div class="col-md-9">
            <dash-tabs></dash-tabs>
        </div>
    </div>

    <!---Dashboard data --->
    <div class="dashboard_data  mt-4">
        <div class="row">
            <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-4">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="dash-card">
                            <h3 class="main_h">Code Extraction</h3>
                            <div class="dash-content">
                                <div class="dash-cItem">
                                    <div>
                                        <h3>{{ source_count }}</h3>
                                        <p>Source Count</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>{{ Extracted_Count }}</h3>
                                        <p>Extracted Count</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>
                                            @if(Extracted_Count){
                                            {{ Extracted_Count / source_count | percent : "1.0-2" }}
                                            }@else{
                                            0
                                            }
                                        </h3>
                                        <p>Total Progress</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dash-card my-3">
                            <h3 class="main_h">Storage Objects</h3>
                            <div class="dash-content">
                                <div class="dash-cItem">
                                    <div>
                                        <h3>{{ source_count1 }}</h3>
                                        <p>Total Count</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>{{ Extracted_Count1 }}</h3>
                                        <p>Converted Count</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>
                                            @if(Extracted_Count1){
                                            {{ Extracted_Count1 / source_count1 | percent : "1.0-2" }}
                                            }@else{
                                            0
                                            }
                                        </h3>
                                        <p>Total Progress</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dash-card">
                            <h3 class="main_h">Code Objects</h3>
                            <div class="dash-content">
                                <div class="dash-cItem">
                                    <div>
                                        <h3>{{ source_count2 }}</h3>
                                        <p>Total Count</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>{{ Extracted_Count2 }}</h3>
                                        <p>Converted Count</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>
                                            @if(Extracted_Count2){
                                            {{ Extracted_Count2 / source_count2 | percent : "1.0-2" }}
                                            }@else{
                                            0
                                            }
                                        </h3>
                                        <p>Total Progress</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dash-card mt-3">
                            <h3 class="main_h">Data Migration</h3>
                            <div class="dash-content">
                                <div class="dash-cItem">
                                    <div>
                                        <h3>{{Extracted_Count3}}</h3>
                                        <p>Total Count</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>{{source_count3}}</h3>
                                        <p>Migrated Count</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>
                                            @if(Extracted_Count3){
                                            {{ source_count3/Extracted_Count3 | percent : "1.0-2" }}
                                            }@else{
                                            0
                                            }
                                        </h3>
                                        <p>Total Progress</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-8">
                <div class="qmig-card py-5">
                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <div class="form-group mb-0">
                                <select class="form-select form-small" (change)="dbconnectionSelection($event)">
                                    <option selected disabled>Select DB Connection</option>
                                    @for(db of ConsList ; track db;){
                                    <option value="{{db.conname}}">{{db.conname}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="qmig-card-body">
                        <canvas height="100%" baseChart [datasets]="barChartData" [labels]="barChartLabels"
                            [options]="barChartOptions" [legend]="barChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!---Dashboard data --->
    @if(migtypeid!="31"){
    <hr class="dash-dotted">
    <div class="dashboard_data  mt-4">
        <!-- <h3 class="main_h mt-2 mb-4">Testing Report's </h3> -->
        <div class="row">
            <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-4">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="dash-card">
                            <h3 class="main_h">Dev Cycle 2 Testing</h3>
                            <div class="dash-content">
                                <div class="dash-cItem">
                                    <div>
                                        <h3>323</h3>
                                        <p>Total Test Cases</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>296</h3>
                                        <p>Passed Test Cases</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>
                                            91.64 %
                                        </h3>
                                        <p>Total Progress</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dash-card my-3">
                            <h3 class="main_h">Dev Cycle 3 Testing </h3>
                            <div class="dash-content">
                                <div class="dash-cItem">
                                    <div>
                                        <h3>323</h3>
                                        <p>Total Test Cases</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>283</h3>
                                        <p>Passed Test Cases</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>
                                            87.61 %
                                        </h3>
                                        <p>Total Progress</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dash-card">
                            <h3 class="main_h">SIT Testing</h3>
                            <div class="dash-content">
                                <div class="dash-cItem">
                                    <div>
                                        <h3>323</h3>
                                        <p>Total Test Cases</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>260</h3>
                                        <p>Passed Test Cases</p>
                                    </div>
                                </div>
                                <div class="dash-cItem">
                                    <div>
                                        <h3>
                                            80.49 %
                                        </h3>
                                        <p>Total Progress</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 col-xxl-8">
                <div class="qmig-card">
                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <div class="form-group mb-0">
                                <select class="form-select form-small">
                                    <option selected disabled>Select DB Connection</option>
                                    @for(db of ConsList ; track db;){
                                    <option value="{{db.conname}}">{{db.conname}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="qmig-card-body">
                        <div style="display: block; width: 100%">
                            <canvas height="100%" baseChart [datasets]="testChartData" [labels]="testChartLabels"
                                [options]="testChartOptions" [legend]="testChartLegend">
                            </canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    }
</section>
import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-confirm-dialog',
  standalone: true,
  imports: [],
  templateUrl: './confirm-dialog.component.html',
  styles: `
    .dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.dialog {
  background: white;
  width: 400px;
  border-radius: 1rem;
  footer {
      text-align: right;
    padding: 10px 0px;
    background: #fafafa;
    border-radius: 0rem 0rem 1rem 1rem;
  }
}

.dialog_h {
    padding: 20px 20px 10px;
    background: #fafafa;
    border-radius: 1rem 1rem 0rem 0rem;
}
    .dialog-msg {
    padding: 20px;
}

.dialog-msg p {
  font-size: 16px;
}

.controls button {
    padding: 8px 20px;
    margin: 0;
    min-height: 25px;
}
  `
})
export class ConfirmDialogComponent {
  @Input() message: string = ''; // Confirmation message
  @Input() confirmVal: string = ''; // Confirmation value text (e.g., "Turn ...?")
  @Output() confirmResponse: EventEmitter<boolean> = new EventEmitter<boolean>();
  showDialog: boolean = false;

  openDialog() {
    this.showDialog = true;
  }

  closeDialog() {
    this.showDialog = false;
    this.confirmResponse.emit(false); // Emit false for cancel action
  }

  confirmAction() {
    this.showDialog = false;
    this.confirmResponse.emit(true); // Emit true for confirm action
  }
}

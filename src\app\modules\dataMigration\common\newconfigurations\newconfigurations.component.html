<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">


        <!-- Extract Tables -->
        <!-- <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button " type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Extract Tables
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="extractForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source
                                            Connection
                                            Name</label>
                                        <select class="form-select" #org (change)="getSchemasList1(org.value)"
                                            formControlName="sourceConnection">
                                            <option disabled>Select a Source Connection</option>
                                            @for ( list of ConsList; track list) {
                                            <option value="{{ list.Connection_ID }}">{{ list.conname }}</option>
                                            }
                                        </select>
                                        @if ( fs.sourceConnection.touched && fs.sourceConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (fs.sourceConnection.errors.required) {Source Connection is Required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection"> Schema
                                            Name</label>
                                        <select class="form-select" #org (change)="getSchemasList1(org.value)"
                                            formControlName="schema">
                                            <option disabled>Select a Schema </option>
                                            @for ( list of schemaList1; track list) {
                                            <option value="{{ list.schema_name }}">{{ list.schema_name }}</option>
                                            }
                                        </select>
                                        @if ( fs.schema.touched && fs.schema.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (fs.schema.errors.required) {Schema is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100 " (click)="extractValue(extractForm.value)"
                                            [disabled]="extractForm.invalid"> <span class="mdi mdi-cog-play"></span>
                                            Extract
                                            Tables
                                            @if(spin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- New Configuration -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Create Configuration
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse " aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-md-6 col-xl-6 mt-4"></div>
                            <div class="col-12 col-md-3 col-xl-3 mt-4"></div>
                            <div class="col-12 col-md-3 col-xl-3 mt-4">
                                <button class="btn btn-upload w-100 " (click)="onEditConfigClick()"
                                    data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                                        class="mdi mdi-checkbox-marked-circle-outline" aria-hidden="true"></span>Edit
                                    Configuration</button>
                            </div>
                        </div>
                        <form class="form qmig-Form" [formGroup]="getForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Operation </label>
                                        <select class="form-select" formControlName="operation"
                                            (change)="getOP(opvalue.value)" #opvalue>
                                            <option>Select Operation</option>
                                            @for(list of configData;track list; ){
                                            <option value="{{list.configvalue}}">{{list.configtype}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.operation.touched && f.operation.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.operation.errors?.['required']) { operation is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <!-- source connection list -->
                                @if( getForm.value.operation != 'Catchup' )
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Source Connection </label>
                                        <select class="form-select" #org1
                                            (change)="getDb(org1.value);getSchemasList(org1.value,'S');onSourceChange(org1.value)"
                                            formControlName="sourceConnection">
                                            <option>Select Source Connection</option>
                                            @for(list of ConsList;track list; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.sourceConnection.touched && f.sourceConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.sourceConnection.errors?.['required']) { Source Connection is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }

                                <!-- schema list -->

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label " for="name">Source {{schemalable}} </label>
                                        <!-- <select class="form-select" #sc (change)="onItemSelect(sc.value)"
                                            formControlName="schemas">
                                            <option>Select Source Schema</option>
                                            @for(sch of schemaList;track sch; ){
                                            <option value="{{sch.schema_name}}">{{sch.schemaname}}</option>
                                            }
                                        </select> -->
                                        <ng-select [placeholder]="'Select Schema Name'" formControlName="schemas"
                                            groupBy="type" [selectableGroup]="true" [items]="schemaList"
                                            (change)="OnSchemasSelect(selectedSchemas);validationstable()"
                                            [multiple]="true" bindLabel="schema_name" [closeOnSelect]="false"
                                            bindValue="schema_name" clearAllText="Clear" [clearSearchOnAdd]="true"
                                            [(ngModel)]="selectedSchemas">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }"
                                                    value={{item.schema_name}} />
                                                {{item.schema_name}}
                                            </ng-template>
                                            <!-- <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.schema_name}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template> -->
                                        </ng-select>
                                        <div class="alert">
                                            @if ( f.schemas.touched && f.schemas.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.schemas.errors?.['required']) { {{schemalable}} required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- tableslist -->
                                @if(isAll!="ALL"){
                                @if(showTargetSchemaAndTables){
                                @if(getForm.value.data_LoadType != 'Data_Dumper'){
                                <!-- @if(!this.tableHide ){ -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label " for="name">Tables </label>
                                        @if(projectId == "3038"){
                                        <input type="text" class="form-control" formControlName="tables" />
                                        }
                                        @else{
                                        <ng-select [placeholder]="'Select Schema Name'" formControlName="tables"
                                            groupBy="type" [selectableGroup]="true" [items]="tablesList"
                                            (change)="tablesSelect(selectedTable)" [multiple]="true"
                                            bindLabel="table_name" [closeOnSelect]="false" bindValue="table_name"
                                            clearAllText="Clear" [clearSearchOnAdd]="true" [(ngModel)]="selectedTable">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }"
                                                    value={{item.table_name}} />
                                                {{item.table_name}}
                                            </ng-template>
                                            <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.table_name}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                        }
                                        <div class="alert">
                                            @if ( f.tables.touched && f.tables.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.tables.errors?.['required']) { Tables required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                }
                                }
                                <!-- } -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Data Load Type
                                            <span class="qmig-tooltip"><i>Tool/Process for migrating data</i></span>
                                        </label>
                                        <select class="form-select" formControlName="data_LoadType">
                                            <option>Select Data Load Type</option>
                                            <option value="Database">Database</option>
                                            @if(migtypeid=="39"){
                                            <option value="Data_Pump">Data Pump</option>
                                            <option value="Sql_Loader">Sql Loader</option>
                                            }
                                            @else if(migtypeid=="30"){
                                            <option value="Sql_Loader">Sql Loader</option>
                                            }
                                            @else if(migtypeid=="38"){
                                            <option value="PG_Dump">PG Dump</option>
                                            }
                                            @else if(migtypeid=="31"){
                                            <option value="Data_Dumper">Data Dumper</option>
                                            }
                                            @else{
                                            <option value="File">File</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.data_LoadType.touched && f.data_LoadType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.data_LoadType.errors?.['required']) { Data Load Type required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- target list -->
                                @if( getForm.value.data_LoadType != 'File' )
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label " for="name">Target Connection </label>
                                        <select class="form-select" #tgtcon
                                            (change)="getTgtId(tgtcon.value);getSchemasList(tgtcon.value,'T');onTargetChange(tgtcon.value)"
                                            formControlName="targetConnection">
                                            <option>Select Target Connection</option>
                                            @for(list of tgtlist;track list; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.targetConnection.touched && f.targetConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.targetConnection.errors?.['required']) { Target Connection is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }


                                <!-- Target schema list -->

                                @if( getForm.value.data_LoadType != 'File' && (migtypeid!="40" && migtypeid!="48"
                                &&migtypeid!="46") )
                                {
                                @if(isAll!="ALL"){
                                @if(showTargetSchemaAndTables){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Target {{schemalable}} </label>
                                        <select class="form-select" #tgtsc (change)="selecttgtschema(tgtsc.value)"
                                            formControlName="tgtschemas">
                                            <option>Select Target Schema</option>
                                            @for(tgtsch of tgtSchemaList;track tgtsch; ){
                                            <option value="{{tgtsch.schema_name}}">{{tgtsch.schema_name}}</option>
                                            }
                                        </select>
                                        <!-- <div class="alert">
                                            @if ( f.tgtschemas.touched && f.tgtschemas.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.tgtschemas.errors?.['required']) { Target {{schemalable}}
                                                required }
                                            </p>
                                            }
                                        </div> -->
                                    </div>
                                </div>
                                }
                                }
                                }

                                <!-- CDC Type -->
                                @if(getForm.value.operation == 'E2E_Data_Load')
                                {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">CDC Load Type </label>
                                        <select class="form-select" formControlName="cdcLoadType">
                                            <option selected>Select CDC Load Type</option>
                                            @if( getForm.value.data_LoadType != 'File' ){
                                            <option value="Database"> Database</option>
                                            <option value="File"> File</option>
                                            }
                                            @else{
                                            <option value="File">File</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.cdcLoadType.touched && f.cdcLoadType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.cdcLoadType.errors?.['required']) { CDC Load Type is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }

                                <!-- configuration name  -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Configuration Name </label>
                                        <input type="text" class="form-control" formControlName="confiName"
                                            maxlength="15" />
                                        <div class="alert">
                                            @if ( f.confiName.touched && f.confiName.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.confiName.errors?.['required']) { Configuration Name required }
                                                @if (f.confiName.errors?.['maxLength']) { Maximium Length is 15
                                                character's }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="row">
                                <div class="col-10 col-sm-6 col-md-10 col-lg-4 col-xl-9">
                                </div>
                                <div class="col-2 col-sm-6 col-md-2 col-lg-4 col-xl-3">
                                    <div class="form-group mt-1">
                                        <div class="text-right">
                                            <button class="btn btn-upload w-100" [disabled]="getForm.invalid"
                                                (click)="openModal1(getForm.value,'1')" data-bs-toggle="offcanvas"
                                                data-bs-target="#demo1"> <span
                                                    class="mdi mdi-checkbox-marked-circle-outline"
                                                    aria-hidden="true"></span>Check
                                                Details</button>
                                        </div>
                                    </div>
                                    <!-- <button class="btn btn-upload w-100" [disabled]="getForm.invalid"
                                        (click)="DataMigrationNewCommand(getForm.value,'1')"> <span
                                            class="mdi mdi-file-cog-outline btn-icon-prepend"></span>Create Config
                                        @if(executed){<app-spinner />}</button> -->
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>



        <!-- Configuration File List -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Configuration Files
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Source
                                        Connection Name</label>
                                    <select class="form-select" #srcFile
                                        (change)="fetchSourceConfigFiles(srcFile.value)">
                                        <option>Select a Source Connection</option>
                                        @for ( src of ConsList; track src) {
                                        <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                <div class="form-group">
                                    <label class="form-label" for="targtetConnection">Target Connection Name</label>
                                    <select class="form-select" #tgtFile
                                        (change)="fetchTargetConfigFiles(tgtFile.value)">
                                        <option>Select Target Connection</option>
                                        @for(tgt of tgtlist;track tgt; ){
                                        <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <!-- search status of storage obj migration -->
                            <div class="col-12 col-sm-6 col-md-6 col-lg-6 d-flex">
                                <div class="custom_search cs-r my-3 me-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Config File" aria-controls="example"
                                        class="form-control" [(ngModel)]="datachange" (keyup)="onKey()" />
                                </div>
                                <button class="btn btn-sync" (click)="getUpdatedRunNumber1()">
                                    @if(getRunSpin1){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </div>
                        </div>


                        <!-- for download file -->
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>S.No</th>
                                        <th>File Name</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(docs of confFiles |searchFilter: datachange|paginate:{
                                    itemsPerPage: 10, currentPage: p, id:'third'};
                                    track docs){
                                    <tr>
                                        <td>{{ p*10+$index+1-10 }}</td>
                                        <td>{{docs.fileName }}</td>
                                        <td>
                                            <button class="btn btn-download" (click)="downloadFile(docs)">
                                                <span class="mdi mdi-cloud-download-outline"></span>
                                            </button>
                                            <button
                                                (click)="toggleSpinner(docs.fileName);deleteAirflowFiles(docs.fileName,docs.filePath)"
                                                class="btn btn-delete">
                                                <span class="mdi mdi-delete btn-icon-prepend"></span>
                                            </button>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <!-- pagination -->
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--- Upload Configuration File-->
        <!-- <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    Upload Configuration File
                </button>
            </h3>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form">
                            <div class="form-group">
                                <label for="formFile" class="form-label d-required">Upload File</label>
                                <div class="custom-file-upload">
                                    <input class="form-control" type="file" id="formFile"
                                        (change)="onFileSelected1($event)">
                                    <div class="file-upload-mask">
                                        @if (fileName == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drag and drop deployment file here or click add deployment file </p>
                                        <button class="btn btn-upload"> Add File </button>
                                        } @else{
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ fileName }} </p>
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-9"></div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100" (click)="uploadFile('configFile')">
                                            <span class="mdi mdi-file-plus"></span> Upload
                                            @if(uploadSpin1){<app-spinner />}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div> -->

        @if(migtypeid=="20")
        {
        <!---Upload Ora2PG Configuration Template-->
        <!-- <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingFour">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseFour" aria-expanded="false" aria-controls="flush-collapseFour">
                    Upload Ora2PG Configuration Template
                </button>
            </h3>
            <div id="flush-collapseFour" class="accordion-collapse collapse" aria-labelledby="flush-headingFour"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form">
                            <div class="form-group">
                                <label for="formFile" class="form-label d-required">Upload File</label>
                                <div class="custom-file-upload">
                                    <input class="form-control" type="file" id="formFile"
                                        (change)="onFileSelected1($event)">
                                    <div class="file-upload-mask">
                                        @if (fileName == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drag and drop deployment file here or click add deployment file </p>
                                        <button class="btn btn-upload"> Add File </button>
                                        } @else{
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ fileName }} </p>
                                        </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-9"></div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100" (click)="uploadFile('uploadConfigTemp')">
                                            <span class="mdi mdi-file-plus"></span> Upload
                                            @if(uploadConfigTempSpin){<app-spinner />}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>
        </div> -->
        }

        <!-- CPU Memory Utilization -->
        <!-- @if(migtypeid=="20"){ <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#cpuMemoryUtil" aria-expanded="false" aria-controls="flush-collapse">
                    CPU Memory Utilization
                </button>
            </h2>
            <div id="cpuMemoryUtil" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="CPUForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source
                                            Connection
                                            Name</label>
                                        <select class="form-select" #cpuSC (change)="getCPUSchema(cpuSC.value)"
                                            formControlName="sourceConnection">
                                            <option disabled>Select a Source Connection</option>
                                            @for ( list of ConsList; track list) {
                                            <option value="{{ list.Connection_ID }}">{{ list.conname }}</option>
                                            }
                                        </select>
                                        @if ( cf.sourceConnection.touched && cf.sourceConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (cf.sourceConnection.errors.required) {Source Connection is Required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100 " (click)="DmCommandForCPU()"
                                            [disabled]="CPUForm.invalid"> <span class="mdi mdi-cog-play"></span>
                                            Start Process
                                            @if(cpu_spin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        } -->
        <!-- Execution Screen -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne1">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne1" aria-expanded="false" aria-controls="flush-collapseOne1">
                    CPU Memory Utilization
                </button>
            </h2>
            <div id="flush-collapseOne1" class="accordion-collapse collapse" aria-labelledby="flush-headingOne1"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-sm-3 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Source Connection </label>
                                    <select class="form-select" (change)="onSourceChange1($event)">
                                        <option>Select Source Connection</option>
                                        @for(srclist of ConsList;track srclist; ){
                                        <option value="{{srclist.Connection_ID}}">{{srclist.conname}}</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3"></div>
                            <div class="col-12 col-sm-2 col-md-2 col-lg-2 col-xl-2"></div>
                            <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-3 mt-4">
                                <div class="form-group">
                                    <div class="body-header-button">
                                        <button type="button" class="btn btn-upload w-100 me-1"
                                            (click)="DatamigrationCommand()">
                                            <span></span> Trigger
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-2 col-md-1 col-lg-1 col-xl-1 d-flex">
                                <div class=" my-3 me-3">
                                    <button class="btn btn-sync">
                                        @if(getRunSpin11){
                                        <app-spinner />
                                        }@else{
                                        <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive mt-3" hidden="tableData">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Source Details</th>
                                        <th>Target Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- @for(con of CpuDetails;
                                    track con;) { -->
                                    <tr>
                                        <td>Total CPU(cores)</td>
                                        <!-- <td>{{totalCpuCores}}</td> -->
                                        <td>--</td>
                                        <td>--</td>
                                    </tr>
                                    <tr>
                                        <td>CPU Utilization Percentage</td>
                                        <!-- <td>{{utilizedCpu.toFixed(2)}}%</td> -->
                                        <td>--</td>
                                        <td>--</td>
                                    </tr>
                                    <tr>
                                        <td>Total Memory(GB)</td>
                                        <!-- <td>{{totalMemoryGb}}</td> -->
                                        <td>--</td>
                                        <td>--</td>
                                    </tr>
                                    <tr>
                                        <td>Memory Utilization Percentage</td>
                                        <!-- <td>{{memoryUtilizationGb.toFixed(2)}}%</td> -->
                                        <td>--</td>
                                        <td>--</td>
                                    </tr>
                                    <!-- }  -->
                                    <!-- @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty</p>
                                        </td>
                                    </tr>
                                    } -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
            <div class="offcanvas-header">
                <h4 class="main_h">Json Details</h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
            </div>
            <!--- Connection Name --->
            <div class="offcanvas-body">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="form-label d-required" for="name">Json Data </label>
                        <textarea class="form-control" type="text" id="read" placeholder="data" rows="15" cols="30"
                            [(ngModel)]="readDataString"></textarea>
                    </div>
                </div>
                <div class="body-header-button mt-1">
                    <button (click)="Update()" class="btn btn-upload w-100 "> Save
                        @if(uploadSpin){<app-spinner />}</button>
                </div>
            </div>
        </div>
        <div class="offcanvas offcanvas-end" tabindex="-1" id="demo1">
            <div class="offcanvas-header">
                <h4 class="main_h"> E2E Migration Details</h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup1()"></button>
            </div>
            <!--Connection Name--->
            <div class="offcanvas-body">
                <form class="form qmig-Form checkForm">
                    <div class="form-group">
                        <label class="form-label d-required" for="name">Source Connection Name</label>
                        : &nbsp;{{ conName }}
                    </div>
                    <!--Schema Name-->
                    <div class="form-group">
                        <label class="form-label d-required" for="name">Source Schema Name</label>
                        : &nbsp;{{ schemavalue || selectedsrcschema.toString() }}
                    </div>
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Data Load Type</label>
                        : &nbsp;{{ getForm.value.data_LoadType }}
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="name"> Target Connection Name</label>
                        : &nbsp;{{ tgtconName }}
                    </div>
                    @if(isAll!="ALL"){
                    @if(showTargetSchemaAndTables){
                    <!--Schema Name-->
                    <div class="form-group">
                        <label class="form-label " for="name">Target Schema Name</label>
                        : &nbsp;{{ getForm.value.tgtschemas }}
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="name"> Tables</label>
                        : &nbsp;{{ selectedTable }}
                    </div>
                    }}
                    <!--Operation-->
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Operation</label>
                        : &nbsp;{{ getForm.value.operation }}
                    </div>
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Config File Name</label>
                        : &nbsp;{{ getForm.value.confiName }}
                    </div>
                    <!--Execute-->
                    <div class="form-group">
                        <button class="btn btn-upload w-100" (click)="DataMigrationNewCommand(getForm.value,'1')"> <span
                                class="mdi mdi-file-cog-outline btn-icon-prepend"></span>Create Config
                            @if(executed){<app-spinner />}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
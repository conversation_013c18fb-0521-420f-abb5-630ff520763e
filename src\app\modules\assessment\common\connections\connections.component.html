<div class="v-pageName">{{pageName}}</div>

<!--- Connection Details --->
<div class="body-main">
  <div class="qmig-card">
    <div class="qmig-card-body">
      <form class="form qmig-Form">
        <div class="row">
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              <label class="form-label d-required" for="targtetConnection">Migration Source</label>
              <select class="form-select" #Myselect (change)="selectFilesAndDatabase(Myselect.value)">
                <option selected disabled>Select a migration source</option>
                @for(miglist of migrationSource;track miglist; ){
                <option value="{{ miglist.values }}"> {{ miglist.option }} </option>
                }
              </select>
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              <label [hidden]="Hidelable" class="form-label d-required" for="name">Connection Type</label>
              @if (requiredDatabase && prjSrcTgtD.value == 'D') {
              <label class="form-label d-required" for="name">Connection Type</label>
              }
              @if ( requiredFile && prjSrcTgtD.value == 'F') {
              <label class="form-label d-required" for="name">File Type </label>
              }
              @else {
              <select class="form-select" #Myselect1 (change)="
                        getProjectSrctgtconfSelectD(Myselect1.value);
                        selectedfiletype(Myselect1.value);
                        setupFolder(Myselect1.value);
                        filterData(Myselect1.value)
                      ">
                <option selected disabled>Select a Connection Type</option>
                @if( requiredDatabase && prjSrcTgtD.value == 'D'){
                @for(list1 of databases;track list1; ){
                <option value="{{ list1.value }}"> {{ list1.option }} </option>
                }
                }
                @else {
                @if(requiredFile && prjSrcTgtD.value == 'F'){
                @for(lit of filetypelist;track lit; ){
                <option value="{{ lit.file_type_id }}"> {{ lit.filetype }} </option>
                }
                }
                }
              </select>
              }
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              @if (requiredDatabase && prjSrcTgtD.value == 'D' && connectionPrj) {
              <label class="form-label d-required" for="name">Database Type</label>
              <input type="text" class="form-control" value="{{ DatabaseType }}" disabled />
              }

            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4" [ngClass]="{
            'display-block':
            requiredDatabase && prjSrcTgtD.value == 'D' && connectionPrj
          }">
            <div class="body-header-button">
              <button [hidden]="sourceButton" (click)="openPopup()" class="btn btn-upload me-2  w-100"
                data-bs-toggle="offcanvas" data-bs-target="#demo" type="button"> <span
                  class="mdi mdi-database-plus"></span> Add Source</button>
              <button [hidden]="targetButton" (click)="openPopup()" class="btn btn-upload me-2 w-100"
                data-bs-toggle="offcanvas" data-bs-target="#demo" type="button"> <span
                  class="mdi mdi-database-plus"></span> Add Target </button>
              <!-- @if(this.migtypeid=="20" ){<button [hidden]="auditButton" (click)="openPopup()"
                class="btn btn-upload w-100" data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                  class="mdi mdi-database-plus"></span> Add Kafka</button>} -->
            </div>
          </div>
        </div>
      </form>
    </div>

    <!---Database Connection Details Table--->
    @if (requiredDatabase && prjSrcTgtD.value == 'D' && connectionPrj) {
    <div class="table-responsive">
      <table class="table table-hover qmig-table">
        <thead>
          <tr>
            <th>S.No</th>
            <th>Connection Id</th>
            <!-- <th>DB Type</th> -->
            <th>Connection Name</th>
            <th>@if((migtypeid=="40" ||migtypeid=="48"||migtypeid=="46") && name=="Target"){Tenant Id}
              @else{Hostname/IP}</th>
            @if((migtypeid=="40"||migtypeid=="48"||migtypeid=="46") && name=="Target"){ }@else {<th>Port</th>}
            <th>@if((migtypeid=="40" ||migtypeid=="48"||migtypeid=="46")&& name=="Target"){WorkSpace Name} @else{DB
              Name}</th>
            @if((migtypeid=="40"||migtypeid=="48" ||migtypeid=="46" )&& name=="Target"){<th>LakeHouse</th>}
            @else if(migtypeid=="31" || migtypeid=="49"){
            } @else{<th>Schema</th>}
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          @for (list of ConData | paginate: { itemsPerPage: 10, currentPage: pageNumber } ; let i = $index ;track list )
          {
          <tr>
            <td>{{ 10 * (pageNumber - 1)+i + 1 }}</td>
            <td>{{list.Connection_ID}}</td>
            <td>{{ list.conname }}</td>
            <td>{{ list.dbhost }}</td>
            @if((migtypeid=="40"||migtypeid=="48" ||migtypeid=="46") && name=="Target"){ }
            @else{<td>{{ list.dbport }}</td>}
            <td>{{ list.dbname }}</td>
            @if(migtypeid=="31" ||
            migtypeid=="42"|| migtypeid=="49") { } @else{<td>{{ list.dbschema }}</td>}
            <td hidden>{{ list.id }}</td>
            <td>
              <button (click)="updateRecord(list)" class="btn btn-download" data-bs-toggle="offcanvas"
                data-bs-target="#demo">
                <span class="mdi mdi-pencil btn-icon-prepend"></span>
              </button>
              <button (click)="deleteDbConnection(list.Connection_ID)" class="btn btn-delete">
                <span class="mdi mdi-delete btn-icon-prepend"></span>
              </button>
            </td>
          </tr>
          } @empty {
          <tr>
            <td colspan="4">
              <p class="text-center m-0 w-100">Empty list of Documents</p>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>

    <div class="custom_pagination">
      <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
    </div>
    }
  </div>
</div>


<!--- For Target, Source and Audit buttons Popups--->
<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
  <div class="offcanvas-header">
    <h4 class="main_h">{{ name }} Details</h4>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
  </div>
  <div class="offcanvas-body">
    <form class="form qmig-Form" [formGroup]="connectionForm">
      @if(this.migtypeid=="47" && name=="Target"){
      <div class="form-group">
        <label class="form-label d-required" for="dbconname">Connection Name</label>
        <input type="text" formControlName="dbconname" placeholder="Name" class="form-control" id="dbconname" />
        @if ( f.dbconname.touched && f.dbconname.invalid) {
        <p class="text-start text-danger mt-1">
          @if (f.dbconname.errors.required) {Connection Name is Required }
        </p>
        }
      </div>
      }
      @else {
      <div class="form-group">
        <label class="form-label d-required" for="dbconname">Connection Name</label>
        <input type="text" formControlName="dbconname" placeholder="Name" class="form-control" id="dbconname" />
        @if ( f.dbconname.touched && f.dbconname.invalid) {
        <p class="text-start text-danger mt-1">
          @if (f.dbconname.errors.required) {Connection Name is Required }
        </p>
        }
      </div>
      }
      @if(this.migtypeid=="47" && name=="Target"){

      }@else {
      <div class="form-group">
        <label class="form-label d-required" for="dbhost">
          Hostname/IP
        </label>
        <input type="text" formControlName="dbhost" [placeholder]="'Hostname/URL'" class="form-control" id="dbhost" />
        @if ( f.dbhost.touched && f.dbhost.invalid) {
        <p class="text-start text-danger mt-1">
          @if (f.dbhost.errors.required) { Hostname/IP is Required }
        </p>
        }
      </div>
      }
      @if((migtypeid=="40" ||migtypeid=="48" || migtypeid=="46" || migtypeid=="47") && name=="Target"){
      }
      @else{
      <div class="form-group">
        <label class="form-label d-required" for="dbhost">Port</label>
        <input type="text" formControlName="port" placeholder="port" class="form-control" id="port" />
        @if ( f.port.touched && f.port.invalid) {
        <p class="text-start text-danger mt-1">
          @if (f.port.errors.required) {Port is Required }
        </p>
        }
      </div>
      }
      @if(migtypeid=="49"){

      }
      @else if(this.migtypeid=="47" && name =='Target'){

      }@else{
      <div class="form-group">
        @if(auditButton){
        <label class="form-label d-required" for="port">@if((migtypeid=="40"||migtypeid=="48" || migtypeid=="46")&&
          name=="Target"){Tenant Id}
          @else {Service Name}</label>
        }
        @else{
        <label class="form-label" for="port">Service Name</label>
        }
        <input type="text" formControlName="serviceName"
          [placeholder]="(migtypeid=='40'||migtypeid=='48'|| migtypeid=='46') && name=='Target'?'Tenant Id':'Service Name'"
          class="form-control" id="serviceName" />
      </div>
      }
      <div class="form-group">
        @if((this.migtypeid=='47') && name =='Target'){
        <label class="form-label d-required" for="dbname">Project ID</label>
        <input type="text" formControlName="dbname" placeholder="Project ID" class="form-control" id="dbname" />
        @if ( f.dbname.touched && f.dbname.invalid) {
        <p class="text-start text-danger mt-1">
          @if (f.dbname.errors.required) { Project ID is Required }
        </p>
        }
        }@else {
        <label class="form-label d-required" for="dbname">DB Name</label>
        <input type="text" formControlName="dbname" placeholder="Database Name" class="form-control" id="dbname" />
        @if ( f.dbname.touched && f.dbname.invalid) {
        <p class="text-start text-danger mt-1">
          @if (f.dbname.errors.required) { DB Name is Required }
        </p>
        }
        }

      </div>

      @if((migtypeid=="40" ||migtypeid=="48"|| migtypeid=="46")&& name=="Target"){
      }
      @else if(migtypeid == "31"|| migtypeid == "49"){

      }
      @else if((this.migtypeid=='47') && name =='Target'){
      <div class="form-group">
        <label class="form-label d-required" for="dbschema">Json Configuration</label>
        <textarea class="form-control" id="dbschema" [placeholder]="'Json'" formControlName="dbschema" rows="15"
          cols="30"></textarea>
        @if ( f.dbschema.touched && f.dbschema.invalid) {
        <p class="text-start text-danger mt-1">
          @if (f.dbschema.errors.required) {Json configuration is Required }
        </p>
        }
      </div>
      }
      @else{
      <div class="form-group">
        <label class="form-label " for="dbschema">Schema</label>
        <input type="text" formControlName="dbschema" [placeholder]="'Schema'" class="form-control" id="dbschema" />
        <!-- @if ( f.dbschema.touched && f.dbschema.invalid) {
        <p class="text-start text-danger mt-1">
          @if (f.dbschema.errors.required) {Schema Name is Required }
        </p>
        } -->
      </div>
      }

      @if(this.migtypeid=="47" && name =='Target'){

      }@else {
      <div class="form-group">
        @if(migtypeid=="35" || migtypeid=="34"){
        <label class="form-label d-required" for="Username">Access Key</label>
        }
        @else{
        <label class="form-label d-required" for="Username"> @if((migtypeid=="40" ||migtypeid=="48"|| migtypeid=="46")
          &&
          name=="Target"){Client Id}
          @else {Username}</label>
        }
        <input [type]="usernametextCheck ? 'password' : 'text'" formControlName="dbuserid"
          [placeholder]="(migtypeid=='40'||migtypeid=='48' || migtypeid=='46') && name=='Target'?'Client Id':'Username'"
          class="form-control" id="dbuserid" />
          <span class="password_show" (click)="sendEyeuser()"
          [ngClass]="usernametextCheck ?'mdi mdi-eye-off' : 'mdi mdi-eye'"></span>
      </div>
      }

      @if(this.migtypeid=="47" && name =='Target'){

      }@else{
      <div class="form-group">
        @if(migtypeid=="35" || migtypeid=="34"){
        <label class="form-label d-required" for="Username">Secret Key</label>
        }
        @else{
        <label class="form-label d-required" for="dbpassword"> @if((migtypeid=="40"||migtypeid=="48" || migtypeid=="46")
          &&
          name=="Target"){Client Secret}
          @else {Password} </label>
        }
        <input type="text" [type]="passwordtextCheck ? 'password' : 'text'" formControlName="dbpassword"
          [placeholder]="(migtypeid=='40' ||migtypeid=='48'|| migtypeid=='46') && name=='Target'?'Client Secret':'Password'"
          class="form-control" id="dbpassword" />
        <span class="password_show" (click)="sendEye()"
          [ngClass]="passwordtextCheck ?'mdi mdi-eye-off' : 'mdi mdi-eye'"></span>
      </div>
      @if(migtypeid=="35" || migtypeid=="34" && migsrctgtValue== "S"){
      <div class="form-group">
        <label class="form-label " for="Username">Dynamo Username</label>
        <input type="text" formControlName="dynamoUser" placeholder="Username" class="form-control" id="dbuserid" />
      </div>
      <div class="form-group">
        <label class="form-label " for="dbpassword">Dynamo Password </label>
        <input type="text" [type]="passwordtextCheck ? 'password' : 'text'" formControlName="dynamoPassword"
          placeholder="Password" class="form-control" id="dbpassword" />
        <span class="password_show" (click)="sendEye()"
          [ngClass]="passwordtextCheck ?'mdi mdi-eye-off' : 'mdi mdi-eye'"></span>
      </div>
      }
      }
      @if(migtypeid=="49"){

      }
      @else if((migtypeid=="40"||migtypeid=="48" || migtypeid=="46" || migtypeid=="47")&& name=="Target"){
      }
      @else{
      <div class="form-group">
        <label class="form-label d-required" for="parallelprocess">Parallelism</label>
        <input type="text" formControlName="parallelprocess" placeholder="Parallelism" class="form-control"
          id="parallelprocess" />
      </div>
      }
      @if(this.migtypeid == '31' && name=="Source"){
        <div class="form-group">
          <label class="form-label" for="parallelprocess">Target Mapping</label>
          <input type="text" formControlName="target_mapping" placeholder="Parallelism" class="form-control"
            id="target_mapping" />
        </div>
      }
      <div class="form-group">
        <div class="body-header-button mt-1">
          @if (addupdateDetails) {
          <button [disabled]="connectionForm.invalid" (click)="addProjectConnection(this.connectionForm.value)"
            class="btn btn-upload w-100 ">
            Add Connection @if(uploadSpin){<app-spinner />}</button>
          }
        </div>
        <div class="body-header-button mt-1">
          @if (!addupdateDetails) {
          <button (click)="update(this.connectionForm.value)" class="btn btn-upload w-100 "
            [disabled]="connectionForm.invalid"> Update @if(uploadSpin){<app-spinner />}</button>
          }
        </div>
        @if(migtypeid=="40"&& migtypeid=="48" && migtypeid=="46" && (migtypeid=="47" && name=="Target")){
        <div class="body-header-button">
          <button class="btn btn btn-sign w-100 mt-1" [disabled]="connectionForm.invalid"
            (click)="TestConnection(this.connectionForm.value)">
            Test Connection
            @if(testconnectionSpin){<app-spinner />}</button>
        </div>
        }@else{
          <div class="body-header-button">
            <button class="btn btn btn-sign w-100 mt-1" [disabled]="connectionForm.invalid"
              (click)="TestConnection(this.connectionForm.value)">
              Test Connection
              @if(testconnectionSpin){<app-spinner />}</button>
          </div>
        }
      </div>
    </form>
  </div>
</div>
<!--- File Connection Details popup--->
<div class="offcanvas offcanvas-end" tabindex="-1" id="audit">
  <div class="offcanvas-header">
    <h4 class="main_h">Create Database</h4>
    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
  </div>
  <div class="offcanvas-body">
    <form class="form qmig-Form" [formGroup]="addFileForm">
      <div class="form-group">
        <label class="form-label d-required" for="name"> FileType Name</label>
        <input type="text" formControlName="filetypename" class="form-control" id="FileType" placeholder="FileType" />
      </div>

      <div class="form-group">

        <div class="body-header-button">
          <button class="btn btn-upload w-100 " [disabled]="addFileForm.invalid"
            (click)="Addfiletype(this.addFileForm.value)"> <span></span> Add @if(filespin){<app-spinner />}</button>

        </div>
      </div>
    </form>
  </div>
</div>
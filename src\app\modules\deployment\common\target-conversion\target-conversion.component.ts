import { Component } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { TabsComponent } from '../tabs/tabs.component';
import { DeploymentService } from '../../../../services/deployment.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-target-conversion',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, TabsComponent],
  templateUrl: './target-conversion.component.html',
  styles: ``
})
export class TargetConversionComponent {
  pi: number = 10;
  pi_A: number = 10;
  pi1: number = 10;
  pi2: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;
  grid_active1: boolean = false;
  not_grid1: boolean = false;
  grid_active2: boolean = false;
  not_grid2: boolean = false;
  fileData: any;
  datachange: any;
  datachange1: any;
  TgtUpdateTgt: any;
  logfile: any;
  project_name: any
  projectId: any
  getRole: any;
  prjSrcTgtData: any = [];
  conid: any;
  SourceConn: any;
  TargetConn: any;
  SourceCo:any;
  srcConn: any;
  schemaList: any = [];
  pageName: string = '';
  datachanges: any;
  datachanges1: any;
  datachanges2: any;
  datachanges3: any;
  pageNumber: number = 1;
  p: number = 1;
  page: number = 1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;

  // @ViewChild(PlyrComponent)
  // plyr: PlyrComponent | undefined;

  // // or get it from plyrInit event
  // player: Plyr | undefined;
  poster = 'assets/videos/qmig_ui.jpg';

  constructor(public fb: FormBuilder,
    public deployment: DeploymentService,
    private toast: HotToastService,
    private route: ActivatedRoute,

  ) {
    this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    this.pageName = this.route.snapshot.data['name'];
  }
  dropdownList = [];
  selectedItems = [];
  targetconform: any;

  schemaName: any = [];
  ngOnInit(): void {
    this.targetconform = this.fb.group({
      runnumber: ['', [Validators.required]],
      contype: ['', [Validators.required]],
      sourcecon: ['', [Validators.required]],
      tgtcon: ['', [Validators.required]],
      scheman: ['', [Validators.required]],
      objectt: ['', [Validators.required]],
      objecttype: ['', [Validators.required]],
      // scrun: ['', [Validators.required]],
      // scschema: ['', [Validators.required]],
    });
    this.getInfraSelect();
    this.getuploadedFiles();
    this.gettargetdeltaFiles();
    this.getBlobfileData();
    this.getRunNumber();
    this.getRunNumbers();
    this.getPrjExeLogSelectTask("null");
    this.GetRequestTableData();
  }

  // select schema data
  selectschema(data: any) {
    this.schemaName = data
    //console.log(data)
  }
  get validate() {
    return this.targetconform.controls;
  }

  iteration: any
  selectIteration(iter: any) {
    this.iteration = iter;
    this.getRunNumbers();
  }
  runNumbers: any;
  getRunNumber() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbers = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }
  blobfile: any;
  getBlobfileData() {
    // this.project.GetBlobfileByFileName("target_conversion.mp4").subscribe((data: any) => {
    //   this.blobfile = data.fileUrl;
    // })
  }

  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }

  TgtCodeShow() {
    this.TgtUpdateTgt = true;
  }
  TgtCodeShows() {
    this.TgtUpdateTgt = false;
  }


  objCategory: any = [
    { values: 'ALL', option: 'ALL' },
    { values: 'Storage_Objects', option: 'Storage_Objects' },
    { values: 'Code_Objects', option: 'Code_Objects' }
  ]
  objectType: any = [];
  obtypevalue: any
  SelectObjectTypes(value: any) {
    let abc = this.objCategory.filter((item: any) => {
      return item.values == value;
    })
    this.obtypevalue = abc[0].option;
    if (value == "ALL") {
      this.objectType = [];
      this.objectType = [
        { values: 'ALL', option: 'ALL' }
      ];

    }
    if (value == "Storage_Objects") {
      this.objectType = [];
      this.objectType = [
        { values: 'ALL', option: 'ALL' },
        { values: 'TABLES', option: 'TABLES' },
        { values: 'VIEWS', option: 'VIEWS' },
        { values: 'MATERIALIZED_VIEWS', option: 'MATERIALIZED_VIEWS' },
        { values: 'SEQUENCES', option: 'SEQUENCES' },
        { values: 'SYNONYMS', option: 'SYNONYMS' },
        { values: 'TYPES', option: 'TYPES' },
      ];
    }
    if (value == "Code_Objects") {
      this.objectType = [];
      this.objectType = [
        { values: 'ALL', option: 'ALL' },
        { values: 'PROCEDURES', option: 'PROCEDURES' },
        { values: 'FUNCTIONS', option: 'FUNCTIONS' },
        { values: 'PACKAGES', option: 'PACKAGES' },
        { values: 'TRIGGERS', option: 'TRIGGERS' },
      ];
    }
  }

  GetDBConnections() {
    const projectId = this.projectId.toString();
    this.deployment.getConList(projectId).subscribe((data) => {
      this.prjSrcTgtData = data['Table1'];
      this.TargetConn = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "T" && item.migsrctype == "D";
      })
      this.SourceCo = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "S" && item.migsrctype == "D";
      })
    })
  }

  prjSchemaLists(conid: any) {
    this.SourceConn = conid;
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnection: conid.toString()
    }
    this.deployment.getschemaList(obj).subscribe((data) => {
      this.schemaList = data['Table1'];
    });
  }

  selectedObCategory: any
  selectObjectCategory(value: any) {
    if (value == "1") {
      this.selectedObCategory = ""
      this.selectedObCategory = "ALL"
    }
    if (value == "2") {
      this.selectedObCategory = ""
      this.selectedObCategory = "StorageObjects"
    }
    if (value == "3") {
      this.selectedObCategory = ""
      this.selectedObCategory = "CodeObjects"
    }
  }
  spin_process: any
  infraData: any
  filtered: any
  getInfraSelect() {
    let id = this.projectId;
    // this.project.InfraSelect(id).subscribe((data) => {
    //   this.infraData = data['jsonResponseData']['Table1'];
    //   this.filtered = this.infraData.filter((element: any) => {
    //     return element.active == 'True';
    //   });
    // });
  }
  selectedObType: any
  selectObjectType(value: any) {
    this.selectedObType = value
  }

  isProcessLoading: any = [];
  isProcessload(value: any) {
    this.isProcessLoading[value] = true
  }
  uploadedData: any = [];
  getuploadedFiles() {
    let requestObj = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "External_Source",
    };
    // this.project.Files(requestObj).subscribe((data) => {
    //   this.uploadedData = data;

    // });
  }
  targetdeltaData: any = [];
  gettargetdeltaFiles() {
    let requestObj = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "External_Target",
    };
    // this.project.Files(requestObj).subscribe((data) => {
    //   this.targetdeltaData = data;

    // });
  }
  spin_delete: any;
  DeleteClientFile(fileName: any) {
    let obj = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "External_Source",
      fileName: fileName,
    }
    this.spin_delete = true
    // this.client.deleteClientFile(obj).subscribe(
    //   (data) => {
    //     this.spin_delete = false
    //     if (data.message === fileName + " Deleted") {
    //       this.alertService.success(data.message);
    //       this.getuploadedFiles()
    //     }
    //     if (data.message === fileName + " DoesNot Exist") {
    //       this.alertService.danger(data.message);
    //     }
    //     if (data.message === fileName + " DoesNot Exist Or Already Deleted") {
    //       this.alertService.danger(data.message);
    //     }
    //   },
    //   (error) => {
    //     this.spin_delete = false
    //     this.alertService.danger('Something happened');
    //   }
    // );
  }
  isLoading: any = [];
  isload(value: any) {
    this.isLoading[value] = true
  }

  // sc schemalist
  schemaNames: any;
  GetSCSchemaList(runno: any) {
    let obj = {
      projectId: this.projectId,
      runno: runno
    }
    this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
      this.schemaNames = data['Table1'];
    });
  }

  selectedDropdown(data: any) {
    if (data == "1") {
      this.GetDBConnections();
    }
    else if (data == "") {

    }
  }

  onItemSelect(item: any) {
    this.schemaName.push(item.schemaname);
  }

  onItemobjectSelect(item: any) {
    this.objectType.push(item.objectType);
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.schemaname);
    this.schemaName.splice(inde, 1);
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.schemaname);
    });
  }
  onDeSelectAll(items: any) {
    this.schemaName = [];
  }

  Deltaconversion: any;
  DeltaCommand(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.contype,
      tgtCon: value.tgtcon,
      srcCon: value.sourcecon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      objecttype: value.objecttype,
      extraction_Category: "TARGET_COMPARISION",
      task: "TARGET_COMPARISION",
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      this.Deltaconversion = data
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data);
      }
    })
  }
  NoDataHide: Boolean = false;
  ExeLog: any = {};
  prjLogData: any;
  selectedrun: any;
  getPrjExeLogSelectTask(value: any) {
    this.selectedrun = value;
    if (value == "") {
      value = "null";
    }
    this.prjLogData = [];
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operationType = "Target_Conversion";
    this.ExeLog.action = "null";
    this.ExeLog.row_id = "null";
    this.ExeLog.runno = value;
    this.ExeLog.operationName = "All";
    this.deployment.PrjExelogSelectTask(this.ExeLog).subscribe((data: any) => {
      this.prjLogData = data['Table1'];
      this.NoDataHide = true;
    })
  }

  ref_spin: boolean = false
  refresh()
  {
    this.GetRequestTableData();
  }

  RequestTableData: any;
  GetRequestTableData() {
    let obj = {
      projectId: this.projectId,
      operationType: 'Target_Conversion'
    }
    this.ref_spin=true;
    this.deployment.GetRequestTableData(obj).subscribe((data: any) => {
      this.RequestTableData = data['Table1'];
      this.ref_spin=false;
    });
  }
  runNumbersData: any;
  getRunNumbers() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbersData = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }

  deleteResponse: any;
  // delete request table data
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.deployment.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      if (data['Table1'][0].v_status) {
        this.toast.success(data['Table1'][0].v_status);
      }
      else {
        this.toast.error(data);
      }
	  this.GetRequestTableData();
    })
  }
  LogsData: any;
  GetFilesExecutionLogsFromDirectory() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iterationForLogs + "/Execution_Logs/Deployment"
    };
    this.LogsData=[];
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.LogsData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }

  iterationForLogs: any;
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value;
    this.GetFilesExecutionLogsFromDirectory();
  }

  fileResponse: any;
  spin_dwld: any;
  //download files
  downloadFile(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }

}

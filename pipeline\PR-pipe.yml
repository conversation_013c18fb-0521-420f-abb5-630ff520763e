trigger:
  branches:
    include:
    - refs/heads/feature*
    - refs/heads/stage
    - refs/heads/master
name: $(SourceBranchName)_$(Date:yyyyMMdd)_$(Rev:.r)
resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/stage
jobs:
- job: job_1
  displayName: Pull Request
  timeoutInMinutes: 20  
  pool:
    vmImage: windows-2022
  steps:
  - checkout: self
    fetchDepth: 1
  - task: ShaykiAbramczyk.CreatePullRequest.CreatePullRequest.CreatePullRequest@1
    displayName: PR from feature to stage
    condition: startsWith(variables['Build.SourceBranch'], 'refs/heads/feature')
    inputs:
      targetBranch: stage
      title: $(Build.SourceBranch) into stage
      linkWorkItems: false
      mergeStrategy: noFastForward
  - task: ShaykiAbramczyk.CreatePullRequest.CreatePullRequest.CreatePullRequest@1
    displayName: PR from stage to main
    condition: startsWith(variables['Build.SourceBranch'], 'refs/heads/main')
    inputs:
      targetBranch: main
      title: $(Build.SourceBranch) into main
      linkWorkItems: false
      mergeStrategy: noFastForward

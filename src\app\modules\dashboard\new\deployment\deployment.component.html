<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-12">
            <dash-tabs></dash-tabs>
        </div>
    </div>
    <!--- Main Content --->
    <div class="qmigTabs mt-3">        
        <div class="row"> 
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #con (change)="getSchema(con.value)">
                        <option selected disabled>Select Source Connection</option>
                        @for(con of connectionsList ; track con;){
                            <option value="{{con}}">{{con}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #sc (change)="getIteration(sc.value)">
                        <option selected disabled>Select Schema Name</option>
                        @for(sc of schemaList; track sc;){
                            <option value="{{sc}}">{{sc}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #it (change)="getOperations(it.value)">
                        <option selected disabled>Select Iteration</option>
                        @for(it of iterationList; track it;){
                            <option value="{{it}}">{{it}}</option>
                        }
                    </select>
                </div>
            </div>   
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Connection</p>
                            <h4 class="mt-1">PRJ1167SRC</h4>
                        </div>
                        <div class="col-3 mt-3">                                
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Iteration</p>
                            <h4 class="mt-1">1714</h4>
                        </div>
                        <div class="col-3 mt-3">                                
                            <img src="../../../../../assets/images/dashboard/chart.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Schema Name</p>
                            <h4 class="mt-1">Ggdb 2</h4>
                        </div>
                        <div class="col-3 mt-3">                                
                            <img src="../../../../../assets/images/dashboard/chart1.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Total Delta Converted</p>
                            <h4 class="mt-1">227</h4>
                        </div>
                        <div class="col-3 mt-3">                                
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Total Delta Extracted Count</p>
                            <h4 class="mt-1">240</h4>
                        </div>
                        <div class="col-3 mt-3">                                
                            <img src="../../../../../assets/images/dashboard/chart.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>New Objects Count</p>
                            <h4 class="mt-1">13</h4>
                        </div>
                        <div class="col-3 mt-3">                                
                            <img src="../../../../../assets/images/dashboard/chart1.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Pending Count </p>
                            <h4 class="mt-1">0</h4>
                        </div>
                        <div class="col-3 mt-3">                                
                            <img src="../../../../../assets/images/dashboard/percentage.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-3 dashCH">
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Converted Percentage</h5>
                    <div style="display: block;">
                        <canvas id="myDoughnutChart" baseChart width="100" height="120" #chart3="base-chart"
                        [type]="'doughnut'"
                        [data]="lineChartData"
                        [options]="lineChartOptions"
                        [legend]="lineChartLegend"
                        [plugins]="lineChartPlugins">
                        </canvas>
                    </div>
              </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Matching and Not Matching Count by Object</h5>
                    <div style="display: block;">
                        <canvas width="400" [type]="'line'" height="200" baseChart #chart3="base-chart" [datasets]="NORIssuesChartData"
                            [labels]="NORIssuesChartLabels" [options]="NORIssuesChartOptions" [legend]="NORIssuesChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">            
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Matching Count and Not Maching Count by Target Schema </h5>
                    <div style="display: block;" class="mt-3">                        
                        <canvas width="400" height="140" baseChart  [datasets]="sourceVSChartData"
                            [labels]="sourceVSChartLabels" [options]="sourceVSChartOptions" [legend]="sourceVSChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
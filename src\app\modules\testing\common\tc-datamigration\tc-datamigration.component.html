<div class="v-pageName">{{pageName}}</div>

<!-- Source Connection Dropdown -->
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Connections
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="form addcontact">
                        <form class="form add_form" [formGroup]="TestCaseForm">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Source Connection</label>
                                        <select class="form-select ml-0" formControlName="connectionName" #Selection
                                            (change)="selectsourceconnection(Selection.value)">
                                            <option selected value="">Source Connection </option>
                                            @for(list of sourceData; track sourceData; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        @if ( f.connectionName.touched && f.connectionName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.connectionName.errors.required) {Source Connection is Required }
                                        </p>
                                        }
                                        <!-- @if ( f.cycle.touched && f.cycle.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.cycle.errors.required) {Run No is Required }
                                    </p>
                                    } -->
                                    </div>
                                </div>

                                <!-- Target Connection Dropdown -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Target Connection</label>
                                        <select class="form-select ml-0" formControlName="targetConnection"
                                            #tgtSelection (change)="testCaseSelections(tgtSelection.value)">
                                            <option selected value="">Target Connection</option>
                                            @for(list of targetData; track targetData; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        @if ( f.targetConnection.touched && f.targetConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.targetConnection.errors.required) {Target Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <!-- Testcase label -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Test Case</label>
                                        <input type="text" formControlName="testCase" placeholder="" id="cstmt"
                                            class="form-control">
                                        @if ( f.testCase.touched && f.testCase.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.testCase.errors.required) {TestCase is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- Threads label -->
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">No of Threads</label>
                                        <input type="text" placeholder="" id="thd" class="form-control"
                                            value="{{threads}}">
                                    </div>
                                </div>
                                <div class="col-md-3 offset-md-9">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100" [disabled]="TestCaseForm.invalid"><span
                                                class="mdi mdi-cog-play"></span>Execute
                                            @if(spinner){<app-spinner-white />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>



        <!--- Documents List --->


        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Documents List
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-6">
                                <div class="custom_search ms-0">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Document" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Folder Name</th>
                                    <th>File Name</th>
                                    <th>Download</th>

                            </thead>
                            <tbody>
                                <!-- @for (documents of ExecututionFiles | searchFilter: searchText | paginate: { itemsPerPage: 10, currentPage: pageNumber } ; track documents; let  i = $index) {
                            <tr> 
                                <td>{{i + 1}}</td>
                                <td>{{documents.fileName  }}</td>
                                <td>Project Docs</td>
                                <td>  
                                   
                                    <button class="btn btn-download" (click)="downloadFile(documents)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                      } @empty { 
                        <tr>
                            <td colspan="4"><p class="text-center m-0 w-100">Empty list of Documents</p></td>
                        </tr>
                       }  -->

                                <tr>
                                    <td>1</td>
                                    <td>1059_TcDataMigration</td>
                                    <td>table_list.txt</td>
                                    <td>
                                        <button class="btn btn-download">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                    </div>
                </div>
            </div>
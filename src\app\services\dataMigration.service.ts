import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { dataMigrationAPIConstant } from '../constant/dataMigrationAPIConstant'
import { environment } from '../environments/environment';
import { GetConfigData, GetFilesFromExpath, GetReqData, GetRequestData, GetTablesByschema, GetTablesByschemaGG, PrjSchemasListSelectData, SchemaListSelect, SchemaListSelect1, SchemaSelect, UploadProjectDocs, airflowValidationCommand, conList, dataLoadStatus, dataStatus, deleteFile, fileStatus, getSchemas, insertTablesCommand, insertTablesCommand1, projectConRunTblInsert, setRedis, setRedisCache, setRedisCache1, setredis, triggerValidationDags } from '../models/interfaces/types';
import { APIConstant } from '../constant/APIConstant';
import { assessmentAPIConstant } from '../constant/assessmentAPIConstant';
@Injectable({
  providedIn: 'root'
})
export class DataMigrationService {

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl+'Migration/'
  apiUrl ="https://localhost:7113/api/v1/Common/Migration/UpdateJsonConfig"

  //ReadLocalJson
  ReadLocalJsonURL = this.apiURL + dataMigrationAPIConstant.ReadJsonConfig;
  ReadLocalJson = (body:any): Observable<any> => {
    return this.apiService.get(this.ReadLocalJsonURL + body  )
  }
  //UpdateJsonConfig
  EditLocalJsonURL = this.apiURL + dataMigrationAPIConstant.UpdateJsonConfig;
  EditLocalJson = (body:any): Observable<any> => {
    return this.apiService.post(this.EditLocalJsonURL ,body )
  }

  //GetFilesFromExpath
  GetFilesFromExpathURL = this.apiURL + APIConstant.common.GetFilesFromExpath;
  GetFilesFromExpath = (body: string): Observable<GetFilesFromExpath> => {
    return this.apiService.get(this.GetFilesFromExpathURL + body)
  }

  
  GetGGPDConfigFilesURL = this.apiURL + dataMigrationAPIConstant.GetConfigFilesFromExpath;
  GetGGPDConfigFiles = (body: any): Observable<GetFilesFromExpath> => {
    return this.apiService.get(this.GetGGPDConfigFilesURL + body.configtype+"&filePath="+encodeURIComponent(body.filePath))
  }

  //downloadLargeFiles
  downloadLargeFilesURL = this.apiURL + dataMigrationAPIConstant.downloadLargeFiles;
  downloadLargeFiles = (body: string): Observable<any> => {
    return this.apiService.get(this.downloadLargeFilesURL + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }

  /*--- Delete Files ---*/

  deleteFileURL = this.apiURL + APIConstant.common.deleteFile;
  deleteFile = (body: string): Observable<deleteFile> => {
    return this.apiService.get(this.deleteFileURL + body)
  }

  //SchemaListSelect}
  SchemaListSelectURL = this.apiURL + dataMigrationAPIConstant.SchemaListSelect;
  SchemaListSelect = (body: SchemaListSelect): Observable<SchemaSelect> => {
    return this.apiService.get(this.SchemaListSelectURL + body.projectid + '&ConId=' + body.connectioId)
  }
  SchemaListSelect1 = (body: SchemaListSelect1): Observable<SchemaSelect> => {
    return this.apiService.get(this.SchemaListSelectURL + body.projectId + '&ConId=' + body.connectionId)
  }

  //airflowValidationCommand
  airflowValidationCommandURL = this.apiURL + dataMigrationAPIConstant.airflowValidationCommand;
  airflowValidationCommand = (body: airflowValidationCommand): Observable<fileStatus> => {
    return this.apiService.post(this.airflowValidationCommandURL, body);
  }

  //triggerValidationDags
  triggerValidationDagsURL = this.apiURL + dataMigrationAPIConstant.triggerValidationDags;
  triggerValidationDags = (body: triggerValidationDags): Observable<fileStatus> => {
    return this.apiService.get(this.triggerValidationDagsURL + body.operation + '&fileName=' + body.fileName)
  }

  //GetCommonFilesFromDirectory
  GetCommonFilesFromDirectoryURL = this.apiURL + dataMigrationAPIConstant.GetCommonFilesFromDirectory;
  GetCommonFilesFromDirectory = (body: any): Observable<any> => {
    return this.apiService.get(this.GetCommonFilesFromDirectoryURL + body)
  }
  //GetRunno
  GetRunnoURL = this.apiURL + dataMigrationAPIConstant.GetRunno;
  GetRunno = (body: string): Observable<any> => {
    return this.apiService.get(this.GetRunnoURL + body)
  }

  //GetReqData
  GetReqDataURL = this.apiURL + dataMigrationAPIConstant.GetReqData;
  GetReqData = (body: GetReqData): Observable<GetRequestData> => {
    return this.apiService.get(this.GetReqDataURL + body.projectId + '&operationType=' + body.operationType)
  }

  //getConList
  getConListURL = this.apiURL + dataMigrationAPIConstant.getConList;
  getConList = (body: string): Observable<conList> => {
    return this.apiService.get(this.getConListURL + body)
  }

  //PrjSchemasListSelectData
  PrjSchemasListSelectDataURL = this.apiURL + dataMigrationAPIConstant.PrjSchemasListSelectData;
  PrjSchemasListSelectData = (body: PrjSchemasListSelectData): Observable<PrjSchemasListSelectData> => {
    return this.apiService.get(this.PrjSchemasListSelectDataURL + body.projectId + '&objectgroupname=' + body.ConId)
  }

  //projectConRunTblInsert

  projectConRunTblInsertURL = this.apiURL + dataMigrationAPIConstant.projectConRunTblInsert;
  projectConRunTblInsert = (body: projectConRunTblInsert): Observable<projectConRunTblInsert> => {
    return this.apiService.post(this.projectConRunTblInsertURL, body);
  }
  //setRedisCache

  setRedisCacheURL = this.apiURL + dataMigrationAPIConstant.setRedisCache;
  setRedisCache = (body: setRedisCache): Observable<fileStatus> => {
    return this.apiService.post(this.setRedisCacheURL, body);
  };
  //setRedisCache1
  setRedisCache1 = (body: setRedisCache1): Observable<fileStatus> => {
    return this.apiService.post(this.setRedisCacheURL, body);
  };
  setRedisCache2 = (body: any): Observable<fileStatus> => {
    return this.apiService.post(this.setRedisCacheURL, body);
  };

  //tgtSchemaSelect
  tgtSchemaSelectURL = this.apiURL + dataMigrationAPIConstant.tgtSchemaSelect;
  tgtSchemaSelect = (body: string): Observable<string> => {
    return this.apiService.get(this.tgtSchemaSelectURL + body)
  }

  //deleteValidationFiles
  deleteValidationFilesURL = this.apiURL + dataMigrationAPIConstant.deleteValidationFiles;
  deleteValidationFiles = (body: string): Observable<string> => {
    return this.apiService.get(this.deleteValidationFilesURL + body)
  }
  //UploadDagFiles
  UploadDagFilesURL = this.apiURL + dataMigrationAPIConstant.UploadDagFiles;
  UploadDagFiles = (body: any): Observable<fileStatus> => {
    return this.apiService.post(this.UploadDagFilesURL, body);
  }

  //getSchemas
  getSchemasURL = this.apiURL + dataMigrationAPIConstant.getSchemas;
  getSchemas = (body: string): Observable<getSchemas> => {
    return this.apiService.get(this.getSchemasURL + body)
  }

  //dataLoadStatus
  dataLoadStatusURL = this.apiURL + dataMigrationAPIConstant.dataLoadStatus;
  dataLoadStatus = (body: dataLoadStatus): Observable<dataStatus> => {
    return this.apiService.get(this.dataLoadStatusURL + body.schema + '&configId=' + body.configId + '&audConId=' + body.audConId)
  }
  //GetConfigData
  GetConfigDataURL = this.apiURL + dataMigrationAPIConstant.GetConfigData;
  GetConfigData = (): Observable<GetConfigData> => {
    return this.apiService.get(this.GetConfigDataURL)
  }

  //data migrations bindu
  //deleteFiles
  deleteFilesURL = this.apiURL + APIConstant.common.deleteFile;
  deleteFiles = (body: string): Observable<string> => {
    return this.apiService.get(this.deleteFilesURL + body);
  }

  //DataCompareFiles
  DataCompareFilesURL = this.apiURL + APIConstant.common.DataCompareFiles;
  DataCompareFiles = (body: string): Observable<string> => {
    return this.apiService.get(this.DataCompareFilesURL + body);
  }
  //GetTablesByschema
  GetTablesByschemaURL = this.apiURL + APIConstant.common.GetTablesByschema;
  GetTablesByschema = (body: GetTablesByschema): Observable<any> => {
    return this.apiService.get(this.GetTablesByschemaURL + body.schemaname + '&conId=' + body.conId);
  }
  //insertTablesCommand
  insertTablesCommandURL = this.apiURL + APIConstant.common.insertTablesCommand
  insertTablesCommand1 = (body: insertTablesCommand1): Observable<any> => {
    return this.apiService.post(this.insertTablesCommandURL, body);
  }

  //insertTablesCommand
  GGPDCrateConifgURL = this.apiURL + dataMigrationAPIConstant.GGPDCrateConifg
  GGPDCrateConifg = (body: insertTablesCommand1): Observable<any> => {
    return this.apiService.post(this.GGPDCrateConifgURL, body);
  }

  /*--Execution datamigration--*/
  // Operation details
  getoperationurl = this.apiURL + dataMigrationAPIConstant.getoperation
  getoperation = (data: any): Observable<any> => {
    return this.apiService.get(this.getoperationurl + data.projectId + '&OperationType=' + data.OperationType);
  };

  GetFilesFromExpathsurl = this.apiURL + dataMigrationAPIConstant.GetFilesFromEx;
  getFilesfromExPath = (data: any): Observable<any> => {
    return this.apiService.get(this.GetFilesFromExpathsurl + data);
  };
  getExcelDagssurl = this.apiURL + dataMigrationAPIConstant.getExcelDags;
  getExcelDags = (data: any): Observable<any> => {
    return this.apiService.get(this.getExcelDagssurl + encodeURIComponent(data));
  };
  getExcelDagssNewurl = this.apiURL + dataMigrationAPIConstant.getExcelDags;
  getExcelDagsNew = (data: any): Observable<any> => {
    return this.apiService.get(this.getExcelDagssNewurl + data);
  };
  getggpdDagssurl = this.apiURL + dataMigrationAPIConstant.getggpdDags;
  getggpdDags = (data: any): Observable<any> => {
    return this.apiService.get(this.getggpdDagssurl + data);
  };
  getggpdDagssNewurl = this.apiURL + dataMigrationAPIConstant.getggpdDagsNew;
  getggpdDagsNew = (data: any): Observable<any> => {
    return this.apiService.get(this.getggpdDagssNewurl + data);
  };
  getDagsurl = this.apiURL + dataMigrationAPIConstant.getDags;
  getDags = (): Observable<any> => {
    return this.apiService.get(this.getDagsurl);
  };
  GetAirflowDirsurl = this.apiURL + dataMigrationAPIConstant.GetAirflowDirs;
  GetAirflowDirs = (body: string): Observable<any> => {
    return this.apiService.get(this.GetAirflowDirsurl + encodeURIComponent(body));
  };
  GetAirflowLogsurl = this.apiURL + dataMigrationAPIConstant.GetAirflowLogs;
  GetAirflowLogs = (body: string): Observable<any> => {
    return this.apiService.get(this.GetAirflowLogsurl + encodeURIComponent(body));
  };

  ModifyDagRunurl = this.apiURL + dataMigrationAPIConstant.ModifyDagRun;
  ModifyDagRun = (body: any): Observable<any> => {
    return this.apiService.get(this.ModifyDagRunurl + body.dagId + '&dag_runId=' + encodeURIComponent(body.dag_runId));
  };
  getScnNumberurl = this.apiURL + dataMigrationAPIConstant.getScnNumber;
  getScnNumber = (body: any): Observable<any> => {
    return this.apiService.get(this.getScnNumberurl + encodeURIComponent(body));
  };

  //configurations in datamigration
  //deleteAirflowFiles

  deleteAirflowFilesUrl = this.apiURL + dataMigrationAPIConstant.deleteAirflowFiles
  deleteAirflowFiles = (data: any): Observable<any> => {
    return this.apiService.get(this.deleteAirflowFilesUrl + encodeURIComponent(data.filename)+'&filePath='+data.filePath);
  };

  //ReplaceFileName
  ReplaceFileNameUrl = this.apiURL + dataMigrationAPIConstant.ReplaceFileName
  ReplaceFileName = (data: any): Observable<any> => {
    return this.apiService.get(this.ReplaceFileNameUrl + encodeURIComponent(data));
  };
  //insertTablesCommand
  //insertTablesCommandURL = this.apiURL + dataMigrationAPIConstant.assessment.insertTablesCommand
  insertTablesCommand = (body: any): Observable<any> => {
    return this.apiService.post(this.insertTablesCommandURL, body);
  }
  //TriggerMultiDagsWithCDC
  TriggerMultiDagsWithCDCURL = this.apiURL + dataMigrationAPIConstant.TriggerMultiDagsWithCDC
  TriggerMultiDagsWithCDC = (body: any): Observable<any> => {
    return this.apiService.post(this.TriggerMultiDagsWithCDCURL, body);
  }
  //getDagStatus
  getDagStatusUrl = this.apiURL + dataMigrationAPIConstant.getDagStatus
  getDagStatus = (data: any): Observable<any> => {
    return this.apiService.get(this.getDagStatusUrl + encodeURIComponent(data.dag_id) + '&dag_runid=' + encodeURIComponent(data.dag_run_id));
  };
  //getDagRuns
  getDagRunsUrl = this.apiURL + dataMigrationAPIConstant.getDagRuns
  getDagRuns = (data: any): Observable<any> => {
    return this.apiService.get(this.getDagRunsUrl + data);
  };

  ScheduleDagsURL = this.apiURL + dataMigrationAPIConstant.ScheduleDags
  ScheduleDags = (body: any): Observable<any> => {
    return this.apiService.post(this.ScheduleDagsURL, body);
  }

  OracleSchemasURL = this.apiURL + dataMigrationAPIConstant.OracleSchemas
  OracleSchemas = (body: any): Observable<any> => {
    return this.apiService.get(this.OracleSchemasURL + body);
  }
  getConfigUri = this.apiURL + dataMigrationAPIConstant.GetConfigMenu
  GetConfigMenu = (body: any): Observable<any> => {
    return this.apiService.get(this.getConfigUri+body);
  }

  /*---- POD Management ----*/
  getpodsUri = this.apiURL + dataMigrationAPIConstant.Getpods
  Getpods = (body: any): Observable<any> => {
    return this.apiService.post(this.getpodsUri,body);
  }

  deletePODURL = this.apiURL + dataMigrationAPIConstant.deletePods
  deletePOD = (body:any): Observable<string> => {
    return this.apiService.post(this.deletePODURL,body);
  }

  clearTaskURL = this.apiURL + dataMigrationAPIConstant.clearTask
  clearTask = (task:string, body:any): Observable<string> => {
    return this.apiService.post(this.clearTaskURL + task ,body);
  }

  markTaskURL = this.apiURL + dataMigrationAPIConstant.markTaskRun
  markTask = (body:any): Observable<string> => {
    return this.apiService.post(this.markTaskURL,body);
  }

  //GetDagsExecutionLogData
  getDagsExecutionLogURL = this.apiURL + dataMigrationAPIConstant.GetDagsExecutionLog;
  getDagsExecutionLog = (body: any): Observable<any> => {
    return this.apiService.get(this.getDagsExecutionLogURL + body.configID + '&tablename=' + body.tablename )
  }

  fetchdynamotablesURL = this.apiURL + dataMigrationAPIConstant.FetchdynamoTables;
  fetchDynamoTables = (body: string): Observable<any> => {
    return this.apiService.get(this.fetchdynamotablesURL + body )
  }

  triggerDataValidationURL = this.apiURL + dataMigrationAPIConstant.triggerDataValidation;
  triggerDataValidation = (body: any): Observable<any> => {
    return this.apiService.get(this.triggerDataValidationURL + body )
  }

  fetchTablesFromDBURL = this.apiURL + dataMigrationAPIConstant.fetchTablesFromDB;
  fetchTablesFromDB = (): Observable<any> => {
    return this.apiService.get(this.fetchTablesFromDBURL  )
  }
  fetchFilesFromDBURL = this.apiURL + dataMigrationAPIConstant.fetchFilesFromDB;
  fetchFilesFromDB = (table:any): Observable<any> => {
    return this.apiService.get(this.fetchFilesFromDBURL +table )
  }

    //getGoldenGatePod
    getGoldenGatePodURL = this.apiURL + APIConstant.common.getGoldenGatePod;
    getGoldenGatePod = (): Observable<string> => {
      return this.apiService.get(this.getGoldenGatePodURL)
    }
  
  //GetGGShellDirectoryEx
  GetGGShellDirectoryExURL = this.apiURL + APIConstant.common.GetGGShellDirectoryEx;
  GetGGShellDirectoryEx = (body: string): Observable<GetFilesFromExpath> => {
    return this.apiService.get(this.GetGGShellDirectoryExURL + body)
  }
  //GetTablesByschemaGG
  GetTablesByschemaGGURL = this.apiURL + APIConstant.common.GetTablesByschemaGG;
  GetTablesByschemaGG = (body: GetTablesByschemaGG): Observable<any> => {
    return this.apiService.get(this.GetTablesByschemaGGURL + body.schema + '&srcId=' + body.srcId)
  }

    //UploadProjectDocs
    UploadProjectDocsURL = this.apiURL + APIConstant.common.UploadProjectDocs;
    UploadProjectDocs = (body: any): Observable<UploadProjectDocs> => {
      return this.apiService.post(this.UploadProjectDocsURL, body);
    }

    GetcdcdataURL = this.apiURL + APIConstant.common.getCdcData;
  GetCdcData = (body: any): Observable<GetConfigData> => {
    return this.apiService.post(this.GetcdcdataURL, body)
  }
  TriggerCDCURL = this.apiURL + APIConstant.common.TriggerCDCCommand;
  TriggerCDC = (body: any): Observable<GetConfigData> => {
    return this.apiService.post(this.TriggerCDCURL, body)
  }

  DataMigrationCommandURL = this.apiURL + APIConstant.common.DataMigrationCommand;
  DataMigrationCommand = (body: any): Observable<GetConfigData> => {
    return this.apiService.post(this.DataMigrationCommandURL, body)
  }

  DataMigrationNewCommandURL = this.apiURL + APIConstant.common.DataMigrationNewCommand;
  DataMigrationNewCommand = (body: any): Observable<GetConfigData> => {
    return this.apiService.post(this.DataMigrationNewCommandURL, body)
  }

  getValidationDagsURL = this.apiURL + dataMigrationAPIConstant.getValidationDags;
  getValidationDags = (data: any): Observable<any> => {
    return this.apiService.get(this.getValidationDagsURL+data);
  };

  getRelicationURL = this.apiURL + dataMigrationAPIConstant.replicationSlot;
  getRelication = (data: any): Observable<any> => {
    return this.apiService.get(this.getRelicationURL+data);
  };

  
  
  GetPGTablesURL = this.apiURL + dataMigrationAPIConstant.getPgTables;
  GetPGTables = (body: any): Observable<any> => {
    return this.apiService.get(this.GetPGTablesURL + body.conid + '&schema=' + body.schema)
  }

  FetchMongoTablesURL = environment.serviceUrl1 + dataMigrationAPIConstant.FetchMongoTables;
  FetchMongoTables = (body: any): Observable<any> => {
    return this.apiService.get(this.FetchMongoTablesURL + body)
  }
  getAuditDagsByConnectionURL = this.apiURL + dataMigrationAPIConstant.getAuditDagsByConnection;
  getAuditDagsByConnection = (body: any): Observable<any> => {
    return this.apiService.get(this.getAuditDagsByConnectionURL + body.srcId + '&tgtId=' + body.tgtId)
  }

  GetAuditDagsStatusURL = this.apiURL + dataMigrationAPIConstant.GetAuditDagsStatus;
  GetAuditDagsStatus = (body: any): Observable<any> => {
    return this.apiService.get(this.GetAuditDagsStatusURL + body.srcId + '&tgtId=' + body.tgtId)
  }
  GetAuditJobTrackStatusURL = this.apiURL + dataMigrationAPIConstant.GetAuditJobTrackStatus;
  GetAuditJobTrackStatus = (body: any): Observable<any> => {
    return this.apiService.get(this.GetAuditJobTrackStatusURL + body.srcId + '&tgtId=' + body.tgtId)
  }

  deleteJobTrackStatusURL = this.apiURL + dataMigrationAPIConstant.deleteJobTrackStatus;
  deleteJobTrackStatus = (body: any): Observable<any> => {
    return this.apiService.get(this.deleteJobTrackStatusURL + body.srcId + '&tgtId=' + body.tgtId)
  }

  getOracleTablesURL = environment.serviceUrl1 + dataMigrationAPIConstant.getOracleTables;
  getOracleTables = (data: any): Observable<any> => {
    return this.apiService.get(this.getOracleTablesURL+data.conid+"&schemaname=" + data.schemaname);
  };
  getDb2TablesURL = environment.serviceUrl1 + dataMigrationAPIConstant.getDb2Tables;
  getDb2Tables = (data: any): Observable<any> => {
    return this.apiService.get(this.getDb2TablesURL+data.conid+"&schemaname=" + data.schemaname);
  };
  getsqlTablesURL = environment.serviceUrl1 + dataMigrationAPIConstant.getSqlTables;
  getsqlTables = (data: any): Observable<any> => {
    return this.apiService.get(this.getsqlTablesURL+data.conid+"&schemaname=" + data.schemaname);
  };
  GetFoldersURL = this.apiURL + dataMigrationAPIConstant.getFolders;
  GetFolders = (body: any): Observable<GetFilesFromExpath> => {
    return this.apiService.get(this.GetFoldersURL +(body))
  }
  GetMariaTablesURL = environment.serviceUrl1+ APIConstant.common.GetMariaTables;
  GetMariaTables = (body: any): Observable<any> => {
    return this.apiService.get(this.GetMariaTablesURL + body.conid + '&dbname=' + body.dbname)
  }

  GetPostgresTablesBySchemaURL = this.apiURL+ APIConstant.common.GetPostgresTablesBySchema;
  GetPostgresTablesBySchema = (body: any): Observable<any> => {
    return this.apiService.get(this.GetPostgresTablesBySchemaURL + body.conid + '&schema=' + body.schema)
  }
   getsqlServerTablesURL = environment.serviceUrl1 + dataMigrationAPIConstant.getSqlServerTables;
  getsqlServerTables = (data: any): Observable<any> => {
    return this.apiService.get(this.getsqlServerTablesURL+data.conid+"&schemaname=" + data.schemaname);
  };
  //deleteTableData
  deleteTableDataURL = this.apiURL + assessmentAPIConstant.deleteTableData;
  deleteTableData = (body: any): Observable<any> => {
    return this.apiService.get(this.deleteTableDataURL + body.projectId + '&requestId=' + body.requestId);
  };

  // getConfigData
  getConfigDataURL = this.apiURL + dataMigrationAPIConstant.getConfigData;
  getConfigData = (body: any): Observable<any> => {
    return this.apiService.get(this.getConfigDataURL + body.srcid + '&tgtid=' + body.tgtid);
  }

  // updateAgentStatus
  updateAgentStatusURL = this.apiURL + dataMigrationAPIConstant.updateAgentStatus;
  updateAgentStatus = (body: any): Observable<any> => {
    return this.apiService.post(this.updateAgentStatusURL, body);
  }

}
<!-- <header -->
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="execProjectForm">
            <div class="row">
                <!-- source connection list -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group" form>
                        <label class="form-label d-required" for="Schema">Source Connection</label>
                        <select class="form-select" formControlName="connectionName" #srccon
                            (change)="getSchemasList(srccon.value,'0')">
                            <option>Select Connection Name</option>
                            @for(ConList of srcConsList;track ConList; ){
                            <option value="{{ConList.Connection_ID}}">{{ConList.conname}}</option>
                            }
                        </select>
                        @if ( f.connectionName.touched && f.connectionName.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.connectionName.errors.required) {ConnectionName is Required }
                        </p>
                        }
                    </div>
                </div>
                <!-- target connection list -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Target Connection</label>
                        <select class="form-select" formControlName="targetconnection" #tgtcon
                            (change)="getSchemasList(tgtcon.value,'1')">
                            <option>Select Target Connection Name</option>
                            @for(tgtList of tgtConsList;track tgtList; ){
                            <option value="{{tgtList.Connection_ID}}">{{tgtList.conname}}</option>
                            }
                        </select>
                        @if ( f.targetconnection.touched && f.targetconnection.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.targetconnection.errors.required) {Target Connection is Required }
                        </p>
                        }
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Source Schema</label>
                        <select class="form-select" formControlName="schema">
                            <option>Select Source Schema</option>
                            @for(srcsch of srcschemaList;track srcsch; ){
                            <option value="{{srcsch.schemaname}}">{{srcsch.schemaname}}</option>
                            }
                        </select>
                        @if ( f.schema.touched && f.schema.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.schema.errors.required) {Source Schema is Required }
                        </p>
                        }
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Target Schema</label>
                        <select class="form-select" formControlName="tgtschema">
                            <option>Select Target Schema</option>
                            @for(tgtsch of tgtschemaList;track tgtsch; ){
                            <option value="{{tgtsch.schemaname}}">{{tgtsch.schemaname}}</option>
                            }
                        </select>
                        @if ( f.tgtschema.touched && f.tgtschema.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.tgtschema.errors.required) {Target Schema is Required }
                        </p>
                        }
                    </div>
                </div>
                <!-- get object type list -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Object Type</label>
                        <select class="form-select" formControlName="objectType">
                            <option>Select Object type</option>
                            @for(list of objectType;track list; ){
                            <option value="{{list.objecttype_name}}">{{list.objecttype_name}}</option>
                            }
                        </select>
                        @if ( f.objectType.touched && f.objectType.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.objectType.errors.required) {ObjectType is Required }
                        </p>
                        }
                    </div>
                </div>
                <!-- check details button -->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-md-6">
                    <button [disabled]="execProjectForm.invalid" class="btn btn-upload w-100 mt-3" (click)="triggere2eCOmmand(execProjectForm.value)">
                        Execute </button>
                </div>
            </div>
        </form>
    </div>
</div>
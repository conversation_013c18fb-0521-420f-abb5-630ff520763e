import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { deleteFile, documentsList } from '../../../../models/interfaces/types';
import { PerofmanceTestingService } from '../../../../services/performanceTesting.service';
import { Content } from '@ngneat/overview';
import * as XLSX from 'xlsx';
import { ActivatedRoute } from '@angular/router';


declare let $: any;
@Component({
  selector: 'app-performance-testing',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],

  templateUrl: './performance-testing.component.html',
  styles: ``
})

export class PerformanceTestingComponent {
  perfForm:any = this.formBuilder.group({
    category: ['', Validators.required],
    tgtconnection: ['', Validators.required],
    config: ['', Validators.required],
    connection: ['', Validators.required],
    connectionType: ['', Validators.required],
    fromDate: [''],
    toDate: [''],
  });

  hidedata: boolean = true;
  data: boolean = false;
  projectId: string;
  ref_spin: boolean = true;
  tabledata: any;
  userData: any = [];
  schemaList: any = [];
  schemaName: any = [];
  selectedschemas: any = [];
  execProjectForm: any;
  selectFile: any;
  fileName: string = '';
  uploadfileSpin: boolean = false;
  fileAdd: boolean = false;
  projectDocuments: any;
  projectDocumentFilter: any;
  pageNumber: number = 1;
  searchText: string = '';
  exe: any;
  contype: string = ""
  dup: string = ""
  mon: string = ""
  tgtId: any
  tgtValue: string = ''
  z: any;
  i: any;
  selectedConname: any
  conName: any
  conId: any;
  ConsList: any;
  tgtList: any
  selectedCategory: string = ""
  hideDrpdwn: boolean = false
  hideD: boolean = false
  value: any;
  selectedDuplicate: string = "";
  selectedMonitor: string = "";
  P2: number = 1;
  P3: number = 1;
  perSchema: any;
  selectedItems = [];
  selectedObjItems1 = [];
  perSchema1: any;
  exe1: any;
  TableData: boolean = false;
  analyzesearchlist: any;
  Exespinner: boolean = false;
  Monspinner: boolean = false;
  excelSpin: boolean = false;
  hideMon: boolean = false;

  pageName: string = ''
  indexMonitor: any;
  Monitor: boolean = false;
  testing: any;
  hideLog: boolean = false;
  uploadForm: any;
  hideTable: boolean = false;
  Callstm: any;
  task: any;
  ParTable: any;
  ConId: any;
  stat: boolean = false;
  PartitonTableData: any;
  hideQuery: boolean = false;
  selectedLog: string = "";
  hideL: boolean = false;
  hideS: boolean = false;
  hidedate: boolean = false;
  Monitdate: boolean = false;

  
  get f(): any {
    return this.uploadForm.controls;
  }


  constructor(private toast: HotToastService, private performanceservices: PerofmanceTestingService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    
  }

  /*--- Page Title ---*/
  pageTitle: string = "Documents"
  pageIcon: string = "assets/images/documents.svg"


  ngOnInit(): void {
    this.GetConsList();
    this.fetchFiles();
    this.filterExecutionReports();
    this.pageName = this.route.snapshot.data['name'];
  }
  /*--- Execute   ---*/
  Execute(data: any) {
    var date = new Date().toLocaleString('en-US', { hour12: false }).split(" ");
    // Now we can access our time at date[1], and monthdayyear @ date[0]
    var time = date[1];
    var mdy = date[0];
    var t = time.split(':').join('_');
    // We then parse  the mdy into parts
    var mdy1 = mdy.split('/');
    var month = parseInt(mdy1[0]);
    var day = parseInt(mdy1[1]);
    var year = parseInt(mdy1[2]);
    // Putting it all together
    var formattedDate = year + '_' + month + '_' + day + '_' + t;
    const obj = {
      projectId: this.projectId.toString(),
      option: parseInt(this.contype),
      fileName: this.fileName + "_" + formattedDate,
      tgtId: data.tgtconnection,
      // fromDate: data.fromDate,
      // toDate: data.toDate,
      category: data.category,
      task:"Full_Table_Scan"
      // task: data.category,
      // config: data.config+"_"+formattedDate,
    }
    //console.log(obj)
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.exe = data
      this.toast.success(data.message)
    })
  }

  /*--- Log explain method ---*/
  InputCall() {
    var stmt=(<HTMLInputElement>document.getElementById("call_stmt")).value;
    const obj = {
      task:"Log_explain_Plan",
      projectId: this.projectId.toString(),
      option: parseInt(this.contype),
      tgtId: this.tgtId,
      callStatement:"\""+stmt+"\"",
    }
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.Callstm = data
      this.toast.success(data.message)
    })

  }
  

  Submit(data: any) {
    const obj = {
      conId: this.conId.toString(),
      schemas: data.category,
      option: parseInt(this.dup),
      task:"Log_explain_Plan"
    }
    //console.log(obj)
    this.performanceservices.Submit(obj).subscribe((data: any) => {
      this.exe1 = data
      this.toast.success(data.message)
    })

  }

  selectSchema(value: any) {
    this.selectedschemas = value;
  }

  /*--- Validation ---*/
  get validate() {
    return this.perfForm.controls;
  }


  /*--- GetDuplicate Index ---*/
  duplicateIndexData: any
  GetDuplicateIndexData() {
    this.Exespinner = true;
    this.TableData = true;
    var obj = {}
    if (parseInt(this.selectedDuplicate) == 0) {
      obj = {
        conId: this.tgtId,
        option: parseInt(this.selectedDuplicate),
      }
    }
    else {
      var schms: any = []
      if (this.selectedschemas.length > 0) {
        this.selectedschemas.filter((item: any) => {
          schms.push("'" + item + "'")
        })
        schms = schms.toString()
      }
      obj = {
        conId: this.tgtId.toString(),
        schemas: schms,
        option: parseInt(this.selectedDuplicate),
      }
    }
    //this.spinner=false
    this.performanceservices.GetDuplicateIndexData(obj).subscribe((data: any) => {
      this.duplicateIndexData = data;
      this.Exespinner = false;
    })

    //console.log(this.perSchema)
  }
  selectedOption:string=""
selectOption(value:any)
{
this.selectedOption=value
if (value == "2") {
  this.hidedate = true;
  this.data = false;
}
else {
  this.hidedate = false;
  this.data = true;
}
}
/*--- GetPerfIndexMonitorDate  ---*/
GetPerfIndexMonitorData() {

  this.Monitdate = true;
  var obj = {}
  if (this.selectedOption == "2") {
  let fromDateValue = (<HTMLInputElement>(document.getElementById('selectedFromDate'))).value
  let toDateValue = (<HTMLInputElement>(document.getElementById('selectedToDate'))).value

  // Format the date if necessary
  let formattedFromDate = this.formatDate(fromDateValue);
  let formattedToDate = this.formatDate(toDateValue);
  
   obj = {
      conId: this.tgtId,
      fromDate: new Date(formattedFromDate),
      toDate: new Date(formattedToDate),
      operation:this.selectedOption
  };
  }
  else{
    obj = {
      conId: this.tgtId,
      operation:this.selectedOption
  };
  }

  this.Monspinner = true;
  


  this.performanceservices.GetPerfIndexMonitorData(obj).subscribe((data: any) => {
      this.indexMonitor = data;
      this.Monspinner = false;
  });
}


formatDate(date: string): string{
  let m = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
    , o = new Date(date)
    , S = o.getDate()
    , x = m[o.getMonth()]
    , u = o.getFullYear();

    if(S<10)
      {
        S=parseInt("0"+S)
      }
  return `${u}-${x}-${S}`
}


  /*--- Schema   ---*/
  GetperfSchemas() {
    const obj = {
      conId: this.tgtId,
    }
    this.performanceservices.GetperfSchemas(obj).subscribe((data: any) => {
      this.perSchema = data;
    })
    // var temp: any = []
    // if (this.value.toString() == "ALL") {
    //   this.selectedschemas= ""
    //   this.schemaList.filter((item: any) => {
    //     temp.push("'" + item.schemaName + "'")
    //   })
    //   this.selectedschemas = temp.toString()
    // }
    // else {
    //   this.value.filter((item: any) => {
    //     temp.push("'" + item + "'")
    //   })
    //   this.selectedschemas = temp.toString()
    // }
  }

  /*--- SelectContype   ---*/
  selectContype(value: string) {
    this.contype = value;
    if (value == "0") {
      this.hidedata = true;
      this.data = false;
    }
    else {
      this.hidedata = false;
      this.data = true;
    }
  }

  /*--- Get Category List   ---*/
  categoryList: any = [
    { option: 'FTS/SEQUENTIAL SCAN', values: 'FTS' },
    { option: 'LONG RUNNING QUERIES', values: 'LRQ' },
    { option: 'HIGH CPU ', values: 'High_CPU' },
    { option: 'BLOATING DETAILS', values: 'Bloating_Details' },
    { option: 'VACUUM ANALYZE DETAILS', values: 'Vaccum_Analyze_Details' },
    { option: 'DUPLICATE INDEX VALIDATION', values: 'DUPLICATE INDEX VALIDATION' },
    { option: 'DATABASE INDEX MONITORING', values: 'DATABASE INDEX MONITORING' },
    { option: 'LOG EXPLAIN PLAN', values: 'LOG EXPLAIN PLAN' },
    { option: 'TABLE PARTITION STRATEGY', values: 'TABLE PARTITION STRATEGY' }
  ]

  /*--- SelectCategory ---*/
  sourceHide: boolean = false;
  selectCategory(value: string) {
    this.selectedCategory = value;
    if (value == "FTS") {
      this.fileName = "FTS";
      this.sourceHide = true
    }
    else {
      this.sourceHide = false
    }
    if (value == "DUPLICATE INDEX VALIDATION") {
      this.hideDrpdwn = true
      this.sourceHide = true
    }
    else {
      this.hideDrpdwn = false
      this.sourceHide = false
    }
    if (value == "DATABASE INDEX MONITORING") {
      this.hideMon = true
      this.Monitor = true
    }
    else {
      this.hideMon = false
      this.Monitor = false
    }
    if (value == "LOG EXPLAIN PLAN") {
      this.hideLog = true
    }
    else {
      this.hideLog = false
    }
    if (value == "TABLE PARTITION STRATEGY") {
      this.hideTable = true
    }
    else
    {
      this.hideTable = false
    }
    if (value == "LRQ") {
      this.hideQuery = true
    }
    else
    {
      this.hideQuery = false
    }
  }

  /*--- Get Operation List   ---*/
  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
  }

  /*--- OpenPopup   ---*/
  openPopup() {
    this.schemaName = []
    this.selectedschemas = ''
    this.execProjectForm.reset();
  }

  /*--- GetReqTableData   ---*/
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.performanceservices.GetReqData(obj)?.subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.ConsList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_name == "Storage_Objects"
        })
      }
    })
  }

  /*--- OnFileSelected  ---*/
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
  }

  /*--- GetDocuments ---*/
  getDocuments() {
    const path = "projectdoc/"
    // this.assesment.getFiles(path).subscribe((data: documentsList) => {
    //   this.projectDocuments = data
    //   this.projectDocumentFilter = data
    // })
  }

  /*--- UploadFile   ---*/
  uploadFile() {
    //console.log("uploaded")
    this.uploadfileSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', "projectdoc");
    // this.assesment.uploadDocuments(formData).subscribe(
    //   (response:any) => {
    //     this.uploadfileSpin = false
    //     this.fileAdd = falseq
    //     this.getDocuments();
    //     this.toast.success(response.message)
    //     $('#demo').offcanvas('hide');
    //   }
    // )
  }
tgtConId:string=""
  /*--- GetSchemasList   ---*/
  getSchemasList(ConnectionId: any) {
    this.tgtConId=ConnectionId
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId
    }
    this.performanceservices.SchemaListSelect(obj).subscribe((data: any) => {
      this.schemaList = data['Table1'];
    })
  }
  ftsFiles: any

  fetchFiles() {
    var path = 'PRJ' + this.projectId + 'SRC/Performance_Tuning/Full_Table_Scan/Reference_Files/'
    this.performanceservices.selectedfileName(path).subscribe((data: any) => {
      this.ftsFiles = data
    })
  }
  fileList: any
  selectedfileName(value: any) {
    this.fileList = value
  }

  /*--- SelectFileType   ---*/
  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;
  }

  /*--- applyFilter ---*/
  applyFilter(filterValue: string) {
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
    this.projectDocuments = this.projectDocumentFilter.filter((el: any) => { return el.fileName.includes(filterValue) })
  }

  /*--- GetConsList ---*/
  GetConsList() {
    this.performanceservices.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.getreqTableData()
      this.ref_spin = false
    })
  }

  /*--- selectDupliacte ---*/
  selectDuplicate(value: string) {
    this.selectedDuplicate = value
    if (value == "1") {
      this.hideD = true
    }
    else {
      this.hideD = false;
    }
  }

 
  ExecututionFiles: any
  datachange4: any
  plog: any
  iterationselected: any
  filterExecutionReports() {
    var path = 'PRJ' + this.projectId + 'SRC' + '/' + 'Performance_Tuning' + '/' + 'Full_Table_Scan' + '/' + 'Reports'
    this.performanceservices.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
      //console.log(this.ExecututionFiles)
    })
  }

  /*--- Download file ---*/
  fileResponse: any;
  downloadFiles: any;
  downloadFile(title: any) {
    this.performanceservices.downloadFiles(title.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      //console.log(blob)
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = title.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
    })
  }

  /*--- Delete Project Document   ---*/
  deleteFile: any;
  fileStatus: any;
  deleteFiles(path: string) {
    this.performanceservices.deleteFile(path).subscribe({
      next: (data: deleteFile) => {
        //console.log(data)
        this.fileStatus = data.message
        this.getDocuments();
        this.toast.success(this.fileStatus)
      },
      error: (error: { message: Content | undefined; }) => {
        this.toast.error(error.message)
      },
    });
  }

  /*--- exportExcelAnalyze ---*/
  fileNamee = 'DuplicateIndexes.xlsx';
  testingg: any = []
  excelSpinn: any = false;
  exportexcelAnalyze(): void {
    this.testingg = []
    this.excelSpinn = true
    var test = this.duplicateIndexData
    for (var el of test) {
      var newEle: any = {};
      newEle.Schemaname = el.schemaname;
      newEle.Tablename = el.tablename;
      //newEle.IndexColumns=el.indexColumns;
      newEle.Indexname = el.indexname;
      newEle.IndexType = el.indexType;
      newEle.DropScript = el.dropScripts;
      this.testingg.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testingg);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileNamee);
  }

  /*--- exportExcelMonitor ---*/
  filesName = 'NewlyCreatedIndexes.xlsx';
  exportexcelMonitor(): void {
    this.testing = []
    this.excelSpinn = true
    var test = this.indexMonitor
    for (var el of test) {
      var newEle: any = {};
      newEle.Type = el.type;
      newEle.SchemaName = el.schemaname;
      //newEle.IndexColumns=el.indexColumns;
      newEle.TableName = el.tablename;
      newEle.IndexName = el.indexname;
      newEle.IndexDefinition = el.indexdefinition;
      newEle.Logdate = el.logdate;
      this.testing.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.filesName);
  }

  GetTablePartition() {
    const obj = {
      Conid:this.tgtId,
    }  
    this.performanceservices.GetTablePartition(obj).subscribe((data: any) => {
    this.PartitonTableData = data;  
    })
  }

  /*--- exportExcelTablePartition ---*/
  fileNames = 'TablePartion.xlsx';
  exportexcelTablePartition(): void {
    this.testing = []
    this.excelSpinn = true
    var test = this.PartitonTableData
    for (var el of test) {
      var newEle: any = {};
      newEle.Type = el.procudurename;
      newEle.SchemaName = el.rowcount;
      //newEle.IndexColumns=el.indexColumns;
      newEle.TableName = el.tablename;
      newEle.IndexName = el.tablesize;
      newEle.IndexDefinition = el.columndetails;
      this.testing.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.filesName);
  }
  /*--- Select Log ---*/
  selectLog(value:string){
    this.selectedLog = value
    if(value == "0")
      {
        this.hideS= true;
      }
      else {
        this.hideS= false;
      }
      if(value == "1")
        {
          this.hideL= true;
        }
        else {
          this.hideL= false;
        }
  }

  

  Showlogsdata:any
  SubmitShowLogs() {
      var stmt=(<HTMLInputElement>document.getElementById("sch_name")).value;
      var stmts=(<HTMLInputElement>document.getElementById("Prodc_name")).value;
      let DateValue = this.perfForm.get('date')?.value ?? '';
      let formattedFromDate = this.formateDate(DateValue);
     const obj = {
      Conid:this.tgtId,
    //   projectId: this.projectId.toString(),
      
    //   tgtId: this.tgtId,
    schema:"\""+stmt+"\"",
    procedure:"\""+stmts+"\"",
    Date:formattedFromDate,
     }
    this.performanceservices.SubmitShowLogs(obj).subscribe((data: any) => {
      this.Showlogsdata = data;
   
    })
  }
  formateDate(date: string): string{
    let m = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
      , o = new Date()
      , S = o.getDate()
      , x = m[o.getMonth()]
      , u = o.getFullYear();
    return `${u}-${x}-${S}`
  }


  /*--- GetPerfIndexMonitorDate  ---*/
  getLongRunningQueries() {

 

 

    
  let fromDateValue = (<HTMLInputElement>(document.getElementById('selectedFromDate'))).value
  let toDateValue = (<HTMLInputElement>(document.getElementById('selectedToDate'))).value

  // Format the date if necessary

  let formattedFromDate = this.formatedDate(fromDateValue);
  let formattedToDate = this.formatedDate(toDateValue);
  
  const obj = {
      conId: this.tgtId,
      fromDate: new Date(formattedFromDate),
      toDate: new Date(formattedToDate),
  };
  
 
  this.performanceservices.GetPerfIndexMonitorData(obj).subscribe((data: any) => {
      this.indexMonitor = data;
      this.Monspinner = false;
  });
}


formatedDate(date: string): string{
  let m = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
    , o = new Date(date)
    , S = o.getDate()
    , x = m[o.getMonth()]
    , u = o.getFullYear();

    if(S<10)
      {
        S=parseInt("0"+S)
      }
  return `${u}-${x}-${S}`
}
  
}

import { Component } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from "../../../../shared/components/spinner/spinner.component";
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { PaginationService } from 'ngx-pagination';
declare let $: any;

@Component({
  selector: 'app-executions',
  standalone: true,
  templateUrl: './executions.component.html',
  styles: ``,
  imports: [NgxPaginationModule, SearchFilterPipe, CommonModule, FormsModule, ReactiveFormsModule, SpinnerComponent]
})
export class ExecutionsComponent {
  ExecutionForm: any
  userData: any = [];
  p: number = 1;
  datachange: any;
  pi: number = 10;
  piv: number = 10;

  grid_active: boolean = false;
  not_grid: boolean = false;

  p1: number = 1;
  p1v: number = 1;
  p2: number = 1;
  pi1: number = 10;
  pi3: number = 10;
  piB: number = 10;
  page1: number = 1;
  page2: number = 1;
  datachange1: any;
  datachangev: any;
  not_grid1: boolean = true;
  grid_active1: boolean = true;

  pi2: number = 10;
  page3: number = 1;
  datachange2: any;
  not_grid2: boolean = true;
  grid_active2: boolean = true;


  dagForm: FormGroup = this.formBuilder.group({
    Dag: ['', [Validators.required]],
    operations: ['', [Validators.required]],
    type: ['']
  })

  getSpin: boolean = false;
  projectId: any;

  getDagSpin: boolean = false;
  serverResponse: any

  dagTriggered: boolean = true;
  scheduleSelect: boolean = false
  operations: any = [
    { option: "Initial load for GG", value: "Initial_load_for_GG" },
    { option: "Initial Data Load", value: "Initial_Data_Load" },
    { option: "Partition Data Load", value: "Partition_Initial_Data_Load" },
    { option: "Catch Up", value: "Incremental_Data_Load" },
    { option: "CDC Load", value: "CDC_Load" },
    { option: "Data Compare", value: "Datacompare" },
    { option: "CDC For Higher Volumes", value: "Cdc_config" },
    { option: "Reverse CDC", value: "Reverse_CDC" }
  ]

  initTypes: any = [{ option: "Normal", value: "default" },
  { option: "High Volume", value: "high_volume" }]
  migtypeid: any
  searchText1: string = ''


  pageName: string = ''
  constructor(private paginationService: PaginationService, private titleService: Title, private route: ActivatedRoute, private toast: HotToastService, private datamigration: DataMigrationService, private formBuilder: FormBuilder, private project: DataMigrationService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getConfigMenu()
    this.fetchConfigFiles()
    if (this.migtypeid == "41") {
      this.fetchvalidationFiles("L")
    }
    this.ExecutionForm = this.formBuilder.group({
      operation: ['', [Validators.required]],
      config: ['', [Validators.required]],

    })
    this.GetConsList()
    //this.getDags()
    this.dagTriggered = false
    this.NoData = false
    this.GetDagRes("refresh")
    // this.getDags();
  }

  get forms(): any {
    return this.ExecutionForm.controls;
  }

  selectedOperation: any
  initialselected: boolean = false
  selectOperation(value: any) {

    // Reset Dag list in Logs
    this.dagFolders = []
    this.taskFolders = []
    this.logFiles = []

    this.selectedOperation = value
    if (this.selectedOperation == "Initial_Data_Load") {
      this.initialselected = true
    }
    else {
      this.initialselected = false
    }
    var type = ""
    if (value == 'Initial_Data_Load') {
      type = "init_"
    }
    else if (value == 'CDC_Data_Load') {
      type = "cdc_"
    }
    else if (value == 'Index_Data_Load') {
      type = "index_"
    }
    else if (value == "E2E_Data_Load") {
      type = "e2e"
    }
    else if (value == "Catchup") {
      type = "inc_"
    }

    let obj = {
      configtype: type,
      filePath: "Config_Files/Data_Migration"
    }
    this.datamigration.GetGGPDConfigFiles(obj).subscribe((data: any) => {
      this.filteredConfigfiles = data
    })

  }
  initType: any
  selecttype(value: any) {
    this.initType = value
  }
  pageNumber1: number = 1
  onKey() {
    var sercachbar = (<HTMLInputElement>document.getElementById("searchdag")).value
    this.p1 = 1;
    this.page2 = 1;
    this.p2 = 1;
    this.page3 = 1;
    this.p = 1;
    this.pageNumber1 = 1;
    //console.log(sercachbar, "bar")
    if (sercachbar.length > 0) {
      this.isDisableAllCheck = true
    }
    else {
      this.isDisableAllCheck = false
    }
  }

  configFiles: any = []
  DCFiles: any
  fetchConfigFiles() {
    const paths = ['Config_Files/Data_Migration', 'Config_Files/CDC', 'Config_Files/Data_Compare']
    paths.forEach((el: string) => {
      this.datamigration.GetFilesFromExpath(el).subscribe((data: any) => {
        el == 'Config_Files/Data_Compare' ? this.DCFiles = data : data.filter((al: any) => { this.configFiles.push(al) })
      })
    })
  }
  filteredConfigfiles: any
  filterConfigFiles() {
    var abc = []
    this.filteredConfigfiles = []
    // if (this.selectedOperation == "Initial_Data_Load") {
    //   abc = this.configFiles.filter((itemk: any) => {
    //     return itemk.fileName.includes("init_")
    //   })
    // }
    // else
    if (this.selectedOperation == "Incremental_Data_Load") {
      abc = this.configFiles.filter((item: any) => {
        return item.fileName.includes("catch_")
      })
    }
    else if (this.selectedOperation == "CDC_Load") {
      abc = this.configFiles.filter((itemy: any) => {
        return itemy.fileName.includes("cdc_")
      })
    }
    // else if (this.selectedOperation == "GG_Initial_Data_Load") {
    //   abc = this.configFiles.filter((itemy: any) => {
    //     return itemy.fileName.includes("initgg_")
    //   })
    // }
    // else if (this.selectedOperation == "Partition_Initial_Data_Load") {
    //   abc = this.configFiles.filter((itemy: any) => {
    //     return itemy.fileName.includes("initPD_")
    //   })
    // }
    else if (this.selectedOperation == "Datacompare") {
      abc = this.DCFiles.filter((itemy: any) => {
        return itemy.fileName.includes("dc_")
      })
    }
    else if (this.selectedOperation == "Reverse_CDC") {
      abc = this.configFiles.filter((itemy: any) => {
        return itemy.fileName.includes("rev_")
      })
    }
    else {
      abc = this.configFiles.filter((itemz: any) => {
        return itemz.fileName.includes("cdchv_")
      })
    }

    this.filteredConfigfiles = abc
  }
  srcId: any
  getDb(value: any) {
    this.srcId = value
    //console.log(value)
  }
  dagsInfo: any
  ref_spin: boolean = false
  isDisableAllCheck: boolean = false
  getDags() {
    this.dagsInfo = []
    this.datamigration.getDags().subscribe((data: any) => {
      this.dagsInfo = data
      this.dagsInfo.filter((item: any) => { return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true })
    })
  }
  getDagFormValue(value: any) {
    //console.log(value)
  }

  dagSelected: boolean = false
  selectedDag: any
  selectedDagName: string = ''
  cdcSelected: boolean = false
  selectDag(value: any) {
    this.dagSelected = true
    this.selectedDag = value
    this.selectedDagName = value.split('/').pop()
    this.selectedDagName == 'CDC_AF_DEMO_504_PM.xlsx' ? this.cdcSelected = true : this.cdcSelected = false;
    //console.log(value.split('/').pop())
  }
  srcCon: any
  SelSrcConn(value: any) {
    this.srcCon = value
  }
  trigger_spin: boolean = false;
  scnValue: any

  executeSpin: boolean = false
  getSCN() {
    this.executeSpin = true
    if (this.migtypeid != "20") {
      this.triggerDag()
    }
    else {
      if (this.srcCon == "" || this.selectedOperation == "Reverse_CDC") {
        this.allIPs = this.allIPs.filter((itemy: any) => {
          return itemy.isSelected == true
        })
        this.triggerDag()
      } else {
        this.datamigration.getScnNumber(this.srcCon).subscribe((data: any) => {
          this.executeSpin = false
          if (data != undefined) {
            this.scnValue = data.scn
            if (this.scnValue != "") {
              this.allIPs = this.allIPs.filter((itemk: any) => {
                return itemk.isSelected == true
              })
              //console.log(this.allIPs)
              $('#updateOrgModal').offcanvas('hide');
              this.triggerDag()
            }
          }
          else {
            $('#updateOrgModal').offcanvas('hide');
            this.toast.error("Failed TO get SCN Number")
          }
        })
      }
    }
  }
  dagRes: any
  dagId: any;
  getRunSpin1: boolean = false;
  triggerDag() {
    this.trigger_spin = true
    const obj = {
      dagList: this.allIPs,
      scn: this.scnValue,
      type: this.initType
    }
    this.datamigration.TriggerMultiDagsWithCDC(obj).subscribe((data: any) => {
      this.dagRes = data;
      this.trigger_spin = false
      this.allIPs = []
      this.showCheckStatus = false
      this.dagsInfo.filter((item: any) => {
        if (item.isSelected == true) {
          item.isSelected = false
        }
      })
      $('#updateOrgModal').offcanvas('hide');
      this.toast.success(data.message)
    },
      error => {
        this.trigger_spin = false
        $('#updateOrgModal').offcanvas('hide');
        this.toast.error('Dag Trigger Failed ');
      })
  }

  CdcTriggerDag() {
    const TriggerDagIPS = this.allIPs.map((data: any) => {
      const smObj = {
        'dag_id': data,
        'isCDC': true
      }
      return smObj
    });
    let obj = {
      dagList: TriggerDagIPS,
      scn: ''
    }
    this.datamigration.TriggerMultiDagsWithCDC(obj).subscribe((data: any) => {
      this.dagRes = data;
      this.toast.success(data.message)
    })
    //console.log(obj)
  }

  dagStatus: any
  GetDagRes(value: any) {
    this.dagTriggered = (localStorage.getItem("DagExecuted") != "false")
    this.ref_spin = true
    this.dagId = localStorage.getItem("Dag_Id")
    const runId = localStorage.getItem("Run_Id")
    if (this.dagId != null && runId != null) {
      const obj = {
        dag_id: this.dagId,
        dag_run_id: runId
      }
      this.datamigration.getDagStatus(obj).subscribe((data: any) => {
        this.dagStatus = data.state;
        this.ref_spin = false
      })
    }
    else {
      this.ref_spin = false
      if (value != "refresh") {
        this.toast.error("Plaese Execute a Dag");
      }
    }
  }

  masterSelected: boolean = false;
  getIPfromCheck: any;
  isCheckBoxSel: boolean = false;
  showCheckStatus: boolean = false
  allIPs: any = []
  checkboxselect($event: any, value: any): void {
    //console.log(value)
    this.getIPfromCheck = value;
    if ($event.target.checked) {
      this.isCheckBoxSel = true
      this.allIPs.length === null ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
      //console.log(this.allIPs.length)
      this.showCheckStatus = false
      const abc = this.dagsInfo.filter((item: any) => {
        return item.dag_id == value.toString()
      })
      //console.log(abc)

      var def = this.allIPs.filter((itemz: any) => {
        return itemz.dag_id == value.toString()
      })
      if (def.length == 0) {
        if (abc[0].isSelected == false) {
          abc[0].isSelected = true
        }
        this.allIPs.push(abc[0])
      }
      else {
        this.allIPs.filter((itema: any) => {
          if (itema.dag_id == value.toString()) {
            itema.isSelected = true
          }
        })
      }
      var check = this.allIPs.filter((itemz: any) => {
        return itemz.isSelected == true
      })
      if (check.length == this.dagsInfo.length) {
        this.showCheckStatus = true
      }
      //console.log(this.allIPs)
    } else {
      //let dag_index = this.dagsInfo.indexOf(value);
      // let i = 0
      this.dagsInfo.filter((item: any) => {
        if (item.dag_id == value) {
          item.isSelected = false
        }
        // i++
      })
      // let index = this.allIPs.indexOf(value);
      // this.dagsInfo[dag_index].isSelected = false
      // let y=0
      this.allIPs.filter((item1: any) => {
        if (item1.dag_id == value) {
          item1.isSelected = false
        }

        // y++
      })
      var check1 = this.allIPs.filter((itemz: any) => {
        return itemz.isSelected == true
      })
      if (check1.length == this.dagsInfo.length) {
        this.showCheckStatus = true
      }
      //this.allIPs[index].isSelected = false
      //const rem = this.allIPs.splice(index, 1);
      //console.log(rem)
      //console.log(this.allIPs)
      this.showCheckStatus = false
      this.allIPs.length === 0 ? this.isCheckBoxSel = false : this.isCheckBoxSel = true
    }
  }
  visibleDags: any[] = [];
  updateVisibleDags() {
    const filtered = this.dagsInfo.filter((dag: any) =>
      dag.dag_id.toLowerCase().includes(this.searchText1.toLowerCase())
    );
    const pageSize = 10;
    const start = (this.p - 1) * pageSize;
    const end = start + pageSize;
    this.visibleDags = filtered.slice(start, end);
    this.showCheckStatus = this.visibleDags.every(
      (item: any) => item.isSelected && item.isDagAvailable !== false
    );
  }

  selectAll($event: any) {
    this.updateVisibleDags(); // update current page slice

    this.showCheckStatus = $event.target.checked;
    if ($event.target.checked) {
      this.visibleDags.forEach((item: any) => {
        if (item.isDagAvailable !== false) {
          item.isSelected = true;
          const exists = this.allIPs.find((x: any) => x.dag_id === item.dag_id);
          if (!exists) {
            this.allIPs.push(item);
          }
        }
      });
      this.isCheckBoxSel = true;
    } else {
      this.visibleDags.forEach((item: any) => {
        item.isSelected = false;
        const index = this.allIPs.findIndex((x: any) => x.dag_id === item.dag_id);
        if (index !== -1) {
          this.allIPs.splice(index, 1);
        }
      });
      this.isCheckBoxSel = this.allIPs.length > 0;
    }
  }

  dagFolders: any
  getArflowDagFolders(path: any) {
    this.datamigration.GetAirflowDirs('dag_id=' + path + '/').subscribe((data: any) => {
      this.dagFolders = data
      //console.log(this.dagFolders)
    })

  }
  taskFolders: any
  getArflowTaskFolders(path: any) {
    this.datamigration.GetAirflowDirs(path).subscribe((data: any) => {
      this.taskFolders = data
      //console.log(this.dagFolders)
    })
  }
  logFiles: any
  getAirflowfiles(path: any) {
    this.selectedairflowPath = path;
    this.datamigration.GetAirflowLogs(path).subscribe((data: any) => {
      this.logFiles = data
      //console.log(this.dagFolders)
    })
  }
  selectedairflowPath: string = '';
  getAirflowfiles1() {
    this.getRunSpin1 = true;
    if (this.selectedairflowPath) {
      this.getAirflowfiles(this.selectedairflowPath);
    }
  }
  fileResponse: any
  spin_dwld: any;
  downloadFile(fileInfo: any) {
    this.datamigration.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(blob);
      document.body.appendChild(downloadLink);
      downloadLink.download = fileInfo.fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  ConsList: any = [];
  tgtList: any
  GetConsList() {
    this.datamigration.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }
  dagData: any
  selectedPath: any
  getConfigDags(path: any) {
    this.showCheckStatus = false
    this.dagsInfo = []
    this.allIPs = []
    if (path.includes("/mnt/extra/")) {
      path = path.replace("/mnt/extra/", "")
    }
    this.selectedPath = path
    //  path="C:\\Users\\<USER>\\Downloads\\catchup_config.xlsx"
    this.p = 1
    // if (this.selectedOperation == 'Partition_Initial_Data_Load' || this.selectedOperation == 'GG_Initial_Data_Load' || this.selectedOperation == "Initial_Data_Load" || this.selectedOperation == 'E2E_Data_Load') {
    this.datamigration.getggpdDagsNew(path).subscribe((data: any) => {
      this.dagsInfo = data.dagList
      this.srcCon = data.sourceConnectionId
      this.dagsInfo.filter((item: any) => {
        item.isSelected = false
        return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
      })
    })
    // } else {
    //   this.datamigration.getExcelDags(path).subscribe((data: any) => {
    //     this.dagsInfo = data.dagList
    //     this.srcCon = data.sourceConnectionId
    //     this.dagsInfo.filter((item: any) => {
    //       item.isSelected = false
    //       return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
    //     })
    //   })
    // }
  }
  reff_spin: boolean = false
  refreshdags() {
    this.dagsInfo = []
    this.allIPs = []
    this.p = 1
    this.reff_spin = true
    this.datamigration.getggpdDagsNew(this.selectedPath).subscribe((data: any) => {
      this.dagsInfo = data.dagList
      this.reff_spin = false
      this.srcCon = data.sourceConnectionId
      this.dagsInfo.filter((item: any) => {
        item.isSelected = false
        return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
      })
    })
  }
  dagruns: any
  seleDag: any
  selectRunDags(dagid: any) {
    //console.log(dagid)
    this.seleDag = dagid
    this.datamigration.getDagRuns(dagid).subscribe((data: any) => {
      this.dagruns = data
    })
  }
  status: any
  NoData: boolean = true
  runId: any
  getStatus(value: any) {
    this.runId = value
    var stat = this.dagruns.filter((item: any) => {
      return item.dag_id == value
    })
    this.status = ""
    if (stat.length != 0) {
      this.status = stat[0].status
      if (this.status == "") {
        this.NoData = false
      }
      else {
        this.NoData = true
      }
    }

  }
  dagstatus: any
  modifyDag() {
    let obj = {
      dagId: this.seleDag,
      dag_runId: this.runId
    }
    this.NoData = false
    this.datamigration.ModifyDagRun(obj).subscribe((data: any) => {
      this.dagstatus = data.message
      this.NoData = false
      this.toast.success(this.dagstatus)
    })
  }
  dagInfo2: any
  getConfigDags1(path: any) {
    this.p = 1
    this.datamigration.getExcelDags(path).subscribe((data: any) => {
      var resp = data.dagList
      this.dagInfo2 = resp.filter((item: any) => {
        item.isSelected = false
        return item.isDagAvailable == true
      })
    })
  }
  count: any = []
  openPopup() {
    this.count = this.allIPs.filter((item: any) => {
      return item.isSelected == true
    })
  }
  scheduleResponse: any
  schedule_spin: boolean = false
  scheduleDags() {
    this.schedule_spin = true
    var datestring = (<HTMLInputElement>document.getElementById("schDate")).value
    var dags: any = []
    let obj1: any = {}
    this.allIPs.forEach((item: any) => {
      obj1 = {}
      if (item.isSelected == true) {
        obj1['dag_id'] = item.dag_id
        dags.push(obj1);
      }
    })
    var date = new Date(datestring)
    var utc = date.toUTCString()
    var utcdate = new Date(utc);
    const year = utcdate.getUTCFullYear();
    const month = this.padZero(date.getUTCMonth() + 1); // Months are zero-based
    const day = this.padZero(date.getUTCDate());
    const hours = this.padZero(date.getUTCHours());
    const minutes = this.padZero(date.getUTCMinutes());
    const seconds = this.padZero(date.getUTCSeconds());

    const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
    let obj = {
      dagsListreqs: dags,
      scheduleDate: formattedDate
    }
    //console.log(obj);
    this.datamigration.ScheduleDags(obj).subscribe((data: any) => {
      this.schedule_spin = false
      this.scheduleResponse = data.message
      if (data.message.includes("Dags scheduled")) {
        this.toast.success(this.scheduleResponse)
      } else {
        this.toast.error(this.scheduleResponse)
      }
      (<HTMLInputElement>document.getElementById("schDate")).value = "";
      $('#updateOrgModal').offcanvas('hide');
    })
  }
  padZero(num: number): string {
    return num < 10 ? '0' + num : num.toString();
  }
  popup(value: any) {
    if (value == 0) {
      this.scheduleSelect = true
    }
    else {
      this.scheduleSelect = false
    }
  }
  configData: any = []
  getConfigMenu() {
    this.configData = []
    this.datamigration.GetConfigMenu(this.migtypeid).subscribe((data: any) => {
      this.configData = data['Table1']
      if (this.migtypeid == "31") {
        this.configData.filter((item: any) => {
          return item.configtype != "Data Compare" && item.configtype != "Catch Up" && item.configtype != "Reverse CDC"
        })
      }
    })
  }
  validationFiles: any = []
  spin_valid: boolean = false
  fetchvalidationFiles(value: any) {
    if (value == "R") {
      this.spin_valid = true
    }
    var path = 'Validation_Reports/'
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.validationFiles = data?.filter((item: any) => {
        return item.fileName.startsWith('postdm_') && item.fileName.endsWith('.csv')
      })
      this.spin_valid = false
    })

  }
  fileSrc: any
  confFiles: any = []
  fetchSourceConfigFiles(conn: any) {
    this.ref_spin = true
    this.fileSrc = conn
    const path = conn + "/Config_Files/Data_Migration";
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.filteredConfigfiles = data
      this.ref_spin = false
    })
  }
  fetchTargetConfigFiles(conn: any) {
    this.ref_spin = true
    const path = this.fileSrc + "/" + conn + "/Config_Files/Data_Migration";
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.filteredConfigfiles = data
      this.ref_spin = false
    })
  }

  srcConId: any
  tgtConId1: any
  selectSrc(value: any) {
    this.srcConId = value
  }
  tgtPath: any
  foldersList: any = []
  selectTgt(value: any) {
    this.tgtConId1 = value
    this.tgtPath = "/mnt/extra/" + this.srcConId + "/" + this.tgtConId1 + "/Dag_Validation_Reports"
    this.datamigration.GetFolders(this.tgtPath).subscribe((data: any) => {
      data.forEach((item: any) => {
        var obj = {
          folderpath: item,
          folderName: item.split(/[/\\]/).pop()
        }
        this.foldersList.push(obj);
      })
    })
  }
  searchText: any
  spinDownload: boolean = false
  DagFolders: any = []
  fetchDagFolders(path: any) {
    this.datamigration.GetFolders(path).subscribe((data: any) => {
      if (data.length > 0) {
        data.forEach((item: any) => {
          var obj = {
            folderpath: item,
            folderName: item.split(/[/\\]/).pop()
          }
          this.DagFolders.push(obj);
        })
      }
      console.log(this.DagFolders)
    })
  }
  NewValidationFiles: any;
  selectedDagPath: string = '';
  fetchNewValidationFiles(path: any) {
    // this.tgtPath = this.tgtPath + "/" + path
    this.selectedDagPath = path;
    path = path.replace("/mnt/extra", "")
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.NewValidationFiles = data
    })
  }
  fetchNewValidationFiles1() {
    if (this.selectedDagPath) {
      this.fetchNewValidationFiles(this.selectedDagPath);
    }
  }
  getRunSpin2:boolean = false;
  fileStatus: any
  deleteFiles(path: string) {
    this.datamigration.deleteFile(path).subscribe({
      next: (data: any) => {
        //console.log(data)
        this.fileStatus = data.message
        //this.fetchAirflowFiles();
        this.toast.success(this.fileStatus)
      },
      error: (error) => {
        this.toast.error(error.message)
      },
    });
  }

}


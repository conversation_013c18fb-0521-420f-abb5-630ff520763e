<section class="login-page">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12 col-lg-6 col-xl-6 col-xxl-7">
                <div class="login-form">
                    <div class="login-from-content">
                        <div class="login-logo">
                            <img src="assets/images/logo.png" alt="logo" class="d-block m-auto" />
                        </div>
                        <h2>Sign in to QMigrator</h2>
                        <p>Upload,migrate and save data</p>

                        <!--- Login Form  --->
                        <form class="form qmig-Form" [formGroup]="loginForm" (ngSubmit)="onSubmit(loginForm.value)">
                            <div class="form-group">
                                <i class="mdi mdi-at"></i>
                                <input type="text" class="form-control" placeholder="<EMAIL>" formControlName="email" />
                                <span class="mdi mdi-close" (click)="resetUser()"></span>
                                @if ( f.email.touched && f.email.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.email.errors.required) { Email is required }
                                        @if (f.email.errors.email) { Please enter valid email. }
                                    </p>                                    
                                }                            
                            </div>
                            <div class="form-group">
                                <i class="mdi mdi-key-variant"></i>
                                <input type="text" class="form-control" placeholder="License key" formControlName="key" />
                                <span class="mdi mdi-close" (click)="resetPassword()"></span>
                                @if ( f.key.touched && f.key.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.key.errors.required) { Key is required }
                                        @if (f.key.errors.minlength) { Minimum length should be 3 letters required. }
                                    </p>                                    
                                } 
                            </div>
                            <div class="form-group" [hidden]="fileExists">
                                <div class="custom-file-upload">
                                    <input class="form-control" type="file" id="formFile" formControlName="file" (change)="sendValue($event)">
                                    <div class="file-upload-mask">
                                        @if (fileName == '') { 
                                            <img src="assets/images/fileUpload.png" alt="img" />                                            
                                            <p>Drag and drop deployment file here or click add deployment file  </p>
                                            <button class="btn btn-upload"> Add File </button>
                                         }
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100"><p> {{ fileName }} </p></div>
                                    </div>                                        
                                </div>                                
                                @if ( f.file.touched && f.file.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.file.errors.required) { File is required }
                                    </p>                                    
                                }
                            </div>
                            <div>
                                <button class="btn btn-sign w-100 " [disabled]="loginForm.invalid"> Sign In @if(spinner){<app-spinner-white/>} </button>
                            </div>
                            <p class="mt-3">Don't have a account? <a href="https://qmigrator.ai/contact"> Contact admin </a> </p>
                        </form>
                    </div>
                </div>
            </div>

            <!--- Welcome image  --->
            <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-5">
                <div class="login-image">
                    <img src="assets/images/logo-white.png" alt="logo">
                    <div class="login-content">
                        <h2>Welcome to QMigrator!</h2>
                        <p>QMigrator is an AI based tool for migrating on-prem or cloud databases such as Oracle, SQL Server, DB2 to Open-source databases like PostgreSQL, MySQL or MariaDB in any cloud. Below are the top features of QMigrator to help you migrate your databases quickly.</p>
                    </div>
                    <div class="login-footer">
                        <ul>
                            <li><a href="https://qmigrator.ai" target="_blank">Marketplace</a></li>
                            <li><a href="#">License</a></li>
                            <li><a href="#">Terms of Use</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
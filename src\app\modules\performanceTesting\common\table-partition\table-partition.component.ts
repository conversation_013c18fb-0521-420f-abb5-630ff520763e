import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { PerofmanceTestingService } from '../../../../services/performanceTesting.service';
import { ActivatedRoute } from '@angular/router';
import * as XLSX from 'xlsx';
import { interval } from 'rxjs';


declare let $: any;
@Component({
  selector: 'app-table-partition',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],

  templateUrl: './table-partition.component.html',
  styles: ``
})

export class TablePartitionComponent {

  hidedata: boolean = true;
  data: boolean = false;
  projectId: string;
  ref_spin: boolean = true;
  ref_spin2: boolean = false;
  ref_spin3: boolean = false;
  PerflogstatusData: any;
  PerflogFile: any;
  tabledata: any;
  schemaName: any = [];
  selectFile: any;
  fileName: string = '';
  FileNameUpload: string = '';
  uploadfileSpin: boolean = false;
  fileAdd: boolean = false;
  projectDocuments: any;
  projectDocumentFilter: any;
  pageNumber: number = 1;
  pageNumbers: number = 1;
  p: number = 1;
  page: number = 1;
  pages: number = 1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  datachange: any;
  hideD: boolean = false
  searchText: string = '';
  exe: any;
  contype: string = ""
  contypes: string = ""
  dup: string = ""
  mon: string = ""
  tgtId: any;
  filenameup: any;
  tgtIds: any;
  tgtValue: string = ''
  fileValue: string = ''
  z: any;
  i: any;
  selectedConname: any
  conName: any
  conId: any;
  ConsList: any;
  tgtList: any
  selectedCategory: string = ""
  hideDrpdwn: boolean = false
  value: any;
  selectedDuplicate: string = "";
  selectedMonitor: string = "";
  P2: number = 1;
  P3: number = 1;
  perSchema: any;
  selectedItems = [];
  selectedObjItems1 = [];
  perSchema1: any;
  exe1: any;
  TableData: boolean = false;
  analyzesearchlist: any;
  Exespinner: boolean = false;
  Monspinner: boolean = false;
  excelSpin: boolean = false;
  hideMon: boolean = false;
  updateBtn: boolean = false;
  pageName: string = ''
  indexMonitor: any;
  Monitor: boolean = false;
  testing: any;
  hideLog: boolean = false;
  uploadForm: any;
  Executionform: any;
  perfForm: any;
  Logform: any;
  showForm: any;
  hideTable: boolean = false;
  showForms: boolean = false;
  Callstm: any;
  task: any;
  ParTable: any;
  ConId: any;
  stat: boolean = false;
  PartitonTableData: any;
  GetPartitonTableData: any;
  hideQuery: boolean = false;
  selectedLog: string = "";
  hideL: boolean = false;
  hideS: boolean = false;
  hidedate: boolean = false;
  filesName: string = "";
  excelSpinn: boolean = false;
  subscription: any;
  subscriptions: any;
  buttonsp: boolean = false;
  showExecuteButton: boolean = false;
  partitionForm: any;
  defaultQueries: string = `
  set maintenance_work_mem = '20GB';
  set work_mem = '1GB';
  set max_parallel_workers = '32';
`;

  fileStatus: boolean = false;

  get f(): any {
    return this.uploadForm.controls;
  }
  constructor(private toast: HotToastService,
    private performanceservices: PerofmanceTestingService,
    public formBuilder: FormBuilder,
    private route: ActivatedRoute) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
  }

  /*--- Page Title ---*/
  pageTitle: string = "Documents"
  pageIcon: string = "assets/images/documents.svg"


  ngOnInit(): void {
    this.GetConsList();
    this.getFiles();
    this.pageName = this.route.snapshot.data['name'];
    this.uploadForm = this.formBuilder.group({
      file1: ['', [Validators.required]],
    });
    this.Executionform = this.formBuilder.group({
      filenamee: ['', Validators.required],
      tgtconn: ['', Validators.required],
    });
    this.perfForm = this.formBuilder.group({
      tgtconnection: ['', Validators.required],
      connectionType: ['', Validators.required],
    });
    this.Logform = this.formBuilder.group({
      filenamee1: ['', Validators.required],
    });
    this.showForm = this.formBuilder.group({
      queries: [this.defaultQueries],
    });
    this.partitionForm = this.formBuilder.group({
      partitionType: [''],
      tgtcon: ['', Validators.required]
    });
    // this.subscription = interval(3000).subscribe(() => {
    //   this.GetPerflogFilestatus();
    //   this.GetPartitionperflog();
    // });

    this.GetPerflogFilestatus(1);
    this.GetPartitionperflog();
  }

  Refresh() {
    this.GetPartionRecomonded();
    this.GetPartitionperflog();
  }

  RefreshFileStatus() {
    var value: any = ""
    value = this.partitionForm.value.partitionType
    if (value == "fullDB") {
      this.GetPerflogFilestatus(0);
    } else {
      this.GetPerflogFilestatus(1);

    }

    this.GetPartitionperflog();
  }


  /*--- Validation ---*/
  get validate() {
    return this.perfForm.controls;
  }

  get validatee() {
    return this.Executionform.controls;
  }

  get validates() {
    return this.uploadForm.controls;
  }
  get upload() {
    return this.partitionForm.controls;
  }
  /*--- Schema   ---*/
  GetperfSchemas() {
    const obj = {
      conId: this.tgtId,
    }
    this.performanceservices.GetperfSchemas(obj).subscribe((data: any) => {
      this.perSchema = data;
    })
  }

  /*--- SelectContype   ---*/
  selectContype(value: string) {
    this.contype = value;
    if (value == "0") {
      this.hidedata = true;
      this.data = false;
    }
    else {
      this.hidedata = false;
      this.data = true;
    }
  }

  selectContypes(value: string) {
    this.contype = value;
  }





  /*--- Get Operation List   ---*/
  selTgtId(value: any) {
    this.tgtId = value;
    this.hideD = false;
    this.tgtList.filter((el: any) => { return el.filename == value ? this.tgtValue = el.conname : '' });
    this.hideD = true;
    this.GetPerflogFiles();
    this.GetPartionRecomonded();
    this.GetPartitionperflog();
  }
  selectFilenames(value: any) {
    this.filenameup = value;
    this.PerflogstatusData.filter((el: any) => { return el.filename == value ? this.fileValue = el.conname : '' });
  }

  selTgtIds(value: any) {
    this.tgtIds = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' });
    this.GetPerflogFilestatus(1);
    this.GetPartitionperflog();
  }



  /*--- GetReqTableData   ---*/
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.performanceservices.GetReqData(obj)?.subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.ConsList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_name == "Storage_Objects"
        })
      }
    })
  }

  ftsFiles: any
  /*--- GetConsList ---*/
  GetConsList() {
    this.performanceservices.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.getreqTableData()
      this.ref_spin = false
    })
  }

  /*--- selectDupliacte ---*/
  selectDuplicate(value: string) {
    this.selectedDuplicate = value
    if (value == "1") {
      this.hideD = true
    }
    else {
      this.hideD = false;
    }
  }

  GetTablePartition() {
    const obj = {
      Conid: this.tgtId,
    }
    this.performanceservices.GetTablePartition(obj).subscribe((data: any) => {
      this.GetPartitonTableData = data;
    })
  }
  PartitonTableDatas: any;
  PartitonTableDatass: boolean = false;
  GetPartionRecomonded() {
    this.PartitonTableDatass = true;
    this.performanceservices.RecomondedPartions().subscribe((data: any) => {
      this.PartitonTableData = data;
      for (const element of this.PartitonTableData) {
        if (element.tablename && element.status == 'Partition already exists') {
          var tblName = element.tablename
        }
      }
      for (const element of this.PartitonTableData) {
        if (tblName == element.tablename) {
          element.updatedStatus = 'Already Existed';
        } else {
          element.updatedStatus = element.status;
        }
      }

      this.PartitonTableDatass = false;
      //this.PartitonTableData = this.PartitonTableData.sort((a: any, b: any) => a.id.localeCompare(b.id));

    })
  }

  /*--- exportExcelTablePartition ---*/
  fileNames = 'TablePartion.xlsx';
  exportexcelTablePartition(): void {
    this.testing = []
    this.excelSpinn = true
    var test = this.PartitonTableData
    for (var el of test) {
      var newEle: any = {};
      newEle.Type = el.procudurename;
      newEle.SchemaName = el.rowcount;
      //newEle.IndexColumns=el.indexColumns;
      newEle.TableName = el.tablename;
      newEle.IndexName = el.tablesize;
      newEle.IndexDefinition = el.columndetails;
      this.testing.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.filesName);
  }

  /*--- UploadFile   ---*/

  uploadFile() {
    this.uploadfileSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', "PRJ1167SRC/Performance_Tuning/Table_Partition_Strategy/Reference_Files");
    this.performanceservices.UploadCloudFiles(formData).subscribe(
      (response: any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        // this.getDocuments();
        this.getFiles();
        this.toast.success(response.message)
        $('#demo').offcanvas('hide');
      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.toast.error('Something went wrong')
        $('#demo').offcanvas('hide');
      })
  }
  spin_process: any;
  uploadedData: any = [];
  getFiles() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Performance_Tuning/Table_Partition_Strategy/Reference_Files"
    };
    this.performanceservices.getFiles(requestObj).subscribe((data) => {
      this.uploadedData = data;
      // this.uploadedData = this.uploadedData.reverse();
    });
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.FileNameUpload = event.target.files[0].name;
  }
  commandspin: boolean = false;
  TablePartitionInsert(value: any) {
    this.fileStatus = true;
    this.commandspin = true;
    const obj = {
      task: "Table_Partition_Strategy",
      projectId: this.projectId.toString(),
      // operation:"Database",
      // option: parseInt(this.contype),
      target_connection_id: this.tgtId,
      fileName: this.removeFileExtension(value.filenamee),
      srcId: "1",
      tgtId: this.tgtIds,
    }
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.Callstm = data;
      this.toast.success(data.message);
      this.commandspin = false;
    })
  }

  commandspin1: boolean = false;
  TablePartitionInsertion() {
    this.commandspin1 = true;
    const obj = {
      task: "Table_Partition_Strategy",
      projectId: this.projectId.toString(),
      // operation:"Database",
      // option: parseInt(this.contype),
      tgtId: this.tgtId,
      fileName: null,
      srcId: "1",
      partitioncreateid: this.partionid.toString(),
    }
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.Callstm = data;
      this.toast.success(data.message);
      this.commandspin1 = false;
    })
    this.GetPartionRecomonded();
    this.GetTablePartition();
    this.GetPartitionperflog();
  }
  commandspin2: boolean = false;

  GetPerflogFiles() {
    // const obj = {
    //   Conid: this.tgtId,
    // }
    this.ref_spin2 = true;
    this.performanceservices.GetPerflogFiles().subscribe((data: any) => {
      this.PerflogFile = data;
      this.ref_spin2 = false;
    })
  }

  removeFileExtension(filename: string): string {
    // Using regular expression to remove any extension
    return filename?.replace(/\.[^/.]+$/, '');
  }
  GetPerflogFilestatus(value: any) {
    var file: any = "";
    if (value == 1) {
      file = this.removeFileExtension(this.filenameup);
    } else {
      file = "AllProcedures_";
    }
    const obj = {
      Conid: this.tgtIds,
      file_name: file,
    }
    this.ref_spin3 = true;
    this.performanceservices.GetPerflogstatus(obj).subscribe((data: any) => {
      this.PerflogstatusData = data;
      this.ref_spin3 = false;
    })
  }


  ngOnDestroy(): void {
    // Unsubscribe to prevent memory leaks
    this.subscription.unsubscribe();
  }


  TablePationLog = 'TablePationLog.xlsx';
  TablePationLogExe: any = [];
  excelspind: boolean = false;
  exportexcelTablePartion(): void {
    this.TablePationLogExe = []
    this.excelspind = true;
    var test = this.PartitonTableData
    // for (var el of test) {
    //   var newEle: any = {};
    //   newEle.Sno = el.id;
    //   newEle.datatype = el.datatype;
    //   newEle.columnname = el.columnname;
    //   newEle.tablename = el.tablename;
    //   newEle.tablesize = el.tablesize;
    //   newEle.Occurrence = el.procedurecount;
    //   newEle.rowcount = el.rowcount;
    //   newEle.status = el.status;
    //   newEle.recommendedpartitions = el.recommendedpartitions;
    //   this.TablePationLogExe.push(newEle);
    // }
    const dataArray = this.PartitonTableData.map((item: any) => Object.values(item));
    var keys = Object.keys(this.PartitonTableData[0]);
    const dataWithHeaders = [keys, ...dataArray];
    const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(dataWithHeaders);
    // const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.TablePationLogExe);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.TablePationLog);
    this.excelspind = false;

  }
  partionid: any;
  onActionClick(id: number): void {
    this.partionid = id;
    const con = this.PartitonTableData.find((item: { id: number; }) => item.id === id);
    if (con) {
      this.selectedCon = con;
      con.isClicked = false;
    }
  }

  onActionClicks(con: any): void {
    if (!con.clicked) {
      // Make sure the API call only happens once
      this.onActionClick(con.id);
    }
  }

  onKey() {
    this.pageNumber = 1;
    this.page2 = 1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3 = 1;
  }
  PerfLogPartition: any;
  GetPartitionperflog() {
    // const obj = {
    //   Conid: this.tgtId.toString(),
    // }
    this.ref_spin = true
    this.performanceservices.PerfPartitionLogstatus().subscribe((data: any) => {
      this.PerfLogPartition = data;
    })
  }

  PerfLogPartitions = 'PerfLogPartitionReport.xlsx';
  PerfLogPartitionexe: any = [];
  excelspin5: boolean = false;
  partionReportstatus(): void {
    this.PerfLogPartitionexe = []
    this.excelspin5 = true;
    var test = this.PerfLogPartition
    // for (var el of test) {
    //   var newEle: any = {};
    //   newEle.Sno = el.sno;
    //   newEle.tablename = el.tablename;
    //   newEle.createddate = el.createddate;
    //   newEle.partitioncount = el.partitioncount;
    //   newEle.beforerowcount = el.beforerowcount;
    //   newEle.afterrowcount = el.afterrowcount;
    //   newEle.partitionscripts = el.partitionscripts;
    //   newEle.Status = el.status;
    //   newEle.error = el.error;
    //   this.PerfLogPartitionexe.push(newEle);
    // }
    const dataArray = this.PerfLogPartition.map((item: any) => Object.values(item));
    var keys = Object.keys(this.PerfLogPartition[0]);
    const dataWithHeaders = [keys, ...dataArray];
    const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(dataWithHeaders);
    // const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.TablePationLogExe);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.PerfLogPartitions);
    // const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.PerfLogPartitionexe);
    // const wb: XLSX.WorkBook = XLSX.utils.book_new();
    // XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    // XLSX.writeFile(wb, this.PerfLogPartitions);
    this.excelspin5 = false;

  }
  selectedCon: any = null;
  openPopup() {
    this.buttonsp = false;
    this.showForms = false;
    this.showExecuteButton = false;
    this.selectedCon = null;
    //this.showForm.reset();
  }
  onYesClick() {
    if (this.selectedCon) {
      this.selectedCon.isClicked = true;
      this.showForms = true;
      this.showExecuteButton = true;
      this.buttonsp = true;
    }
  }

  executeQueries(value: any) {
    const obj = {
      task: "Table_Partition_Strategy",
      projectId: this.projectId.toString(),
      // operation:"Database",
      // option: parseInt(this.contype),
      tgtId: this.tgtId,
      fileName: null,
      srcId: "1",
      partitioncreateid: this.partionid.toString(),
      serverParameter: value.queries.replace(/\n/g, '').trim()
    }
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.Callstm = data;
      this.toast.success(data.message);
      this.commandspin1 = false;
      this.GetPartionRecomonded();
    })
    this.GetPartitionperflog();
    this.onYesClick();
    this.closeModal();
    this.GetPartionRecomonded();
  }

  SkipMethod() {
    this.TablePartitionInsertion();
    this.openPopup();
  }


  // Close the modal if "No" is clicked
  closeModal() {
    this.buttonsp = false;
    this.showForms = false;
    this.showExecuteButton = false;
    // this.showForm.reset();
  }

  queryListRowwise: { sno: string, tablename: string, createddate: Date, partitioncount: string, beforerowcount: string, afterrowcount: string, partitionscripts: string, status: string, error: string }[] = []; // This will hold the query list in required format
  selectedData: any[] = [];
  onClickChange(con: any) {
    this.selectedData.push(con.sno);
    this.queryListRowwise.push({
      sno: con.sno,
      tablename: con.tablename,
      beforerowcount: con.beforerowcount,
      afterrowcount: con.afterrowcount,
      partitioncount: con.partitioncount,
      createddate: con.createddate,
      partitionscripts: con.partitionscripts,
      status: con.status,
      error: con.error,
    });
    this.partionReportstatusRowLevel();
    this.queryListRowwise = [];
  }

  PerfLogPartitionsExcel = 'PerfLogPartitionReportExcel.xlsx';
  PerfLogPartitionexe1: any = [];
  excelspin6: boolean = false;
  partionReportstatusRowLevel(): void {
    this.PerfLogPartitionexe1 = []
    this.excelspin6 = true;
    var test = this.queryListRowwise
    for (var el of test) {
      var newEle: any = {};
      newEle.Sno = el.sno;
      newEle.tablename = el.tablename;
      newEle.beforerowcount = el.beforerowcount;
      newEle.afterrowcount = el.afterrowcount;
      newEle.partitioncount = el.partitioncount;
      newEle.createddate = el.createddate;
      newEle.partitionscripts = el.partitionscripts;
      newEle.Status = el.status;
      newEle.error = el.error;
      this.PerfLogPartitionexe1.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.PerfLogPartitionexe1);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.PerfLogPartitionsExcel);
    this.excelspin6 = false;

  }
  AllProcStatus: any
  AllProcStatus_spin: boolean = false
  currentDateTime: any;
  fetchCuurentDate() {
    const now = new Date();
    var formattedDate = now.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
    formattedDate = formattedDate.replace(/[\/,]/g, '_').replace(/[\/,:\s]/g, '_');
    this.currentDateTime = formattedDate
  }
  tgconid: any
  selectTgtForFullDB(value: any) {
    this.tgconid = value;
  }
  AllProcList: any = [];
  blob: any;
  excelfilename: any;
  fetchAllProcs(conid: any) {
    this.fileStatus = true;
    this.AllProcStatus_spin = true;
    this.AllProcStatus = "Fetching DB Procs...";
    this.fetchCuurentDate();
    this.performanceservices.FetchAllProcedures(conid).subscribe((data: any) => {
      this.AllProcList = data;
      this.AllProcStatus = "DB Procs Fetched...";
      setTimeout(() => { }, 2000);
      this.AllProcStatus = "";
      this.AllProcStatus = "Creating Excel File...";
      const headers = ["call statements"];
      const dataArray = this.AllProcList.map((item: any) => Object.values(item));
      const dataWithHeaders = [headers, ...dataArray];
      const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(dataWithHeaders);
      // const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.AllProcList);
      const csv = XLSX.utils.sheet_to_csv(ws)
      // const wb: XLSX.WorkBook = XLSX.utils.book_new();
      // XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
      // const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

      // Step 4: Convert buffer to Blob
      this.blob = new Blob([csv], { type: 'text/csv' });
      this.excelfilename = "AllProcedures_" + this.currentDateTime + ".csv";
      this.AllProcStatus = "Created Excel File...";
      setTimeout(() => { }, 2000);
      this.AllProcStatus = "";
      this.AllProcStatus_spin = false;
      this.uploadFileForFullDB();
    }, error => {
      this.AllProcStatus = "Failed to fetch DB Procs...";
      this.AllProcStatus_spin = false
      this.AllProcStatus = "";
      this.toast.error('Something went wrong')
    })
  }
  uploadFileForFullDB() {
    this.AllProcStatus_spin = true
    this.AllProcStatus = "Uploading Excel File...";
    var path = "PRJ1167SRC/Performance_Tuning/Table_Partition_Strategy/Reference_Files";
    const formData: FormData = new FormData();
    formData.append('file', this.blob, this.excelfilename);
    formData.append('path', path);
    this.performanceservices.UploadCloudFiles(formData).subscribe(
      (response: any) => {
        this.AllProcStatus_spin = false
        this.fileAdd = false
        this.toast.success(response.message)
        this.AllProcStatus = "Uploaded Excel File...";
        setTimeout(() => { }, 2000);
        this.AllProcStatus = "";
        this.AllProcStatus_spin = false
        this.getFiles();
        this.TablePartitionInsertForAllProc()
      },
      error => {
        this.AllProcStatus = "Failed to Upload Excel File...";
        this.AllProcStatus_spin = false
        this.AllProcStatus = "";
        this.toast.error('Something went wrong')
      })
  }
  TablePartitionInsertForAllProc() {
    this.AllProcStatus_spin = true
    this.AllProcStatus = "Executed Python..."
    const obj = {
      task: "Table_Partition_Strategy",
      projectId: this.projectId.toString(),
      fileName: this.removeFileExtension(this.excelfilename),
      srcId: "1",
      tgtId: this.tgconid,
    }
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.Callstm = data;
      this.AllProcStatus = "Completed..."
      this.toast.success(data.message);
      setTimeout(() => { }, 2000);
      this.AllProcStatus = "";
      this.AllProcStatus_spin = false
    }, error => {
      this.AllProcStatus = "Failed to Execute Python...";
      this.AllProcStatus = "";
      this.AllProcStatus_spin = false
      this.toast.error('Something went wrong')
    })
  }

  logData:any=[]
  log_spin:boolean=false
  getlogs() {
    this.fetchCuurentDate();
    this.log_spin=true
    this.performanceservices.GetPerfLogs().subscribe((data: any) => {
      this.logData = data;
      var file="Logs_"+this.currentDateTime+".csv";
      const dataArray = this.logData.map((item: any) => Object.values(item));
      var keys = Object.keys(this.logData[0]);
      const dataWithHeaders = [keys, ...dataArray];
      const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(dataWithHeaders);
      // const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.TablePationLogExe);
      const wb: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
      XLSX.writeFile(wb, file);
      this.log_spin=false
    })
  }
}
import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { HotToastService } from '@ngxpert/hot-toast';
import { SqlService } from '../../../../services/sqlService.service';
import { CommonService } from '../../../../services/common.service';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import html2canvas from 'html2canvas';
import jspdf from 'jspdf';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
@Component({
  selector: 'app-sql-script',
  standalone: true,
  imports: [FormsModule, CommonModule,SpinnerComponent, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './sql-script.component.html',
  styles: ``
})
export class SqlScriptComponent {
  projectId: string = '';
  asessment_id: any;
  getRole: any;
  mssqlscript: any;
  sqlscriptForm: any;
  add_spin: boolean = false;
  defaultValue: string = '';
  defaultfuncValue: string = '';
  defaultCateValue: string = '';
  migDetailSelectData: any = [];
  dbConnName: any;
  targetData: any = [];
  prjSrcTgt: any = {};
  ConsList: any;
  executed:boolean=false;
   pageName:string = ''
  constructor(private titleService: Title,private toast: HotToastService, private project: SqlService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.asessment_id = JSON.parse((localStorage.getItem('assessment_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    this.pageName = this.route.snapshot.data['name'];
  }
  ngOnInit(): void {
this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.sqlscriptForm = this.formBuilder.group({
      scriptControl: ['', [Validators.required]],
      scriptfunctionControl: ['', [Validators.required]],
      scriptCategoryControl: ['', [Validators.required]],
      ddlfunctionnames:['',[Validators.required]]
    });
    this.GetConsList();
    this.getCategorySelect()

  }

  get f():any{
    return this.sqlscriptForm.controls;
  }

  categoryData: any
  categoryName: any
  getCategorySelect() {
    const req = {
      projectId: this.projectId.toString()
    };
    this.project.PrjSqlScriptCategorySelect(req).subscribe((data) => {
      const obj = data['Table1'];
      this.categoryData = obj;
      this.categoryName = (obj[0]['scriptcategory']).toString();
    });
  }

  InsertSqlScript(formData: any) {
    this.add_spin = true;
    const data = {
      sqlScript: formData.scriptControl,
      sqlFunction: formData.scriptfunctionControl,
      sqlConnId: this.dbConnName.toString(),//connectionId
      projectId: this.projectId.toString(),
      categoryName: this.Selectedcategory,//formData.scriptCategoryControl,
      iteration: 1
    };
    this.project.SQLScriptInsert(data).subscribe((resdata: any) => {
      if (resdata) {
        this.toast.success('Data Saved  Successfully');
        this.defaultValue = "";
        this.defaultCateValue = "";
        this.defaultfuncValue = "";
        this.add_spin = false;
      } else {
        this.toast.error('Data Saved  Failed!');
        this.defaultValue = "";
        this.defaultCateValue = "";
        this.defaultfuncValue = "";
        this.add_spin = false;
      }
      this.add_spin = false;
    });
  }

  ddlcheckedChnaged(value: any) {
    this.dbConnName = value;
  }



  // getProjectSrctgtconfSelect() {
  //   this.prjSrcTgt.projectId = this.projectId.toString();
  //   this.prjSrcTgt.migsrcType = 'D';
  //   this.prjSrcTgt.connectionName = 'null';
  //   this.project.ProjectSrctgtconfSelect(this.prjSrcTgt).subscribe((data) => {
  //     let obj = data['Table1'];
  //     this.targetData = obj.filter((item: any) => {
  //       return item.migsrctgt == 'S' && item.migsrctype == 'D';
  //     });
  //   });
  // }
  GetConsList() {
    const projectId = this.projectId.toString();
    this.project.getConList(projectId).subscribe((data: any) => {
      const condata = data['Table1'];
      this.targetData = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
    });

  }
  Selectedcategory: any
  selectCategory(value: any) {
    this.Selectedcategory = value
  }
}

<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-3">
            <h3 class="main_h mt-2">Code extraction Reports </h3>
        </div>
        <div class="col-md-9">
                <dash-tabs></dash-tabs>            
        </div>
    </div>


    <!--- Main Content --->
    <div class="qmigTabs mt-3">        
        <div class="row">            
            <div class="col-md-2 pe-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100">
                        <option selected disabled>Select Database</option>
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #con (change)="getSchema(con.value)">
                        <option selected disabled>Select Source Connection</option>
                        @for(con of connectionsList ; track con;){
                            <option value="{{con}}">{{con}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #sc (change)="getIteration(sc.value)">
                        <option selected disabled>Select Schema Name</option>
                        @for(sc of schemaList; track sc;){
                            <option value="{{sc}}">{{sc}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #it (change)="getOperations(it.value)">
                        <option selected disabled>Select Iteration</option>
                        @for(it of iterationList; track it;){
                            <option value="{{it}}">{{it}}</option>
                        }
                    </select>
                </div>
            </div>            
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100">
                        <option selected disabled>Select Object Type</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3 dashCH">
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Source Count</h5>
                    <h1><span class="mdi mdi-database-arrow-up"></span> 8233</h1>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Extraction Count</h5>
                    <h1><span class="mdi mdi-database-arrow-down"></span> 7916</h1>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total Progress</h5>
                    <div style="display: block;">
                        <canvas id="myDoughnutChart" baseChart width="100" height="120" #chart3="base-chart"
                        [type]="'doughnut'"
                        [data]="lineChartData"
                        [options]="lineChartOptions"
                        [legend]="lineChartLegend"
                        [plugins]="lineChartPlugins">
                        </canvas>
                    </div>
              </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Source vs Extraction Count %</h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart [datasets]="sourceVSChartData"
                            [labels]="sourceVSChartLabels" [options]="sourceVSChartOptions" [legend]="sourceVSChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>        
        <div class="col-md-6 mt-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Source vs Extraction Count by Schema</h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart #chart1="base-chart" [datasets]="longRunningChartData"
                            [labels]="longRunningChartLabels" [options]="longRunningChartOptions" [legend]="longRunningChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>        
        <div class="col-md-6 mt-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Source vs Extraction Count by Object Type</h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart #chart1="base-chart" [datasets]="sObjectChartData"
                            [labels]="sObjectChartLabels" [options]="sObjectChartOptions" [legend]="sObjectChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
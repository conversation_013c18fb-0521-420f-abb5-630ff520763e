import { Component } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { DashboardService } from '../../../../services/dashboard.service';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { AssessmentService } from '../../../../services/assessment.service';
import { NgChartsModule } from 'ng2-charts';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CommonModule, DecimalPipe, PercentPipe } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ConnectionWithCountItem } from '../../../../models/interfaces/dashBoardTypes';
import { NumberSuffixPipe } from '../../../../shared/pipes/number-suffix.pipe';
declare let $: any;

@Component({
  selector: 'app-data-migration-new',
  standalone: true,
  imports: [TabsComponent, NgChartsModule, PercentPipe, SpinnerComponent, NgSelectModule, ReactiveFormsModule, FormsModule, DecimalPipe, CommonModule, NumberSuffixPipe],
  templateUrl: './data-migration-new.component.html',
  styles: ``
})
export class DataMigrationNewComponent {
  projectId: any;
  migtypeid: any;
  getRunSpin1: boolean = false;
  constructor(private toast: HotToastService, private dbService: DashboardService, private datamigration: DataMigrationService, private assessmentService: AssessmentService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
  }

  ngOnInit(): void {
    this.GetConsList();
  }
  formattedDate = this.formatDate(Date());
  formatDate(date: string): string {
    //debugger;
    let m = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
      , o = new Date(date)
      , S = o.getDate()
      , x = m[o.getMonth()]
      , u = o.getFullYear()
      , hours = o.getHours()
      , minutes = o.getMinutes()
      , seconds = o.getSeconds()
      , milliseconds = o.getMilliseconds();

    if (S < 10) {
      S = parseInt("0" + S)
    }
    return `${u}-${x}-${S}T${hours}:${minutes}:${seconds}.${milliseconds}`
  }

  getUpdatedRunNumber1() {
    this.getRunSpin1 = false;
    this.schemaList = [];
    this.tgtSchemaList = [];
    this.selectedTable = [];
    this.ObjectNamesList = [];
    if (this.selectedConId) {
      if (this.migtypeid == '38') {
        this.getOperationsList(this.selectedConId, "null", null);
      } else if (this.migtypeid == '40') {
        this.getOperationsList(this.selectedConId, "null", null);
        this.getSchemasList(this.selectedConId)
      } else if (this.migtypeid == '31') {
        this.GetmariasrcCounts(this.selectedConId, "null", null);
      }
      else {
        this.getOperationsList(this.selectedConId, "null", null);
        this.getSchema(this.selectedConId);
      }
    }
    if (this.selectedtgtConId) {
      if (this.migtypeid == '40') {
        this.GetFabricSchemas(this.selectedtgtConId);
        this.GetFabricsDetails(this.selectedtgtConId, null, null)
      } else if (this.migtypeid == '31') {
        this.GetmariatgtCounts(this.selectedtgtConId, "null", null)
      } else {
        this.getTgtSchema(this.selectedtgtConId);
        this.getTgtData(this.selectedtgtConId, null, null)
      }

    }
  }
  getRunSpin2: boolean = false;
  getsnapshotDetails() {
    this.getRunSpin2 = true;
    if (this.selectedConId && this.selectedSchema) {
      if (this.migtypeid == '38') {
        this.getOperationsList(this.selectedConId, this.selectedSchema, null);
      } else if (this.migtypeid == '31') {
        this.GetmariasrcCounts(this.selectedConId, this.selectedSchema, null);
      } else if (this.migtypeid == '40') {
        this.getOperationsList(this.selectedConId, this.selectedSchema, null);
        this.getSchemasList(this.selectedConId)
      }
      else {
        this.getOperationsList(this.selectedConId, this.selectedSchema, null);
        this.getSchema(this.selectedConId);
      }
    }
    if (this.selectedtgtConId && this.selectedtgtSchema) {
      if (this.migtypeid == '40') {
        this.GetFabricSchemas(this.selectedtgtConId);
        this.GetFabricsDetails(this.selectedtgtConId, this.selectedtgtSchema, null)
      } else if (this.migtypeid == '31') {
        this.GetmariatgtCounts(this.selectedtgtConId, this.selectedtgtSchema, null)
      } else {
        this.getTgtSchema(this.selectedtgtConId);
        this.getTgtData(this.selectedtgtConId, this.selectedtgtSchema, null)
      }

    }
    this.selectedTable = [];
    this.getRunSpin2 = false;
  }
  getRunSpin3: boolean = false;
  getvalidationDetails() {
    this.selectedSchema2 = null;
    this.selectedtarget2 = null;
    this.selectedSchema3 = null;
    this.selectedtarget3 = null;
    this.GetValidationAuditData();
  }
  getRunSpin4: boolean = false;
  getlivevalidationDetails() {
    this.selectedSchema4 = null;
    this.selectedtarget4 = null;
    this.selectedTables = null;
    this.SourceRowCounts = "";
    this.TargetRowCount = "";
    this.totalTablesCount1 = 0;
    this.extractedTablesCount1=0;
  }
  ConsList: any = [];
  tgtList: any
  GetConsList() {
    this.datamigration.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }
  /*--- Fetch Iteration ---*/
  gb: any;
  numRows: number = 0;
  extractedTablesCount: number = 0;
  totalTablesCount: number = 0;
  operationsList: any
  getOperationsList(Conid: any, Schema: string, TableName: any) {
    const normalizedSchema = Schema == null ? null : Schema.toUpperCase();
    const normalizedTables: string[] = Array.isArray(TableName)
      ? TableName.map((t: string) => t.toUpperCase())
      : TableName
        ? [TableName.toUpperCase()]
        : [];
    const obj = {
      Conid: Conid,
      schema: Schema
    }
    if (this.migtypeid == '38') {
      this.dbService.getTgtDatat(obj).subscribe((data: any) => {
        this.operationsList = data;

        if (!Array.isArray(data) || data.length === 0) {
          this.gb = '0.00';
          this.numRows = 0;
          this.totalTablesCount = 0;
          return;
        }

        let filteredData = data;
        if (normalizedTables.length > 0) {
          filteredData = data.filter((item: any) => {
            const schemaMatch = item.schema?.toUpperCase() === normalizedSchema;
            const tableName = (item.table_name || '').toUpperCase();
            const tableMatch = normalizedTables.includes(tableName);

            // if (!schemaMatch || !tableMatch) {
            // }

            return schemaMatch && tableMatch;
          });
        }
        this.gb = filteredData.reduce((sum: number, item: any) => {
          return sum + (parseFloat(item.total_size_gb) || 0);
        }, 0).toFixed(2);

        this.numRows = filteredData.reduce((sum: number, item: any) => {
          return sum + (parseInt(item.row_count, 10) || 0);
        }, 0);

        this.totalTablesCount = filteredData.filter((item: any) => item.table_name).length;
      });
    } else {
      this.dbService.GetSourceCountslist(obj).subscribe((data: any) => {
        this.operationsList = data;
        if (Array.isArray(data) && data.length > 0) {
          this.gb = data.reduce((sum: number, item) => {
            const gbValues = item.gb.split(/\s+/).map(Number);
            return sum + gbValues.reduce((gbSum: number, value: number) => gbSum + value, 0);
          }, 0).toFixed(2);
          this.numRows = data.reduce((sum: number, item) => {
            const numRowsValues = item.num_Rows.split(/\s+/).map(Number);
            return sum + numRowsValues.reduce((rowSum: number, value: number) => rowSum + value, 0);
          }, 0);
          this.totalTablesCount = Array.isArray(data)
            ? data.filter((item: any) => item.table_name).length
            : 0;
        }
        if (TableName === null || TableName === 'null' || TableName === undefined) {
          const filteredData = data.filter((item: any) => item.table_name === TableName);
        } else {
          const normalizedTables = Array.isArray(TableName)
            ? TableName.map((t: string) => t.toUpperCase())
            : TableName
              ? [TableName.toUpperCase()]
              : [];

          const filteredData = normalizedTables.length > 0
            ? data.filter((item: any) =>
              normalizedTables.includes((item.table_name || '').toUpperCase())
            )
            : data;


          if (filteredData.length > 0) {
            this.gb = filteredData.reduce((sum: number, item: any) => {
              const gbValues = item.gb.split(/\s+/).map(Number);
              return sum + gbValues.reduce((gbSum: number, value: number) => gbSum + value, 0);
            }, 0).toFixed(2);
            this.numRows = filteredData.reduce((sum: number, item: any) => {
              const numRowsValues = item.num_Rows.split(/\s+/).map(Number);
              return sum + numRowsValues.reduce((rowSum: number, value: number) => rowSum + value, 0);
            }, 0);
            this.totalTablesCount = Array.isArray(filteredData)
              ? filteredData.filter((item: any) => item.table_name).length
              : 0;
          }
        }
      });
    }


  }
  schemaList: any;
  /*--- Fetch Schemas ---*/
  getSchema(value: any) {
    if (this.migtypeid == '38') {
      this.assessmentService.tgtSchemaSelect(value).subscribe((data: any) => {
        this.schemaList = data
      })
    } else {
      this.dbService.getSchemalist(value).subscribe((data: any) => {
        this.schemaList = data
      })
    }

  }
  getSchemasList(value: string) {
    const obj = {
      projectid: this.projectId,
      connectioId: this.selectedConId,
    };
    this.datamigration.SchemaListSelect(obj).subscribe((data: any) => {
      if (value == "S") {
        this.schemaList = data['Table1'];
      } else {
        this.tgtSchemaList = data['Table1'];
        this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => {
          return item.schemaname != "ALL"
        });
      }
    });
  }
  selectedConId: any;
  selectedSchema: any;
  relatedSchema: any;
  isSourceConnectionSelected: boolean = false;
  onSourceConnectionChange(conId: any) {
    this.snapShot = false;
    this.isSourceConnectionSelected = true;
    this.isSourceschemaSelected = true;
    this.selectedConId = conId;
    this.schemaList = [];
    this.ObjectNamesList = [];
    if (this.selectedConId) {

      if (this.migtypeid == '38') {
        this.getOperationsList(this.selectedConId, "null", null);
        this.GetValidationAuditData();
        this.getSchema(conId);
      } else if (this.migtypeid == '40') {
        this.getOperationsList(this.selectedConId, "null", null);
        this.GetValidationAuditData();
        this.getSchemasList(this.selectedConId)
      } else if (this.migtypeid == '31') {
        this.getmariaSchemasList(this.selectedConId, "S");
        const local = this.ConsList.filter((item: any) => item.Connection_ID == this.selectedConId);
        this.relatedSchema = local.length > 0 ? local[0].dbname : "null";
        this.GetmariasrcCounts(this.selectedConId, this.relatedSchema, null);
        this.GetMariaTables()
        this.GetValidationAuditData();
      }
      else {
        this.getOperationsList(this.selectedConId, "null", null);
        this.GetValidationAuditData();
        this.getSchema(conId);
      }
    }
  }

  onSchemaChange(schema: string) {
    this.selectedSchema = schema;
    if (this.selectedConId && this.selectedSchema) {
      if (this.migtypeid == '38') {
        this.getOperationsList(this.selectedConId, this.selectedSchema, null);
        this.GetPostgresTables(this.selectedSchema)
      } else {
        this.getOperationsList(this.selectedConId, this.selectedSchema, null);
        this.getTableDetails(this.selectedConId, this.selectedSchema);
      }
      // this.GetValidationAuditData();
    }

  }
  tgtSchemaList: any
  getTgtSchema(tgt: string) {
    this.assessmentService.tgtSchemaSelect(tgt).subscribe((data: any) => {
      this.tgtSchemaList = data;
    });
  }
  //getThroughPutList
  troughput: any
  getThroughput(fromDate: string, toDate: string): void {
    const obj = {
      conId: this.selectedConId,
      fromDate: fromDate,
      toDate: toDate
    }
    this.dbService.getThroughPutList(obj).subscribe((data: any) => {
      this.troughput = data['Table1'];
    });

  }
  selectedTable: any;
  ObjectNamesList: any;
  getTableDetails(conid: any, sch: any) {
    let obj = {
      Conid: conid,
      objectType: "TABLE",
      schemaname: sch
    }
    this.assessmentService.getObjNames(obj).subscribe((data: any) => {
      this.ObjectNamesList = data.sort((a: any, b: any) =>
        a.objectName.localeCompare(b.objectName)
      );
    })
  }
  GetPostgresTables(value: any) {
    this.ObjectNamesList = []
    let obj = {
      conid: this.selectedConId,
      objectType: "TABLE",
      schema: value
    }
    this.datamigration.GetPostgresTablesBySchema(obj).subscribe((data: any) => {
      this.ObjectNamesList = data.sort((a: any, b: any) =>
        a.objectName.localeCompare(b.objectName)
      );
    })
  }
  onTableChange(tab: any) {
    this.selectedTable = tab;
    if (this.migtypeid == '31' && this.selectedConId && this.relatedSchema && this.selectedTable) {
      this.GetmariasrcCounts(this.selectedConId, this.relatedSchema, this.selectedTable)
    }
    else if (this.selectedConId && this.selectedSchema && this.selectedTable) {
      this.getOperationsList(this.selectedConId, this.selectedSchema, this.selectedTable);
    }
    if (this.migtypeid == '31' && this.selectedtgtConId && this.relatedSchema1 && this.selectedTable) {
      this.GetmariatgtCounts(this.selectedtgtConId, this.relatedSchema1, this.selectedTable)
    }
    else if (this.selectedtgtConId && this.selectedtgtSchema) {
      if (this.migtypeid == '40') {
        this.GetFabricsDetails(this.selectedtgtConId, this.selectedtgtSchema, this.selectedTable)
      }
      else {
        this.getTgtData(this.selectedtgtConId, this.selectedtgtSchema, this.selectedTable);

      }

    } else {
    }
  }
  selectedtgtConId: any;
  isTgtConnectionSelected: boolean = false;
  relatedSchema1: any;
  ontargetConnectionChange(conId: any) {
    this.isSourceConnectionSelected = true
    this.isTgtConnectionSelected = true;
    this.selectedtgtConId = conId;
    // Fetch schemas
    //this.getTgtData(conId, null, null);
    this.tgtSchemaList = [];
    // this.ObjectNamesList = [];
    if (this.selectedtgtConId) {
      if (this.migtypeid == '40') {
        this.GetFabricSchemas(this.selectedtgtConId);
        this.GetFabricsDetails(this.selectedtgtConId, null, null)
        this.GetValidationAuditData();
        //  this.GetValidationAuditData1();
      } else if (this.migtypeid == '31') {
        this.getmariaSchemasList(this.selectedtgtConId, 'T');
        const local = this.tgtList.filter((item: any) => item.Connection_ID == this.selectedtgtConId);
        this.relatedSchema1 = local.length > 0 ? local[0].dbname : "null";
        this.GetmariatgtCounts(this.selectedtgtConId, this.relatedSchema1, null)
        this.GetValidationAuditData();
      } else {
        this.getTgtSchema(conId);
        this.getTgtData(this.selectedtgtConId, null, null)
        this.GetValidationAuditData();
        // this.GetValidationAuditData1();
      }

    }
  }
  selectedtgtSchema: any;
  ontgtSchemaChange(schema: string) {
    this.selectedtgtSchema = schema;
    if (this.selectedtgtConId && this.selectedtgtSchema) {
      if (this.migtypeid == '40') {
        this.GetFabricsDetails(this.selectedtgtConId, this.selectedtgtSchema, null)
      }
      else {
        this.getTgtData(this.selectedtgtConId, this.selectedtgtSchema, null)
      }
      // this.GetValidationAuditData();
    }

  }
  tgtDataList: any;
  Tgtgb: any;
  TGtnumRows: number = 0;

  getTgtData(tgt: any, schema: any, TableName: any) {
    const normalizedSchema = schema == null ? null : schema.toUpperCase();
    const normalizedTables: string[] = Array.isArray(TableName)
      ? TableName.map((t: string) => t.toUpperCase())
      : TableName
        ? [TableName.toUpperCase()]
        : [];

    const obj = { Conid: tgt, schema: schema };

    this.dbService.getTgtDatat(obj).subscribe((data: any) => {
      this.tgtDataList = data;

      if (!Array.isArray(data) || data.length === 0) {
        this.Tgtgb = '0.00';
        this.TGtnumRows = 0;
        this.extractedTablesCount = 0;
        return;
      }
      let filteredData = data;
      if (normalizedTables.length > 0) {
        filteredData = data.filter((item: any) => {
          const schemaMatch = item.schema?.toUpperCase() === normalizedSchema;
          const tableName = (item.table_name || '').toUpperCase();
          const tableMatch = normalizedTables.includes(tableName);
          if (!schemaMatch || !tableMatch) {
          }
          return schemaMatch && tableMatch;
        });
      }
      this.Tgtgb = filteredData.reduce((sum: number, item: any) => {
        return sum + (parseFloat(item.total_size_gb) || 0);
      }, 0).toFixed(2);

      this.TGtnumRows = filteredData.reduce((sum: number, item: any) => {
        return sum + (parseInt(item.row_count, 10) || 0);
      }, 0);

      this.extractedTablesCount = filteredData.filter((item: any) => item.table_name).length;
    });
  }


  selectedItems: any = [];
  onTableSelect(tableName: any) {
    const normalizedTables = Array.isArray(tableName)
      ? tableName.map((t: string) => t.toUpperCase())
      : [tableName.toUpperCase()];
    const normalizedSchema = this.selectedSchema?.toUpperCase(); // assumed

    const match = this.tgtDataList.find((item: any) =>
      item.schema === normalizedSchema &&
      item.table_name === normalizedTables
    );

    if (match) {
      this.Tgtgb = match.total_size_gb;
      this.TGtnumRows = match.row_count;
      this.extractedTablesCount = match.table_name || 0;
    } else {
      this.Tgtgb = 0;
      this.TGtnumRows = 0;
    }

  }
  CpuDetails: any;
  totalCpuCores: any;
  utilizedCpu: any;
  totalMemoryGb: any;
  memoryUtilizationGb: any;
  selectedConnectionName: any;
  fromDate: any
  toDate: any
  getCPUData(fromDate: any, toDate: any) {
    let obj = {
      conId: this.selectedConId,
      fromDate: fromDate,
      toDate: toDate
    }
    this.dbService.getCPUData(obj).subscribe((data: any) => {
      this.CpuDetails = data['Table1'];

      if (Array.isArray(this.CpuDetails) && this.CpuDetails.length > 0) {
        const cpu = this.CpuDetails[0];
        this.totalCpuCores = parseFloat(cpu.total_cpu_cores);
        this.utilizedCpu = parseFloat(cpu.cpu_utilization_percentage);
        this.totalMemoryGb = parseFloat(cpu.total_memory_gb);
        this.memoryUtilizationGb = parseFloat(cpu.memory_utilization_percentage);

      }
    });
  }
  isValueNaN(value: number): boolean {
    return isNaN(value);
  }
  executeCPUData(fromDate: string, toDate: string) {

    if (!toDate) {
      toDate = this.formattedDate;
    }
    this.getCPUData(fromDate, toDate);
    this.getThroughput(fromDate, toDate)
  }
  TargetRowCount: any
  GetTargetRowCount() {
    let obj = {
      conid: this.selectedtgtConId,
      table: this.selectedTables
        ?.map((t: string) => `${this.selectedtarget4?.toUpperCase()}.${t.toUpperCase()}`)
        .join(',')
    }
    this.dbService.getTargetRowCount(obj).subscribe((data: any) => {
      this.TargetRowCount = data.rows;
      this.isLoading = false;
    })
  }
  GetFabricRowCount() {
    let obj = {
      conid: this.selectedtgtConId,
      table: this.selectedTables
        ?.map((t: string) => `${this.selectedtarget4?.toUpperCase()}.${t.toUpperCase()}`)
        .join(',')
    }
    this.dbService.GetFabricRowCount(obj).subscribe((data: any) => {
      this.TargetRowCount = data.rows;
      this.isLoading = false;
    })
  }
  SourceRowCounts: any
  GetSourceRowCounts() {
    if (this.migtypeid == '38') {
      let obj = {
        conid: this.selectedConId,
        table: this.selectedTables
          ?.map((t: string) => `${this.selectedSchema4?.toUpperCase()}.${t.toUpperCase()}`)
          .join(',')
      }
      this.dbService.getTargetRowCount(obj).subscribe((data: any) => {
        this.SourceRowCounts = data[0].rows;
        this.isLoading = false;
      })
    } else {
      let obj = {
        conid: this.selectedConId,
        table: this.selectedTables
          ?.map((t: string) => `${this.selectedSchema4?.toUpperCase()}.${t.toUpperCase()}`)
          .join(',')
      }
      this.dbService.GetSourceRowCounts(obj).subscribe((data: any) => {
        this.SourceRowCounts = data[0].rows;
        this.isLoading = false;
      })
    }
  }
  //GetValidationAuditData
  connectionTableData: any[] = [];
  filteredTableSum: number = 0;
  filteredTableCount: number = 0;
  filteredSum: number = 0;
  filteredCount: number = 0;
  connectionData: any[] = [];
  sourceSchemas: any[] = [];
  targetSchemas: any[] = [];
  latestsourceSchemas: any[] = [];
  latesttargetSchemas: any[] = [];
  allSourceSchemas: any[] = [];
  allTargetSchemas: any[] = [];
  allData: any
  tableData: any
  GetValidationAuditData() {
    const auditConfigs = [
      {
        srcId: this.selectedConId || "null",
        tgtId: this.selectedtgtConId || "null",
        srcSchema: this.selectedSchema2 || "null",
        tgtSchema: this.selectedtarget2 || "null",
        tableName: "null",
        type: 'summary'
      },
      {
        srcId: this.selectedConId || "null",
        tgtId: this.selectedtgtConId || "null",
        srcSchema: this.selectedSchema3 || "null",
        tgtSchema: this.selectedtarget3 || "null",
        tableName: "null",
        type: 'table'
      }
    ];

    auditConfigs.forEach(config => {
      this.dbService.GetValidationAuditData(config).subscribe((data: any) => {
        if (config.type === 'summary') {
          this.allData = data.connectionWithCount || [];
          if (this.allSourceSchemas.length === 0) {
            this.allSourceSchemas = [...new Set(this.allData.map((item: ConnectionWithCountItem) => item.source_Schema))];
            this.sourceSchemas = [...this.allSourceSchemas];
          }

          if (this.allTargetSchemas.length === 0) {
            this.allTargetSchemas = [...new Set(this.allData.map((item: ConnectionWithCountItem) => item.target_Schema))];
            this.targetSchemas = [...this.allTargetSchemas];
          }

          this.connectionData = this.allData;
          this.updateFilteredTotals();

        } else if (config.type === 'table') {
          this.tableData = data.connectionWithTableCount || [];
          if (this.liveallSourceSchemas.length === 0) {
            this.liveallSourceSchemas = [...new Set(this.tableData.map((item: ConnectionWithCountItem) => item.source_Schema))];
            this.latestsourceSchemas = [...this.liveallSourceSchemas];
          }

          if (this.liveallTargetSchemas.length === 0) {
            this.liveallTargetSchemas = [...new Set(this.tableData.map((item: ConnectionWithCountItem) => item.target_Schema))];
            this.latesttargetSchemas = [...this.liveallTargetSchemas];
          }

          this.connectionTableData = this.tableData;
          this.updateFilteredTableData();
        }
      });
    });
  }

  liveallSourceSchemas: any[] = [];
  liveallTargetSchemas: any[] = [];


  updateFilteredTotals(sourceSchema?: string, targetSchema?: string) {
    let filtered = this.connectionData;

    if (sourceSchema) {
      filtered = filtered.filter(item => item.source_Schema === sourceSchema);
    }

    if (targetSchema) {
      filtered = filtered.filter(item => item.target_Schema === targetSchema);
    }

    this.filteredSum = filtered.reduce((sum, item) => sum + Number(item.sum || 0), 0);
    this.filteredCount = filtered.reduce((cnt, item) => cnt + Number(item.count || 0), 0);
  }
  updateFilteredTableData(sourceSchema?: string, targetSchema?: string) {
    let filtered = this.connectionTableData;

    if (sourceSchema) {
      filtered = filtered.filter(item => item.source_Schema === sourceSchema);
    }

    if (targetSchema) {
      filtered = filtered.filter(item => item.target_Schema === targetSchema);
    }
    this.totalTableCount = filtered.reduce((sum, item) => sum + (+item.totaltableCount || 0), 0);
    this.migratedTableCount = filtered.reduce((sum, item) => sum + (+item.migrated_tables || 0), 0);
    this.sourceRowCount = filtered.reduce((sum, item) => sum + (+item.source_row_count || 0), 0);
    this.targetRowCount = filtered.reduce((sum, item) => sum + (+item.target_row_count || 0), 0);
  }
  migratedTableCount: any
  totalTableCount: any
  sourceRowCount: any
  targetRowCount: any
  slicedData(data: any[]): any[] {
    return data.slice(0, 3)

  }
  selectedSchema2: any;
  isSourceschemaSelected: boolean = false;
  onSchemaChange2(schema: string) {
    this.isSourceschemaSelected = true;
    this.selectedSchema2 = schema;
    if (this.selectedConId && this.selectedSchema2) {
      this.GetValidationAuditData();
    }
  }
  selectedtarget2: any
  ontargetChange2(value: string) {
    this.selectedtarget2 = value;
    if (this.selectedConId && this.selectedSchema2 && this.selectedtarget2) {
      this.GetValidationAuditData();
    }
  }
  selectedSchema3: any
  onSchemaChange3(schema: string) {
    this.selectedSchema3 = schema;
    if (this.selectedConId && this.selectedSchema3) {
      this.GetValidationAuditData();
    }
  }
  selectedtarget3: any
  ontargetChange3(value: string) {
    this.selectedtarget3 = value;
    if (this.selectedConId && this.selectedSchema3 && this.selectedtarget3) {
      this.GetValidationAuditData();
    }
  }
  selectedSchema4: any
  onSchemaChange4(schema: string) {
    this.selectedSchema4 = schema;
    if (this.selectedConId && this.selectedSchema4) {
      if (this.migtypeid == '38') {
        this.GetPostgresTables1(this.selectedSchema4)
      } else if (this.migtypeid == '31') {
        this.GetMariaTables1()
      } else {
        this.getTableDetails1(this.selectedConId, this.selectedSchema4);
      }
    }

  }
  selectedtarget4: any
  ontgtSchemaChange4(schema: string) {
    this.selectedtarget4 = schema;
    if (this.selectedtgtConId && this.selectedtarget4) {
      if (this.migtypeid == '40') {
      } else {
      }
    }

  }
  liveObjectNamesList: any;
  getTableDetails1(conid: any, sch: any) {
    let obj = {
      Conid: conid,
      objectType: "TABLE",
      schemaname: sch
    }
    this.assessmentService.getObjNames(obj).subscribe((data: any) => {
      this.liveObjectNamesList = data.sort((a: any, b: any) =>
        a.objectName.localeCompare(b.objectName)
      );
    })
  }
  GetPostgresTables1(value: any) {
    this.liveObjectNamesList = []
    let obj = {
      conid: this.selectedConId,
      objectType: "TABLE",
      schema: value
    }
    this.datamigration.GetPostgresTablesBySchema(obj).subscribe((data: any) => {
      this.liveObjectNamesList = data.sort((a: any, b: any) =>
        a.objectName.localeCompare(b.objectName)
      );
    })
  }
  selectedTables: any;
  onTableChangelive(tab: any) {
    this.selectedTables = tab;
    this.isLoading = true;
    if (this.selectedConId && this.selectedSchema4 && this.selectedTables) {
      this.isLoading = true;
      if (this.migtypeid == '38') {
        this.Totallivecounts(this.selectedConId, this.selectedSchema4, this.selectedTables);
        this.GetTargetRowCount();
      } else if (this.migtypeid == '31') {
        this.Totallivecounts(this.selectedConId, this.selectedSchema4, this.selectedTables);
        this.GetmariasrcTablesCounts();
      } else {
        this.Totallivecounts(this.selectedConId, this.selectedSchema4, this.selectedTables);
        this.GetSourceRowCounts();
      }
      
        this.isLoading = false;
     
    }
    if (this.selectedtgtConId && this.selectedtarget4) {
      this.isLoading = true;
      if (this.migtypeid == '40') {
        this.countsdata(this.selectedtgtConId, this.selectedtarget4, this.selectedTables);
        this.GetFabricRowCount()
      } else if (this.migtypeid == '31') {
        this.countsdata(this.selectedtgtConId, this.selectedtarget4, this.selectedTables);
        this.GetmariatgtTablesCounts();
      } else {
        this.countsdata(this.selectedtgtConId, this.selectedtarget4, this.selectedTables);
        this.GetTargetRowCount();
      }
     
        this.isLoading = false;
     
    } else {
    }
  }
  isLoading: boolean = false;
  snapShot: boolean = false;
  extractedTablesCount1: number = 0;
  countsdata(tgt: any, schema: any, TableName: any) {
    if (this.migtypeid == '40') {
      const normalizedSchema = schema == null ? null : schema.toUpperCase();
      const normalizedTables: string[] = Array.isArray(TableName)
        ? TableName.map((t: string) => t.toUpperCase())
        : TableName
          ? [TableName.toUpperCase()]
          : [];

      const obj = {
        conid: tgt,
        schema: schema
      };

      this.dbService.GetFabricsData(obj).subscribe((data: any) => {
        const tableData = Array.isArray(data) ? data : [];

        this.fabDetails = tableData;

        if (!Array.isArray(tableData) || tableData.length === 0) {
          this.Tgtgb = '0.00';
          this.TGtnumRows = 0;
          this.extractedTablesCount = 0;
          return;
        }
        let filteredData = tableData;

        if (normalizedTables.length > 0) {
          filteredData = tableData.filter((item: any) => {
            const schemaMatch = item.schema?.toUpperCase() === normalizedSchema;
            const tableName = (item.table_name || '').toUpperCase();
            const tableMatch = normalizedTables.includes(tableName);

            if (!schemaMatch || !tableMatch) {
            }

            return schemaMatch && tableMatch;
          });
        }

        this.Tgtgb = '0.00';
        this.TGtnumRows = 0;
        this.extractedTablesCount1 = filteredData.filter((item: any) => item.table_name).length;
      });
    } else if (this.migtypeid == '31') {
      const normalizedSchema = schema == null ? null : schema.toUpperCase();
      const normalizedTables: string[] = Array.isArray(TableName)
        ? TableName.map((t: string) => t.toUpperCase())
        : TableName
          ? [TableName.toUpperCase()]
          : [];

      const obj = { Conid: tgt, schema: schema };
      this.dbService.GetmariasnapshotCounts(obj).subscribe((data: any) => {
        this.tgtDataList = data;

        if (!Array.isArray(data) || data.length === 0) {
          console.warn('Empty or invalid data');
          this.Tgtgb = '0.00';
          this.TGtnumRows = 0;
          this.extractedTablesCount1 = 0;
          return;
        }
        let filteredData = data;
        if (normalizedTables.length > 0) {
          filteredData = data.filter((item: any) => {
            const schemaMatch = item.schema?.toUpperCase() === normalizedSchema;
            const tableName = (item.table_name || '').toUpperCase();
            const tableMatch = normalizedTables.includes(tableName);

            if (!schemaMatch || !tableMatch) {
            }

            return schemaMatch && tableMatch;
          });
        }

        this.extractedTablesCount1 = filteredData.filter((item: any) => item.table_name).length;
      });
    } else {
      const normalizedSchema = schema == null ? null : schema.toUpperCase();
      const normalizedTables: string[] = Array.isArray(TableName)
        ? TableName.map((t: string) => t.toUpperCase())
        : TableName
          ? [TableName.toUpperCase()]
          : [];

      const obj = { Conid: tgt, schema: schema };

      this.dbService.getTgtDatat(obj).subscribe((data: any) => {
        this.tgtDataList = data;

        if (!Array.isArray(data) || data.length === 0) {
          console.warn('Empty or invalid data');
          this.Tgtgb = '0.00';
          this.TGtnumRows = 0;
          this.extractedTablesCount = 0;
          return;
        }
        let filteredData = data;

        if (normalizedTables.length > 0) {
          filteredData = data.filter((item: any) => {
            const schemaMatch = item.schema?.toUpperCase() === normalizedSchema;
            const tableName = (item.table_name || '').toUpperCase();
            const tableMatch = normalizedTables.includes(tableName);

            if (!schemaMatch || !tableMatch) {
            }

            return schemaMatch && tableMatch;
          });
        }
        this.extractedTablesCount1 = filteredData.filter((item: any) => item.table_name).length;
      });
    }
  }
  totalTablesCount1: number = 0;
  Totallivecounts(Conid: any, Schema: string, TableName: any) {
    if (this.migtypeid == '38') {
      const normalizedSchema = Schema == null ? null : Schema.toUpperCase();
      const normalizedTables: string[] = Array.isArray(TableName)
        ? TableName.map((t: string) => t.toUpperCase())
        : TableName
          ? [TableName.toUpperCase()]
          : [];

      const obj = { Conid: Conid, schema: Schema };

      this.dbService.getTgtDatat(obj).subscribe((data: any) => {
        this.operationsList = data;

        if (!Array.isArray(data) || data.length === 0) {
          console.warn('Empty or invalid data');
          this.gb = '0.00';
          this.numRows = 0;
          this.totalTablesCount1 = 0;
          return;
        }

        let filteredData = data;

        if (normalizedTables.length > 0) {
          filteredData = data.filter((item: any) => {
            const schemaMatch = item.schema?.toUpperCase() === normalizedSchema;
            const tableName = (item.table_name || '').toUpperCase();
            const tableMatch = normalizedTables.includes(tableName);

            if (!schemaMatch || !tableMatch) {
            }

            return schemaMatch && tableMatch;
          });
        }
        this.totalTablesCount1 = filteredData.filter((item: any) => item.table_name).length;
      });
    } else if (this.migtypeid == '31') {
      const obj = {
        Conid: Conid,
        schema: Schema
      }
      this.dbService.GetmariasnapshotCounts(obj).subscribe((data: any) => {
        this.operationsList = data;
        if (Array.isArray(data) && data.length > 0) {
          this.totalTablesCount1 = Array.isArray(data)
            ? data.filter((item: any) => item.table_name).length
            : 0;
        }

        if (TableName === null || TableName === 'null' || TableName === undefined) {
          const filteredData = data.filter((item: any) => item.table_name === TableName);
        } else {
          const normalizedTables = Array.isArray(TableName)
            ? TableName.map((t: string) => t.toUpperCase())
            : TableName
              ? [TableName.toUpperCase()]
              : [];

          const filteredData = normalizedTables.length > 0
            ? data.filter((item: any) =>
              normalizedTables.includes((item.table_name || '').toUpperCase())
            )
            : data;


          if (filteredData.length > 0) {
            this.totalTablesCount1 = Array.isArray(filteredData)
              ? filteredData.filter((item: any) => item.table_name).length
              : 0;
          }
        }
      });
    } else {
      const obj = {
        Conid: Conid,
        schema: Schema
      }
      this.dbService.GetSourceCountslist(obj).subscribe((data: any) => {
        this.operationsList = data;
        if (Array.isArray(data) && data.length > 0) {
          this.totalTablesCount1 = Array.isArray(data)
            ? data.filter((item: any) => item.table_name).length
            : 0;
        }

        if (TableName === null || TableName === 'null' || TableName === undefined) {
          const filteredData = data.filter((item: any) => item.table_name === TableName);
        } else {
          const normalizedTables = Array.isArray(TableName)
            ? TableName.map((t: string) => t.toUpperCase())
            : TableName
              ? [TableName.toUpperCase()]
              : [];

          const filteredData = normalizedTables.length > 0
            ? data.filter((item: any) =>
              normalizedTables.includes((item.table_name || '').toUpperCase())
            )
            : data;


          if (filteredData.length > 0) {
            this.totalTablesCount1 = Array.isArray(filteredData)
              ? filteredData.filter((item: any) => item.table_name).length
              : 0;
          }
        }
      });
    }
  }
  //GetCDCAuditData
  CDCDetails: any;
  getCDCAuditData(fromDate: any, toDate: any) {
    let obj = {
      conId: this.selectedConId,
      fromDate: fromDate,
      toDate: toDate
    }
    this.dbService.GetCDCAuditData(obj).subscribe((data: any) => {
      this.CDCDetails = data['Table1'];
    });
  }
  formatLag(lag: number): string {
    if (lag < 60) {
      return `${lag} sec`;
    } else {
      const mins = (lag / 60).toFixed(1);  // 1 decimal place
      return `${mins} min`;
    }
  }
  executeCDCData(fromDate: string, toDate: string) {

    if (!toDate) {
      toDate = this.formattedDate;
    }
    this.getCDCAuditData(fromDate, toDate);
    //this.getThroughput(fromDate, toDate)
  }
  //GetFabricsData
  fabDetails: any
  GetFabricsDetails(tgt: any, schema: any, TableName: any) {
    const normalizedSchema = schema == null ? null : schema.toUpperCase();
    const normalizedTables: string[] = Array.isArray(TableName)
      ? TableName.map((t: string) => t.toUpperCase())
      : TableName
        ? [TableName.toUpperCase()]
        : [];

    const obj = {
      conid: tgt,
      schema: schema
    };

    this.dbService.GetFabricsData(obj).subscribe((data: any) => {

      // Check structure - adjust here if needed
      const tableData = Array.isArray(data) ? data : [];

      this.fabDetails = tableData;

      if (!Array.isArray(tableData) || tableData.length === 0) {
        this.Tgtgb = '0.00';
        this.TGtnumRows = 0;
        this.extractedTablesCount = 0;
        return;
      }

      // Log each row for debugging
      tableData.forEach((item: any, idx: number) => {
      });

      let filteredData = tableData;

      if (normalizedTables.length > 0) {
        filteredData = tableData.filter((item: any) => {
          const schemaMatch = item.schema?.toUpperCase() === normalizedSchema;
          const tableName = (item.table_name || '').toUpperCase();
          const tableMatch = normalizedTables.includes(tableName);

          if (!schemaMatch || !tableMatch) {
          }

          return schemaMatch && tableMatch;
        });
      }


      // No actual GB or row count fields in fabric data; setting to 0.
      this.Tgtgb = '0.00';
      this.TGtnumRows = 0;
      this.extractedTablesCount = filteredData.filter((item: any) => item.table_name).length;

    });
  }


  //GetFabricSchemas
  FabricSchemasDetails: any;
  GetFabricSchemas(tgt: string) {
    this.dbService.GetFabricSchemas(tgt).subscribe((data: any) => {
      this.tgtSchemaList = data;
    });
  }


  // New UI 
  getTrendInfo(source: number, target: number) {
    const change = (target / source) * 100;
    const absChange = Math.abs(change).toFixed(1);

    let label: string, icon: string, color: string, textColor: string;

    // if (change < 0) {
    //   label = `${absChange}% Reduction`;
    //   icon = 'mdi-arrow-down';
    //   color = 'bg-success';
    //   textColor = 'text-success';
    // } else if (change > 0) {
    //   label = `${absChange}% Increase`;
    //   icon = 'mdi-arrow-up';
    //   color = 'bg-warning';
    //   textColor = 'text-warning';
    // } else {
    //   label = 'No Change';
    //   icon = 'mdi-swap-horizontal';
    //   color = 'bg-secondary';
    //   textColor = 'text-secondary';
    // }

    label = `${absChange}% Change`;
    icon = 'mdi-arrow-up';
    color = 'bg-success';
    textColor = 'text-success';

    return {
      label,
      icon,
      color,
      textColor,
      width: Math.abs(change)
    };
  }

  getmariaSchemasList(ConnectionId: any, value: string) {
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId,
    };
    if (this.migtypeid == "31" && value == "S") {
      this.schemaList = []
      var local = this.ConsList.filter((item: any) => {
        return item.Connection_ID == ConnectionId
      })
      let obj = {
        schema_name: local[0].dbname
      }
      this.schemaList.push(obj)
    }
    else if (this.migtypeid == "31" && value == "T") {
      this.tgtSchemaList = []
      this.tgtList.filter((item: any) => {
        if (item.Connection_ID == ConnectionId) {
          let obj = {
            schema_name: item.dbname
          }
          this.tgtSchemaList.push(obj)
        }
      })
    }
    else {
      this.datamigration.SchemaListSelect(obj).subscribe((data: any) => {
        if (value == "S") {
          this.schemaList = data['Table1'];
        } else {
          this.tgtSchemaList = data['Table1'];
          this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => {
            return item.schemaname != "ALL"
          });
        }
        if (this.migtypeid == "35" || this.migtypeid == "34") {
        }
      });
    }
  }
  GetMariaTables() {
    let obj = {
      conid: this.selectedConId,
      dbname: this.relatedSchema
    }
    this.datamigration.GetMariaTables(obj).subscribe((data: any) => {
      // this.ObjectNamesList = data
      this.ObjectNamesList = data.sort((a: any, b: any) =>
        a.objectName.localeCompare(b.objectName)
      );
    })
  }
  GetMariaTables1() {
    let obj = {
      conid: this.selectedConId,
      dbname: this.selectedSchema4
    }
    this.datamigration.GetMariaTables(obj).subscribe((data: any) => {
      this.liveObjectNamesList = data.sort((a: any, b: any) =>
        a.objectName.localeCompare(b.objectName)
      );
    })
  }
  GetmariatgtCounts(tgt: any, schema: any, TableName: any) {
    const normalizedSchema = schema == null ? null : schema.toUpperCase();
    const normalizedTables: string[] = Array.isArray(TableName)
      ? TableName.map((t: string) => t.toUpperCase())
      : TableName
        ? [TableName.toUpperCase()]
        : [];

    const obj = { Conid: tgt, schema: schema };
    this.dbService.GetmariasnapshotCounts(obj).subscribe((data: any) => {
      this.tgtDataList = data;

      if (!Array.isArray(data) || data.length === 0) {
        this.Tgtgb = '0.00';
        this.TGtnumRows = 0;
        this.extractedTablesCount = 0;
        return;
      }
      let filteredData = data;
      if (normalizedTables.length > 0) {
        filteredData = data.filter((item: any) => {
          const schemaMatch = item.schema?.toUpperCase() === normalizedSchema;
          const tableName = (item.table_name || '').toUpperCase();
          const tableMatch = normalizedTables.includes(tableName);

          if (!schemaMatch || !tableMatch) {
          }

          return schemaMatch && tableMatch;
        });
      }
      this.Tgtgb = filteredData.reduce((sum: number, item: any) => {
        return sum + (parseFloat(item.total_size_gb) || 0);
      }, 0).toFixed(2);

      this.TGtnumRows = filteredData.reduce((sum: number, item: any) => {
        return sum + (parseInt(item.row_count, 10) || 0);
      }, 0);
      this.extractedTablesCount = filteredData.filter((item: any) => item.table_name).length;
    });
  }
  //
  GetmariatgtTablesCounts() {
    let obj = {
      conid: this.selectedtgtConId,
      table: this.selectedTables
        ?.map((t: string) => `${this.selectedtarget4?.toUpperCase()}.${t.toUpperCase()}`)
        .join(',')
    }
    this.dbService.GetmariaTablesCounts(obj).subscribe((data: any) => {
      if (data?.rows !== undefined) {
        this.TargetRowCount = Number(data.rows);
      } else {
        this.TargetRowCount = 0;
      }
      this.isLoading = false;
    })
  }
  GetmariasrcTablesCounts() {
    let obj = {
      conid: this.selectedConId,
      table: this.selectedTables
        ?.map((t: string) => `${this.selectedSchema4?.toUpperCase()}.${t.toUpperCase()}`)
        .join(',')
    }
    this.dbService.GetmariaTablesCounts(obj).subscribe((data: any) => {
      if (data?.rows !== undefined) {
        this.SourceRowCounts = Number(data.rows);
      } else {
        this.SourceRowCounts = 0;
      }
      this.isLoading = false;
    })
  }
  GetmariasrcCounts(Conid: any, Schema: string, TableName: any) {
    const obj = { Conid, schema: Schema };
    const normalizedTables = Array.isArray(TableName)
      ? TableName.map((t: string) => t.toUpperCase())
      : TableName ? [TableName.toUpperCase()] : [];

    console.time('API_CALL');
    this.dbService.GetmariasnapshotCounts({ Conid: Conid, schema: Schema }).subscribe((data: any[]) => {
      console.timeEnd('API_CALL');
      console.time('PROCESSING');

      this.operationsList = data;
      let totalSize = 0, totalRows = 0, tableCount = 0;

      for (const item of data) {
        const tableName = (item.table_name || '').toUpperCase();
        const matches = normalizedTables.length === 0 || normalizedTables.includes(tableName);

        if (matches) {
          // Avoid splitting unless it's a multi-value field (confirm with backend)
          const gb = parseFloat(item.total_size_gb) || 0;
          const rows = parseInt(item.row_count, 10) || 0;

          totalSize += gb;
          totalRows += rows;
          if (item.table_name) tableCount++;
        }
      }

      this.gb = totalSize.toFixed(2);
      this.numRows = totalRows;
      this.totalTablesCount = tableCount;

      console.timeEnd('PROCESSING');
    });
  }

}


/* --- Assessment Module ---*/

//extract schema
export interface conlist {
  Table1: {
    filter(arg0: (item: any) => boolean): any
    Connection_ID: string,
    conname: string,
    filename: string,
    migsrctgt: string,
    migsrctype: string
  }
}
export interface fileStatus {
  message: string
}

//connections
export interface GetDBConnections {
  Table1: {
    Connection_ID: string,
    acl: string,
    conname: string,
    dbhost: string,
    dbname: string,
    dbpassword: string,
    dbport: string,
    dbschema: string,
    dbuserid: string,
    migenv: string,
    migsrctgt: string,
    migsrctype: string,
    parallelprocess: string,
    service_name: string

  }
}
export interface getfiletype {
  Table1: {
    file_type_id: string,
    filetype: string
  }

}

export interface UpdateConnection {
  v_status: string,
  v_id: string
}

export interface updateconnection {
  dbconname: string,
  dbhost: string,
  dbname: string,
  dbpassword: string,
  dbschema: string,
  dbuserid: string,
  fileName: string,
  id: string,
  json: string
  migenv: string,
  migsrctgt: string,
  migsrctype: string,
  parallel: string,
  parallelprocess: string,
  port: string,
  project_id: string
  schema: string,
  serviceName: string,
}

export interface projectMigdetails {
  jsonResponseData: {
    Table1: [
      id: string,
      project_id: string,
      migsrctype: string,
      migsrctgt: string,
      migenv: string,
      dbconname: string,
      dbhost: string,
      dbname: string,
      dbschema: string,
      dbport: string,
      dbuserid: string,
      dbpassword: string
    ]
  }
}
export interface downloadDocs {
  path: string
}

export interface uploadDocs extends downloadDocs {
  file: string
}


export interface documentsList {
  created_dt: string,
  fileName: string,
  filePath: string
}

export interface deleteFile {
  message: string
}

export interface insertSchema {
  projectId: string,
  conId: string
}
/*--- Testing Module end ---*/


//sukanya changes//

export interface sourceConnectionName {
  dbconname: string,
  migsrctype: string,
  migsrctgt: string
}
export interface schemaListObj {
  projectId: string,
  connectionId: string
}

// export interface schemaTable {
//   schema_id: string,
//   schemaname: string
// }
export interface schemaTable {
  connection_id: string,
  schemaname: string,
  schema_id:string
}

export interface SchemaTableList {
  Table1: schemaTable[]
}


export interface RunInfo {
  iteration: string,
  dbschema: string,
  iter_schema_operation: string,
  schemaname: string,
  run_id: string,
  operation_name: string,
  objecttype_name: string,
  operation_type: string
}

export interface RunInfoTable {
  Table1: RunInfo[]
}

export interface workLoadReqObj {
  iterationNo: string,
  operationType: string,
  schemaName: string,
  objectType: string,
  connectionId: string,
  projectId: string
}
export interface ConnectionObj {
  Connection_ID: string,
  migsrctype: string,
  migsrctgt: string,
  filename: string,
  conname: string
}
export interface ConnectionObjTable {
  Table1: ConnectionObj[]
}

export interface FileInsertObj {
  projectID: string
  fileName: string,
  uploadDateTime: string,
  uploadStatus: string,
  conversionStatus: string,
  acl: " "
}


export interface Response{
  message:string
}

/*--- Testing Module start ---*/


export interface Files{
  projectID:string,
  containerName:string,
  folderName:string,
  subFolderName:string
}

export interface deletefiles{
  message:string

}

export interface docList{
  created_date:string,
  fileName:string,
  filePath:string
}

export interface test{
}



export interface RunInfo{
  iteration:string,
  dbschema:string,
  iter_schema_operation:string,
  schemaname:string,
  run_id:string,
  operation_name:string,
  objecttype_name:string,
  operation_type:string
}
 
export interface RunInfoTable{
  Table1:RunInfo[]
}
export interface ProjectSrctgtconfSelect{

        id:string,
        project_id:string,
        migsrctype:string,
        migsrctgt:string,
        dbconname:string,
        dbhost:string,
        dbname:string,
        dbschema:string,
        dbport:string,
        dbuserid:string,
        dbpassword:string,
        acl:string,
        service_name:string,
  
}

export interface ProjectSrctgtconfSelectTable{
Table1:ProjectSrctgtconfSelect[]
}

export interface TestcaseBatchids{
  run_id:string,
  tgt_object_id:string,
  tgt_testid:string,
  test_group:string,
  object_signature:string,
  created_dt:string,
  updated_dt:string,
  global_variable:string,
  last_execution_dt:string,
  testcase_status:string,
  exec_time_limit:string,
  user_id:string,
  user_testcase_id:string,
  module_name:string,
  service_name:string,
  error_log:string,
  ended_execution:string,
  strated_execution:string,
  code_object_name:string,

}
export interface TestcaseBatchidsTable{
  Table1:TestcaseBatchids[]
  }

  export interface GetDataByBatchId{
    tgt_test_id:string,
    iteration:string,
    code_object_name:string,
    object_type:string,
    object_signature:string
  }

  export interface GetDataByBatchIdTable{
    Table1:GetDataByBatchId[]
  }

  

/*--- Testing Module end ---*/


export interface redisCommand {
  projectId: string;
  conId: string;
  contype:string;
}

export interface migdetailData {
  projectId: string;
  migsrcType: string;
}
export interface reqObj {
  projectId: string;
  folderName: string;
  containerName: string;
}
export interface deployBtn {
  projectId: string;
  sqlFileName: string;
}

export interface executeBtn {
  dbconname: string;
  projectId: string;
  queryTobeExecuted: string;
}
export interface exeBtn {
  Status: string
}
export interface uploadCommonBinaryFileToBlob {
  fileName: string;
  content: string;
}

//UploadBinaryFileToShare
export interface UploadBinaryFileToShare {
  projectID: string;
  containerName: string;
  folderName: string;
  fileName: string;
  content: string;
}

//addPrjConnection
export interface addPrjConnection {
  project_id: number;
  migsrctype: string;
  migsrctgt: string;
  migenv: string;
  fileName: string;
  dbconname: string;
  dbhost: string;
  dbname: string;
  schema: string;
  port: string;
  dbuserid: string;
  dbpassword: string;
}
//updatePrjConnection
export interface updatePrjConnection {
  project_id: number;
  id: number;
  migsrctype: string;
  migsrctgt: string;
  migenv: string;
  fileName: string;
  dbconname: string;
  dbhost: string;
  dbname: string;
  schema: string;
  port: string;
  dbuserid: string;
  dbpassword: string;
}
//addProjectObject
export interface addconnection {
  token: string;
  project_id: number;
  migsrctgt: string;
  migsrctype: string;
  migenv: string;
  filename: string;
  dbconname: string;
  dbhost: string;
  dbname: string;
  schema: string;
  port: string;
  dbuserid: string;
  dbpassword: string;
  parallel: string;
  json: string;
  dynamoUser:string;
  dynamoPassword:string;
  isDynamo:boolean;

}
//deleteDbConnection
export interface deleteDbConnection {
  projectId: string,
  id: string;
}

//testSourceConn
export interface testSourceConn {
  dbHost: string,
  dbName: string,
  dbPort: string,
  dbUserid: string,
  dbPassword: string;
}

//addfiletype
export interface addfiletype {
  projectId: string,
  fileType: string;

}
export interface srctgtConfInsert {
  projectId: String,
  migsrctype: String,
  migsrctgt: String,
  migenv: String,
  filename: String,
  filetypeId: String,
  dbconname: String,
  dbhost: String,
  dbname: String,
  dbschema: String,
  dbport: String,
  dbuserid: String,
  dbpassword: String;
}
//deleteFileShareFile
export interface deleteFileShareFile {
  containerName: string,
  folderName: string,
  fileName: string,
  projectId: string,
}
//deleteFileCon
export interface deleteFileCon {
  connectionId: string,
  projectId: string;
}

//*data migration screens*/

//SchemaListSelect
export interface SchemaListSelect {
  projectid: string,
  connectioId: string;
}

//airflowValidationCommand
export interface airflowValidationCommand {
  projectId: string,
  srcId: string,
  tgtId: string,
  fileName: string,
  schema: string,
  tgtschema:string,
  task:string
}
//triggerValidationDags
export interface triggerValidationDags {
  operation: string,
  fileName: string;
}

//GetReqData
export interface GetReqData {
  projectId: string,
  operationType: string;
}
export interface GetRequestData {
  Table1: {
    request_id: string,
    connection_id: string,
    operation_id: string,
    schemaname: string,
    status: string,
    remarks: string,
    objectname: string,
    objecttype: string,
    others: string,
    acl: string,
    created_by: string,
    created_dt: string,
    updated_by: string,
    updated_dt: string,
    operation_name: string,
    conname: string
  }

}
//PrjSchemasListSelectData
export interface PrjSchemasListSelectData {
  projectId: string,
  ConId: string;
}
//projectConRunTblInsert
export interface projectConRunTblInsert {
  projectId: string,
  connection_id: number,
  operationId: number,
  schema: string,
  status: string,
  remarks: string,
  objectname: string,
  objecttype: string,
}

//setRedisCache
export interface setRedisCache {
  projectId: string,
  option: number,
  schema: string,
  connection: string,
  Object: string,
  srcConId: string,
  tgtConId: string,
  jobName: string
}
export interface setRedis {
  projectId: string,
  option: number,
  tgtConId: string,
  filePath: string,
  schema: string,
  targetSchema: string,
  jobName: string
}
// Object Code Interface 
export interface ObjectCode {
  Table1: {
    tgt_id: string,
    run_id: string,
    objecttype_id: string,
    object_name: string,
    tgt_code: string,
    src_code: string,
    updated_code: string,
    deployed: string,
    feature_created: string,
    override: string,
    acl: string,
  }

}
export interface ObjectType {
  Table1: {
    objecttype_id: string,
    objecttype_name: string
  }

}
export interface ObjectSelect {
  Table1: {
    object_name: string,
    src_id: string
  }
}

export interface GetObjectType {
  runno: string,
  schema: string
}

//ObjectsSelect
export interface ObjectsSelect {
  runno: string,
  schemaid: string,
  ObjTypeId: string,
  deployed: string
}
export interface GetObjectCode {
  Runnid: string,
  ObjTypeId: string,//this.currentSchema,
  Schemaid: string,
  ObjectName: string,
  Opt: string
}
//RedisCacheInsertion
export interface RedisCacheInsertion {
  projectId: string,
  option: number,
  iteration: string,
  schema: string,
  srcConId: string,
  tgtConId: string,
  objectType: string,
  objectName: string,
  jobName: string,
  timeTaken: string,
  Observations: string,
  targetSchema:string
  // targetSchema:string
}
export interface Operation {
  Table1: {
    operation_id: string,
    operation_name: string
  }

}

//GetProjectTgtDeployStatusSelect
export interface GetProjectTgtDeployStatusSelect {
  projectId: string,
  srcid: string
}
export interface UserData {
  fileName: string,
  fileUrl: string
}


//*golden gate start*/

//insertTablesCommand

export interface insertTablesCommand {
  operation: string,
  projectId: string,
  schema ?: string,
  srcId: string
}

//GetTablesByschemaGG
export interface GetTablesByschemaGG {
  schema: string,
  srcId: string
}

//runGGScripts
export interface runGGScripts {
  cmd: string,
  podName: string,
  fileName: string
}
export interface ggCreateFileReq {
  task:string,
  podName: string,
  operation: string,
  schema: string,
  schemaId: string,
  srcId: string,
  tgtId: string,
  podService: string,
  trailPath: string,
  ggHome: string,
  oracleHome: string,
  tableList: string,
  projectId: string
}

export interface UploadProjectDocs {
  file: string,
  path: string
}
//dataLoadStatus
export interface dataLoadStatus {
  schema: string,
  configId: string,
  audConId: string
}
export interface Table {
  Connection_ID: string,
  conname: string,
  filename: string,
  migsrctgt: string,
  migsrctype: string
}
export interface listSchema {
  Table1: {
    schemaname: string
  }

}
export interface configFiles {
  config_id: string,
  created_dt: string,
  fileName: string,
  filePath: string
}

export interface operation {
  option: string,
  value: string
}

export interface schemalist {
  schema_id: string,
  schemaname: string
}

export interface setredis {
  projectId: string,
  option: number,
  tgtConId: string,
  filePath: string,
  schema: string,
  targetSchema: string,
  jobName: string
}

export interface objtype {
  runno: string,
  schemaid: string
}
export interface redis {
  projectId: string,
  option: number,
  deployOption: string,
  schema: string,
  objectType: string,
  single_Check: string,
  iteration: string,
  jobname: string,
  srcConId: string
}
export interface redis1 {
  projectId: string,
  option: number,
  deployOption: string,
  schema: string,
  objectType: string,
  single_Check: string,
  tgtConId: string,
  jobName: string,
  target_schema:string
}
export interface DeleteSchema {
  schemaname: string,
  conId: string
}
export interface redis2 {
  projectId: string,
  option: number,
  iteration: string,
  targetSchema: string,//this.currentSchema,
  objectType: string,
  tgtConId: string,
  objectName: string,
  jobName: string,
}
export interface obj {
  runno: string,
  schemaid: string,
  objTypeId: string,
  deployed: string
}
export interface ob {
  runno: string,
  schemaid: string,
  objTypeId: string,
  deployed: string
}
//getObjectTypes
export interface getObjectTypes {
  projectId: string,
  objectgroupname: string
}

//manual conversion
export interface conList {
  Table1: {
    filter(arg0: (item: any) => boolean): any
    Connection_ID: string,
    migsrctype: string,
    migsrctgt: string,
    filename: string,
    conname: string
  }

}
export interface schemalist {
  Table1: {
    filter(arg0: (item: any) => boolean): any
    connection_id: string,
    schemaname: string,
    schema_id: string
  }
}
export interface manualSave {

  Table1: {
    v_status: string,
    v_tgt_id: string

  }
}
export interface GetFilesFromExpath {
  fileName: string,
  filePath: string,
  created_dt: string
}

export interface SchemaSelect {
  Table1: {
    schema_id: string,
    schemaname: string
  }

}
export interface createFile {
  message: string
}
export interface GetConfigData {
  Table1: {
    config_id: string,
    config_file_name: string,
    created_dt: string
  }
}
export interface getSchemas {
  schemaname: string
}
export interface dataStatus {
  Table1: {
    TotalTables: string,
    Tables_Failed: string,
    Tables_Success: string,
    Tables_YetToStart: string
  }
}

//code extraction and assessment
//GetIndividualLogs
export interface GetIndividualLogs {
  projectId: string,
  operationType: string,
  action: string,
  row_id: string,
  runno: string,
  operationName: string
}
//GetOperations
export interface GetOperations {
  projectId: string,
  OperationType: string,
}
//setRedisCache
export interface assessmentsetRedisCache {
  projectId: string,
  option: number,
  schema: string,
  connection: string,
  Object: string,
  srcConId: string,
  jobName: string,
  iteration: string,
}
//ReportsFilterByTime
export interface ReportsFilterByTime {
  path: string,
  hrs: any,
  validationFlag: boolean
}
//deleteTableData
export interface deleteTableData {
  projectId: string,
  requestId: string
}
//setRedisCache
export interface setRedisCache1 {
  projectId: string,
  option: number,
  schema: string,
  connection: string,
  Object: string,
  srcConId: string,
  jobName: string,
  iteration: string,
}
//projectDocumentsDetailSelect
export interface projectDocumentsDetailSelect {
  projectID: string
  containerName: string,
  folderName: string
}
export interface req {
  projectId: string,
  containerName: string,
  folderName: string
}
export interface reqData {
  Table1: {
    request_id: string,
    connection_id: string,
    operation_id: string,
    schemaname: string,
    status: string,
    remarks: string,
    objectname: string,
    objecttype: string,
    others: string,
    created_by: string,
    created_dt: string,
    updated_by: string,
    updated_dt: string,
    operation_name: string,
    conname: string

  }
}
export interface GetIndividual {
  Table1: {
    exelog_id: string,
    operation_type: string,
    operation_name: string,
    objecttype_name: string,
    schemaname: string,
    iteration: string,
    activity_date: string,
    migtask: string,
    error_message: string
  }
}
export interface GetRunno {
  Table1: {
    iteration: string,
    dbschema: string,
    iter_schema_operation: string,
    schemaname: string,
    run_id: string,
    operation_name: string,
    objecttype_name: string,
    operation_type: string
  }
}
export interface deleteTabledata {
  Table1: {
    v_status: string
  }
}

//code migration
export interface SchemaListSelect1{
  projectId:string,
  connectionId:string
}

//data compare
//GetTablesByschema
export interface GetTablesByschema{
  schemaname:string,
  conId:string,
  }
  //insertTablesCommand
export interface insertTablesCommand1{
  projectId: string,
  operation: string,
  srcId: string,
  tgtId: string,
  schema: string,
  chunkPercentage:string,
  filePercentage:string,
  chunkSize:string,
  level:string,
  largeTableConcurrency:string,
  tablename:string,
  method:string,
  fileName:string 
}
/*--- DBA Module Start ---*/
export interface getdbs{
  ConId:string
}

export interface getroles{
  ConId:string,
  dbname:string
}
export interface GetSearchRolesInfo{
  ConId:string, 
    dbname: string,
      role: string
}
export interface  GetSearchRolesInfo{
  ConId:string, 
    dbname: string,
      role: string
}
export interface dbsresponse{
  dbname:string
}
export interface dbresList{
  dblist:dbsresponse
}

/*--- DBA Module End ---*/

export interface downloadDocs{
  path:string
}

export interface uploadDocs extends downloadDocs{
  file:string
}


export interface documentsList{
  created_dt:string,
  fileName:string,
  filePath:string
}

export interface deleteFile{
  message:string
}

export interface insertSchema{
  projectId:string,
  conId:string
}

export interface GetReqData{
  projectId: string,
      operationType: string
}

export interface SchemaListSelect{
  projectid: string,
  connectioId: string
}

export interface Execute{
projectId: string,
srcId : string,
tgtId : string,
fromDate : Date
toDate  : Date,
category : string,
task: string
}
export interface Submit{
  conId: string,
  schemas: string,
  option: number,


}


export interface GetDuplicateIndexData{
  conid: string,
  schema: string,
  option: string,
}

//GetExceution
export interface GetExceution{
  projectId: string,
  jobName: string,
  fileName:string
}
export interface TestcycleReqObj {
  TgtConId: string,
  BatchId: string,
  option: string,
  task: string,
  projectId: string,
  SrcConId?:string,
  flag?:string,
}
import { Component } from '@angular/core';
import { HotToastService } from '@ngxpert/hot-toast';
import { DbaService } from '../../../../services/dba.service';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators, } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import jspdf from 'jspdf';

declare let $: any;

@Component({
  selector: 'app-dba-monitoring',
  standalone: true,
  imports: [FormsModule, CommonModule, ReactiveFormsModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './dba-monitoring.component.html',
  styles: ``
})
export class DbaMonitoringComponent {
  projectId: string;
  pageName: string = '';
  tgtId: any;
  Categories: any;
  tgtList: any;
  tgtValue: string = '';
  tgtcategory: string = '';
  ConsList: any;
  ref_spin: boolean = false;
  pageNumber: number = 1;
  pageNumber1: number = 1;
  pageNumber2: number = 1;
  searchText: string = '';
  p: number = 1;
  p1: number = 1;
  selectedData: any[] = [];
  selectedDatas: any[] = [];
  selectedDatass: any[] = [];
  selectedDatasss: any[] = [];
  selectedSnos: number[] = [];
  selectAll: boolean = false;

  // Dba insert related form
  DBAInsertForm: any = this.formBuilder.group({
    categoryn1: ['', Validators.required],
    scriptfunctionname: ['', Validators.required],
    categoryname: ['',],
    scriptquery: ['', Validators.required],
  });
  DBAExecuteForm: any = this.formBuilder.group({
    tgtconnection: ['', Validators.required],
    categoryn: ['', Validators.required],
    scriptquery: ['', Validators.required],
  });
  DBAReports: any = this.formBuilder.group({
    tgtconnection: ['', Validators.required],
    categoryn: ['', Validators.required],
    scriptquery: ['', Validators.required],
    runnumber: ['', Validators.required],
  });

  constructor(private toast: HotToastService, private dbaservice: DbaService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
  }
  ngOnInit(): void {
    this.pageName = this.route.snapshot.data['name'];
    this.GetConsList();
    this.DBATaskList();
    this.GetDBARunIds();
  }

  // Validation Method
  get validate(): any {
    return this.DBAInsertForm.controls;
  }
  get validates(): any {
    return this.DBAExecuteForm.controls;
  }
  get validates1(): any {
    return this.DBAReports.controls;
  }
  categorySelect(value: any) {
    if (value == "New") {
      this.DBAInsertForm.controls['categoryname'].setValidators([Validators.required])
      this.DBAInsertForm.controls['categoryname'].updateValueAndValidity()
    }
  }
  // Script Insertion
  add_spin: Boolean = false;
  ScriptInsert: any;
  DBAScriptInsertion(value: any) {
    this.add_spin = true
    let tempcategory = ""
    if (value.categoryn1 != "New") {
      tempcategory = value.categoryn1
    }
    else {
      tempcategory = value.categoryname
    }
    const obj = {
      name: value.scriptfunctionname,
      category: tempcategory,
      scriptTEXT: value.scriptquery.replace(/[\r\n]+/g, ' ')
    }
    this.dbaservice.DBAScriptInsertion(obj).subscribe((data: any) => {
      this.ScriptInsert = data.Table1[0].result;
      if (this.ScriptInsert === 'Success') {
        this.toast.success("New Script Insertion successfully completed");
      } else if (this.ScriptInsert === 'Failed') {
        this.toast.error('New Script Unable to Insert');
      } else {
        this.toast.error('Something went wrong!');
      }
      this.add_spin = false
      this.DBATaskList();
    }, (error) => {
      this.add_spin = false
    })
  }
  CategoryLists: any;
  DBACategoryList() {
    const obj = {
      category: this.Categories.toString(),
    }
    this.dbaservice.DBACategoryName(obj).subscribe((data: any) => {
      this.CategoryLists = data.Table1;
    })
  }
  DBAtaskData: any;
  DBAtaskDataWithNew: any;
  DBATaskList() {
    const obj = {
      projectId: this.projectId.toString(),
    }
    this.dbaservice.DBATaskList(obj).subscribe((data: any) => {
      this.DBAtaskData = this.removeDuplicates(data.Table1, 'category');
      this.DBAtaskDataWithNew = this.removeDuplicates(data.Table1, 'category');
      let ob = {
        category: "New",
      }
      this.DBAtaskDataWithNew.unshift(ob);
    })
  }

  removeDuplicates(array: any[], key: string): any[] {
    return array.filter((value, index, self) =>
      index === self.findIndex((t) => (
        t[key] === value[key]
      ))
    );
  }
  /*--- Get Operation List   ---*/

  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
  }

  selCategory(value: any) {
    this.Categories = value;
    //  this.DBAtaskData.filter((el: any) => { return el.category == value ? this.tgtcategory = el.name : '' })
    this.DBACategoryList();
  }

  /*--- GetConsList ---*/
  GetConsList() {
    this.dbaservice.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.ref_spin = false
    })
  }


  queryList: { taskname: string, query: string }[] = []; // This will hold the query list in required format
  executeBtn: boolean = false;
  currentlyChecked(isChecked: any) {
    if (isChecked.target.checked) {
      this.executeBtn = true;
    } else {
      this.executeBtn = false;
    }
  }

  // This method is triggered when any checkbox is clicked
  onCheckboxChange(con: any) {
    if (con.isSelected) {
      // Add to selected data and query list
      this.selectedDatas.push(con.script);
      this.selectedData.push(con.name);
      this.selectedDatasss.push(con.id);
      this.queryList.push({
        taskname: con.name,  // Pass the task name
        query: con.script    // Pass the query
      });
    } else {
      // Remove from selected data and query list
      const dataIndex = this.selectedData.indexOf(con.name);
      const scriptIndex = this.selectedDatas.indexOf(con.script);
      const idIndex = this.selectedDatasss.indexOf(con.id);
      const queryIndex = this.queryList.findIndex(q => q.taskname === con.name);

      if (dataIndex !== -1) {
        this.selectedData.splice(dataIndex, 1);
      }
      if (scriptIndex !== -1) {
        this.selectedDatas.splice(scriptIndex, 1);
      }
      if (idIndex !== -1) {
        this.selectedDatasss.splice(idIndex, 1);
      }
      if (queryIndex !== -1) {
        this.queryList.splice(queryIndex, 1);  // Remove corresponding query
      }
    }

  }

  toggleAllSelection() {
    // Set all rows' isSelected based on the state of selectAll checkbox
    this.CategoryLists.forEach((con: { isSelected: boolean; }) => {
      con.isSelected = this.selectAll;
    });

    // Update selectedData, selectedDatas, selectedDatasss based on selectAll state
    if (this.selectAll) {
      this.selectedData = this.CategoryLists.map((con: { name: any; }) => con.name);  // assuming name is unique
      this.selectedDatas = this.CategoryLists.map((con: { script: any; }) => con.script);  // assuming script is unique
      this.selectedDatasss = this.CategoryLists.map((con: { id: any; }) => con.id);  // assuming id is unique
      this.queryList = this.CategoryLists.map((con: { name: any; script: any; }) => ({
        taskname: con.name,
        query: con.script
      }));
    } else {
      this.selectedData = [];
      this.selectedDatas = [];
      this.selectedDatasss = [];
      this.queryList = [];
    }

  }

  // This method helps with tracking the items in `ngFor`
  trackByFn(index: number, item: any): number {
    return item.id;  // Assuming `id` is unique for each item
  }


  onKey() {
    this.pageNumber = 1;
    this.p = 1;
    this.p1 = 1;
  }

  queryexe: any;
  queryspin: boolean = false;
  showReport: boolean = false
  insertScripts() {
    const obj = {
      conid: this.tgtId,
      queryList: this.queryList,
      categoryname: this.Categories.toString(),
    }
    this.queryspin = true;
    this.dbaservice.DBAScriptQueryExecution(obj).subscribe((data: any) => {
      this.queryexe = data;
      this.GetDBARunIds()
      this.queryspin = false;
      if (this.queryexe == "200") {
        this.toast.success("Query Execution Succefully Completed!");
        this.showReport = true

      }
      else {
        this.showReport = false
        this.toast.error("Something went wrong during execution. Please try again!");
      }
    })
  }
  dbarunids: any = []
  GetDBARunIds() {
    let obj = {
      conId: this.tgtCon,
      categoryName: this.Categories,
      taskName: this.task_Name.toString() //tasknames by coma seperate
    }
    this.dbaservice.GetDBARunids(obj).subscribe((data: any) => {
      this.dbarunids = data['Table1'];
    })
  }
  p3: number = 1
  datachange4: any
  dbaExecResults: any = []
  jobj: any
  keys: any = []
  categoryName: any
  taskName: any
  executedDate: any
  hidelabels: boolean = false
  GetDBAExecResults(runId: any) {
    this.dbaservice.GetDBAExecResultsByRunid(runId).subscribe((data: any) => {
      this.dbaExecResults = data['Table1'];
      this.jobj = JSON.parse(this.dbaExecResults[0].execution_result)
      this.keys = Object.keys(this.jobj[0]);
      this.categoryName = this.dbaExecResults[0].category_name
      this.taskName = this.dbaExecResults[0].task_name
      this.executedDate = this.dbaExecResults[0].executed_at
      this.hidelabels = true
    })
  }
  pdfspin: any
  generatePDF() {
    let html1 = document.getElementById('tblexample') as HTMLCanvasElement;
    let divHeight = $('#tblexample').height();
    let divWidth = $('#tblexample').width();
    let ratio = divHeight / divWidth;
    this.pdfspin = true;
    html2canvas(html1).then(canvas => {
      let pdf = new jspdf('p', 'pt', [canvas.width, canvas.height]);
      let imgData = canvas.toDataURL("image/jpeg", 1.0);
      let width = pdf.internal.pageSize.getWidth();
      let height = pdf.internal.pageSize.getHeight();
      height = ratio * width;
      pdf.addImage(imgData, 'JPEG', 0, 0, width - 20, height - 10);
      let docname = "TestCycleExecutedData_" + this.projectId.toString();
      pdf.save(docname + '.pdf');
      if (html1) {
        this.pdfspin = false;
      }
    },
      (error) => {
        this.pdfspin = false;
      });

  }
  TestCaseForm: any
  exportexcel(): void {
    var dt = new Date().toLocaleDateString('en-US');
    var fileName = 'DBAExecutionReport_' + dt + '.xlsx';

    /* pass here the table id */
    // let element = document.getElementById('tblexample');
    // const ws: XLSX.WorkSheet = XLSX.utils.table_to_sheet(element);
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.jobj, {
      header: this.keys // Replace with your column keys
    });

    /* generate workbook and add the worksheet */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    /* save to file */
    XLSX.writeFile(wb, fileName);
  }
  openPopup() {
    this.TestCaseForm.reset();
  }
  query: any
  updateRecord(value: any) {
    this.query = value.query
  }
  tgtCon: any
  category_Name: any
  task_Name: any
  selectTGTCon(value: any) {
    this.tgtCon = value
  }
  selctCategoryName(value: any) {
    this.category_Name = value
    this.Categories = value
    this.DBACategoryList()

  }
  selectTaskName(value: any) {
    this.task_Name = value

  }
}


import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { HotToastService } from '@ngxpert/hot-toast';
import * as XLSX from 'xlsx';
import { PerofmanceTestingService } from '../../../../services/performanceTesting.service';
import { ActivatedRoute } from '@angular/router';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';

@Component({
  selector: 'app-performance-tuning-insights',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './performance-tuning-insights.component.html',
  styles: ``
})
export class PerformanceTuningInsightsComponent {

  BloatedForm:any = this.formBuilder.group({
    schemaName:['', Validators.required],
    tgtconn: ['', Validators.required],
  });
  hidedata: boolean = true;
  data: boolean = false;
  projectId: string;
  ref_spin: boolean = true;
  ref_spin1: boolean = false;
  ref_spin2: boolean = false;
  ref_spin3: boolean = false;
  ref_spin4: boolean = false;
  tabledata: any;
  userData: any = [];
  schemaList: any = [];
  schemaName: any = [];
  schemanames: any = [];
  schemanamess: any = [];
  selectedschemas: any = [];
  selectedschemas1: any = [];
  selectedschemas2: any = [];
  selectedschemas3: any = [];
  execProjectForm: any;
  selectFile: any;
  fileName: string = '';
  uploadfileSpin: boolean = false;
  fileAdd: boolean = false;
  projectDocuments: any;
  projectDocumentFilter: any;
  pageNumber: number = 1;
  searchText: string = '';
  exe: any;
  contype: string = ""
  dup: string = ""
  mon: string = ""
  tgtId: any
  tgtValue: string = ''
  z: any;
  i: any;
  selectedConname: any
  conName: any
  conId: any;
  ConsList: any;
  tgtList: any
  selectedCategory: string = ""
  hideDrpdwn: boolean = false
  hideD: boolean = false
  value: any;
  selectedDuplicate: string = "";
  selectedMonitor: string = "";
  P2: number = 1;
  P3: number = 1;
  perSchema: any;
  perSchemas:any;
  selectedItems = [];
  selectedObjItems1 = [];
  selectedObjItems2 = [];
  selectedObjItems3 = [];
  perSchema1: any;
  exe1: any;
  TableData: boolean = false;
  analyzesearchlist: any;
  Exespinner: boolean = false;
  Monspinner: boolean = false;
  excelSpin: boolean = false;
  hideMon: boolean = false;

  pageName: string = ''
  indexMonitor: any;
  Monitor: boolean = false;
  testing: any;
  hideLog: boolean = false;
  hideTable: boolean = false;
  Callstm: any;
  task: any;
  ParTable: any;
  ConId: any;
  stat: boolean = false;
  PartitonTableData: any;
  hideQuery: boolean = false;
  selectedLog: string = "";
  hideL: boolean = false;
  hideS: boolean = false;
  hidedate: boolean = false;
  hideTab: boolean = false;

  
  get f(): any {
    return this.BloatedForm.controls;
  }


  constructor(private toast: HotToastService, private performanceservices: PerofmanceTestingService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    
  }

  /*--- Page Title ---*/
  pageTitle: string = "Documents"
  pageIcon: string = "assets/images/documents.svg"


  ngOnInit(): void {
    this.GetConsList();
    this.pageName = this.route.snapshot.data['name'];
  }
  



  /*--- Schema   ---*/
  GetperfSchemas() {
    const obj = {
      Conid: this.tgtId,
    }
    this.performanceservices.PerfSchemanamewithAll(obj).subscribe((data: any) => {
      this.perSchemas = data;
     // this.perSchema = this.perSchemas.sort((a:any, b:any) => a.schemaname.localeCompare(b.schemaname));
this.perSchema = this.perSchemas.sort((a: any, b: any) => {
  // If 'a' or 'b' is 'all', place it at the beginning
  if (a.schemaname === 'all') return -1; // 'all' should come first
  if (b.schemaname === 'all') return 1;  // 'all' should come first

  // Otherwise, perform alphabetical sorting
  return a.schemaname.localeCompare(b.schemaname);
});
    }) 
  }

  /*--- SelectContype   ---*/
  selectContype(value: string) {
    this.contype = value;
    if (value == "0") {
      this.hidedata = true;
      this.data = false;
    }
    else {
      this.hidedata = false;
      this.data = true;
    }
  }

  

  

  /*--- Get Operation List   ---*/
  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' });
    this.GetperfSchemas();
  }
  selTgtIds(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' });
    this.GetperfSchemas();
  }
  selTgttId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' });
    this.GetperfSchemas();
    this.GetRatioIndexes();
  }
  // selTgttIdd(value: any) {
  //   this.tgtId = value
  //   this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' });
  //   this.GetRatioIndexes();
  // }

  

  /*--- GetReqTableData   ---*/
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.performanceservices.GetReqData(obj)?.subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.ConsList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_name == "Storage_Objects"
        })
        //console.log(this.tabledata)
      }
    })
  }
  
  ftsFiles: any
  /*--- GetConsList ---*/
  GetConsList() {
    this.performanceservices.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.getreqTableData()
      this.ref_spin = false
    })
  }

  /*--- Select Log ---*/
  selectLog(value:string){
    this.selectedLog = value
    if(value == "0")
      {
        this.hideS= true;
      }
      else {
        this.hideS= false;
      }
      if(value == "1")
        {
          this.hideL= true;
        }
        else {
          this.hideL= false;
        }
  }

  Showlogsdata:any
  selecteddate:any;
  selectschema:any;
  selectedprocedurename:any;
  SubmitShowLogs(value:any) {
    debugger;
     this.hideTab=true;
     if (value.fromDate ===null) {
      this.selecteddate= value.fromDate;
    }
    else
    {
      this.selecteddate = value.fromDate;
    }
    if (value.ProcudureName ===null) {
      this.selectedprocedurename= value.ProcudureName;
    }
    else
    {
      this.selectedprocedurename = value.ProcudureName;
    }
    if (value.connectionType ===null) {
      this.selectschema= value.connectionType;
    }
    else
    {
      this.selectschema = value.connectionType;
    }
     const obj = {
       Conid:this.tgtId,
       schema:this.selectschema,
       procedure:this.selectedprocedurename,
       exec_date: this.selecteddate,
     }
    this.performanceservices.SubmitShowLogs(obj).subscribe((data: any) => {
      this.Showlogsdata = data;
   
    })
  }

  
  formatDate(date: string): string{
    debugger;
    let m = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
      , o = new Date(date)
      , S = o.getDate()
      , x = m[o.getMonth()]
      , u = o.getFullYear();

      if(S<10)
        {
          S=parseInt("0"+S)
        }
    return `${u}-${x}-${S}`
  }
  
  
  /*--- Log explain method ---*/
  InputCall(value:any) {
    const obj = {
      task:"Log_Explain_Plan",
      projectId: this.projectId.toString(),
      operation:"Database",
      option: parseInt(this.contype),
      target_connection_id: this.tgtId,
      callStatement: value.callstatement,
      tgtId: this.tgtId,
    }
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.Callstm = data
      this.toast.success(data.message)
    })

  }

UsedIndexesData:any;
UnUsedIndexesData:any;
BloatIndexesData:any;
BloatIndexesExcelData:any;
RatioIndexesData:any;
GetUsedIndexes(value:any) {
  const obj = {
      Conid: this.tgtId,
      schemaname: value.schemaName.toString(),
      Days: value.number1.toString(),
  }; 
  this.ref_spin1=true;
  this.UsedIndexesData=[];
  this.performanceservices.GetUsedIndexes(obj).subscribe((data: any) => {
  this.UsedIndexesData = data;  
  this.ref_spin1=false;
  })
}

schemanam=['null'];
GetUnUsedIndexes(value:any) {
  const obj = {
      Conid: this.tgtId,
      schemaname: value.schemaNames.toString(),
      Days: value.number2.toString(),
  };
  this.ref_spin2=true;
  this.UnUsedIndexesData=[];
  this.performanceservices.GetUnusedIndexes(obj).subscribe((data: any) => {
  this.UnUsedIndexesData = data;  
  this.ref_spin2=false;
  })
}

dataFetched:boolean = true
GetBloatedIndexes(value:any) {
  this.dataFetched = false
  const obj = {
    Conid:this.tgtId,
    schemaname:value.schemaName.toString(),
  }   
  this.ref_spin3=true;
  this.BloatIndexesData=[];
  this.performanceservices.BloatedIndexes(obj).subscribe((data: any) => {
  this.BloatIndexesData = data;  
  this.ref_spin3=false;
  })
}

GetBloatedIndexExcel(value:any) {
  const obj = {
    Conid:this.tgtId,
    schemaname:value.schemaName.toString(),
  }   
  this.performanceservices.BloatedIndexExcel(obj).subscribe((data: any) => {
  this.BloatIndexesExcelData = data;  
  })
}

GetRatioIndexes() {
  const obj = {
    Conid:this.tgtId,
  }  
  this.ref_spin4=true;
  this.performanceservices.GetRatioIndexes(obj).subscribe((data: any) => {
  this.RatioIndexesData = data[0].cachehitratio;  
  this.ref_spin4=true;
  })
}

selectSchema(value: any) {
  this.selectedschemas = value;

}
selectSchema1(value: any) {
  if(value.schemaName==='all')
  {
    this.selectedschemas1 = null;
  }
  else{
    this.selectedschemas1 = value;
  }
  
  
}
selectSchem2(value: any) {
  this.selectedschemas2 = value;
}
selectSchema3(value: any) {
  this.selectedschemas3 = value;
}

fileNamee2 = 'UnUsedIndexes.xlsx';
UnUsedIndexes: any = []
   excelSpinn2: any = false;
   exportexcelUnUsedIndex(): void {
    this.UnUsedIndexes = [];
    this.excelSpinn1 = true;
    var test = this.UnUsedIndexesData;
    for (var el of test) {
      var newEle: any = {};
      newEle.IndexName = el.indexname;
      newEle.SchemaName = el.schemaname;
      newEle.TableName = el.tablename;
      newEle.IndexSize = el.index_size;
      newEle.TotalScans = el.totalscans;
      newEle.TotalFetch = el.totalfetch;
      this.UnUsedIndexes.push(newEle);
    }
     const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.UnUsedIndexes);
     const wb: XLSX.WorkBook = XLSX.utils.book_new();
     XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
     XLSX.writeFile(wb, this.fileNamee2);
   }

   excelSpinn1: any = false;
   fileName1 = 'UsedIndexes.xlsx';
   UsedIndexes: any = []
   exportexcelusedIndex(): void {
     this.UsedIndexes = [];
     this.excelSpinn1 = true;
     var test = this.UsedIndexesData;
    for (var el of test) {
      var newEle: any = {};
      newEle.IndexName = el.indexname;
      newEle.Schema = el.schemaname;
      newEle.TableName = el.tablename;
      newEle.IndexSize = el.index_size;
      newEle.TotalScans = el.totalscans;
      newEle.Totalfetch = el.totalfetch;
      this.UsedIndexes.push(newEle);
    }
     const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.UsedIndexes);
     const wb: XLSX.WorkBook = XLSX.utils.book_new();
     XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
     XLSX.writeFile(wb, this.fileName1);
   }

   fileNamee3 = 'BloatIndex.xlsx';
   BloatIndex: any = []
   excelSpinn3: any = false;
   exportexcelShowlogsBloat(): void {
     this.BloatIndex = [];
     this.excelSpinn3 = true;
     var test = this.BloatIndexesExcelData;
     for (var el of test) {
       var newEle: any = {};
        //  newEle.IndexName = el.indexname;
      //  newEle.TableName = el.tablename;
      //  newEle.Schema = el.schemaname;
      //  newEle.IndexSize = el.indexsize;
          newEle.VacumTable = el.vacumtable;
      //  newEle.Reindex = el.reindex;
       this.BloatIndex.push(newEle);
     }
     const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.BloatIndex);
     const wb: XLSX.WorkBook = XLSX.utils.book_new();
     XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
     XLSX.writeFile(wb, this.fileNamee3);
   }
   
}

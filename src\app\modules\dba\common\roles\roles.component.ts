import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DbaService } from '../../../../services/dba.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonModule } from '@angular/common';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import * as XLSX from 'xlsx';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-roles',
  standalone: true,
  imports: [CommonModule,NgSelectModule, FormsModule, CommonModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './roles.component.html',
  styles: ``

})
export class RolesComponent {
  roleForm: any
  projectId: any
  ConsList: any
  tableHide: boolean = false
  pageNumber: number = 1;
  p2: number = 1;
  page1: number = 1;
  pi: number = 10;
  searchText1: string = '';
  fileResponse: any;
 pageName:string = ''

  constructor(private fb: FormBuilder, private dba: DbaService,private route: ActivatedRoute) {
    
    
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
   
  }
  ngOnInit(): void {

    this.GetConsList()
    this.roleForm = this.fb.group({
      connection: ['', [Validators.required]],
      db: ['', [Validators.required]],
      rolename: ['',Validators.required],

    })
    this.pageName = this.route.snapshot.data['name'];
  }
  selectedItems = [];
  checkAll = [];
  schemaList = [];
  spinner: boolean = false
  get getroleControl() {
    return this.roleForm.controls;
  }
  // Connection details
  GetConsList() {
    this.dba.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }

  // Database details
  dblist: any
  connId: any
  Getdbname(conId: string) {
    this.dblist = []
    this.connId = conId
    this.dba.getdbnames(conId).subscribe((data: any) => {
      this.dblist = data
    });
  }

  // Roles details
  rolelist: any
  selecteddbname: any
  Getroles(dbname: string) {
    this.selecteddbname = dbname
    let obj = {
      ConId: this.connId,
      dbname: dbname
    }
    this.dba.getrolenames(obj).subscribe((data: any) => {
      this.rolelist = data
      this.rolelist.filter((item: any) => {
        item.type = "ALL"
      })
      // this.rolelist[0].push(obj)
      //console.log(this.rolelist)
    });

  }
  selectedRole: string = ""
  selectRole(value: any) {
    var temp: any = []
    if (value.toString() == "ALL") {
      this.selectedRole = ""
      this.rolelist.filter((item: any) => {
        temp.push("'" + item.rolename + "'")
      })
      //console.log(temp)
      this.selectedRole = temp.toString()
    }
    else {
      value.filter((item: any) => {
        temp.push("'" + item + "'")
      })
      //console.log(temp)

      this.selectedRole = temp.toString()
    }

  }

  get f() {
    return this.roleForm.controls;
   
  }
  fileName = 'Roles.xlsx';
  testing: any = []
  excelSpin: any = false;
  exportexcel(): void {
    this.testing = []
    this.excelSpin = true
    var test = this.searchRoleList
    for (var el of test) {
      var newEle: any = {};
      newEle.rolename = el.roleName;
      newEle.memberof = el.memberof[0];
      this.testing.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);

    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName);

  }

  // Search role button
  searchRoleList: any
  GetSearchRolesInfo() {
    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname,
      role: this.selectedRole
    }
    this.spinner = true
    this.dba.fetchRoleInfo(obj).subscribe((data: any) => {
      this.searchRoleList = data
      if (this.searchRoleList.length > 0) {
        var i=1;
        this.searchRoleList.filter((item:any)=>{
          item.sno=i
          i++
        })
        //console.log(this.searchRoleList)
        this.tableHide = true
        this.spinner = false
      }
      
    });

  }


}


import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder ,FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { HotToastService } from '@ngxpert/hot-toast';
import { DeploymentService } from '../../../../services/deployment.service';
import * as XLSX from 'xlsx';

declare let $: any;
@Component({
  selector: 'app-delta-files',
  standalone: true,
  imports: [BreadCrumbComponent,ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './delta-files.component.html',
  styles: ``
})

export class DeltaFilesComponent {
  SourceDeltafilename: any;
  Sourcefilename:any;
  Targetfilename: any;
  //runNumbers: any;
  piA: number = 10;
  piB: number = 10;
  p: number = 1;
  p1: number = 1;
  p2: number = 1;
  page: number = 1;
  page1: number = 1;
  page2: number = 1;
  pages:number=1;
  pi: number = 10;
  datachange1: any;
  uploadfileSpin: boolean = false;
  pageName:string = '';
  project_name: any
  projectId: any
  getRole: any;
  UserInfo: any;
  SourceFileName: string = '';
  TragetFileName: string = '';
  constructor(public formBuilder: FormBuilder,
    private route: ActivatedRoute,
    public deployment: DeploymentService,
    private toast: HotToastService
  ) {
    this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    let userJson = localStorage.getItem('userData') as string;
    this.UserInfo = JSON.parse(userJson);
    this.pageName = this.route.snapshot.data['name'];
    this.Sourcefilename = localStorage.getItem('Sourcefilename');
    this.SourceDeltafilename = localStorage.getItem('SourceDeltaFile');
    this.Targetfilename = localStorage.getItem('Targetfilename');
  }

  ngOnInit(): void {
    this.deltafilesform = this.formBuilder.group({
      runnumber: ['', [Validators.required]], 
      scschema: ['', [Validators.required]],    
    });
    this.uploadForm=this.formBuilder.group({
      file1: ['', [Validators.required]], 
    });
    this.uploadsForm=this.formBuilder.group({
      file2: ['', [Validators.required]], 
    });
    this.getRunNumbers();
    }
 
  selectFile: any;
  deltafilesform:any;
  uploadForm:any;
  uploadsForm:any;
  fileName: string = '';
  
  get validate() {
    return this.deltafilesform.controls;
  }
  get validates() {
    return this.uploadForm.controls;
  }
  get validatess() {
    return this.uploadsForm.controls;
  }

  iteration: any
  selectIteration(iter: any) {
    this.iteration = iter;
    this.getFiles();
  }
  runNumbersData: any;
  getRunNumbers() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbersData = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }

  fileResponse: any;
  spin_dwld: any;
  downloadFiles(path:string,filename:string) {
    var pth=path.split("/mnt/pypod/")[1]
    pth="/mnt/eng/"+pth
    this.deployment.downloadLargeFiles(pth).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = filename;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }

  run:any;
  selectRunnumber(itera: any) {
    this.run = itera;
    this.GetSCSchemaList(itera);
  }
  schemaNamess:any;
  GetSCSchemaList(runno: any) {
    this.schemaNamess = []
    let obj = {
      projectId: this.projectId,
      runno: runno
    }
    this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
      this.schemaNamess = data['Table1'];
    });
  }
  deltaschema:any;
  sourceSelected: boolean = false
  sourceSel(value: string) {
    value != '' ? this.sourceSelected = true : this.sourceSelected = false;
    this.deltaschema=value;
    this.selSchema(value);
    this.GetSourceFilePath();
  }
  
  schemaSel:boolean = false
  selSchema(value:any){
    if(value != ''){
      this.schemaSel = true
    }else{
      this.schemaSel = false
    }
  }

  GetSourceFilePathData:any;
  GetSourceFilePath()
  {
    const obj = {
      iteration:this.run,
      projectId: this.projectId.toString(),
      objectName: null,
      type:"S",
      schemaName:this.deltaschema.toString(),
    }
    this.deployment.GetSourceFilePath(obj).subscribe((data: any) => {
      this.GetSourceFilePathData = data['Table1'];
      for (const element of this.GetSourceFilePathData) {
        let schemaobjtype = element.baseline_filepath.split('/')[6] + "/" + element.baseline_filepath.split('/')[7]
        element.baselineFile = element.objectname + "_baseline.sql"
        element.currentFile = element.objectname + "_current.sql"
        element.htmlFile = element.objectname + ".html"
        element.schemaobj = schemaobjtype
        element.lastmodified = ""
        element.basefilename = element.objectname
      }
      this.GetTargetPaths(this.deltaschema);
    });
  }
  selectedSchema:any
  
 
  SourceFilePath: any
  TargetFilePath: any
  FilePaths(src: any, tgt: any) {
    this.SourceFilePath = src;
    this.TargetFilePath = tgt;
  }
  sendValueSrc($event: any): void {
    //this.readThisSrc($event.target);
  }
  sendValuetgt($event: any): void {
    // this.readThistgt($event.target);
  }
  NoDataHide: boolean = false;
  targetPaths: any
  istgtFilepathsEmpty: boolean = false
  GetTargetPaths(schema: any) {
      const obj = {
        iteration:this.run,
        projectId: this.projectId.toString(),
        objectName: null,
        type:"T",
        schemaName:this.deltaschema.toString(),
      }
    this.deployment.GetSourceFilePath(obj).subscribe((data: any) => {
      this.targetPaths = data['Table1']
      if (this.targetPaths != undefined) {
        if (this.targetPaths.length > 0) {
          for (const element of this.targetPaths) {
            element.baselineFile = element.objectname + "_baseline.sql"
            element.currentFile = element.objectname + "_current.sql"
            element.htmlFile = element.objectname + ".html"
          }
          var count = 0
          for (let y = 0; y < this.GetSourceFilePathData.length; y++) {
            for (let z = 0; z < this.targetPaths.length; z++) {
              if (this.GetSourceFilePathData[y].objectname == this.targetPaths[z].objectname) {
                this.GetSourceFilePathData[y].target_baseline_filepath = this.targetPaths[z].baseline_filepath
                this.GetSourceFilePathData[y].target_current_filepath = this.targetPaths[z].current_filepath
                this.GetSourceFilePathData[y].target_html_filepath = this.targetPaths[z].delta_filepath
                this.GetSourceFilePathData[y].target_baselineFile = this.targetPaths[z].baselineFile
                this.GetSourceFilePathData[y].target_currentFile = this.targetPaths[z].currentFile
                this.GetSourceFilePathData[y].target_htmlFile = this.targetPaths[z].htmlFile
                this.GetSourceFilePathData[y].target_objectname = this.targetPaths[z].objectname
                this.GetSourceFilePathData[y].src_tgt_diff_file = this.targetPaths[z].htmlFile
                this.GetSourceFilePathData[y].src_tgt_diff_path = this.targetPaths[z].src_tgt_diff_path
                if (this.GetSourceFilePathData[y].target_baseline_filepath != "" || this.GetSourceFilePathData[y].target_current_filepath != "") {
                  this.istgtFilepathsEmpty = true
                }
                if (this.GetSourceFilePathData[y].target_baseline_filepath != "" || this.GetSourceFilePathData[y].target_current_filepath != "" || this.GetSourceFilePathData[y].target_html_filepath != "" ||
                  this.GetSourceFilePathData[y].src_tgt_diff_path != "" || this.GetSourceFilePathData[y].baseline_filepath != "" || this.GetSourceFilePathData[y].current_filepath != "" ||
                  this.GetSourceFilePathData[y].delta_filepath != "") {
                  count++
                }


              }

            }
          }
          if (count != 0) {
            this.NoDataHide = true;
          }
          else {
            this.NoDataHide = false;
          }
        }
      }
      else {
        var count1 = 0
        for (const element1 of this.GetSourceFilePathData) {
          if (element1.baseline_filepath != "" || element1.current_filepath != "" ||
            element1.delta_filepath != "") {
            count1++
          }
        }
        if (count1 != 0) {
          this.NoDataHide = true;
        }
        else {
          this.NoDataHide = false;
        }

      }
    })
  }
  
  excelData:any=[]
  downloadExecel()
  {
    for(var el of this.GetSourceFilePathData)
    {
      var obj:any={}
      obj.RunNumber= this.iteration
      obj.Schema=this.selectedSchema
      obj.ObjectName=el.objectname
      obj.ObjectType=el.objecttype
      obj.SourceBaseLineFile=""
      obj.SourceCurrentFile=""
      obj.SourceDifferenceFile=""
      if(el.baseline_filepath!="")
      {
        obj.SourceBaseLineFile=el.baselineFile
      }
      if(el.current_filepath!="")
      {
        obj.SourceCurrentFile=el.currentFile
      }
      if(el.delta_filepath!="")
      {
        obj.SourceDifferenceFile=el.htmlFile
      }
      if(el.baseline_filepath!="" && el.current_filepath!="" && el.delta_filepath=="")
      {
        obj.status="Matching"
      }
      if(el.baseline_filepath!="" && el.current_filepath!="" && el.delta_filepath!="")
      {
        obj.status="Modified"
      }
      if(el.baseline_filepath=="" && el.current_filepath!="" && el.delta_filepath=="")
      {
        obj.status="New"
      }
      if(el.baseline_filepath!="" && el.current_filepath=="" && el.delta_filepath=="")
      {
        obj.status="Deleted"
      }
      this.excelData.push(obj);
    }
    this.exportexcel()
  }
  fileNames = 'SourceFiles.xlsx';
testing:any=[]
exportexcel(): void {
  /* pass here the table id */
//  let element = document.getElementById('example');
  var test=this.excelData
  const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(test);
  /* generate workbook and add the worksheet */
  const wb: XLSX.WorkBook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  /* save to file */
  XLSX.writeFile(wb, this.fileNames);
}

/*--- File upload    ---*/ 

onFileSelected(event: any) {
  const file: File = event.target.files[0];
  this.selectFile = file
  this.SourceFileName = event.target.files[0].name;
}
onFileSelected1(event: any) {
  const file: File = event.target.files[0];
  this.selectFile = file
  this.TragetFileName = event.target.files[0].name;
}
fileAdd:boolean=false;
uploadFile() {
  //console.log("uploaded")
  this.uploadfileSpin = true
  const formData: FormData = new FormData();
  formData.append('file', this.selectFile, this.selectFile.name);
  formData.append('path', "PRJ1167SRC/Delta_process/"+ this.run + "/Target_Current_Zip/");
  this.deployment.UploadCloudFiles(formData).subscribe(
    (response:any) => {
      this.uploadfileSpin = false
      this.fileAdd = false
      // this.getDocuments();
      this.uploadForm.controls.file.reset()
      this.SourceFileName = '';
      this.Targetfilename = '';
      this.getFiles();
      this.toast.success(response.message)
      $('#demo').offcanvas('hide');
    },
    error => {
      this.uploadfileSpin = false
      this.fileAdd = false
      this.uploadForm.controls.file.reset()
      this.SourceFileName = '';
      this.Targetfilename = '';
      this.toast.error('Something went wrong')
      this.openPopup()
      $('#demo').offcanvas('hide');
    }
  )
}
spin_process: any;
uploadedData: any = [];
getFiles() {
  let requestObj = {
    path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iteration + "/Target_Current_Zip"
  };
  this.deployment.getFiles(requestObj).subscribe((data) => {
    this.uploadedData = data;
    this.uploadedData = this.uploadedData.reverse();
  });
}
openPopup() {
  this.uploadForm.reset();
  this.SourceFileName = '';
  this.Targetfilename = '';
}

}

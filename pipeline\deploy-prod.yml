# Variable 'category' was defined in the Variables tab
# Variable 'image' was defined in the Variables tab
# Variable 'namespace' was defined in the Variables tab
# Variable 'registry' was defined in the Variables tab
# Variable 'tag' was defined in the Variables tab
# Variable 'token' was defined in the Variables tab
# Variable 'projectId' was defined in the Variables tab
# Agent Queue 'Azure Pipelines' was used with unrecognized Agent Specification, vmImage property must be specified to determine image - https://docs.microsoft.com/en-us/azure/devops/pipelines/agents/hosted?view=azure-devops&tabs=yaml#software
trigger: none
resources:
  repositories:
  - repository: self
    type: git
    ref: refs/heads/stage
jobs:
- job: Job_1
  displayName: Agent job 1
  pool:
    name: Azure Pipelines
  steps:
  - checkout: self
    fetchDepth: 1
  - task: Bash@3
    displayName: Set Env
    inputs:
      targetType: inline
      script: >-
        category=$(category)

        if [ -n "$category" ] ; then
            echo "Using category.. $category";
            if [[ "$category" =~ "Deploy-UI" ]];
            then echo "##vso[task.setvariable variable=deploy]qmig-app";
            echo "##vso[task.setvariable variable=cont]app";
            elif [[ "$category" =~ "Deploy-API" ]];
            then echo "##vso[task.setvariable variable=deploy]qmig-eng";
            echo "##vso[task.setvariable variable=cont]eng";
            fi
        fi
  - task: Kubernetes@1
    displayName: kubectl set
    inputs:
      kubernetesServiceEndpoint: bed6d9f9-1005-4bae-92e3-9c03045e0e64
      namespace: $(namespace)
      command: set
      arguments: image deploy $(deploy) $(cont)=$(registry)/$(image):$(tag)
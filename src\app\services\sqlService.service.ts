import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { APIConstant } from '../constant/APIConstant';
import { sqlAPIConstant } from '../constant/sqlAPIConstant';
import { environment } from '../environments/environment';
import { ApiService } from './api.service';
import { GetFilesFromExpath } from '../models/interfaces/types';


@Injectable({
  providedIn: 'root'
})
export class SqlService {
  apiURL = environment.serviceUrl;
  constructor(
    private apiService: ApiService) { }

       
    SQLScriptInsertURL = this.apiURL + sqlAPIConstant.PrjSqlScriptExecute
    SQLScriptInsert = (body: any): Observable<any> => {
      return this.apiService.post(this.SQLScriptInsertURL, body);
    };
    // SQLScriptExecute(data: any): Observable<any> {
    // const Script = this.apiURL + sqlAPIConstant.PrjSqlScriptExecute
    //   return this.http.post( Script, data);
    // }
    SQLScriptExecuteURL = this.apiURL + sqlAPIConstant.PrjSqlScriptExecute
    SQLScriptExecute = (body: any): Observable<any> => {
      return this.apiService.post(this.SQLScriptExecuteURL, body);
    };
  
    // PrjSqlScriptFunctionSelect(data: any): Observable<any> {
    //   const PrjSqlScriptExecute=this.apiURL + sqlAPIConstant.PrjSqlScriptFunctionSelect;
    //   return this.http.get(PrjSqlScriptExecute+ data.projectId + "&functionname=" + data.functionname , data);
    // }
  
    PrjSqlScriptFunctionSelectURL = this.apiURL + sqlAPIConstant.PrjSqlScriptFunctionSelect;
    PrjSqlScriptFunctionSelect = (body:any): Observable<any> => {
      return this.apiService.get(this.PrjSqlScriptFunctionSelectURL +body.projectId + "&functionname=" + body.functionname , body )
    }
  
    // PrjSqlQueryExecute(data: any): Observable<any> {
    //  const PrjSqlQueryExecute= this.apiURL + sqlAPIConstant.PrjSqlQueryExecute;
    //   return this.http.post(PrjSqlQueryExecute, data);
    // }
  
    PrjSqlQueryExecuteURL = this.apiURL + sqlAPIConstant.PrjSqlQueryExecute
    PrjSqlQueryExecute = (body: any): Observable<any> => {
      return this.apiService.post(this.PrjSqlQueryExecuteURL, body);
    };
    // PrjSqlScriptcycleSelect(data: any): Observable<any> {
    //   const PrjSqlScriptcycleSelect=this.apiURL + sqlAPIConstant.PrjSqlScriptcycleSelect;
    //   return this.http.get(PrjSqlScriptcycleSelect + data.projectId + "&functionId=" + data.functionId);
    // }
    PrjSqlScriptcycleSelectURL = this.apiURL + sqlAPIConstant.PrjSqlScriptcycleSelect
    PrjSqlScriptcycleSelect = (body: any): Observable<any> => {
      return this.apiService.get(this.PrjSqlScriptcycleSelectURL+ body.projectId + "&functionId=" + body.functionId);
    };
    // PrjSqlScriptcycleSelectwithIteration(data: any): Observable<any> {
    //   const PrjSqlScriptcycleSelectwithIteration=this.apiURL + sqlAPIConstant.PrjSqlScriptcycleSelectwithIteration;
    //   return this.http.get(PrjSqlScriptcycleSelectwithIteration + data.projectId + "&functionId=null&iteration=" + data.iteration);
    // }
    PrjSqlScriptcycleSelectwithIterationURL = this.apiURL + sqlAPIConstant.PrjSqlScriptcycleSelectwithIteration
    PrjSqlScriptcycleSelectwithIteration = (body: any): Observable<any> => {
      return this.apiService.get(this.PrjSqlScriptcycleSelectwithIterationURL+ body.projectId + "&functionId=null&iteration=" + body.iteration);
    };
    // PrjSQLLogSelect(data: any): Observable<any> {
    //  const PrjSQLLogSelect= this.apiURL + sqlAPIConstant.PrjSQLLogSelect;
    //   return this.http.get(PrjSQLLogSelect + data.projectId);
    // }
    PrjSQLLogSelectURL = this.apiURL + sqlAPIConstant.PrjSQLLogSelect
    PrjSQLLogSelect = (body: any): Observable<any> => {
      return this.apiService.get(this.PrjSQLLogSelectURL+ body.projectId);
    };
    // PrjSqlScriptCategorySelect(data: any): Observable<any> {
    //   const PrjSqlScriptCategorySelect=this.apiURL + sqlAPIConstant.PrjSqlScriptCategorySelect;
    //   return this.http.get( PrjSqlScriptCategorySelect  + data.projectId);
    // }
    PrjSqlScriptCategorySelectURL = this.apiURL + sqlAPIConstant.PrjSqlScriptCategorySelect
    PrjSqlScriptCategorySelect = (body: any): Observable<any> => {
      return this.apiService.get(this.PrjSqlScriptCategorySelectURL+ body.projectId);
    };
    // PrjIterationSelect(data: any): Observable<any> {
    //   const PrjSQLRuninfoIteration=this.apiURL + sqlAPIConstant.PrjSQLRuninfoIteration;
    //   return this.http.get( PrjSQLRuninfoIteration+ data.projectId);
    // }
    PrjIterationSelectURL = this.apiURL + sqlAPIConstant.PrjSQLRuninfoIteration
    PrjIterationSelect = (body: any): Observable<any> => {
      return this.apiService.get(this.PrjIterationSelectURL+ body.projectId);
    };
    // downloadLargeFiles(data: any) {
    //   const DownloadLargeFile=this.apiURL + sqlAPIConstant.DownloadLargeFile;
    //   return this.http.get(DownloadLargeFile+ encodeURIComponent(data), { responseType: 'blob' as 'json' });
    // }
    downloadLargeFilesURL = this.apiURL + sqlAPIConstant.DownloadLargeFile
    downloadLargeFiles = (body: any): Observable<any> => {
      return this.apiService.get(this.downloadLargeFilesURL+ encodeURIComponent(body), { responseType: 'blob' as 'json' });
    };
    // PrjIterationByCategorySelect(data: any): Observable<any> {
    //  const PrjIterationByCategorySelect= this.apiURL + sqlAPIConstant.PrjIterationByCategorySelect;
    //   return this.http.get( PrjIterationByCategorySelect + data.projectId + "&categoryName=" + data.categoryName);
    // }
    PrjIterationByCategorySelectURL = this.apiURL + sqlAPIConstant.PrjIterationByCategorySelect
    PrjIterationByCategorySelect = (body: any): Observable<any> => {
      return this.apiService.get(this.PrjIterationByCategorySelectURL+ body.projectId + "&categoryName=" + body.categoryName);
    };
    // ProjectSrctgtconfSelect(data: any): Observable<any> {
    //   const GetProjectSrctgtconfSelect=this.apiURL + sqlAPIConstant.GetProjectSrctgtconfSelect;
    //   return this.http.post(GetProjectSrctgtconfSelect,data
    //   );
    // }
    ProjectSrctgtconfSelectURL = this.apiURL + sqlAPIConstant.GetProjectSrctgtconfSelect
    ProjectSrctgtconfSelect = (body: any): Observable<any> => {
      return this.apiService.post(this.ProjectSrctgtconfSelectURL, body);
    };
    // projectMigDetailSelect(data: any) {
    //   const GetProjectMigDetailSelect=this.apiURL + sqlAPIConstant.GetProjectMigDetailSelect;
    //   return this.http.post(GetProjectMigDetailSelect,
    //     data
    //   );
    // }
    projectMigDetailSelectURL = this.apiURL + sqlAPIConstant.GetProjectMigDetailSelect
    projectMigDetailSelect = (body: any): Observable<any> => {
      return this.apiService.post(this.projectMigDetailSelectURL, body);
    };
    // insertSqlMiCommand(data:any){
    //  const InsertSQLMiCommand= this.apiURL + sqlAPIConstant.InsertSQLMiCommand;
    //   return this.http.post(InsertSQLMiCommand,data
    //   );
    // }
    insertSqlMiCommandURL = this.apiURL + sqlAPIConstant.InsertSQLMiCommand
    insertSqlMiCommand = (body: any): Observable<any> => {
      return this.apiService.post(this.insertSqlMiCommandURL, body);
    };
    // getConList(data: any): Observable<any> {
    //   const GetConnectionList=this.apiURL + sqlAPIConstant.GetConnectionList;
    //   return this.http.get( GetConnectionList + data);
    // }
    getConListURL = this.apiURL + sqlAPIConstant.GetConnectionList
    getConList = (body: any): Observable<any> => {
      return this.apiService.get(this.getConListURL+ body);
    };
    // getServers()
    // {
    //   const GetServerlist=this.apiURL + sqlAPIConstant.GetServerlist;
    //   return this.http.get(GetServerlist);
    // }
    getServersURL = this.apiURL + sqlAPIConstant.GetServerlist
    getServers = (): Observable<any> => {
      return this.apiService.get(this.getServersURL);
    };
    // getsqlServerKey()
    // {
    //   const getsqlServerKey=this.apiURL + sqlAPIConstant.getsqlServerKey;
    //   return this.http.get(getsqlServerKey);
    // }
  
    getsqlServerKeyURL = this.apiURL + sqlAPIConstant.getsqlServerKey
    getsqlServerKey = (): Observable<any> => {
      return this.apiService.get(this.getsqlServerKeyURL);
    };
  
  
    // getInventoryDirs()
    // {
    //  const GetInventoryFolders= this.apiURL + sqlAPIConstant.GetInventoryFolders;
    //   return this.http.get(GetInventoryFolders);
    // }
    getInventoryDirsURL = this.apiURL + sqlAPIConstant.GetInventoryFolders
    getInventoryDirs = (): Observable<any> => {
      return this.apiService.get(this.getInventoryDirsURL);
    };
  
    // ScriptDownload(){
    //   const downloadScript=this.apiURL + sqlAPIConstant.downloadScript;
    //   return this.http.get(downloadScript,{responseType: 'blob' as 'json'});
    // }
    ScriptDownloadURL = this.apiURL + sqlAPIConstant.downloadScript
    ScriptDownload = (): Observable<any> => {
      return this.apiService.get(this.ScriptDownloadURL,{responseType: 'blob' as 'json'});
    };
    // downloadInventoryZipFiles(data:any)
    // {
    //   const downloadInventoryZip=this.apiURL + sqlAPIConstant.downloadInventoryZip;
    //   return this.http.get(downloadInventoryZip+data,{responseType: 'blob' as 'json'});
    // }
    downloadInventoryZipFilesURL = this.apiURL + sqlAPIConstant.downloadInventoryZip
    downloadInventoryZipFiles = (body: any): Observable<any> => {
      return this.apiService.get(this.downloadInventoryZipFilesURL+ body);
    };
    // triggerInventory(data:any)
    // {
    //  const InsertInventorDetalis= this.apiURL + sqlAPIConstant.InsertInventorDetalis;
    //   return this.http.post(InsertInventorDetalis ,data);
    // }
    triggerInventoryURL = this.apiURL + sqlAPIConstant.InsertInventorDetalis
    triggerInventory = (body: any): Observable<any> => {
      return this.apiService.post(this.triggerInventoryURL, body);
    };
    GetFilesFromExpathURL = this.apiURL + APIConstant.common.GetFilesFromExpath;
    GetFilesFromExpath = (body: string): Observable<GetFilesFromExpath> => {
      return this.apiService.get(this.GetFilesFromExpathURL + body)
    }

}

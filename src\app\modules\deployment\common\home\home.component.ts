import { Component } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { DeploymentService } from '../../../../services/deployment.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './home.component.html',
  styles: ``
})
export class HomeComponent {
  deploymentForm:any
  upload_spin: boolean = false
  fileName: string = '';
  connectionsForm: any;
  processBtn: boolean = false;
  datachange: any;
  logfile: any = [];
  incrementform:any;
  incrementgetform:any;
  pageName:string = '';
  pageNumber: number = 1;
  pi: number = 10;
  p: number = 1;
  page: number = 1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  prjSrcTgtData: any;
  sourceDelta: any;
  datachanges: any;
  datachanges1: any;
  datachanges2: any;
  project_name: any
  projectId: any
  getRole: any;
  Spin: any;
  UserInfo: any;

  constructor(
    private formBuider: FormBuilder,
    public deployment:DeploymentService,
    private toast: HotToastService,
    private route: ActivatedRoute,
    ) {
      this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    let userJson = localStorage.getItem('userData') as string;
    this.UserInfo = JSON.parse(userJson);
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
    this.incrementform = this.formBuider.group({
      fileName: ['', [Validators.required]],
    });
    this.incrementgetform = this.formBuider.group({
      conname: ['', [Validators.required]],
    });
    this.GetFilesExecutionLogsFromDirectory();
  }
  get validate() {
    return this.incrementgetform.controls;
  }
  fileResult: any;
  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    this.selectFile = file;
    this.fileName = event.target.files[0].name;
    let myReader: FileReader = new FileReader();
    
    myReader.onloadend = (e) => {
      this.fileResult = myReader.result;
    };
    myReader.readAsDataURL(file);
  }
  currentlyCheked(isChecked: any) {
    if (isChecked.target.checked) {
      this.processBtn = true;
    } else {
      this.processBtn = false;
    }
  }
  fileupload:any;
  uploadfileSpin: boolean = false;
  uploadResponse: any;
   selectFile: any;
   fileAdd: boolean = false;
  uploadFile() {
    this.uploadfileSpin = true
    const sourceFileUpload: FormData = new FormData();
    sourceFileUpload.append('file', this.selectFile, this.selectFile.name);
    sourceFileUpload.append('path', "projectdoc");
    this.deployment.uploadDocuments(sourceFileUpload).subscribe(
      (response:any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.getFiles();
        this.toast.success(response.message)
      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.toast.error('Something went wrong')
      }
    )
  }
  uploadedData:any=[];
  getFiles() {
    let requestObj = {
      path: "Code/Delta_process/run_no/Scurrent/"
    };
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.uploadedData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }
  fileResponse: any;
  spin_dwld: any;
  //download files
  downloadFile(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  LogsData: any;
  GetFilesExecutionLogsFromDirectory() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/1514/Execution_Logs/Deployment"
    };
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.LogsData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }
}


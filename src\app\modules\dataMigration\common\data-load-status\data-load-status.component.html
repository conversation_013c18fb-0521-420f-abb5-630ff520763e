<div class="v-pageName">{{pageName}}</div>

<div class="qmig-card">
    <h3 class="main_h px-3 pt-3">Dag Status</h3>
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="execProjectForm">
            <div class="row">
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="name">Config File</label>
                        <select formControlName="sourceConnection" placeholder="Name"
                            class="form-select" #srcValue (change)="getDagExecutionlog(srcValue.value)">
                            <option disabled>Select a Config File</option>
                            @for ( list of configFiles; track list) {
                            <option value="{{ list.config_id }}">{{list.config_file_name }}</option>
                            }
                        </select>
                        @if ( f.sourceConnection.touched && f.sourceConnection.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.sourceConnection.errors?.['required']) { Config File is
                            required }
                        </p>
                        }
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@if(dagExecustionList){
    <div class="qmig-card mt-4">
        <div class="row">
            <div class="col-12 col-md-6">                
                <div class="row mt-2 ps-1">
                    <div class="col-md-3 pe-1">
                        <div class="form-group mb-0">
                            <select class="form-select form-small" #sv (change)="filterDags(sv.value)">
                                <option selected value="">Select Table</option>
                                @for(table of dagTables; track table){
                                    <option value={{table}}>{{table}}</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-6">
                <div class="custom_search me-1 mt-1 mb-1">
                    <span class="mdi mdi-magnify"></span>
                    <input type="text" placeholder="Search Dags" class="form-control"  [(ngModel)]="searchText" (keyup)="onKey()">
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>Sr No</th>
                        <th>Schema</th>
                        <th>Table</th>
                        <th>Dag</th>
                        <th>Dag Start</th>
                        <th>Dag End</th>
                        <th>Dag Duration</th>
                        <th>Extraction Duration</th>
                        <th>Load Duration</th>
                        <th>Status</th>
                        <th>Task Duration</th>
                        <th>Task Name</th>
                        <th>Transform Duration</th>
                    </tr>
                </thead>
                <tbody>
                    @for (con of dagExecustionList | searchFilter: searchText | paginate: { itemsPerPage: 10, currentPage: pageNumber } ;
                    track con;) {
                    <tr>
                        <td>{{pageNumber*10+$index+1-10}}</td>
                        <td>{{con.schema_name}}</td>
                        <td>{{con.table_name}}</td>
                        <td>{{con.dag_name}}</td>
                        <td>{{con.dag_startdate}}</td>
                        <td>{{con.dag_enddate}}</td>
                        <td>{{con.dag_duration}}</td>
                        <td>{{con.extraction_duration}}</td>
                        <td>{{con.load_duration}}</td>
                        <td>
                            @if(con.status=='Pass'){
                            <span class="badge badge-csuccess">success</span>
                            }@else{
                            <span class="badge badge-cdanger">{{con.status}}</span>
                            }
                        </td>
                        <td>{{con.task_duration}}</td>
                        <td>{{con.task_name}}</td>
                        <td>{{con.transform_duration}}</td>
                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="13">
                            <p class="text-center m-0 w-100">Empty list of Documents</p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
    </div>
}

<!---Reports & Logs---->
<div class="qmig-card mt-4" hidden>
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Data Load Status
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Sr No</th>
                                    <th>Total Tables</th>
                                    <th>Tables Migrated</th>
                                    <th>Tables Failed</th>
                                    <th>Tables Yet to start</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (con of tableStatus | paginate: { itemsPerPage: 10, currentPage: pageNumber } ;
                                track con;) {
                                <tr>
                                    <td>{{pageNumber*10+$index+1-10}}</td>
                                    <td>{{con.TotalTables}}</td>
                                    <td>{{con.Tables_Success}}</td>
                                    <td>{{con.Tables_Failed}}</td>
                                    <td>{{con.Tables_YetToStart}}</td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="5">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
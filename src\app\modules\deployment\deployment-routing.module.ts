import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './common/home/<USER>';
import { DeltaProcessComponent } from './common/delta-process/delta-process.component';
import { DeltaFilesComponent } from './common/delta-files/delta-files.component';
import { DeltaControlComponent } from './common/delta-control/delta-control.component';
import { SourceBaselineComponent } from './common/source-baseline/source-baseline.component';
import { SourceCompareComponent } from './common/source-compare/source-compare.component';
import { TargetConversionComponent } from './common/target-conversion/target-conversion.component';
import { TargetBaselineComponent } from './common/target-baseline/target-baseline.component';
import { TargetCurrentComponent } from './common/target-current/target-current.component';
import { TargetCompareComponent } from './common/target-compare/target-compare.component';
import { TargetIntegrateComponent } from './common/target-integrate/target-integrate.component';
import { BCCompareComponent } from './common/b-c-compare/b-c-compare.component';
import { STCompareComponent } from './common/s-t-compare/s-t-compare.component';
import { LayoutComponent } from '../../../app/shared/components/layout/layout.component';

const routes: Routes = [
  {
    path: '', component: LayoutComponent, children: [
      { path: 'incremental', component: HomeComponent , data: { name: 'Incremental' } },
      { path: 'sourceUpload', component: DeltaProcessComponent, data: { name: 'Source Upload' } },
      { path: 'deltaFiles', component: DeltaFilesComponent, data: { name: 'Delta Files' } },
      { path: 'deltaControl', component: DeltaControlComponent, data: { name: 'Delta Control' } },
      { path: 'sourceBaseline', component: SourceBaselineComponent, data: { name: 'Source Baseline' } },
      { path: 'sourceCompare', component: SourceCompareComponent, data: { name: 'Source Compare' } },
      { path: 'targetConversion', component: TargetConversionComponent, data: { name: 'Target Conversion' } },
      { path: 'targetBaseline', component: TargetBaselineComponent, data: { name: 'Target Baseline' } },
      { path: 'targetCurrent', component: TargetCurrentComponent, data: { name: 'Target Current' } },
      { path: 'targetCompare', component: TargetCompareComponent, data: { name: 'Target Compare' } },
      { path: 'targetintergate', component: TargetIntegrateComponent, data: { name: 'Target Intergate' } },
      { path: 'BandCCompare', component:BCCompareComponent , data: { name: 'B&C Compare' } },
      { path: 'SandTCompare', component: STCompareComponent, data: { name: 'S&T Compare' } },
    
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DeploymentRoutingModule { }

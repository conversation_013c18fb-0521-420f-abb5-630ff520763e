<div class="v-pageName">{{pageName}}</div>

<!--- Connection Details --->
<div class="body-main">
  <div class="">
    <div class="">
        <div class="word-doc">
        <h1>Schema Management</h1>

        <h2>Source Schema Extraction</h2>
        <p>This is a mandatory step; we need to extract Schema names from the Source connection to have the schema names populated in the dropdowns in subsequent operations.</p>
        <p>Select the source connection and click on the execute button to start fetch.</p>
        <p>It may take a few minutes to extract & get reflected in the next operations.</p>
        <img src="assets/images/help/Schema Management screen/Source schema extraction/image 1.png"  style="width: 100%;">
        <img src="assets/images/help/Schema Management screen/Source schema extraction/image 2.png" style="width: 100%;">


        <h2>Target Schema Extraction</h2>
        <p>We need to extract Schema names from the target connection to have the schema names populated in the dropdowns in subsequent operations.</p>
        <img src="assets/images/help/Schema Management screen/Target schema extraction/image 1.png"  style="width: 100%;">
        <img src="assets/images/help/Schema Management screen/Target schema extraction/image 2.png" style="width: 100%;">
        <p>Select the Target connection and click on the execute button to start fetch.</p>
        <p>It may take a few minutes to extract & get reflected in the next operations.</p>

        <h2>Create Target Schema</h2>
        <p>When the user is performing Data Migration we need to have source schema and target schema. So, if the target schema is not present in the target database, we need to create a target schema.</p>
        <p>Select the target connection and give the required schema name.</p>
        <p>Click on the <strong>+ Create Schema</strong> button to create the target schema.</p>
        <img src="assets/images/help/Schema Management screen/create target schema/image 1.png"  style="width: 100%;">
        <img src="assets/images/help/Schema Management screen/create target schema/image 2.png" style="width: 100%;">
        <h2>Drop Target Schema</h2>
        <p>If the user wants to perform data migration from scratch, they should drop all objects from the target database (Oracle) instead of dropping the entire schema.</p>
        <p>Select the target connection and the required schema name.</p>
        <p>Click on the <strong>Drop Target Schema</strong> button, and the schema gets dropped.</p>
        <img src="assets/images/help/Schema Management screen/Drop target Schema/image 1.png"  style="width: 100%;">
        <img src="assets/images/help/Schema Management screen/Drop target Schema/image 2.png" style="width: 100%;">
        </div>
    </div>
  </div>
</div>
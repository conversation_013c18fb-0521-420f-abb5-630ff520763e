import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { environment } from '../environments/environment';
import { performanceAPIConstant } from '../constant/performanceTestingAPIConstatnt';
import { Observable } from 'rxjs';
import { Execute, GetReqData, SchemaListSelect, Submit, deleteFile, downloadDocs, uploadDocs } from '../models/interfaces/types';
@Injectable({
  providedIn: 'root'
})
export class PerofmanceTestingService {
  SubmitCall: any;
  CallStatements(obj: { conId: any; }) {
    throw new Error('Method not implemented.');
  }

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl + "Performance/"

  getConListURL = this.apiURL + performanceAPIConstant.getConList;
  getConList = (body: string): Observable<string> => {
    return this.apiService.get(this.getConListURL + body)
  }
  GetReqDataURL = this.apiURL + performanceAPIConstant.GetReqData;
  GetReqData = (body: GetReqData): Observable<GetReqData> => {
    return this.apiService.get(this.GetReqDataURL + body.projectId + '&operationType=' + body.operationType
    )
  }
  SchemaListSelectURL = this.apiURL + performanceAPIConstant.SchemaListSelect;
  SchemaListSelect = (body: SchemaListSelect): Observable<SchemaListSelect> => {
    return this.apiService.get(this.SchemaListSelectURL + body.projectid + '&ConId=' + body.connectioId)
  }
  ExecuteURL = this.apiURL + performanceAPIConstant.Execute;
  Execute = (body: any): Observable<Execute> => {
    return this.apiService.post(this.ExecuteURL, body);
  }

  SubmitShowLogsURL = this.apiURL + performanceAPIConstant.SubmitShowLogs;
  SubmitShowLogs = (body: any): Observable<any> => {
    return this.apiService.post(this.SubmitShowLogsURL, body);
  }

  Submit = (body: Submit): Observable<any> => {
    return this.apiService.post(this.ExecuteURL, body);
  }
  selectedfileNameURL = this.apiURL + performanceAPIConstant.selectedfileName;
  selectedfileName = (body: string): Observable<string> => {
    return this.apiService.get(this.selectedfileNameURL + body)
  };
  //GetFilesFromDir

  GetFilesFromDirURL = this.apiURL + performanceAPIConstant.GetFilesFromDir;
  GetFilesFromDir = (body: any): Observable<string> => {
    return this.apiService.get(this.GetFilesFromDirURL + body)
  }
  /*--- Download Files ---*/

  downloadFileURL = this.apiURL + performanceAPIConstant.fileDownload;
  downloadFiles = (body: string): Observable<downloadDocs> => {
    return this.apiService.get(this.downloadFileURL + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }

  /*--- Delete Files ---*/

  deleteFileURL = this.apiURL + performanceAPIConstant.deleteFile;
  deleteFile = (body: string): Observable<deleteFile> => {
    return this.apiService.get(this.deleteFileURL + body)
  }


  GetperfSchemasURL = this.apiURL + performanceAPIConstant.GetperfSchemas;
  GetperfSchemas = (body: any): Observable<any> => {
    return this.apiService.get(this.GetperfSchemasURL + body.conId)
  }
  GetDuplicateIndexDataURL = this.apiURL + performanceAPIConstant.GetDuplicateIndexData;
  GetDuplicateIndexData = (body: any): Observable<any> => {
    return this.apiService.post(this.GetDuplicateIndexDataURL, body);
  }
  GetPerfIndexMonitorDataURL = this.apiURL + performanceAPIConstant.GetPerfIndexMonitorData;
  GetPerfIndexMonitorData = (body: any): Observable<any> => {
    return this.apiService.post(this.GetPerfIndexMonitorDataURL, body)
  }
  GetCallStatementsURL = this.apiURL + performanceAPIConstant.Execute;
  GetCallStatements = (body: any): Observable<Execute> => {
    return this.apiService.post(this.GetCallStatementsURL, body);
  }
  GetTablePartitionURL = this.apiURL + performanceAPIConstant.GetTablePartition;
  GetTablePartition = (body: any): Observable<any> => {
    return this.apiService.get(this.GetTablePartitionURL + body.Conid)
  }

  getLongRunningQueriesURL = this.apiURL + performanceAPIConstant.getLongRunningQueries;
  getLongRunningQueries = (body: any): Observable<any> => {
    return this.apiService.post(this.getLongRunningQueriesURL, body);
  }

  GetSuggestedIndexURL = this.apiURL + performanceAPIConstant.GetSuggestedIndex;
  GetSuggestedIndex = (body: any): Observable<any> => {
    return this.apiService.get(this.GetSuggestedIndexURL + body.Conid)
  }
  UploadCloudFilesURL = this.apiURL + performanceAPIConstant.UploadCloudFiles
  UploadCloudFiles = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.UploadCloudFilesURL, body);
  };

  GetFilesurl = this.apiURL + performanceAPIConstant.getFiles
  getFiles(data: any) {
    return this.apiService.get(this.GetFilesurl + data.path);
  }

  GetUnusedIndexesURL = this.apiURL + performanceAPIConstant.GetUnusedIndexes;
  GetUnusedIndexes = (body: any): Observable<any> => {
    return this.apiService.post(this.GetUnusedIndexesURL, body)
  }

  GetUsedIndexesURL = this.apiURL + performanceAPIConstant.GetUsedIndexes
  GetUsedIndexes = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.GetUsedIndexesURL, body);
  };

  BloatedIndexesURL = this.apiURL + performanceAPIConstant.BloatedIndexes;
  BloatedIndexes = (body: any): Observable<any> => {
    return this.apiService.post(this.BloatedIndexesURL, body)
  }

  BloatedIndexExcelURL = this.apiURL + performanceAPIConstant.BloatedIndexExcel;
  BloatedIndexExcel = (body: any): Observable<any> => {
    return this.apiService.post(this.BloatedIndexExcelURL, body)
  }

  GetRatioIndexesURL = this.apiURL + performanceAPIConstant.GetRatioIndexes;
  GetRatioIndexes = (body: any): Observable<any> => {
    return this.apiService.get(this.GetRatioIndexesURL + body.Conid)
  }

  GetPerflogFilesURL = this.apiURL + performanceAPIConstant.GetPerflogFiles;
  GetPerflogFiles = (): Observable<any> => {
    return this.apiService.get(this.GetPerflogFilesURL )
  }

  GetPerflogstatusURL = this.apiURL + performanceAPIConstant.Perflogstatus;
  GetPerflogstatus = (body: any): Observable<any> => {
    return this.apiService.post(this.GetPerflogstatusURL, body)
  }

  QueryInsightBasedoncolumnsURL = this.apiURL + performanceAPIConstant.QueryInsightBasedoncolumns;
  QueryInsightBasedoncolumns = (body: any): Observable<any> => {
    return this.apiService.post(this.QueryInsightBasedoncolumnsURL, body)
  }

  RecomondedIndexTableURL = this.apiURL + performanceAPIConstant.RecomondedIndexTable;
  RecomondedIndexTable = (body: any): Observable<any> => {
    return this.apiService.get(this.RecomondedIndexTableURL + body.Conid)
  }
  RecomondedIndexCountURL = this.apiURL + performanceAPIConstant.RecomondedIndexCount;
  RecomondedIndexCount = (body: any): Observable<any> => {
    return this.apiService.get(this.RecomondedIndexCountURL + body.Conid)
  }

  QueryInsightBasedoncolumnsByURL = this.apiURL + performanceAPIConstant.QueryInsightCoulumnByDate;
  QueryInsightCoulumnByDate = (body: any): Observable<any> => {
    return this.apiService.post(this.QueryInsightBasedoncolumnsByURL, body)
  }
  PerfIndexStatusUpdateURL = this.apiURL + performanceAPIConstant.PerfIndexStatusUpdate;
  PerfIndexStatusUpdate = (body: any): Observable<any> => {
    return this.apiService.post(this.PerfIndexStatusUpdateURL, body)
  }
  PerfSchemanamewithAllurl = this.apiURL + performanceAPIConstant.PerfSchemanamewithAll;
  PerfSchemanamewithAll = (body: any): Observable<any> => {
    return this.apiService.get(this.PerfSchemanamewithAllurl + body.Conid)
  }

  IndexeExcelDownLoadURL = this.apiURL + performanceAPIConstant.IndexeExcelDownLoad;
  IndexeExcelDownLoad = (body: any): Observable<any> => {
    return this.apiService.post(this.IndexeExcelDownLoadURL, body)
  }

  IndexeTimeStampurl = this.apiURL + performanceAPIConstant.IndexeTimeStamp;
  IndexeTimeStamp = (body: any): Observable<any> => {
    return this.apiService.get(this.IndexeTimeStampurl + body.Conid)
  }

  DroppedIndexCounturl = this.apiURL + performanceAPIConstant.DroppedIndexCount;
  DroppedIndexCount = (body: any): Observable<any> => {
    return this.apiService.get(this.DroppedIndexCounturl + body.Conid)
  }
  DroppedIndexUpdateURL = this.apiURL + performanceAPIConstant.DroppedIndexUpdate;
  DroppedIndexUpdate = (body: any): Observable<any> => {
    return this.apiService.post(this.DroppedIndexUpdateURL, body)
  }

  FailedIndexesURL = this.apiURL + performanceAPIConstant.FailedIndexes;
  FailedIndexes = (body: any): Observable<any> => {
    return this.apiService.get(this.FailedIndexesURL+ body.Conid)
  }

  IndexStatementExecuteURL = this.apiURL + performanceAPIConstant.IndexStatementExecute;
  IndexStatementExecute = (body: any): Observable<any> => {
    return this.apiService.post(this.IndexStatementExecuteURL, body)
  }

  GRecomondedPartionsURL = this.apiURL + performanceAPIConstant.RecomondedPartions;
  RecomondedPartions = (): Observable<any> => {
    return this.apiService.get(this.GRecomondedPartionsURL )
  }
  IndexTuningFiledlogURL = this.apiURL + performanceAPIConstant.IndexTuningFiledlog;
  IndexTuningFiledlog = (body: any): Observable<any> => {
    return this.apiService.get(this.IndexTuningFiledlogURL + body.Conid)
  }
  uploadLargeDocumentURL = this.apiURL + performanceAPIConstant.uploadLargeFiles
  uploadLargeDocument = (body: any): Observable<uploadDocs> => {
    return this.apiService.post(this.uploadLargeDocumentURL, body);
  };
  downloadLargeFilesURL = this.apiURL + performanceAPIConstant.downloadLargeFiles;
  downloadLargeFiles = (body: string): Observable<any> => {
    return this.apiService.get(this.downloadLargeFilesURL + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }
  DuplicateIndexSchemaURL = this.apiURL + performanceAPIConstant.DuplicateIndexSchema;
  DuplicateIndexSchema = (body: any): Observable<any> => {
    return this.apiService.post(this.DuplicateIndexSchemaURL, body);
  }
  PerfPartitionLogstatusURL = this.apiURL + performanceAPIConstant.PerfPartitionLogstatus;
  PerfPartitionLogstatus = (): Observable<any> => {
    return this.apiService.get(this.PerfPartitionLogstatusURL )
  }
  FetchAllProceduresURL = this.apiURL + performanceAPIConstant.FetchAllProcedures;
  FetchAllProcedures = (body:any): Observable<any> => {
    return this.apiService.get(this.FetchAllProceduresURL+body )
  }
  GetPerfLogsURL = this.apiURL + performanceAPIConstant.GetPerfLogs;
  GetPerfLogs = (): Observable<Execute> => {
    return this.apiService.get(this.GetPerfLogsURL);
  }
}
<div class="v-pageName">{{pageName}}</div>

<!---Code Compare List --->
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Create New Validation
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="validationForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Source Connection</label>
                                        <select formControlName="connectionType" placeholder="Name" class="form-select"
                                            #src (change)="fetchDynamoTables(src.value)">
                                            <option disabled>Select a Source Connection</option>
                                            @for ( list of ConsList; track list) {
                                            <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                            }
                                        </select>
                                        @if ( f.connectionType.touched && f.connectionType.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.connectionType.errors.required) {Source Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Target Connection</label>
                                        <select formControlName="targetConnection" class="form-select">
                                            <option disabled>Select a Target Connection</option>
                                            @for ( list of tgtlist; track list) {
                                            <option value="{{ list.Connection_ID }}"> {{ list.conname }}</option>
                                            }
                                        </select>
                                        @if ( f.targetConnection.touched && f.targetConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.targetConnection.errors.required) {Target Connection is Required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Table Name</label>
                                        <select class="form-select" formControlName="table">
                                            <option disabled selected>Select Table Name</option>
                                            @for ( table of tablesList; track table) {
                                            <option value="{{ table.table_name }}"> {{ table.table_name }}</option>
                                            }
                                        </select>
                                        @if ( f.table.touched && f.table.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.table.errors.required) {Table is Required
                                            }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">File Name</label>
                                        <input type="text" formControlName="fileName" class="form-control"
                                            placeholder="fileName" id="fname" maxlength="15" />
                                        @if ( f.fileName.touched && f.fileName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.fileName.errors.required) {File Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Rows</label>
                                        <input type="text" formControlName="rows" class="form-control"
                                            placeholder="rows" id="fname" maxlength="15" />
                                        @if ( f.rows.touched && f.rows.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.rows.errors.required) {Rows is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Filter Expression</label>
                                        <input type="text" formControlName="filter_expression" class="form-control"
                                            placeholder="Filter Expression" id="fname" maxlength="15" />
                                        @if ( f.filter_expression.touched && f.filter_expression.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.filter_expression.errors.required) {Filter Expression is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Filter Value</label>
                                        <input type="text" formControlName="filter_value" class="form-control"
                                            placeholder="Filter Value" id="fname" maxlength="15" />
                                        @if ( f.filter_value.touched && f.filter_value.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.filter_value.errors.required) {Filter Value is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100 " [disabled]="validationForm.invalid"
                                            (click)="TriggerValidation(validationForm.value)">
                                            <span
                                                class="mdi mdi-file-cog-outline btn-icon-prepend"></span>Create</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Data Validation Files
                </button>
            </h3>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <!-- <div class="col-sm-4 col-md-4 col-xl-2">
                            <div class="form-group">
                                <select class="form-select" #table (change)="fetchfilesfromDB(table.value)">
                                    <option selected value="">Select Tables</option>
                                    @for (tbl of tablesres; track tbl) {
                                    <option value="{{ tbl.table_name }}"> {{ tbl.table_name }}</option>
                                    }
                                </select>
                            </div>
                        </div> -->
                        <!-- <div class="col-sm-4 col-md-4 col-xl-2">
                            <div class="form-group">
                                <select class="form-select">
                                    <option selected value="">Select File Name</option>
                                </select>
                            </div>
                        </div> -->
                        <div class="col-sm-8 col-md-8 col-xl-8">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Data Compare Report" class="form-control"
                                    [(ngModel)]="datachange" (keyup)="onkey()">
                            </div>
                        </div>
                    </div>

                    <!-- for download file -->
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>File Name</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(docs of filesres |searchFilter: searchText
                                |paginate:{
                                itemsPerPage: 10, currentPage: pageNumber, id:'first'};
                                track docs){
                                <tr>
                                    <td>{{ pageNumber*10+$index+1-10 }}</td>
                                    <td>{{docs.filename }}</td>
                                    <td>{{docs.status }}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(docs.filename)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber = $event" id="first"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
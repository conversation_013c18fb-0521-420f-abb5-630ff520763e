<!--- Bread Crumb --->
<div class="v-pageName">{{pageName}}</div>

  
<!---Project Documents List --->
<div class="body-main">
    <div class="row">
        <div class="col-6 col-md-6 col-xl-6 offset-md-6">
            <div class="body-header-button">
                <button class="btn btn-sign me-2 " data-bs-toggle="offcanvas" data-bs-target="#demo" type="button"
                    [hidden]="buttondisable"> <span class="mdi mdi-database-plus"></span>Create Database</button>
                <button class="btn btn-upload" data-bs-toggle="offcanvas" data-bs-target="#databaseDetails"> <span
                        class="mdi mdi-link-plus"></span>Add Connection</button>
            </div>
        </div>
    </div>
    <div class="qmig-card mt-3 pt-3">
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th appResizeTable>Select</th>
                        <th appResizeTable>Connection Name</th>
                        <th appResizeTable>Hostname/IP</th>
                        <th appResizeTable>Port</th>
                        <th appResizeTable>DB Name</th>
                        <th appResizeTable>Schema</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @for (list of migDetailSelectData ; track list; let i = $index) {
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="flexRadioDefault"
                                    id="flexRadioDefault2" (click)="currentlyCheked(list)" value="{{list.dbname}}"
                                    name="optradio">
                            </div>
                        </td>
                        <td>{{list.dbconname}}</td>
                        <td>{{list.dbhost}}</td>
                        <td>{{list.dbport}}</td>
                        <td>{{list.dbname}}</td>
                        <td>{{list.dbschema}}</td>
                        <td hidden>{{list.id}}</td>
                        <td>
                            <button class="btn btn-download" data-bs-toggle="offcanvas"
                                data-bs-target="#databaseDetails" (click)="updateRecord(list)">
                                <span class="mdi mdi-pencil btn-icon-prepend"></span> @if(recordSpin){<app-spinner />}
                            </button>
                            <button class="btn btn-delete" (click)="deleteConnection(list.id)">
                                <span class="mdi mdi-delete btn-icon-prepend"></span>
                            </button>
                        </td>
                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="4">
                            <p class="text-center m-0 w-100">Empty list of Documents</p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!--- Create Database Query Details --->
<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
    <div class="offcanvas-header">
        <h4 class="main_h">Create Database</h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="dbForm">
            <div class="form-group">
                <label class="form-label d-required" for="db_query">Database Name</label>
                <input type="text" formControlName="db_query" placeholder="{{dbquery}}" class="form-control"
                    id="db_query" />
                @if ( getcontrol.db_query.touched && getcontrol.db_query.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (getcontrol.db_query.errors.required) { Database Name is required }
                </p>
                }
            </div>
            <div class="form-group">
                <div class="body-header-button">
                    <button class="btn btn-upload w-100 " [disabled]="dbForm.invalid"
                        (click)="executeBtn(dbForm.value)"> <span></span> Execute
                        @if(Executespin){<app-spinner />}</button>
                </div>
            </div>
        </form>
    </div>
</div>
<!--- Add Database Details --->
<div class="offcanvas offcanvas-end" tabindex="-1" id="databaseDetails">
    <div class="offcanvas-header">
        <h4 class="main_h">Database Details</h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="DBConnectionForm">
            <div class="form-group">
                <label class="form-label d-required" for="Name">Name</label>
                <input type="text" formControlName="dbconname" class="form-control" placeholder="Name" id="dbconname" />
                @if ( formvalidator.dbconname.touched && formvalidator.dbconname.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (formvalidator.dbconname.errors.required) {Name is Required }
                </p>
                }
            </div>
            <div class="form-group">
                <label class="form-label d-required" for="dbName">Database Name</label>
                <input type="text" formControlName="dbname" id="dbname" class="form-control" placeholder="dbName" />
                @if ( formvalidator.dbname.touched && formvalidator.dbname.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (formvalidator.dbname.errors.required) {Database is Required }
                </p>
                }
            </div>
            <div class="form-group">
                <label class="form-label d-required" for="server">Hostname/IP</label>
                <input type="text" formControlName="dbhost" class="form-control" placeholder="Hostname/IP"
                    id="dbhost" />
                @if ( formvalidator.dbhost.touched && formvalidator.dbhost.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (formvalidator.dbhost.errors.required) {Hostname is Required }
                </p>
                }
            </div>
            <div class="form-group">
                <label class="form-label d-required" for="schema">Schema</label>
                <input type="text" formControlName="schema" class="form-control" placeholder="schema" id="schema" />
                @if ( formvalidator.schema.touched && formvalidator.schema.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (formvalidator.schema.errors.required) {schema is Required }
                </p>
                }
            </div>
            <div class="form-group">
                <label class="form-label d-required" for="port">Port</label>
                <input type="text" formControlName="port" class="form-control" placeholder="port" id="port" />
                @if ( formvalidator.port.touched && formvalidator.port.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (formvalidator.port.errors.required) {port is Required }
                </p>
                }
            </div>
            <div class="form-group">
                <label class="form-label d-required" for="userid">Username</label>
                <input type="text" formControlName="dbuserid" class="form-control" placeholder="Username"
                    id="dbuserid" />
                @if ( formvalidator.dbuserid.touched && formvalidator.dbuserid.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (formvalidator.dbuserid.errors.required) {Username is Required }
                </p>
                }
            </div>
            <div class="form-group">
                <label class="form-label d-required" for="serviceName">Service Name</label>
                <input type="text" formControlName="serviceName" class="form-control" placeholder="serviceName"
                    id="serviceName" />
            </div>
            <div class="form-group  password_label">
                <label class="form-label d-required" for="password">Password
                </label>
                <input type="text" formControlName="dbpassword" [type]="textCheck ? 'password' : 'text'"
                    class="form-control" placeholder="password" id="dbpassword" />
                <span class="password_show" (click)="sendEye()"
                    [ngClass]="textCheck ?'mdi mdi-eye-off' : 'mdi mdi-eye'"></span>
                @if ( formvalidator.dbpassword.touched && formvalidator.dbpassword.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (formvalidator.dbpassword.errors.required) {Username is Required }
                </p>
                }
            </div>
            <div class="form-group">
                <div class="body-header-button">
                    @if (addupdateDetails) {
                    <button class="btn btn-upload w-100 " [disabled]="DBConnectionForm.invalid"
                        (click)="addPrjConnection(DBConnectionForm.value)">
                        Add @if(Addspin){<app-spinner />}</button>
                    }
                </div>
                <div class="body-header-button">
                    @if (!addupdateDetails) {
                    <button class="btn btn-upload w-100 " [disabled]="DBConnectionForm.invalid"
                        (click)="updatePrjConnection(DBConnectionForm.value)">
                        Update @if(updateSpin){<app-spinner />}</button>
                    }
                </div>
            </div>
        </form>
    </div>
</div>
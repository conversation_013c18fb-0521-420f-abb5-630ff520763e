<div class="v-pageName">{{pageName}}</div>

<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Test Cycles
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="cyclesForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="cycles">Run No</label>
                                        <select class="form-select" formControlName="iteration" #iter
                                            (change)="SelectIteration(iter.value)">
                                            <option selected value="">Select a Run No</option>
                                            @for(list of iterationList; track list;){
                                            <option value="{{ list.iteration }}">{{ list.iteration }}</option>
                                            }
                                        </select>
                                        @if ( f.iteration.touched && f.iteration.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.iteration.errors.required) {Run No is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Operation</label>
                                        <select class="form-select" formControlName="operation" #cyc
                                            (change)="selectCycles(cyc.value)">
                                            <option selected value="">Select Operation</option>
                                            @for(list of operation;track list;){
                                            <option value="{{list.value }}">
                                                {{ list.option }}
                                            </option>
                                            }
                                        </select>
                                        @if ( f.operation.touched && f.operation.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.operation.errors.required) {Operation is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!cycleHide">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source Connection
                                        </label>
                                        <select class="form-select" formControlName="srcCon"
                                            aria-label="Default select example">
                                            <option selected value="">Select Source Connection</option>
                                            @for(ConsList of ConsList;track ConsList; ){
                                            <option value="{{ConsList.Connection_ID}}">{{ConsList.conname}}</option>
                                            }
                                        </select>
                                        @if ( f.srcCon.touched && f.srcCon.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.srcCon.errors.required) {Source Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Target Connection
                                        </label>
                                        <select class="form-select" formControlName="tgtCon"
                                            aria-label="Default select example">
                                            <option selected value="">Select Target Connection</option>
                                            @for(ConsList of tgtList;track ConsList; ){
                                            <option value="{{ConsList.Connection_ID}}">{{ConsList.conname}}</option>
                                            }
                                        </select>
                                        @if ( f.tgtCon.touched && f.tgtCon.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.tgtCon.errors.required) {Target Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!cycleHide">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Kafka Connection
                                        </label>
                                        <select class="form-select" formControlName="kafkaCon"
                                            aria-label="Default select example">
                                            <option selected value="">Select Kafka Connection</option>
                                            @for(ConsList of kafkaList;track ConsList; ){
                                            <option value="{{ConsList.Connection_ID}}">{{ConsList.conname}}</option>
                                            }
                                        </select>
                                        @if ( f.kafkaCon.touched && f.kafkaCon.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.kafkaCon.errors.required) {Kafka Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!cycleHide">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Execution
                                            Option</label>
                                        <select class="form-select" formControlName="exeOption"
                                            aria-label="Default select example">
                                            <option selected value="">Select a Execution Option</option>
                                            @for(ConsList of exeoption;track ConsList; ){
                                            <option value="{{ConsList.value}}">{{ConsList.option}}</option>
                                            }
                                        </select>
                                        @if ( f.exeOption.touched && f.exeOption.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.exeOption.errors.required) {Execution Option is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Execution Time Limit </label>
                                        <input type="number" class="form-control" formControlName="exeTimeLimit" />
                                        <div class="alert">
                                            @if ( f.exeTimeLimit.touched && f.exeTimeLimit.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.exeTimeLimit.errors.required) { Execution Time Limit required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 col-xl-9"></div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-cmd-3">
                                    <div class="form-group">
                                        <button class="btn btn-upload w-100 " [disabled]="cyclesForm.invalid"
                                            (click)="TriggerWebTestCommand(cyclesForm.value)">
                                            <span class="mdi mdi-checkbox-marked-circle-outline"
                                                aria-hidden="true"></span>Execute</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <hr>
                        <div class="row">
                            <div class="col-md-8"></div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-upload w-100 mb-3" (click)="generatePDF()">Convert
                                    to
                                    PDF </button>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-sign w-100 mb-3" (click)="exportexcel()" type="button"><i
                                        class="mdi mdi-cloud-upload-outline btn-icon-prepend"></i>&nbsp; Export
                                    Excel </button>
                            </div>
                        </div>

                        <div class="row">
                            <table class="table table-hover qmig-table" id="tblexample">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <div class="form-check m-0">
                                                <label class="form-check-label">
                                                    <input type="checkbox" (click)="checkUncheckAll($event)"
                                                        [(checked)]="masterSelected" class="form-check-input"> <i
                                                        class="input-helper"></i>
                                                </label>
                                            </div>
                                        </th>
                                        <th>Test Case ID</th>
                                        <th>Object Name</th>
                                        <th> Status</th>
                                        <th>Last Exec Date</th>
                                        <th>Duration Time</th>
                                        <th>Last Exec Cycle</th>
                                        <!-- <th>Action</th>
                                    <th>Download</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (documents of this.selectedCycleTestCases | searchFilter:
                                    datachange1| paginate: { itemsPerPage: pi, currentPage: p } ; track
                                    documents; let i = $index) {

                                    <tr>
                                        <!-- <td class="py-1">
                                        <div class="form-check form-check-flat form-check-primary mt-m10">
                                            <label class="form-check-label">
                                                [(ngModel)]="documents.isSelected"
                                                <input type="checkbox" 
                                                    name="list_name" value="{{documents.tgt_test_id}}"
                                                    (click)="onChange(documents,documents.tgt_test_id, $event)"
                                                    #checkValue class="form-check-input" />
                                                <i class="input-helper"></i>
                                            </label>
                                        </div>
                                    </td> -->
                                        <td>
                                            <div class="form-check mt-33">
                                                <label class="form-check-label">
                                                    <input type="checkbox" class="form-check-input" #checkValue
                                                        (click)="onChange(documents.testcase_id, $event)"
                                                        [checked]="documents.isSelected">
                                                    <i class="input-helper"></i>
                                                </label>
                                            </div>
                                        </td>
                                        <td>{{documents.testcase_id}}</td>
                                        <td>{{documents.object_name}}</td>
                                        <td>{{documents.execution_status}}</td>
                                        <td>{{documents.updated_date1}}</td>
                                        <td>{{documents.execution_time_limit}}</td>
                                        <td>{{documents.operation_name }}</td>
                                        <td>
                                            <button class="btn btn-download" data-bs-toggle="offcanvas"
                                                data-bs-target="#test" (click)="updateRecord(documents)">
                                                <span class="mdi mdi-pencil btn-icon-prepend"></span>
                                            </button>
                                        </td>
                                        <!-- <td>
                                    <button class="btn btn-download" (click)="downloadFile1(documents)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                    </td> -->
                                    </tr>
                                    }
                                </tbody>
                            </table>

                            <div class="custom_pagination">
                                <pagination-controls (pageChange)="p = $event"></pagination-controls>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Execution Status
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-6">
                            <!-- <h3 class="main_h py-4 ps-3"> Code Scan Status -->
                            <button class="btn btn-sync mt-4" (click)="getreqTableData()">Reload
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                            <!-- </h3> -->
                        </div>
                        <div class="col-12 col-sm-6 col-md-6">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange2" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Run No</th>
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>Schema Name</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of tabledata| searchFilter: datachange2| paginate:{ itemsPerPage: piA,
                                currentPage:
                                p1,
                                id:'second' };
                                track con;) {
                                <tr>
                                    <!-- <td>{{p1*piA+$index+1-piA}}</td> -->
                                    <td>{{con.run_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_name }}</td>
                                    <td>{{ con.schemaname }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        @switch (con.status) {
                                        @case ('I') {
                                        <span>Initialize</span>
                                        }
                                        @case ('P') {
                                        <span>Pending</span>
                                        }
                                        @default {
                                        <span>Completed</span>
                                        }
                                        }
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p1 = $event" id="second"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
         <!--Execution Logs-->
         <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Execution Logs
                </button>
            </h2>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="accordion-body">
                        <form class="form qmig-Form">
                            <div class="row">
                                <div class="col-md-4 col-xl-3 ">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Run
                                            No.</label>
                                        <select class="form-select" #code (change)="selectIterforlogs(code.value)">
                                            <option selected value="">Select Run Number </option>
                                            @for( list of iterationList;track list;){
                                            <option value="{{ list.iteration}}">{{list.iteration}}</option>
                                            }
                                        </select>

                                    </div>
                                </div>
                                <div class="col-md-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required"
                                            for="targtetConnection">Operation</label>
                                        <select class="form-select" formControlName="operationType" #oplog
                                             (change)="operationLogs(oplog.value)">
                                            <option selected value="">Select Operation</option>
                                            @for(operation of operation;track operation; ){
                                            <option value="{{ operation.value }}"> {{ operation.option }} </option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 col-xl-5 offset-md-1">
                                    <div class="custom_search mt-4 mb-3 mr-auto ">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Logs " aria-controls="example"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>File Name</th>
                                        <th>Created Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(logs of ExecututionFiles |searchFilter: datachangeLogs|paginate:{
                                        itemsPerPage: 10, currentPage: page3, id:'Four'};
                                        track logs; let i=$index){
                                    <tr>
                                        <td>{{10*page3+i+1-10}}</td>
                                        <td>{{logs.fileName }}</td>
                                        <td>{{logs.created_dt }}</td>
                                        <td>
                                            <button class="btn btn-download" (click)="downloadFile(logs)">
                                                <span class="mdi mdi-cloud-download-outline"></span>
                                            </button>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty list of Reports</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="page3 = $event" id="four"></pagination-controls>
                        </div>
                        <!-- <div class="row">
                            <div class="col-md-12 mt-2">
                                @for(logs of ExecututionFiles |searchFilter: datachangeLogs|paginate:{
                                itemsPerPage: 10, currentPage: page3, id:'Four'};
                                track logs; let i=$index){
                                <div class="client_contact mt-0">
                                    <div class="row">
                                        <div class="col-md-12 px-0">
                                            <ul>
                                                <li>
                                                    <b text="Download" class="toltip">
                                                        <i class="fas fa-cloud-download-alt"></i> :
                                                    </b>
                                                    <a data-toggle="tooltip" data-placement="bottom" download=""
                                                        id="validrepo" class="btn btn-download table-data-fixed"
                                                        (click)="downloadFile(logs)" title="{{ logs.fileName}}">{{
                                                        logs.fileName}}</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                }

                            </div>
                        </div> -->
                    <!-- pagination -->
                    <!-- <div class="custom_pagination">
                        <pagination-controls (pageChange)="page3 = $event" id="Four">
                        </pagination-controls>
                    </div> -->
                </div>
                
            </div>
        </div>
        <!-- Deployment Logs-->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                     Deployment Logs
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="accordion-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row">
                                    
                                    <div class="col-md-3 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="targtetConnection">Run
                                                No</label>
                                            <select class="form-select" formControlName="operationType" #iter
                                                (change)="selectIterforlDepogs(iter.value)">
                                                <option selected value="">Select Run No</option>
                                                @for(role of iterationList ;track role; ){
                                                <option value="{{ role.iteration }}"> {{ role.iteration
                                                    }}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required"
                                                for="targtetConnection">Operation</label>
                                            <select class="form-select" formControlName="operationType" #opdeplog
                                                (change)="operationDepLogs(opdeplog.value)">
                                                <option selected value="">Select Operation</option>
                                                @for(operation of operation;track operation; ){
                                                <option value="{{ operation.value }}"> {{ operation.option }}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3 offset-md-1">
                                        <div class="custom_search mt-4">
                                            <span class="mdi mdi-magnify"></span>
                                            <input type="text" placeholder="Search Reports"
                                                aria-controls="example" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>File Name</th>
                                        <th>Created Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(validrepo of DepLogFiles |searchFilter:
                                datachange|paginate:{
                                itemsPerPage: 10, currentPage: p, id:'third'};
                                track validrepo; let i=$index){
                                    <tr>
                                        <td>{{10*p+i+1-10}}</td>
                                        <td>{{validrepo.fileName }}</td>
                                        <td>{{validrepo.created_dt }}</td>
                                        <td>
                                            <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                                <span class="mdi mdi-cloud-download-outline"></span>
                                            </button>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty list of Reports</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                        </div>
                        <!-- <div class="row">
                            <div class="col-md-12 mt-2">
                                @for(validrepo of DepLogFiles |searchFilter:
                                datachange|paginate:{
                                itemsPerPage: 10, currentPage: p, id:'third'};
                                track validrepo; let i=$index){
                                <div class="client_contact mt-0">
                                    <div class="row">
                                        <div class="col-md-12 px-0">
                                            <ul>
                                                <li>
                                                    <b text="Download" class="toltip">
                                                        <i class="fas fa-cloud-download-alt"></i> :
                                                    </b>
                                                    <a data-toggle="tooltip" data-placement="bottom"
                                                        download="" id="validrepo"
                                                        class="btn btn-download table-data-fixed"
                                                        (click)="downloadFile(validrepo)"
                                                        title="{{ validrepo.fileName }}">{{
                                                        validrepo.fileName }}</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                }

                            </div>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                        </div> -->
                    </div>

            </div>
        </div>
        <!-- Reports -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne4" aria-expanded="false" aria-controls="flush-collapseOne">
                    Reports
                </button>
            </h2>
            <div id="flush-collapseOne4" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="accordion-body">
                    <!-- <h3 class="main_h">Reports & Logs</h3> -->

                    <div class="row">
                        <div class="col-md-3 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Run
                                    No</label>
                                <select class="form-select" #iter4 (change)="SelectIterationForReports(iter4.value)">
                                    <option selected value="">Select Run No</option>
                                    @for(role of iterationList ;track role; ){
                                    <option value="{{ role.iteration }}"> {{ role.iteration
                                        }}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Operation</label>
                                <select class="form-select" #oprep (change)="operationReports(oprep.value)">
                                    <option selected value="">Select Operation</option>
                                    @for(list of operation;track list;){
                                    <option value="{{list.value }}">
                                        {{ list.option }}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3 offset-md-1">
                            <div class="custom_search mt-4">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Reports" aria-controls="example" />
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Created Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(validrepo4 of CycleReport |searchFilter:
                                datachange4|paginate:{
                                itemsPerPage: 10, currentPage: p3, id:'fourth'};
                                track validrepo4; let i=$index){
                                <tr>
                                    <td>{{10*p3+i+1-10}}</td>
                                    <td>{{validrepo4.fileName }}</td>
                                    <td>{{validrepo4.created_dt }}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(validrepo4)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p3 = $event" id="fourth"></pagination-controls>
                    </div>
                    <!-- <div class="row">
                        <div class="col-md-12 mt-2">
                            @for(validrepo4 of CycleReport |searchFilter:
                            datachange4|paginate:{
                            itemsPerPage: 10, currentPage: p3, id:'fourth'};
                            track validrepo4; let i=$index){
                            <div class="client_contact mt-0">
                                <div class="row">
                                    <div class="col-md-12 px-0">
                                        <ul>
                                            <li>
                                                <b text="Download" class="toltip">
                                                    <i class="fas fa-cloud-download-alt"></i> :
                                                </b>
                                                <a data-toggle="tooltip" data-placement="bottom" download=""
                                                    id="validrepo4" class="btn btn-download table-data-fixed"
                                                    (click)="downloadFile(validrepo4)"
                                                    title="{{ validrepo4.fileName }}">{{
                                                    validrepo4.fileName }}</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            }

                        </div>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p3 = $event" id="fourth"></pagination-controls>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="test">
    <div class="offcanvas-header">
        <h4 class="main_h">Test cycle Details </h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="TestCaseForm">
            <div class="form-group">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="object_type">Execute Type</label>
                            <input class="form-control" type="text" id="objecttype_name" placeholder=""
                                formControlName="objecttype_name" [readonly]="disable">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-0">
                            <label class="d-block" for="name">Execution Time</label>
                            <input class="form-control" type="text" id="execution_time"
                                formControlName="exec_time_limit" placeholder="" [readonly]="disable">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="d-block" for="name">Execute Statement</label>
                            <textarea class="form-control" type="text" id="name" placeholder="Execution Statement Name"
                                formControlName="objectname" rows="5"></textarea>
                        </div>
                    </div>
                    <!-- <div class="col-md-12">
                        <div class="form-group mb-0">
                            <label class="d-block" for="name">Initial Error messsage</label>
                            <input class="form-control" type="text" id="error_log" formControlName="initial_error_log"
                                placeholder="">
                        </div>
                    </div> -->
                    <div class="col-md-12">
                        <div class="form-group mb-0" >
                            <label class="d-block mt-3" for="name">Error messsage</label>
                            <input class="form-control" type="text" id="error_log" formControlName="error_log" [readonly]="disable"
                                placeholder="">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="text-right mt-3">
                            <button (click)="saveBtn(this.TestCaseForm.value)" class="btn btn-sign ">Save
                                <!-- <i
                                      *ngIf="Spin" class="fa fa-spinner fa-spin"></i> -->
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
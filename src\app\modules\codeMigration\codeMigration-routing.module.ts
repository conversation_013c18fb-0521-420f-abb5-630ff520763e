import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { CodeObjectComponent } from './common/code-object/code-object.component';
import { DeployComponent } from './common/deploy/deploy.component';
import { ManualConversionComponent } from './common/manual-conversion/manual-conversion.component';
import { StorageObjectComponent } from './common/storage-object/storage-object.component';
import { ValidationComponent } from './common/validation/validation.component';
import { PgcheckComponent } from './common/pgcheck/pgcheck.component';
import { DalconversionComponent } from './common/dalconversion/dalconversion.component';
import { ManualConversionCoComponent } from './common/manual-conversion-co/manual-conversion-co.component';
import { ConversionAgentComponent } from './common/conversion-agent/conversion-agent.component';
import { ModuleAgentComponent } from './common/module-agent/module-agent.component';

const routes: Routes = [
  {path:'', component:LayoutComponent, children:[
    {path: '', redirectTo: 'storageObject', pathMatch: 'full' },
    {path:'codeObject', component:CodeObjectComponent,data:{name:'Code Objects'}},
    {path:'deploy', component:DeployComponent,data:{name:'Deploy'}},
    {path:'manualConversion', component:ManualConversionComponent, data:{name:'Manual Conversion'}},
    {path:'manualConversionCO', component:ManualConversionCoComponent, data:{name:'Manual Conversion'}},
    {path:'storageObject', component:StorageObjectComponent, data:{name:'Storage Objects'}},
    {path:'validation', component:ValidationComponent, data:{name:'Validation'}},
    {path:'pgcheck', component:PgcheckComponent,data:{name:'Pg Check'}},
    {path:'dalconversion', component:DalconversionComponent,data:{name:'DAL Conversion'}},
    {path:'conversionAgent', component:ConversionAgentComponent, data:{name:'Conversion Agent'}},
    {path:'moduleAgent', component:ModuleAgentComponent, data:{name:'Module Agent'}}
  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CodeMigrationRoutingModule { }

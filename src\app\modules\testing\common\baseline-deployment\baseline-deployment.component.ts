import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { SpinnerWhiteComponent } from '../../../../shared/components/spinner-white/spinner-white.component';
import { TestingService } from '../../../../services/testing.service';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgSelectComponent, NgSelectModule } from '@ng-select/ng-select';
import { CommonModule } from '@angular/common';

declare let $: any;
@Component({
  selector: 'app-baseline-deployment',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, SpinnerWhiteComponent, SpinnerComponent, NgSelectModule],
  templateUrl: './baseline-deployment.component.html',
  styles: ``
})
export class BaselineDeploymentComponent {

  pageName: string = ''
  projectId: string = ""

  /*--- Form ---*/
  TestCaseForm: any;
  formSpinner: boolean = false

  targetData: any = [];
  sourceData: any = [];
  sourcepath: any = [];
  targetpath: any = [];

  /*-- Document--*/
  uploadForm = this.formBuilder.group({
    file: ['', [Validators.required]],
    // fileType: [Validators.required],    
  })
  selectFile: any;
  fileName: string = '';

  constructor(
    private titleService: Title,
    private formBuilder: FormBuilder,
    private toast: HotToastService,
    private route: ActivatedRoute,
    private testingService: TestingService,
  ) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit() {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList()
    this.sourcepath = `PRJ${this.projectId}SRC/Source_Backup_Files`;
    this.targetpath = `PRJ${this.projectId}SRC/Target_DB_Backup_Files`;
    this.fetchBaselineFiles(this.sourcepath)
    this.fetchBaselineFiles(this.targetpath)

    this.TestCaseForm = this.formBuilder.group({
      operation: ['', [Validators.required]],
      sourceConnection: [''],
      targetConnection: [''],
      sourceFile: [''],
      targetFile: [''],
      schema: [''],
      tables: [''],

    })
  }

  get f(): any {
    return this.TestCaseForm.controls
  }

  get u(): any {
    return this.uploadForm.controls;
  }


  GetOperation(data: string) {
    if (data === 'Source_Restore') {
      this.TestCaseForm.controls['sourceConnection'].setValidators([Validators.required]);
      this.TestCaseForm.controls['sourceFile'].setValidators([Validators.required]);
      this.TestCaseForm.controls['schema'].clearValidators();
      this.TestCaseForm.controls['tables'].clearValidators();
      this.TestCaseForm.controls['targetConnection'].clearValidators();
      this.TestCaseForm.controls['targetFile'].clearValidators();
    } else if (data === 'Target_Backup') {
      this.TestCaseForm.controls['targetConnection'].setValidators([Validators.required]);
      this.TestCaseForm.controls['targetFile'].clearValidators();
      this.TestCaseForm.controls['schema'].setValidators([Validators.required]);
      this.TestCaseForm.controls['tables'].setValidators();
      this.TestCaseForm.controls['sourceConnection'].clearValidators();
      this.TestCaseForm.controls['sourceFile'].clearValidators();
    } else if (data === 'Target_Restore') {
      this.TestCaseForm.controls['schema'].clearValidators();
      this.TestCaseForm.controls['tables'].clearValidators();
      this.TestCaseForm.controls['targetConnection'].setValidators([Validators.required]);
      this.TestCaseForm.controls['targetFile'].setValidators([Validators.required]);
      this.TestCaseForm.controls['sourceConnection'].clearValidators();
      this.TestCaseForm.controls['sourceFile'].clearValidators();
    }

    this.TestCaseForm.controls['sourceConnection'].updateValueAndValidity();
    this.TestCaseForm.controls['sourceFile'].updateValueAndValidity();
    this.TestCaseForm.controls['targetConnection'].updateValueAndValidity();
    this.TestCaseForm.controls['targetFile'].updateValueAndValidity();
    this.TestCaseForm.controls['schema'].updateValueAndValidity();
    this.TestCaseForm.controls['tables'].updateValueAndValidity();
  }

  /* Connection List*/
  GetConsList() {
    this.testingService.getConList(this.projectId.toString()).subscribe((data: any) => {
      const condata = data['Table1'];
      this.sourceData = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.targetData = condata.filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }

  /*--- Upload File ---*/
  openPopup() {
    this.uploadForm.reset();
    this.fileName = ''
  }

  /*--- File upload    ---*/
  fileUploadSpin: boolean = false
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
  }
  uploadFile() {
    this.fileUploadSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', this.sourcepath);
    this.testingService.uploadDocuments(formData).subscribe(
      (response: any) => {
        this.fetchBaselineFiles(this.sourcepath)
        this.fileUploadSpin = false
        this.uploadForm.controls.file.reset()
        this.fileName = ''
        this.toast.success(response.message)
        $('#demo').offcanvas('hide');
      },
      error => {
        this.fileUploadSpin = false
        this.uploadForm.controls.file.reset()
        this.fileName = ''
        this.toast.error('Something went wrong')
        this.openPopup()
        $('#demo').offcanvas('hide');
      }
    )
  }
  selectedItems: any
  schemaList: any = []
  selectedtabItems: any=[]
  selectTgtId: any
  GetSchemaList(ConnectionId: any) {
    this.selectTgtId = ConnectionId
    this.testingService.SchemaListSelect(ConnectionId).subscribe((data: any) => {
      this.schemaList = data;
    });
  }
  schemas: any = []
  selectSchema(schema: any) {
    this.schemas = []
    this.schemas.push("'" + schema + "'")
    this.GetTablesList(this.selectTgtId)
  }
  tablesList: any = []
  GetTablesList(ConnectionId: any) {
    let obj = {
      connectioId: ConnectionId,
      schemaname: this.schemas.toString()
    }
    this.testingService.tablesSelect(obj).subscribe((data: any) => {
      this.tablesList = data;
      this.tablesList.filter((item: any) => {
        item.type = "ALL"
      })
    });
  }

  triggerBaselineComand() {
    this.formSpinner = true
    var data: any = []
    if (this.TestCaseForm.value.operation == 'Source_Restore') {
      data = {
        SrcConId: this.TestCaseForm.value.sourceConnection,
        restoreFilePath: this.TestCaseForm.value.sourceFile.replace("/mnt/eng", "/mnt/pypod"),
        task: this.TestCaseForm.value.operation,
        projectid: this.projectId.toString()
      }
    }
    else if (this.TestCaseForm.value.operation == 'Target_Backup') {
      var tabs: any = []
      if(this.selectedtabItems.length!=0){
      if (this.selectedtabItems.toString() == "ALL") {
        this.tablesList.forEach((element: any) => {
          tabs.push(element.tablename)
        })
      }
      else{
        tabs=this.selectedtabItems;
      }
    }

      data = {
        TgtConId: this.TestCaseForm.value.targetConnection,
        schema: this.schemas.toString().replace(/'/g, ""),
        tablename: tabs.length==0?'':tabs.toString(),
        task: this.TestCaseForm.value.operation,
        projectid: this.projectId.toString()
      }
    }
    else {
      data = {
        TgtConId: this.TestCaseForm.value.targetConnection,
        restoreFilePath: this.TestCaseForm.value.targetFile.replace("/mnt/eng", "/mnt/pypod"),
        task: this.TestCaseForm.value.operation,
        projectid: this.projectId.toString()
      }
    }

    this.testingService.baselineCommand(data).subscribe((response: any) => {
      this.formSpinner = false
      this.toast.success(response.message)
    }, error => {
      this.formSpinner = false
      this.toast.error('Something went wrong')
    })
  }
  SourceBackupData: any = [];
  TargetBackupData: any = [];

  fetchBaselineFiles(value: any) {
    this.testingService.getFilesFromDirectory(value).subscribe((response: any) => {
      if (value.includes("Source_Backup_Files")) {
        this.SourceBackupData = response
      }
      else {
        this.TargetBackupData = response
      }
      // if(response?.length!=0){
      // this.toast.success(response?.message)
      // }
    })
  }
}

import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { goldenGateAPIConstant } from '../constant/goldenGateAPIConstant'
import { environment } from '../environments/environment';
import { GetConfigData, GetFilesFromExpath, GetTablesByschemaGG, SchemaListSelect, SchemaSelect, UploadProjectDocs, conList, createFile, fileStatus, ggCreateFileReq, insertTablesCommand, runGGScripts } from '../models/interfaces/types';
import { APIConstant } from '../constant/APIConstant';

@Injectable({
  providedIn: 'root'
})
export class GoldenGateService {

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl + 'GoldenGate/'

    //getConList
    getConListURL = this.apiURL + goldenGateAPIConstant.getConList;
    getConList = (body: string): Observable<conList> => {
      return this.apiService.get(this.getConListURL + body)
    }
  //SchemaListSelect}
  SchemaListSelectURL = this.apiURL + goldenGateAPIConstant.SchemaListSelect;
  SchemaListSelect = (body: SchemaListSelect): Observable<SchemaSelect> => {
    return this.apiService.get(this.SchemaListSelectURL + body.projectid + '&ConId=' + body.connectioId)
  }
  
  //insertTablesCommand
  insertTablesCommandURL = this.apiURL + goldenGateAPIConstant.insertTablesCommand;
  insertTablesCommand = (body: insertTablesCommand): Observable<fileStatus> => {
    return this.apiService.post(this.insertTablesCommandURL, body);
  }

  //GetTablesByschemaGG
  GetTablesByschemaGGURL = this.apiURL + APIConstant.common.GetTablesByschemaGG;
  GetTablesByschemaGG = (body: GetTablesByschemaGG): Observable<any> => {
    return this.apiService.get(this.GetTablesByschemaGGURL + body.schema + '&srcId=' + body.srcId)
  }

  //getGoldenGatePod
  getGoldenGatePodURL = this.apiURL + APIConstant.common.getGoldenGatePod;
  getGoldenGatePod = (): Observable<string> => {
    return this.apiService.get(this.getGoldenGatePodURL)
  }

  //GetFilesFromExpath
  GetFilesFromExpathURL = this.apiURL + goldenGateAPIConstant.GetFilesFromExpath;
  GetFilesFromExpath = (body: string): Observable<GetFilesFromExpath> => {
    return this.apiService.get(this.GetFilesFromExpathURL + body)
  }

  //GetGGShellDirectoryEx
  GetGGShellDirectoryExURL = this.apiURL + APIConstant.common.GetGGShellDirectoryEx;
  GetGGShellDirectoryEx = (body: string): Observable<GetFilesFromExpath> => {
    return this.apiService.get(this.GetGGShellDirectoryExURL + body)
  }

  //runGGScripts
  runGGScriptsURL = this.apiURL + goldenGateAPIConstant.runGGScripts;
  runGGScripts = (body: runGGScripts): Observable<any> => {
    return this.apiService.post(this.runGGScriptsURL, body);
  }

  //ggCreateFileReq

  ggCreateFileReqURL = this.apiURL + goldenGateAPIConstant.ggCreateFileReq;
  ggCreateFileReq = (body: ggCreateFileReq): Observable<createFile> => {
    return this.apiService.post(this.ggCreateFileReqURL, body);
  }

  //getGGFiles
  getGGFilesURL = this.apiURL + goldenGateAPIConstant.getGGFiles;
  getGGFiles = (body: string): Observable<GetFilesFromExpath> => {
    return this.apiService.get(this.getGGFilesURL + body)
  }

  //deleteFiles
  deleteFilesURL = this.apiURL + goldenGateAPIConstant.deleteFiles;
  deleteFiles = (body: string): Observable<fileStatus> => {
    return this.apiService.get(this.deleteFilesURL + body)
  }

  //downloadLargeFiles
  downloadLargeFilesURL = this.apiURL + goldenGateAPIConstant.downloadLargeFiles;
  downloadLargeFiles = (body: string): Observable<any> => {
    return this.apiService.get(this.downloadLargeFilesURL  + encodeURIComponent(body), { responseType: 'blob' as 'json' })
  }

  //UploadProjectDocs
  UploadProjectDocsURL = this.apiURL + APIConstant.common.UploadProjectDocs;
  UploadProjectDocs = (body: any): Observable<UploadProjectDocs> => {
    return this.apiService.post(this.UploadProjectDocsURL, body);
  }
  ExecuteInPodURL = this.apiURL + APIConstant.common.executeInPodFile;
  ExecuteInPod(body: any): Observable<any> {
    return this.apiService.post(this.ExecuteInPodURL, body);
  }
  DataMigrationCommandURL = environment.serviceUrl1 + goldenGateAPIConstant.DataMigrationCommand;
  DataMigrationCommand = (body: any): Observable<GetConfigData> => {
    return this.apiService.post(this.DataMigrationCommandURL, body)
  }

  // generate Pod 
  generateGGPodURL = this.apiURL + goldenGateAPIConstant.generateGGPod;
  generateGGPod(body: any): Observable<any> {
    return this.apiService.post(this.generateGGPodURL, body);
  }
  
}


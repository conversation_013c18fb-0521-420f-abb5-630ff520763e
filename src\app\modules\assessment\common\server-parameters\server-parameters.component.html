<div class="v-pageName">{{pageName}}</div>

<!---Reports & Logs---->
<div class="qmig-card">
    <!-- <h3 class="main_h px-3 pt-3">Extraction </h3> -->
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Server Parameters
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <!---Code Extraction List --->
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="codeextractionForm">
                            <div class="row">
                                <!-- Connection Name -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Connection
                                            Name</label>
                                        <select class="form-select" formControlName="connectionName" #Myselect (change)="
                                        selectedfiletype(Myselect.value);" aria-label="Default select example">
                                            <option>Select a Connection Name</option>
                                            @for(ConsList of ConsList;track ConsList; ){
                                            <option value="{{ConsList.Connection_ID}}">{{ConsList.conname}}</option>
                                            }
                                        </select>
                                        @if ( f.connectionName.touched && f.connectionName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.connectionName.errors.required) {Connection Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- Check Details -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4">
                                    <div class="form-group mt-1">
                                        <div class="text-right">
                                            <button class="btn btn-upload w-100"
                                                (click)="openModal(this.codeextractionForm.value)"
                                                data-bs-toggle="offcanvas" data-bs-target="#demo"
                                                [disabled]="codeextractionForm.invalid"> <span
                                                    class="mdi mdi-checkbox-marked-circle-outline"
                                                    aria-hidden="true"></span>Check
                                                Details</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    Server Parameter Report
                </button>
            </h3>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-md-6 col-xl-6">
                            <h3 class="main_h pt-3 ps-3">Server Parameters Reports List
                                <button class="btn btn-sync" (click)="filterExecutionReports1()">
                                    @if(ref_spin){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </h3>
                        </div>
                        <div class="col-md-6 col-xl-6">
                            <div class="custom_search cs-r mb-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Extraction Reports" class="form-control"
                                [(ngModel)]="datachange" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover qmig-table" id="example" style="width: 100%">
                        <thead>
                            <tr>
                                <th>S.NO</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(validrepo of ServerReports |searchFilter: datachange|paginate:{
                                itemsPerPage: pi, currentPage: p, id:'second'};
                                track validrepo;){
                                <tr>
                                    <td>{{p*pi+$index+1-pi}}</td>
                                    <td>{{validrepo.fileName }}</td>
                                    <td>{{validrepo.created_dt}}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="p2 = $event" id="first"></pagination-controls>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Execution Logs
                </button>
            </h3>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-sm-6 col-md-8 col-xl-10">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(exelogs of ExecutionLogs |searchFilter:
                            datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: page4, id:'five'};
                            track exelogs){
                            <tr>
                                <td>{{ page4*10+$index+1-10 }}</td>
                                <td>{{exelogs.fileName }}</td>
                                <td>{{exelogs.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(exelogs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page4 = $event" id="five">
                    </pagination-controls>
                </div>
            </div>
        </div>
        <!---Awr Execution Report--->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Server Parameters Execution Status
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-6">
                            <h3 class="main_h py-4 ps-3"> Server Parameters Status
                                <button class="btn btn-sync" (click)="getreqTableData()">
                                    @if(status_spin){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </h3>
                        </div>
                        <div class="col-12 col-sm-6 col-md-6">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Sno</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of tabledata| searchFilter: datachange1| paginate:{ itemsPerPage: piC,
                                currentPage:
                                p3,
                                id:'third' };
                                track con;) {
                                <tr>
                                    <td>{{p3*piC+$index+1-piC}}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        @switch (con.status) {
                                        @case ('I') {
                                        <span>Initialize</span>
                                        }
                                        @case ('P') {
                                        <span>Pending</span>
                                        }
                                        @default {
                                        <span>Completed</span>
                                        }
                                        }
                                    </td>
                                    <td>
                                        <button class="btn btn-delete" (click)="deleteTableDatas(con.request_id)">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p3 = $event" id="third"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
        <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
            <div class="offcanvas-header">
                <h4 class="main_h"> Code Extration</h4>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
            </div>
            <!--Connection Name--->
            <div class="offcanvas-body">
                <form class="form qmig-Form checkForm">
                    <div class="form-group">
                        <label class="form-label d-required" for="name"> Connection Name</label>
                        : &nbsp;{{ conName }}
                    </div>
                    <!--Execute-->
                    <div class="form-group">
                        <div class="body-header-button">
                            <!-- projectConRunTblInserts(codeextractionForm.value, false)" -->
                            <button class="btn btn-upload w-100 me-1" (click)="AssessmentCommand()">
                                Execute @if(runinfospins){<app-spinner />}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    </div>
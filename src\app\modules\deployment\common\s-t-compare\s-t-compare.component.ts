import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DeploymentService } from '../../../../services/deployment.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { TabsComponent } from '../tabs/tabs.component';


@Component({
  selector: 'app-s-t-compare',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, TabsComponent],

  templateUrl: './s-t-compare.component.html',
  styles: ``
})
export class STCompareComponent {

  pi: number = 10;
  pi_A: number = 10;
  pi1: number = 10;
  pi2: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;
  grid_active1: boolean = false;
  not_grid1: boolean = false;
  grid_active2: boolean = false;
  not_grid2: boolean = false;
  fileData: any;
  datachange: any = [];

  datachange1: any;
  TgtUpdateTgt: any;
  logfile: any;
  project_name: any
  projectId: any
  getRole: any
  prjSrcTgtData: any = [];

  targetcomapareform: any;
  targetdeltaform: any;
  NoDataHide: boolean = false;
  Targetfilename: any = "";
  datachanges: any;
  datachanges1: any;
  datachanges2: any;
  datachanges3: any;
  pageNumber: number = 1;
  p: number = 1;
  page: number = 1;
  pages:number=1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  SourceCo:any;
  TargetConn:any;
  viewData:any;

  // @ViewChild(PlyrComponent)
  // plyr: PlyrComponent | undefined;

  // // or get it from plyrInit event
  // player: Plyr | undefined;
  poster = 'assets/videos/qmig_ui.jpg';
  iteratSel: boolean = true;
  UserInfo: any;
  pageName: string = '';
  constructor(public fb: FormBuilder,
    public deployment: DeploymentService,
    private toast: HotToastService,
    private route: ActivatedRoute,
  ) {
    this.pageName = this.route.snapshot.data['name'];
    this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    this.Targetfilename = localStorage.getItem('Targetfilename');
    let userJson = localStorage.getItem('userData') as string;
    this.UserInfo = JSON.parse(userJson);
  }
  dropdownList = [];
  selectedItems = [];

  schemaName: any = [];

  ngOnInit(): void {
    this.targetcomapareform = this.fb.group({
      tgtcon: ['', [Validators.required]],
      runnumber: ['', [Validators.required]],
      contype: ['', [Validators.required]],
      sourcecon: ['', [Validators.required]],
      scheman: ['', [Validators.required]],
      objectt: ['', [Validators.required]],
      objectttype:['', [Validators.required]],
      viewType:['', [Validators.required]],
      // objecttype: ['', [Validators.required]],
    });
    this.targetdeltaform = this.fb.group({
      scrun: ['', [Validators.required]],
      scschema: ['', [Validators.required]],
    });
    this.getInfraSelect()
    this.getBlobfileData();
    this.getRunNumber();
    this.getRunNumbers();
    this.getPrjExeLogSelectTask("null");
    this.GetRequestTableData();
  }

  // select schema data
  selectschema(data: any) {
    this.schemaName = data;
    this.getObjectNames(data);
    //console.log(data)
  }
  get validate() {
    return this.targetcomapareform.controls;
  }
  runNumbers: any;
  getRunNumber() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbers = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }


  blobfile: any;
  getBlobfileData() {
    // this.project.GetBlobfileByFileName("target_compare.mp4").subscribe((data: any) => {
    //   this.blobfile = data.fileUrl;
    // })
  }
  deltaschema:any;
  sourceSelected: boolean = false
  sourceSel(value: string) {
    value != '' ? this.sourceSelected = true : this.sourceSelected = false;
    this.deltaschema=value;
    this.GetSourceFilePath();
  }

  get validates() {
    return this.targetdeltaform.controls;
  }
  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }

  TgtCodeShow() {
    this.TgtUpdateTgt = true;
  }
  TgtCodeShows() {
    this.TgtUpdateTgt = false;
  }

  onKey() {
    this.pageNumber = 1;
    this.page2=1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3=1;
  }
 
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all') {
      this.p1 = 1
    }
    if (value == '20') {
      this.p1 = 1
    }
    if (value == '50') {
      this.p1 = 1
    }
    if (value == '100') {
      this.p1 = 1
    }
  }

  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }

  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }
  
  GetDBConnections() {
    const projectId = this.projectId.toString();
    this.deployment.getConList(projectId).subscribe((data) => {
      this.prjSrcTgtData = data['Table1'];
      this.TargetConn = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "T" && item.migsrctype == "D";
      });
      this.SourceCo = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "S" && item.migsrctype == "D";
      });
    })
  }
  schemaList: any = []
  srcConnection: any
  conid: any;
  SourceConn: any;
  srcConn: any;
  prjSchemaLists(conid: any) {
    this.SourceConn = conid;
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnection: conid.toString()
    }
    this.deployment.getschemaList(obj).subscribe((data) => {
      this.schemaList = data['Table1'];
    });
  }
  schemaNames: any;

  uploadedData: any = [];
  getuploadedFiles(schema: any) {
    let requestObj: any = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "Reports",
      subFolderName1: "Postgres",
      subfoldername2: schema,
    };

    // GetAzureNestedFiles
    // this.project.GetFilesBySchema(requestObj).subscribe((data: any) => {
    //   if (data.value != "" && data.value != null) {
    //     // //console.log("success")
    //   }
    //   else {
    //     this.uploadedData = data;
    //     this.NoDataHide = true;
    //   }
    // });
  }
  spin_process: any
  infraData: any
  filtered: any
  getInfraSelect() {
    let id = this.projectId;
    // this.project.InfraSelect(id).subscribe((data) => {
    //   this.infraData = data['jsonResponseData']['Table1'];
    //   this.filtered = this.infraData.filter((element: any) => {
    //     return element.active == 'True';
    //   });
    // });
  }
  objCategory: any = [
    { values: '1', option: 'ALL' },
    { values: '2', option: 'Storage_Objects' },
    { values: '3', option: 'Code_Objects' }
  ]
  SelectObjectTypes(value: any) {
    var obj1 = ""
    this.objectType = [];
    if (value == "All") {
      obj1 = "0"
      this.objectType = [
        { values: 'ALL', option: 'ALL' }
      ];
    }
    if (value == "Storage_Objects") {
      obj1 = "1"
      this.objectType = [
        { values: 'ALL', option: 'ALL' },
        { values: 'TABLES', option: 'TABLES' },
        { values: 'VIEWS', option: 'VIEWS' },
        { values: 'MATERIALIZED_VIEWS', option: 'MATERIALIZED_VIEWS' },
        { values: 'SEQUENCES', option: 'SEQUENCES' },
        { values: 'SYNONYMS', option: 'SYNONYMS' },
        { values: 'TYPES', option: 'TYPES' },
      ];
    }
    if (value == "Code_Objects") {
      obj1 = "2"
      this.objectType = [
        { values: 'ALL', option: 'ALL' },
        { values: 'PROCEDURES', option: 'PROCEDURES' },
        { values: 'FUNCTIONS', option: 'FUNCTIONS' },
        { values: 'PACKAGES', option: 'PACKAGES' },
        { values: 'TRIGGERS', option: 'TRIGGERS' },
      ];
    }
    let abc = this.objCategory.filter((item: any) => {
      return item.values == value;
    })
    this.obtypevalue = abc[0].option;
  }
  objectType: any = [];
  obtypevalue: any
  iteration: any
  excelFileName: any = "No Data Found"
  // selectIteration(iter: any) {
  //   this.iteration = iter
  //   let obj = {
  //     projectID: this.projectId.toString(),
  //     containerName: 'qmigratorfiles' + this.projectId,
  //     path: "Code/Delta_process/" + iter + "/Target/EXCELREPORT/"
  //   }
  //   // this.project.getFileLogs(obj).subscribe((data: any) => {
  //   //   //console.log(data)
  //   //   if (data) {
  //   //     this.iteratSel = false
  //   //     this.excelFileName = data['0'].name
  //   //   } else {
  //   //     this.excelFileName = "No Data Found"
  //   //   }
  //   //   //console.log(this.excelFileName)
  //   // })
  // }

  selectIteration(iter: any) {
    this.iteration = iter;
  }

  selectedDropdown(data: any) {
    if (data == "1") {
      this.GetDBConnections();
    }
    else if (data == "") {

    }
  }
  onItemSelect(item: any) {
    this.schemaName.push(item.schemaname);
  }

  onItemobjectSelect(item: any) {
    this.objectType.push(item.objectType);
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.schemaname);
    this.schemaName.splice(inde, 1);
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.schemaname);
    });
  }
  onDeSelectAll(items: any) {
    this.schemaName = [];
  }

  uploadedData1: any
  spin_delete: any;
  isLoading: any = [];
  schemaData: any
  getFileSchemas(iteration: any) {
    let obj = {
      projectId: this.projectId,
      iteration: iteration,
      migType: "target"
    }
    // this.project.GetFileSchemas(obj).subscribe((data: any) => {
    //   this.schemaData = data['Table1'];
    //   for (const element of this.schemaData) {
    //     element.schemaname = element.schemaname.toUpperCase();
    //   }
    // })
  }
  GetSchema(runno: any) {
    let obj = {
      projectId: this.projectId,
      runno: runno
    }
    // this.project.GetSchemaSelect1(obj).subscribe((data: any) => {
    //   this.schemaNames = data['Table1'];
    // });
  }
  sas: any
  download_path: any
  tempval1: any
  tempval2: any
  tempval3: any


  isLoading1: any = [];
  isLoading2: any = [];
  isLoading3: any = [];

  isload1(value: any) {
    this.tempval1 = value
    this.isLoading1[value] = true
    this.isLoading2[this.tempval2] = false
    this.isLoading3[this.tempval3] = false

  }
  isload2(value: any) {
    this.tempval2 = value
    this.isLoading1[this.tempval1] = false
    this.isLoading2[value] = true
    this.isLoading3[this.tempval3] = false

  }
  isload3(value: any) {
    this.tempval3 = value
    this.isLoading1[this.tempval1] = false
    this.isLoading2[this.tempval2] = false
    this.isLoading3[value] = true

  }

  spin_file: boolean = false
  targetPaths: any
  GetTargetPaths(schema: any) {
    let obj = {
      projectId: this.projectId.toString(),
      schemaName: schema,
      objectName: "null",
      iteration: this.iteration
    }
    // this.project.getTargetFilePaths(obj).subscribe((data: any) => {
    //   this.targetPaths = data['Table1']
    //   this.NoDataHide = true;
    //   for (const element of this.targetPaths) {
    //     let schemaobjtype = element.baseline_filepath.split('/')[6] + "/" + element.baseline_filepath.split('/')[7]
    //     element.baselineFile = element.objectname + "_baseline.sql"
    //     element.currentFile = element.objectname + "_current.sql"
    //     element.htmlFile = element.objectname + ".html"
    //     element.schemaobj = schemaobjtype
    //     element.lastmodified = ""
    //   }

    // })
  }
  spin_integrate1: boolean = false
  SandTCompareBtn() {
    this.spin_integrate1 = true
    let obj = {
      projectID: this.projectId.toString(),
      command: 'delta_src_tgt_compre.sh',
      ObjectType: this.UserInfo.email,
      id: this.projectId.toString(),
      //ObjectType: this.srcConnection,
      // DbName: migType,
      schema: "'" + this.schemaName.toString() + "'",
      ConnectionName: this.iteration,//iteration or run no.
      Connection: this.srcConnection,
      isQbook: true
      //Operation: this.selectedObType
    }


    // this.project.ExecuteCommandOnVM(obj).subscribe(
    //   (data: any) => {
    //     this.spin_integrate1 = false;
    //     if ((data.message == 'Command Executed Successfully')) {
    //       this.alertService.success(data.message);
    //     }
    //   },
    //   (error) => {
    //     this.spin_integrate1 = false;
    //     this.alertService.danger(
    //       'Something Went Wrong please try Again Later!'
    //     );
    //   }
    // );
  }
  spin_downl: boolean = false
  sass: any
  downloadExcelFile(filename: any) {
    if (this.iteration != "") {
      this.spin_downl = true;
      let obj = {
        projectID: this.projectId.toString(),
        containerName: "qmigratorfiles" + this.projectId,
        filename: filename,
        path: "Code/Delta_process/" + this.iteration + "/Source/EXCELREPORT/Oracle_New_Delta_Objects.xlsx"
      }
      //   this.project.GetFileContentfrompath(obj).subscribe((data: any) => {
      //     this.sass = data.message
      //     const downloadLink = document.createElement('a');
      //     const fileName = filename;
      //     downloadLink.href = ' data:application/octet-stream;base64,' + this.sass
      //     downloadLink.download = fileName;
      //     downloadLink.click();
      //     this.spin_downl = false
      //   })
      // }
      // else {
      //   this.alertService.danger("File Path Empty");
      // }
    }
  }

  schemaNamess:any;
  GetSCSchemaList(runno: any) {
    this.schemaNamess = [];
    let obj = {
      projectId: this.projectId,
      runno: runno,
    }
    this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
      this.schemaNamess = data['Table1'];
    });
  }
  Deltabaseline: any;
  DeltaCommand(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.contype,
      srcCon: value.sourcecon,
      tgtCon: value.tgtcon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      objectName: value.objectttype,
      viewOption:value.viewType,
      extraction_Category: "S_T_COMPARE",
      task: "S_T_COMPARE",
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      this.Deltabaseline = data
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data);
      }
    })
  }
  ExeLog: any = {};
  prjLogData: any;
  selectedrun: any;
  getPrjExeLogSelectTask(value: any) {
    this.selectedrun = value;
    if (value == "") {
      value = "null";
    }
    this.prjLogData = [];
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operationType = "S_T_COMPARE";
    this.ExeLog.action = "null";
    this.ExeLog.row_id = "null";
    this.ExeLog.runno = value;
    this.ExeLog.operationName = "null";
    this.deployment.PrjExelogSelectTask(this.ExeLog).subscribe((data: any) => {
      this.prjLogData = data['Table1'];
    
    })
  }
  ref_spin: boolean = false
  refresh()
  {
    this.GetRequestTableData();
  }
  RequestTableData: any;
  GetRequestTableData() {
    let obj = {
      projectId: this.projectId,
      operationType: 'S_T_Compare'
    }
    this.ref_spin=true;
    this.deployment.GetRequestTableData(obj).subscribe((data: any) => {
      this.RequestTableData = data['Table1'];
      this.ref_spin=false;
    });
  }

  runNumbersData: any;
  getRunNumbers() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbersData = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }
  deleteResponse: any;
  // delete request table data
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.deployment.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      if (data['Table1'][0].v_status) {
        this.toast.success(data['Table1'][0].v_status);
      }
      else {
        this.toast.error(data);
      }
	  this.GetRequestTableData();
    })
  }
  LogsData: any;
  GetFilesExecutionLogsFromDirectory() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iterationForLogs + "/Execution_Logs/Deployment/S_T_Compare"
    };
    this.LogsData=[];
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.LogsData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }

  iterationForLogs: any;
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value;
    this.GetFilesExecutionLogsFromDirectory();
  }

  fileResponse: any;
  spin_dwld: any;
  //download files
  downloadFile(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  run:any;
  selectRunnumber(itera: any) {
    this.run = itera;
    this.GetSCSchemaList(itera);
  }
  GetSourceFilePathData:any;
  GetSourceFilePath()
  {
    const obj = {
      iteration:this.run,
      projectId: this.projectId.toString(),
      objectName: null,
      type:"T",
      schemaName:this.deltaschema.toString(),
    }
    this.deployment.GetSourceFilePath(obj).subscribe((data: any) => {
      this.GetSourceFilePathData = data['Table1'];
      for (const element of this.GetSourceFilePathData) {
        let schemaobjtype = element.baseline_filepath.split('/')[6] + "/" + element.baseline_filepath.split('/')[7]
        element.baselineFile = element.objectname + "_baseline.sql"
        element.currentFile = element.objectname + "_current.sql"
        element.htmlFile = element.objectname + ".html"
        element.schemaobj = schemaobjtype
        element.lastmodified = ""
        element.basefilename = element.objectname
      }
    });
  }
  downloadFiles(path:string,filename:string) {
    var pth=path.split("/mnt/pypod/")[1]
    pth="/mnt/eng/"+pth
    this.deployment.downloadLargeFiles(pth).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = filename;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  objectNames:any;
  selectedSchema:string = ''
  getObjectNames(value: string) {
    this.selectedSchema = value
    let object = {
      RunNo :this.iteration,
      schemaName: value
    }
    this.viewData = [ { value: 'Format_View', view: 'SourceStatements-TargetFile' },{ value: 'Target_View', view: 'SourceStatements-TargetStatements' }]
    this.deployment.getObjectNames(object).subscribe((data: any) => {
      this.objectNames = data['Table1']
    })
  }
  
 
}


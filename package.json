{"name": "qmigrator-qube-a17", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:QMigratorQubeA17": "node dist/qmigrator-qube-a17/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^17.3.0", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/platform-server": "^17.3.0", "@angular/router": "^17.3.0", "@angular/ssr": "^17.3.6", "@ng-select/ng-select": "^12.0.7", "@ngneat/overview": "^6.0.0", "@ngxpert/hot-toast": "^2.0.0", "bootstrap": "^5.3.3", "chart.js": "^3.4.0", "express": "^4.18.2", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "marked": "^9.1.6", "ng2-charts": "^3.1.2", "ngx-markdown": "^17.1.1", "ngx-pagination": "^6.0.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.6", "@angular/cli": "^17.3.6", "@angular/compiler-cli": "^17.3.0", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}
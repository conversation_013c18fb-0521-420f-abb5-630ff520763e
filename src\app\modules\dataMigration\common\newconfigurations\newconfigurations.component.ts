import { Component } from '@angular/core';
import { FormBuilder, FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { HotToastService, Toast } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { DashboardService } from '../../../../services/dashboard.service';

declare let $: any;


@Component({
  selector: 'app-newconfigurations',
  standalone: true,
  imports: [NgSelectModule, CommonModule, FormsModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './newconfigurations.component.html',
  styles: ``
})
export class NewconfigurationsComponent {
  projectId: string = ""
  migtypeid: string = ""
  executed: boolean = false

  pageName: string = ''
  schemalable: string = ''
  ExecutionForm: any;
  Editform: any;
  getRunSpin11:boolean = false;
  constructor(private titleService: Title, private route: ActivatedRoute, private dbService: DashboardService,private formBuilder: FormBuilder, private datamigration: DataMigrationService, private toast: HotToastService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.extractForm = this.formBuilder.group({
      sourceConnection: ['', Validators.required],
      schema: ['', Validators.required]
    })
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }

    this.CPUForm = this.formBuilder.group({
      sourceConnection: ['', Validators.required]
    })
    this.monitorForm = this.formBuilder.group({
      sourceConnection: ['', Validators.required],
      targetConnection: [''],
    })
    this.Editform = this.formBuilder.group({
      sourceConnection: ['', Validators.required],
      targetConnection: ['', Validators.required],
    })
    this.getConfigMenu();
    this.GetConsList();
    this.getPodList();
    //this.fetchConfigFiles('1')
    this.ExecutionForm = this.formBuilder.group({
      operation: ['', [Validators.required]],
      config: ['', [Validators.required]],

    })
  }
  cdcLoadData: any = [
    { name: "Database", value: "Database" },
    { name: "File", value: "File" },
  ]
  DBAUpdateForm = this.formBuilder.group({
    read: [''],
  })
  getForm = this.formBuilder.group({
    operation: ['', [Validators.required]],
    confiName: ['', [Validators.required, Validators.maxLength(15)]],
    sourceConnection: ['', [Validators.required]],
    targetConnection: [''],
    schemas: [''],
    tgtschemas: [''],
    tables: [''],
    request_CPU: [''],
    limit_CPU: [''],
    request_CPU_measure: [''],
    request_Memory_measure: [''],
    limit_CPU_measure: [''],
    limit_Memory_measure: [''],
    data_LoadType: [''],
    cdcLoadType: [''],
  })
  extractForm: any
  CPUForm: any
  monitorForm: any

  get f() {
    return this.getForm.controls;
  }
  get fs() {
    return this.extractForm.controls;
  }
  get cf() {
    return this.CPUForm.controls;
  }
  get mf() {
    return this.monitorForm.controls;
  }
  configData: any
  configData1: any
  getConfigMenu() {
    this.datamigration.GetConfigMenu(this.migtypeid).subscribe((data: any) => {
      this.configData = data['Table1']
      this.configData = this.configData.filter((item: any) => {
        return item.configtype != "Data Compare" && item.configtype != "CDC"
      })

      this.configData1 = this.configData.filter((item: any) => {
        return item.configtype != "Catchup"
      })
    })
  }

  ConsList: any = [];
  schemaList: any = []
  tgtlist: any = []
  auditList: any = []
  GetConsList() {
    this.datamigration.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtlist = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      this.auditList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'A' && item.conname != '';
      });
      this.auditList = this.auditList[0]
    });
  }
  configFiles: any = []
  confFiles: any
  confFiles1: any
  ref_spin: boolean = false

  fetchConfigFiles(value: any) {
    this.confFiles = []
    if (value == '0') {
      this.ref_spin = true
    }
    const path = "Config_Files/Data_Migration"//"AIRFLOW_FILES/Data_Migration_Reports/"
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.confFiles = data
      // this.fetchConfigFiles1()
      this.ref_spin = false
    })
  }
  fileSrc: any
  fetchSourceConfigFiles(conn: any) {
    this.confFiles = []
    this.ref_spin = true
    this.fileSrc = conn
    const path = conn + "/Config_Files/Data_Migration";
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.confFiles = data
      this.ref_spin = false
    })
  }
  tgtconn: any
  fetchTargetConfigFiles(conn: any) {
    this.confFiles = []
    this.tgtconn = conn;
    this.ref_spin = true
    const path = this.fileSrc + "/" + conn + "/Config_Files/Data_Migration";
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.confFiles = data
      this.ref_spin = false
    })
  }

  filterFiles(value: any) {
    var type = ""
    if (value == 'Initial_Data_Load') {
      type = "init_"
    }
    else if (value == 'CDC_Data_Load') {
      type = "cdc_"
    }
    else if (value == 'Index_Data_Load') {
      type = "index_"
    }
    else if (value == "E2E_Data_Load") {
      type = "e2e"
    }
    this.confFiles1 = this.confFiles.filter((item: any) => {
      return item.fileName.startsWith(type)
    })
  }
  fetchConfigFiles1() {
    const path = "Config_Files/CDC";//"AIRFLOW_FILES/Data_Migration_Reports/"
    var tempFiles: any = []
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      tempFiles = data
      tempFiles.filter((item: any) => {
        this.confFiles.push(item);
      })
      this.confFiles.sort((a: any, b: any) => {
        return new Date(b.created_dt).getTime() - new Date(a.created_dt).getTime();
      });
    })
  }
  tgtSchemaList: any = []
  selectedItemstgt: any
  getSchemasList(ConnectionId: any, value: string) {
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId,
    };
    if (this.migtypeid == "31" && value == "S") {
      this.schemaList = []
      var local = this.ConsList.filter((item: any) => {
        return item.Connection_ID == ConnectionId
      })
      let obj = {
        schema_name: local[0].dbname
      }
      this.schemaList.push(obj)
    }
    else if (this.migtypeid == "31" && value == "T") {
      this.tgtSchemaList = []
      this.tgtlist.filter((item: any) => {
        if (item.Connection_ID == ConnectionId) {
          let obj = {
            schema_name: item.dbname
          }
          this.tgtSchemaList.push(obj)
        }
      })
    }
    else {
      this.datamigration.SchemaListSelect(obj).subscribe((data: any) => {
        if (value == "S") {
          this.schemaList = data['Table1'];
          this.schemaList = this.schemaList.filter((item: any) => item.schema_name !== 'ALL');
          this.schemaList.forEach((item: any) => {
            item.type = "ALL"
          })
          this.selectedSourceConnection = value;
        } else {
          this.tgtSchemaList = data['Table1'];
          this.selectedSourceConnection = value;
          this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => {
            return item.schemaname != "ALL"
          });
        }
        if (this.migtypeid == "35" || this.migtypeid == "34") {
          this.fetchDynamoTables(ConnectionId);
        }
      });
      this.ReadLocalJson()
      this.ReadLocalJson1()
    }
  }
  schemaList1: any = []
  getSchemasList1(ConnectionId: any) {
    // this.getTableSrcId = ConnectionId
    //console.log(ConnectionId)
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId
    }

    this.datamigration.SchemaListSelect(obj).subscribe((data: any) => {
      this.schemaList1 = data['Table1'];
    })


    // if (this.migtypeid == "35" || this.migtypeid == "34") {
    //   this.fetchDynamoTables(ConnectionId);
    // }
  }
  tablesList: any = []
  fetchDynamoTables(conId: any) {
    this.datamigration.fetchDynamoTables(conId).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  DataMigrationCommand(formData: any) {
    this.executed = true
    let obj: any = []
    if (formData.operation == "Catchup") {
      obj = {
        projectId: this.projectId.toString(),
        srcId: formData.sourceConnection,
        tgtId: formData.targetConnection,
        schema: formData.schemas,
        tgtschema: formData.tgtschemas,
        task: formData.operation,
        fileName: formData.confiName,
        reffilename: formData.refFile.split(".")[0],
        auditFlag: formData.auditLogs,
        podConfig: formData.podConfig,
        parentTask: formData.prioperation
      }
    }
    else {
      if (formData.auditLogs == "") {
        formData.auditLogs = false
      }

      obj = {
        projectId: this.projectId.toString(),
        srcId: formData.sourceConnection,
        tgtId: formData.targetConnection,
        schema: formData.schemas,
        tgtschema: formData.tgtschemas,
        concurrency: formData.concurrency,
        chunkSize: formData.chunkSize,
        task: formData.operation,
        fileName: formData.confiName,
        auditFlag: formData.auditLogs,
        batchsize: formData.batchsize,
        dataLoadType: (this.migtypeid == "40" || this.migtypeid == "41" || this.migtypeid == "46") ? "Default" : formData.data_LoadType,
        podName: formData.podName,
        ggHome: formData.ggHome,
        shellScript: formData.shellScript,
        trailPath: formData.trailPath,
        tableList: this.showschemaa ? '' : this.selectedTable.toString(),
        cdcoption: formData.cdcLoadType,
        readsize: formData.readsize,
        podConfig: formData.podConfig,
        dagExecute: formData.dagExecute,
        cdcType: formData.cdcType,
      }

    }
    console.log(obj)
    this.datamigration.DataMigrationCommand(obj).subscribe((data: any) => {
      this.executed = false
      this.toast.success(data.message);
    })
  }
  DmCommandForCPU() {
    this.cpu_spin = true
    let obj = {
      task: "CPU_Memory_Utilization",
      projectId: this.projectId.toString(),
      srcId: this.CPUSchema
    }
    this.datamigration.DataMigrationCommand(obj).subscribe((data: any) => {
      this.cpu_spin = false
      this.CPUForm.reset();
      this.toast.success(data.message);
    })
  }
  extractValue(value: any) {
    this.spin = true
    let obj = {
      task: "Extract_Tables",
      projectId: this.projectId.toString(),
      schema: value.schema,
      srcId: value.sourceConnection
    }
    this.datamigration.DataMigrationCommand(obj).subscribe((data: any) => {
      this.spin = false
      this.CPUForm.reset();
      this.toast.success(data.message);
    })
  }
  tgtId: any
  tgtconName:any
  getTgtId(value: any) {
    this.tgtId = value
    const selectedtgtconname = this.tgtlist.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.tgtconName = selectedtgtconname[0].conname;
  }
  conName:any;
  getDb(value: any) {
    this.srcId = value;
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.conName = selectedconname[0].conname;

  }
  getAuditConnId: any
  getauditCon(value: any) {
    this.getAuditConnId = value
  }
  nocatchupcdc: boolean = false
  mariaInitial: boolean = false
  getOP(value: any) {

    if (value == "CDC" || value == "Catchup") {
      this.nocatchupcdc = true
    }
    else {
      this.nocatchupcdc = false
    }

    if (this.migtypeid == "31" && value == "Initial_Data_Load") {
      this.mariaInitial = true
    } else {
      this.mariaInitial = false
    }

    if (value == "Catchup") {
      this.clearAllValidators()
      this.setRequiredValidators(['operation', 'podConfig', 'confiName', 'refFile', 'prioperation']);
      this.updateAllControls()
    }
    else {
      this.clearAllValidators()

      if (value == "Initial_Data_Load") {
        if (this.migtypeid == "31") {
          this.setRequiredValidators(['operation', 'sourceConnection', 'confiName',
            'data_LoadType'
          ]);
        }
        else if (this.migtypeid == "40" || this.migtypeid == "41" || this.migtypeid == "46") {
          this.setRequiredValidators(['operation', 'sourceConnection', 'targetConnection', 'concurrency', 'confiName',
            'podConfig', 'chunkSize', 'schemas', 'tables', 'dagExecute'
          ]);
        }
        else {
          this.setRequiredValidators(['operation', 'sourceConnection', 'confiName',
            'data_LoadType'
          ]);
        }
        this.updateAllControls()
      }
      else if (value == "E2E_Data_Load") {
        this.setRequiredValidators(['operation', 'sourceConnection', 'confiName',
          'data_LoadType', 'cdcLoadType'
        ]);
        this.clearValidators(['tgtschemas'])
        this.updateAllControls()
      }
      else {
        this.setRequiredValidators(['operation', 'sourceConnection', 'targetConnection', 'concurrency', 'confiName',
          'podConfig', 'chunkSize', 'schemas', 'tables', 'dagExecute',
          'data_LoadType', 'cdcType', 'batchsize'
        ]);
        this.clearValidators(['tgtschemas'])
        this.updateAllControls()
      }

    }
  }



  updatePodconfigValidation(value: any) {
    if (value == "Custom") {
     // this.setRequiredValidators(['request_Memory', 'limit_Memory']);
      this.updateAllControls()
    }
  }

  updategoldengateValidation(value: any) {
    if (value == "Golden_Gate") {
      this.setRequiredValidators(['podName', 'ggHome', 'shellScript', 'trailPath']);
      this.updateAllControls()
    } else {
      this.clearValidators(['podName', 'ggHome', 'shellScript', 'trailPath']);
      this.updateAllControls()
    }
  }

  setRequiredValidators(controls: string[]) {
    controls.forEach(control => {
      (this.getForm.controls[control as keyof typeof this.getForm.controls] as FormControl).setValidators([Validators.required]);
    });
  }
  clearValidators(controls: string[]) {
    controls.forEach(control => {
      (this.getForm.controls[control as keyof typeof this.getForm.controls] as FormControl).clearValidators();
    });
  }
  clearAllValidators() {
    Object.keys(this.getForm.controls).forEach(controlName => {
      const control = this.getForm.get(controlName);
      control?.clearValidators();
      control?.updateValueAndValidity();
    });
  }
  updateAllControls() {
    Object.keys(this.getForm.controls).forEach(control => {
      (this.getForm.controls[control as keyof typeof this.getForm.controls] as FormControl).updateValueAndValidity();
    });
  }

  tgtschemaname: any;
  selecttgtschema(value: string) {
    this.tgtschemaname = value
  }
  selectedsrcschema: string[] = [];
  showschemaa: boolean = false
  schemaName: any = []
  srcId: any
  schemaID: any = []
  selectedTable: any = [];
  selectedtgtschema: any
  schemavalue: any = [];
  isAll: any;
  showTargetSchemaAndTables: boolean = true;
  onItemSelect(item: any) {
    this.schemavalue = item.toString();
    this.showTargetSchemaAndTables = item.length === 1;
    this.isAll = item.toString();
    if (item == "ALL") {
      this.schemaList.forEach((item: any) => {
        if (this.migtypeid == "31") {
          this.selectedsrcschema.push(item.schemaname)
        }
        else {
          this.selectedsrcschema.push(item.schema_name);
        }
        this.selectedsrcschema = this.selectedsrcschema.filter((item: any) => item !== 'ALL');
      })
      console.log("selectedsrcschema", this.selectedsrcschema);
    }
    else if (item != undefined) {
      this.showschemaa = false;
      const schemaId = this.schemaList.filter((el: any) => el.schemaname == item.toString())
      this.schemaName.push(item);
      //console.log(schemaId)
      let obj = {
        schema: item,
        srcId: this.srcId
      }
      if (this.migtypeid == "35" || this.migtypeid == "34") {

      }
      else {
        this.datamigration.GetTablesByschemaGG(obj).subscribe((data: any) => {
          this.tablesList = data['Table1']
          this.tablesList.forEach((item: any) => {
            item.type = "ALL"
          })
          //console.log("Tables", this.tablesList)

        })
      }

      //this.schemaID.push(schemaId[0].schema_id)
      //this.schemasCheck()
    }
    else {
      this.showschemaa = true;
    }
  }
  tablesSelect(value: any) {
    if (value == "ALL") {
      this.tablesList.forEach((item: any) => {
        this.selectedTable.push(item.table_name)
      })
    }
  }
  schemaSelect(value: any) {
    if (value == "ALL") {
      this.tablesList.forEach((item: any) => {
        this.selectedTable.push(item.table_name)
      })
    }
  }
  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }
  selectedSchemas: any = []
  tableHide: boolean = false
  OnSchemasSelect(item:any) {
    this.schemavalue = item.toString();
    this.showTargetSchemaAndTables = item.length === 1;
    this.isAll = item.toString();
    if (item == "ALL") {
      this.schemaList.forEach((item: any) => {
        if (this.migtypeid == "31") {
          this.selectedsrcschema.push(item.schemaname)
        }
        else {
          this.selectedsrcschema.push(item.schema_name);
        }
        this.selectedsrcschema = this.selectedsrcschema.filter((item: any) => item !== 'ALL');
      })
      console.log("selectedsrcschema", this.selectedsrcschema);
    }
    else if (this.selectedSchemas.length == 1) {
      this.tableHide = false
      let obj = {
        schema: this.selectedSchemas.toString(),
        srcId: this.srcId
      }
      if (this.migtypeid == "35" || this.migtypeid == "34") {

      }
      else if (this.migtypeid == "31") {
        this.GetMariaTables(this.selectedSchemas.toString())
      }
      else if (this.migtypeid == "46") {
        this.getDb2Tables(this.selectedSchemas.toString())
      }
      else if (this.migtypeid == "38") {
        this.GetPostgresTables(this.selectedSchemas.toString())
      }
      else if (this.migtypeid == "30") {
          this.getSqlTables(this.selectedSchemas.toString())
        }
        else if (this.migtypeid == "48") {
          this.getSqlTables(this.selectedSchemas.toString())
        }
      else if (this.migtypeid == "40") {
        var env = "";
        this.ConsList.filter((item: any) => {
          if (this.srcId == item.Connection_ID) {
            if (item.migenv == "MSSQL") {
              env = "MSSQL";
            }
            else if (item.migenv == "Postgres") {
              env = "Postgres";
            }
          }
        })
        if (env == "MSSQL") {
          this.getSqlTables(this.selectedSchemas.toString())
        }
        else if (env == "Postgres") {
          this.GetPostgresTables(this.selectedSchemas.toString())
        }
        else {
          this.getOracleTables(this.selectedSchemas.toString())
        }
      }
      else {
        this.getOracleTables(this.selectedSchemas.toString())
        // this.datamigration.GetTablesByschemaGG(obj).subscribe((data: any) => {
        //   this.tablesList = data['Table1']
        //   this.tablesList.forEach((item: any) => {
        //     item.type = "ALL"
        //   })
        //console.log("Tables", this.tablesList)

        // })
      }
    }
    else {
      this.tableHide = true
    }
  }

  auditChecked: boolean = false
  getCheckValue($event: any): void {
    //console.log($event.target.checked)
    if ($event.target.checked == true) {
      this.auditChecked = true
    }
    else {
      this.auditChecked = false
    }
  }
  shellScriptList: any = []
  podList: any = []
  getPodList() {
    this.datamigration.getGoldenGatePod().subscribe((data: any) => this.podList = data)
  }
  callShellScript(value: any) {
    this.datamigration.GetGGShellDirectoryEx(value + '/scripts').subscribe((data: any) => this.shellScriptList = data)
  }
  dataLoadType: any = []
  filterConfig(value: any) {
    this.configFiles = []
    this.confFiles.forEach((el: any) => {
      if (value == "Partition_Initial_Data_Load") {
        if (el.fileName.includes("initPD_")) {
          this.configFiles.push(el)
        }
      }
      else if (value == "GG_Initial_Data_Load") {
        if (el.fileName.includes("initgg_")) {
          this.configFiles.push(el)
        }
      }
      else if (value == "Initial_Data_Load") {
        if (el.fileName.includes("init_")) {
          this.configFiles.push(el)
        }
      }

    })
  }
  spin: boolean = false
  cpu_spin: boolean = false

  CPUSchema: string = '';
  getCPUSchema(value: string) {
    this.CPUSchema = value
  }
  p: number = 1;
  pi: number = 10;
  page1: number = 1;

  datachange: any;
  Files: any;
  not_grid: boolean = true;
  grid_active: boolean = true;
  onKey() {
    this.p = 1;
    this.page1 = 1;
  }
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all' || value == '20' || value == '50' || value == '100') {
      this.p = 1;
      this.page1 = 1;
    }
  }
  fileResponse: any
  spin_dwld: any;
  downloadFile(fileInfo: any) {
    this.datamigration.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(blob);
      document.body.appendChild(downloadLink);
      downloadLink.download = fileInfo.fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }

  delete_file: boolean = false
  deleteAirflowFiles(value: any, filepath: any) {
    var obj = {
      filename: value,
      filePath: filepath.split(/[/\\]/).slice(0, -1).join('/'),
    }
    this.datamigration.deleteAirflowFiles(obj).subscribe((data: any) => {
      this.fetchTargetConfigFiles(this.tgtconn)
      //this.fetchConfigFiles('1')
      this.toast.success(data.message)
    })
  }
  loadingSpinners: { [key: string]: boolean } = {};
  toggleSpinner(itemId: string): void {
    this.loadingSpinners[itemId] = !this.loadingSpinners[itemId];
  }
  fileName: any
  selFile: any
  onFileSelected1(event: any) {
    const file: File = event.target.files[0];
    this.selFile = file
    this.fileName = file.name;
    //console.log(this.selFile)
    //console.log(this.selFile.name)
    //this.uploadFile(file);
  }

  uploadSpin1: boolean = false
  uploadConfigTempSpin: boolean = false
  filePath: string = ''
  uploadFile(data: string) {

    const formData: FormData = new FormData();
    if (data == 'configFile') {
      this.uploadSpin1 = true
      this.filePath = "Config_Files/Data_Migration/"
      formData.append('file', this.selFile, this.selFile.name);
      formData.append('path', this.filePath);
    } else {
      this.uploadConfigTempSpin = true
      this.filePath = "Ora2Pg_Configuration_Template/"
      formData.append('file', this.selFile, 'ora2pg_configuration.txt');
      formData.append('path', this.filePath);
    }
    this.datamigration.UploadProjectDocs(formData).subscribe(
      response => {
        this.fileName = ''
        this.uploadSpin1 = false
        //this.ReplaceFileName()
        this.fetchConfigFiles('1')
        this.toast.success("File uploaded successfully")
      },
      error => {
        this.fileName = ''
        this.uploadSpin1 = false
        this.toast.warning("Error uploading file")
      }
    );
  }
  // Execution screen section

  // selectedOperation: any
  // initialselected: boolean = false
  // filteredConfigfiles:any
  // dagFolders:any
  // taskFolders:any
  // logFiles:any
  // selectOperation(value: any) {

  //   // Reset Dag list in Logs
  //   this.dagFolders = []
  //   this.taskFolders = []
  //   this.logFiles = []

  //   this.selectedOperation = value
  //   if (this.selectedOperation == "Initial_Data_Load") {
  //     this.initialselected = true
  //   }
  //   else {
  //     this.initialselected = false
  //   }
  //   var type = ""
  //   if (value == 'Initial_Data_Load') {
  //     type = "init_"
  //   }
  //   else if (value == 'CDC_Data_Load') {
  //     type = "cdc_"
  //   }
  //   else if (value == 'Index_Data_Load') {
  //     type = "index_"
  //   }
  //   else if (value == "E2E_Data_Load") {
  //     type = "e2e"
  //   }
  //   else if (value == "Catchup") {
  //     type = "inc_"
  //   }

  //   let obj = {
  //     configtype: type,
  //     filePath: "Config_Files/Data_Migration"
  //   }
  //   this.datamigration.GetGGPDConfigFiles(obj).subscribe((data: any) => {
  //     this.filteredConfigfiles = data
  //   })

  // }
  // DCFiles:any
  // filterConfigFiles() {
  //   var abc = []
  //   this.filteredConfigfiles = []
  //   if (this.selectedOperation == "Incremental_Data_Load") {
  //     abc = this.configFiles.filter((item: any) => {
  //       return item.fileName.includes("catch_")
  //     })
  //   }
  //   else if (this.selectedOperation == "CDC_Load") {
  //     abc = this.configFiles.filter((itemy: any) => {
  //       return itemy.fileName.includes("cdc_")
  //     })
  //   }
  //   else if (this.selectedOperation == "Datacompare") {
  //     abc = this.DCFiles.filter((itemy: any) => {
  //       return itemy.fileName.includes("dc_")
  //     })
  //   }
  //   else if (this.selectedOperation == "Reverse_CDC") {
  //     abc = this.configFiles.filter((itemy: any) => {
  //       return itemy.fileName.includes("rev_")
  //     })
  //   }
  //   else {
  //     abc = this.configFiles.filter((itemz: any) => {
  //       return itemz.fileName.includes("cdchv_")
  //     })
  //   }

  //   this.filteredConfigfiles = abc
  // }
  // dagSelected:boolean=false
  // selectedDag:any
  // selectedDagName:any
  // cdcSelected:boolean=false
  // selectDag(value: any) {
  //   this.dagSelected = true
  //   this.selectedDag = value
  //   this.selectedDagName = value.split('/').pop()
  //   this.selectedDagName == 'CDC_AF_DEMO_504_PM.xlsx' ? this.cdcSelected = true : this.cdcSelected = false;
  //   //console.log(value.split('/').pop())
  // }

  // dagData: any
  // selectedPath: any
  // showCheckStatus: boolean = false
  // allIPs:any = []
  // dagsInfo:any
  // srcCon:any
  // isDisableAllCheck:boolean=false
  // getConfigDags(path: any) {
  //   this.showCheckStatus = false
  //   this.dagsInfo = []
  //   this.allIPs = []
  //   this.selectedPath = path
  //   //  path="C:\\Users\\<USER>\\Downloads\\catchup_config.xlsx"
  //   this.p = 1
  //   // if (this.selectedOperation == 'Partition_Initial_Data_Load' || this.selectedOperation == 'GG_Initial_Data_Load' || this.selectedOperation == "Initial_Data_Load" || this.selectedOperation == 'E2E_Data_Load') {
  //   this.datamigration.getggpdDags(path).subscribe((data: any) => {
  //     this.dagsInfo = data.dagList
  //     this.srcCon = data.sourceConnectionId
  //     this.dagsInfo.filter((item: any) => {
  //       item.isSelected = false
  //       return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
  //     })
  //   })
  // }
  // count: any = []
  // openPopup() {
  //   this.count = this.allIPs.filter((item: any) => {
  //     return item.isSelected == true
  //   })
  // }
  // scheduleSelect:boolean=false
  // popup(value: any) {
  //   if (value == 0) {
  //     this.scheduleSelect = true
  //   }
  //   else {
  //     this.scheduleSelect = false
  //   }
  // }

  // executeSpin: boolean = false
  // scnValue:any
  // getSCN() {
  //   this.executeSpin = true
  //   if (this.migtypeid != "20") {
  //     this.triggerDag()
  //   }
  //   else {
  //     if (this.srcCon == "" || this.selectedOperation == "Reverse_CDC") {
  //       this.allIPs = this.allIPs.filter((itemy: any) => {
  //         return itemy.isSelected == true
  //       })
  //       this.triggerDag()
  //     } else {
  //       this.datamigration.getScnNumber(this.srcCon).subscribe((data: any) => {
  //         this.executeSpin = false
  //         if (data != undefined) {
  //           this.scnValue = data.scn
  //           if (this.scnValue != "") {
  //             this.allIPs = this.allIPs.filter((itemk: any) => {
  //               return itemk.isSelected == true
  //             })
  //             //console.log(this.allIPs)
  //             $('#updateOrgModal').offcanvas('hide');
  //             this.triggerDag()
  //           }
  //         }
  //         else {
  //           $('#updateOrgModal').offcanvas('hide');
  //           this.toast.error("Failed TO get SCN Number")
  //         }
  //       })
  //     }
  //   }
  // }
  // dagRes: any
  // dagId: any;
  // trigger_spin:boolean=false
  // initType:any
  // triggerDag() {
  //   this.trigger_spin = true
  //   const obj = {
  //     dagList: this.allIPs,
  //     scn: this.scnValue,
  //     type: this.initType
  //   }
  //   this.datamigration.TriggerMultiDagsWithCDC(obj).subscribe((data: any) => {
  //     this.dagRes = data;
  //     this.trigger_spin = false
  //     this.allIPs = []
  //     this.showCheckStatus = false
  //     this.dagsInfo.filter((item: any) => {
  //       if (item.isSelected == true) {
  //         item.isSelected = false
  //       }
  //     })
  //     $('#updateOrgModal').offcanvas('hide');
  //     this.toast.success(data.message)
  //   },
  //     error => {
  //       this.trigger_spin = false
  //       $('#updateOrgModal').offcanvas('hide');
  //       this.toast.error('Dag Trigger Failed ');
  //     })
  // }

  // reff_spin: boolean = false
  // refreshdags() {
  //   this.dagsInfo = []
  //   this.allIPs = []
  //   this.p = 1
  //   this.reff_spin = true
  //   this.datamigration.getggpdDags(this.selectedPath).subscribe((data: any) => {
  //     this.dagsInfo = data.dagList
  //     this.reff_spin = false
  //     this.srcCon = data.sourceConnectionId
  //     this.dagsInfo.filter((item: any) => {
  //       item.isSelected = false
  //       return item.isDagAvailable ? this.isDisableAllCheck = false : this.isDisableAllCheck = true
  //     })
  //   })
  // }
  // dagruns: any
  // seleDag: any
  // selectRunDags(dagid: any) {
  //   //console.log(dagid)
  //   this.seleDag = dagid
  //   this.datamigration.getDagRuns(dagid).subscribe((data: any) => {
  //     this.dagruns = data
  //   })
  // }

  // p1: number = 1;
  // p2: number = 1;
  // page2: number = 1;
  // page3: number = 1;

  // onKey1() {
  //   var sercachbar = (<HTMLInputElement>document.getElementById("searchdag")).value
  //   this.p1 = 1;
  //   this.page2 = 1;
  //   this.p2 = 1;
  //   this.page3 = 1;
  //   this.p = 1;
  //   //console.log(sercachbar, "bar")
  //   if (sercachbar.length > 0) {
  //     this.isDisableAllCheck = true
  //   }
  //   else {
  //     this.isDisableAllCheck = false
  //   }
  // }
  DataMigrationNewCommand(fd: any, option: any) {
    var taskop = ""
    if (option == "0") {
      taskop = "Monitoring_Process";
    }
    else if (option == "1") {
      taskop = fd.operation;
    }
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnectionId: fd.sourceConnection,
      targetConnectionId: fd.targetConnection,
      schema: this.schemavalue === "ALL" ? this.selectedsrcschema.toString() : this.selectedSchemas.toString(),
      targetSchema: fd.tgtschemas,
      task: taskop,
      tablename: this.projectId == "3038"
      ? fd.tables?.toString() ?? ''
      : this.selectedTable?.toString() ?? '',
      fileName: fd.confiName,
      dataLoadType: fd.data_LoadType,
      cdcType: fd.cdcLoadType,
      jobName: "qmig-migrt",
      codeoption:true
    }
    console.log(obj)
    this.datamigration.DataMigrationNewCommand(obj).subscribe((data: any) => {
      this.toast.success(data.message);
    })
  }
  getOracleTables(value: any) {
    let obj = {
      conid: this.srcId,
      schemaname: value
    }
    this.datamigration.getOracleTables(obj).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.table_name = item.tablename
      })

      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  getSqlTables(value: any) {
    let obj = {
      conid: this.srcId,
      schemaname: value
    }
    if (this.migtypeid == "30") {
      this.datamigration.getsqlServerTables(obj).subscribe((data: any) => {
        this.tablesList = data
        this.tablesList.forEach((item: any) => {
          item.type = "ALL"
        })
      })
    }
    else {
      this.datamigration.getsqlTables(obj).subscribe((data: any) => {
        this.tablesList = data
        this.tablesList.forEach((item: any) => {
          item.type = "ALL"
        })
      })
    }

  }
  getDb2Tables(value: any) {
    let obj = {
      conid: this.srcId,
      schemaname: value
    }
    this.datamigration.getDb2Tables(obj).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.table_name = item.tablename
      })

      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  GetMariaTables(value: any) {
    let obj = {
      conid: this.srcId,
      dbname: value
    }
    this.datamigration.GetMariaTables(obj).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.table_name = item.tableName
      })

      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  GetPostgresTables(value: any) {
    this.tablesList = []
    let obj = {
      conid: this.srcId,
      schema: value
    }
    this.datamigration.GetPostgresTablesBySchema(obj).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  openModal() {
    $('#demo').offcanvas('show');
  }
  openPopup() {

  }
  //ReadLocalJson
  readData: any
  ReadLocalJson() {
    const path = "/mnt/extra/" + this.srcId + "/Config_Files/chunk_configuration.json"
    this.updatePath = path;
    this.datamigration.ReadLocalJson(path).subscribe((data: any) => {
      this.readData = data;
      this.readDataString = JSON.stringify(data, null, 0);
    })
  }
  ReadLocalJson1() {
    const path = "/mnt/extra/" + this.srcId + "/" + this.tgtId + "/Config_Files/chunk_configuration.json"
    this.updatePath = path;
    this.datamigration.ReadLocalJson(path).subscribe((data: any) => {
      this.readData = data;
      this.readDataString = JSON.stringify(data, null, 0);
    })
  }
  uploadSpin: boolean = false;
  readDataString: any =[]
  selectedSourceConnection: string = '';
  selectedTargetConnection: string = '';
  onEditConfigClick() {
    // if (!this.selectedSourceConnection || !this.selectedTargetConnection) {
    //    this.toast.error("Please select both Source and Target connections.");
    //   return;
    //}
  
    this.openModal(); 
  }
  tableData:boolean = true;
  onSourceChange(value: any) {
    this.selectedSourceConnection = value;
    
  }
  onSourceChange1(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.selectedSourceConnection = target.value; 
  }
  onTargetChange(value: string) {
    this.selectedTargetConnection = value;
  }
  //EditLocalJson
  editData: any
  updatePath: string = '';
  Update() {
    const parsedData = this.readDataString.replace(/[\r\n\/]/g, '')// JSON.stringify(this.readDataString);
    this.uploadSpin = true;
    let obj = {
      path: this.updatePath,
      jsonData: parsedData//.replace(/[\r\n\/]/g, '')
    }
    this.datamigration.EditLocalJson(obj).subscribe({
      next: () => {
        this.uploadSpin = false;
        this.toast.success("Updated Successfully");
        $('#demo').offcanvas('hide');
      },
      error: (err) => {
        console.error('API Error:', err);
        this.uploadSpin = false;
        this.toast.error('Update failed. Please try again.');
      }
    });
  }

  getRunSpin1: boolean = false;
  getUpdatedRunNumber1() {
    this.getRunSpin1 = false
    this.fetchSourceConfigFiles(this.fileSrc)
    this.fetchTargetConfigFiles(this.tgtconn)
  }
  runinfospins: boolean = false;
  CpuDetails: any;
  DatamigrationCommand() {
    let obj = {
      task: "CPU_Memory_Utilization",
      projectId: this.projectId.toString(),
      sourceConnectionId: this.selectedSourceConnection,
    }
    this.dbService.datamigrationNewCommand(obj).subscribe((data: any) => {
      this.CpuDetails = data['Table1'];
      this.runinfospins = false;
      $('#demo').offcanvas('hide');
      this.toast.success("Successfully triggered the CPU utilization Pod")
    },
      error => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong')
      })
  }
  validationstable() {
    const tablesControl = this.getForm.get('tables');
    const tgtschemaControl = this.getForm.get('tgtschemas');
    if(this.migtypeid =='48'){
      if (this.isAll === "ALL") {
        tablesControl?.clearValidators();
        tablesControl?.setValue(null);
      }
      else if (Array.isArray(this.selectedSchemas) && this.selectedSchemas?.length === 1) {
        tablesControl?.setValidators([Validators.required]);
      }
      else {
        tablesControl?.clearValidators();
        tablesControl?.setValue(null);
      }
      tablesControl?.updateValueAndValidity();
    }else{
      if (this.isAll === "ALL") {
        tablesControl?.clearValidators();
        tablesControl?.setValue(null);
  
        tgtschemaControl?.clearValidators();
        tgtschemaControl?.setValue(null);
      }
      else if (Array.isArray(this.selectedSchemas) && this.selectedSchemas?.length === 1) {
        tablesControl?.setValidators([Validators.required]);
        tgtschemaControl?.clearValidators();
        tgtschemaControl?.setValue(null);
      }
      else {
        tablesControl?.clearValidators();
        tablesControl?.setValue(null);
  
        tgtschemaControl?.clearValidators();
        tgtschemaControl?.setValue(null);
      }
      tablesControl?.updateValueAndValidity();
      tgtschemaControl?.updateValueAndValidity();
    }
    
  }
  openModal1(fd:any,option:any){
    var taskop = ""
    if (option == "0") {
      taskop = "Monitoring_Process";
    }
    else if (option == "1") {
      taskop = fd.operation;
    }
    
  }
  openPopup1(){
    this.getForm.reset()
  }
}


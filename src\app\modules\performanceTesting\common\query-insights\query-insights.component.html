<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <h3 class="main_h px-3 pt-3"> Query-Insights </h3>
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="perfForm">
            <div class="row">
                <!--- Category  --->
                <div class="col-md-3 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="targtetConnection"> Connection
                            Name</label>
                        <select formControlName="tgtconnection" #Myselect2 (change)="
                        selTgtId(Myselect2.value)" class="form-select">
                            <option value="" selected disabled>Select Connection Name</option>
                            @for(list of tgtList;track list; ){
                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                            }
                        </select>
                        <div class="alert">
                            @if(validate.tgtconnection.touched && validate.tgtconnection.invalid) {
                            <p class="text-start text-danger mt-1">Connection Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-md-3 col-xl-3">
                    <div class="form-group">
                        <label class="form-label" for="targtetConnection"> From Date</label>
                        <input type="datetime-local" formControlName="fromDate" (change)="selectfromDate(fromDate)"
                            class="form-control" [(ngModel)]="fromDate" id="fromDate">
                    </div>
                </div>
                <div class="col-md-3 col-xl-3">
                    <div class="form-group">
                        <label class="form-label" for="targtetConnection">To Date</label>
                        <input type="datetime-local" formControlName="toDate" class="form-control"
                            (change)="selecttoDate(toDate)" [(ngModel)]="toDate" id="toDate">
                    </div>
                </div>
            </div>
        </form>

        <h6>Table columns</h6>
        <div class="perfChecks">
            <!-- All checkbox to select/deselect all -->
            <div class="form-check">
                <input type="checkbox" class="form-check-input" [(ngModel)]="selectAll"
                    (change)="selectBoxesData('All', $event)" /> All
            </div>
            @for(list of columns;track list; ){
            <div class="form-check">
                <input type="checkbox"  [checked]="list.checked" class="form-check-input"
                    (change)="selectBoxesData(list.value, $event)" />
                {{list.option}}
            </div>
            }
            <div class="col-md-3"></div>
            <div class="form-group">
                <div class="body-header-button">
                    <button class="btn btn-upload w-100 me-1" (click)="columnupdate()">
                        <span></span> Fetch</button>
                </div>
            </div>

            <!-- Individual checkboxes -->
            <!-- <div class="form-check">
                <input type="checkbox" [(ngModel)]="userid" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('userid', userid)" />
                UserId
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="queryid" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('queryid', queryid)" />
                QueryId
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="calls" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('calls', calls)" />
                Calls
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="total_exec_time" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('total_exec_time', total_exec_time)" />
                Total Exe Time
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="min_exec_time" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('min_exec_time', min_exec_time)" />
                Min Exe Time
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="max_exec_time" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('max_exec_time', max_exec_time)" />
                Max Exe Time
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="shared_blks_read" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('shared_blks_read', shared_blks_read)" />
                Shared Blk Read
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="shared_blks_hit" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('shared_blks_hit', shared_blks_hit)" />
                Shared Blk Hit
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="shared_blks_written" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('shared_blks_written', shared_blks_written)" />
                Shared Blk Written
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="temp_blks_read" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('temp_blks_read', temp_blks_read)" />
                Temp Blk Read
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="temp_blks_written" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('temp_blks_written', temp_blks_written)" />
                Temp Blk Written
            </div>
            <div class="form-check">
                <input type="checkbox" [(ngModel)]="sa.query" [disabled]="selectAll" class="form-check-input"
                    (change)="updateSelectedColumns('sa.query', sa.query)" />
                Query
            </div> -->
        </div>
        <p [hidden]="TableData" class="text-danger mt-2"><b>Note: </b> To display the required columns in the table,
            select the checkboxes
            above. </p>
    </div>
    <div [hidden]="!columnSelected">
        <div class="row">
            <div class="col-12 col-sm-6 col-md-6">
                <h3 class="main_h py-4 ps-3">
                    <button class="btn btn-sync">
                        @if(ref_spin1){
                        <app-spinner />
                        }@else{
                        <span class="mdi mdi-refresh"></span>
                        }
                    </button>
                    <button class="btn btn-sync" (click)="exportexcelIndexTuningExcel()">
                        <span class="mdi mdi-cloud-download-outline"></span>
                    </button>
                </h3>
            </div>

            <!-- search status of  -->
            <div class="col-12 col-sm-6 col-md-6">
                <div class="custom_search cs-r my-3 me-3">
                    <span class="mdi mdi-magnify"></span>
                    <input type="text" placeholder="Search Filter" class="form-control" [(ngModel)]="datachanges"
                        (keyup)="onKey()">
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        @for(list of columns;track list;){
                        @if(list.checked){
                        <th>{{list.option}}</th>
                        }
                        }
                        <!-- <th *ngIf="userid">User Id</th>
                        <th *ngIf="queryid">Query Id</th>
                        <th *ngIf="calls">Calls</th>
                        <th *ngIf="total_exec_time">Total Exe Time</th>
                        <th *ngIf="min_exec_time">Min Exe Time</th>
                        <th *ngIf="max_exec_time">Max Exe Time</th>
                        <th *ngIf="shared_blks_read">Shared Bulks Read</th>
                        <th *ngIf="shared_blks_hit">Shared Bulks Hit</th>
                        <th *ngIf="shared_blks_written">Shared Bulks Written</th>
                        <th *ngIf="temp_blks_read">Temp Bulks Read</th>
                        <th *ngIf="temp_blks_written">Temp Bulks Written</th>
                        <th *ngIf="sa.query">Query</th> -->
                    </tr>
                </thead>
                <tbody>
                    @for(row of QueryColumn|searchFilter: datachanges|
                    paginate: { itemsPerPage: 10, currentPage: pageNumber };
                    track row;){<tr>
                        @for(col of tableColumns;track col;){
                            @if(row[col] != 0){
                                <td>{{row[col]}}</td>
                            }
                            <!-- <td>{{ row[col] === 0 ? [hide]='true' : row[col]}}</td> -->
                        }
                    </tr>}

                    <!-- <tr
                        *ngFor="let col of QueryColumn |searchFilter: datachanges| paginate: { itemsPerPage: 10, currentPage: pageNumber }">
                        <td *ngIf="userid">{{ col.userid === 0 ? '' : col.userid }}</td>
                        <td *ngIf="queryid">{{ col.queryid === 0 ? '' : col.queryid }}</td>
                        <td *ngIf="calls">{{ col.calls === 0 ? '' : col.calls }}</td>
                        <td *ngIf="total_exec_time">{{ col.totalexectime === 0 ? '' : col.totalexectime }}
                        </td>
                        <td *ngIf="min_exec_time">{{ col.minexectime === 0 ? '' : col.minexectime }}</td>
                        <td *ngIf="max_exec_time">{{ col.maxexectime === 0 ? '' : col.maxexectime }}</td>
                        <td *ngIf="shared_blks_read">{{ col.sharedblksread === 0 ? '' : col.sharedblksread
                            }}</td>
                        <td *ngIf="shared_blks_hit">{{ col.sharedblkshit === 0 ? '' : col.sharedblkshit }}
                        </td>
                        <td *ngIf="shared_blks_written">{{ col.sharedblkswritten === 0 ? '' :
                            col.sharedblkswritten }}</td>
                        <td *ngIf="temp_blks_read">{{ col.tempblksread === 0 ? '' : col.tempblksread }}</td>
                        <td *ngIf="temp_blks_written">{{ col.tempblkswritten === 0 ? '' :
                            col.tempblkswritten }}</td>
                        <td *ngIf="sa.query">{{ col.query === 0 ? '' : col.query }}</td>
                    </tr>-->
                    
                    <tr *ngIf="NoData">
                        <td colspan="12">
                            <p class="text-center m-0 w-100">No Results</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
    </div>
</div>
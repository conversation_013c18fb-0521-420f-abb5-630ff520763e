import { Component, ViewChild } from '@angular/core';
import { DashboardService } from '../../../../services/dashboard.service';
import { connections, getConnections, getCounts } from '../../../../models/interfaces/dashBoardTypes';
import { BaseChartDirective, NgChartsModule, } from 'ng2-charts';
import { PercentPipe } from '@angular/common';
import { TabsComponent } from '../tabs/tabs.component';
import { Title } from '@angular/platform-browser';
import { ChartType, ChartOptions, ChartConfiguration } from 'chart.js'; // Import ChartType from chart.js

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [NgChartsModule, PercentPipe, TabsComponent],
  templateUrl: './home.component.html',
  styles: ``
})
export class NewHomeComponent {
  @ViewChild(BaseChartDirective, { static: true }) chart: BaseChartDirective | undefined;

  username: string | null;
  ConsList: any

  getData: any;
  data: any;
  projectId: any;
  getRole: any;
  Token: any;
  project_name: any;
  Extracted_Count: number = 0;
  source_count: number = 0;
  Extracted: number = 0;
  Extracted_Count1: number = 0;
  source_count1: number = 0;
  Extracted1: number = 0;
  Extracted_Count2: number = 0;
  source_count2: number = 0;
  Extracted2: number = 0;
  getData1: any;
  getData2: any;
  date: any;
  dbname: any = [];
  DbConname_1: any = [];
  Extracted_Count3: number = 0;
  source_count3: number = 0;
  migtypeid: string | null;

  /*--- Chart Data ---*/

  public barChartLabels = ['Code Extraction', 'Storage Objects', 'Code Objects', 'Data Migration'];
  public barChartType = 'bar';
  public barChartLegend = true;
  public barChartData = [
    {
      data: [98, 45, 45, 95, 93],
      label: 'Percentage',
      borderColor: '#8b36ff',
      backgroundColor: '#8b36ff',
      hoverBackgroundColor: '#4c00b5',
      barThickness: 50, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    },
  ];
  public barChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
          label: (tooltipItem) => {
            const datasetLabel = tooltipItem.dataset.label || '';
            const dataPoint = tooltipItem.formattedValue;
            // Create custom labels for each bar
            let TotalTestCase = '';
            let passedTestCases = '';
            if (tooltipItem.dataIndex === 0) {
              TotalTestCase = `Source Count : ${this.source_count}`;
              passedTestCases = `Extracted Count : ${this.Extracted_Count}`;
            } else if (tooltipItem.dataIndex === 1) {
              TotalTestCase = `Total Count : ${this.source_count1}`;
              passedTestCases = `Conversion Count : ${this.Extracted_Count1}`;
            } else if (tooltipItem.dataIndex === 2) {
              TotalTestCase = `Total Count : ${this.source_count2}`;
              passedTestCases = `Converted Count : ${this.Extracted_Count2}`;
            } else if (tooltipItem.dataIndex === 3) {
              TotalTestCase = `Total Count : ${this.totalCount}`;
              passedTestCases = `Migrated Count :  ${this.Migratedcount}`;
            }

            // Return multiple labels for the tooltip
            return [
              datasetLabel + ': ' + dataPoint + ' %',  // Main dataset label
              TotalTestCase,  // Custom label
              passedTestCases,  // Custom label
            ];
          }
        }
      }
    }
  };

  /*--- Testing Chart Data ---*/

  public testChartLabels = ['Dev Cycle 2 Testing', 'Dev Cycle 3 Testing', 'SIT Testing'];
  public testChartType = 'bar';
  public testChartLegend = true;
  public testChartData = [
    {
      data: [91, 87, 80],
      label: 'Percentage',
      borderColor: '#cf30fc',
      backgroundColor: '#f75989',
      hoverBackgroundColor: '#f5346e',
      barThickness: 50, // Set exact bar thickness (smaller number for thinner bars)
      maxBarThickness: 70, // Set max bar thickness if auto-calculating
    }
  ];
  public testChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
          labelPointStyle: function (context) {
            return {
              pointStyle: 'circle',
              rotation: 0
            };
          },
          label: (tooltipItem) => {

            const datasetLabel = tooltipItem.dataset.label || '';
            const dataPoint = tooltipItem.formattedValue;

            // Create custom labels for each bar
            let TotalTestCase = '';
            let passedTestCases = '';
            if (tooltipItem.dataIndex === 0) {
              TotalTestCase = '323';
              passedTestCases = '296';
            } else if (tooltipItem.dataIndex === 1) {
              TotalTestCase = '323';
              passedTestCases = '283';
            } else if (tooltipItem.dataIndex === 2) {
              TotalTestCase = '323';
              passedTestCases = '260';
            }

            // Return multiple labels for the tooltip
            return [
              datasetLabel + ': ' + dataPoint + ' %',  // Main dataset label
              "Total Test Cases : " + TotalTestCase,  // Custom label
              "Passed Test Cases : " + passedTestCases,  // Custom label
            ];
          }
        }
      }
    }
  };


  constructor(private dService: DashboardService, private titleService: Title) {
    this.project_name = localStorage.getItem('project_name');
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') || 'null');
    this.Token = localStorage.getItem('currentUser') || 'null';
    this.username = localStorage.getItem('Email')
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
  }
  ngOnInit(): void {
    this.getConsList()
    this.getCounts('null', 'vw_assessments_summary_select');
    this.getCounts('null', 'vw_storage_objects_summary_select');
    this.getCounts('null', 'vw_code_objects_summary_select');
    this.titleService.setTitle(`QMigrator - Dashboard Home`);
    this.getDataMigrationLisst(null)
  }


  /*--- get Connections ---*/
  getConsList() {
    this.dService.getConList(this.projectId.toString()).subscribe((data: getConnections) => {
      const condata = data['Table1'];
      this.ConsList = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
    });
  }

  /*--- get Data base connections ---*/
  datamigrationData: any;
  totalCount: any;
  Migratedcount: any;
  differenceCount: any;
  percentage: any;
  getDataMigrationLisst(value: any) {
    this.dService.getDataMigrationList(value).subscribe((data: any) => {
      this.getData3 = data.Table1;
  
      const result = this.getData3?.[0];
      if (result) {
        const totalTables = Number(result.total_table_count) || 0;
        const migratedTables = Number(result.migrated_tables) || 0;
        const percentage = totalTables > 0 ? (migratedTables / totalTables * 100) : 0;
  
        this.totalCount = totalTables;
        this.Migratedcount = migratedTables;
        this.percentage = percentage.toFixed(2);
      }
  
      if (!this.dbname.includes(data.dbconname)) {
        this.dbname.push(data.dbconname);
      }
    });
    this.barChartData[0].data[3] = (this.Migratedcount / this.totalCount * 100)
  }
  

  /*--- get List Data ---*/
  getCounts(con: string, spName: string) {

    this.barChartData[0].data = [];
    // this.barChartData[1].data = [];
    // this.barChartData[2].data = [];


    const obj = {
      projectID: this.projectId.toString(),
      token: this.Token,
      sp: spName + '(' + con + ')',
    };

    this.dService.getCounts(obj).subscribe((data: any) => {
      const tableData = data['Table1'];
      tableData?.forEach((item: any) => {
        switch (spName) {
          case 'vw_assessments_summary_select':
            this.source_count += Number(item.total_count) || 0;
            this.Extracted_Count += Number(item.extracted_count) || 0;
            break;
          case 'vw_storage_objects_summary_select':
            this.source_count1 += Number(item.total_count) || 0;
            this.Extracted_Count1 += Number(item.matched_count) || 0;
            break;
          case 'vw_code_objects_summary_select':
            this.source_count2 += parseFloat(item.total_count) || 0;
            this.Extracted_Count2 += parseFloat(item.matched_count) || 0;
            break;
        }
        if (!this.dbname.includes(item.dbconname)) {
          this.dbname.push(item.dbconname);
        }
      });

      switch (spName) {
        case 'vw_assessments_summary_select':
          this.getData = tableData;
          const extractionPer = (this.Extracted_Count / this.source_count * 100)
          // this.barChartData[1].data.push(this.source_count)
          // this.barChartData[2].data.push(this.Extracted_Count)
          this.barChartData[0].data[0] = extractionPer
          break;
        case 'vw_storage_objects_summary_select':
          this.getData1 = tableData;
          const storagePer = (this.Extracted_Count1 / this.source_count1 * 100)
          // this.barChartData[1].data.push(this.source_count1)
          // this.barChartData[2].data.push(this.Extracted_Count1)
          this.barChartData[0].data[1] = storagePer
          break;
        case 'vw_code_objects_summary_select':
          this.getData2 = tableData;
          const codePer = (this.Extracted_Count2 / this.source_count2 * 100)
          // this.barChartData[1].data.push(this.source_count2)
          // this.barChartData[2].data.push(this.Extracted_Count2)
          this.barChartData[0].data[2] = codePer
          break;
      }
      // this.barChartData[0].data[3] = (this.Migratedcount / this.totalCount * 100)
      this.chart?.update();
    });

  }

  /*---- On Select Data base ---*/
  getData3: any;
  dbconnectionSelection(event: any) {
    localStorage.setItem("dbName", event.target.value)
    const selecteddb = event.target.value;
    this.source_count = 0;
    this.Extracted_Count = 0;
    this.source_count1 = 0;
    this.Extracted_Count1 = 0;
    this.source_count2 = 0;
    this.Extracted_Count2 = 0;
    this.totalCount = 0;
    this.Migratedcount = 0;
    this.getDataMigrationLisst(selecteddb);
    this.getCounts("'" + selecteddb + "'", 'vw_assessments_summary_select');
    this.getCounts("'" + selecteddb + "'", 'vw_storage_objects_summary_select');
    this.getCounts("'" + selecteddb + "'", 'vw_code_objects_summary_select');
    this.getData?.map((item: any) => {
      if (item.dbconname == selecteddb) {
        this.source_count += Number(item.total_count) || 0;
        this.Extracted_Count += Number(item.extracted_count) || 0;
      }
    });
    this.getData1?.map((item: any) => {
      if (item.dbconname == selecteddb) {
        this.source_count1 += Number(item.total_count) || 0;
        this.Extracted_Count1 += Number(item.matched_count) || 0;
      }
    });
    this.getData2?.map((item: any) => {
      if (item.dbconname == selecteddb) {
        this.source_count2 += parseFloat(item.total_count) || 0;
        this.Extracted_Count2 += parseFloat(item.matched_count) || 0;
      }
    });
    this.getData3?.map((item: any) => {
      if (item.dbconname == selecteddb) {
        this.totalCount += parseFloat(item.totalCount) || 0;
        this.Migratedcount += parseFloat(item.Migratedcount) || 0;
      }
    });
  }
}

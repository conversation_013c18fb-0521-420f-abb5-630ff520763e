export const deploymentAPIConstant = {
    GetConnectionList: 'GetConnectionList?projectId=',
    GetschemaList: 'GetSchemaList?projectId=',
    GetRunNoForstmts: 'GetRunNoForstmts?projectId=',
    getFiles: 'GetFilesFromDirectory?path=',
    uploadDocuments: 'UploadExtraFiles',
    GetSchemaSelect: 'SCSchemaSelect?projectId=',
    GetSourceFilePath: 'SourceFilePathsForFileshare',
    DeltaCommand: 'DeltaCommand',
    GetPrjRuninfoSelectAll: 'GetPrjRuninfoSelectAll?projectId=',
    PrjExelogSelectTask: 'GetIndividualLogs',
    GetRequestTableData: 'GetRequestTableData?projectId=',
    deleteTableData: 'DeleteRequestTableData?projectId=',
    downloadLargeFiles: 'DownloadLargeFile?filePath=',
    GetObjectNames:'GetObjectNames?RunNo=',
    GetObjectCode:'GetObjectCode?RunNo=',
    TargetFilePathsForFileshare:'TargetFilePathsForFileshare',
    UploadCloudFiles:'UploadCloudFiles',
    GetRunno: 'GetRunNoForstmts?projectId=',
    GetSchemasByRunId: 'GetSchemasByRunId?runid=',
    DeltaUpdate: 'UpdateTablesData',
    changeObjectStatus:'changeObjectStatus',
    getdeltaDeploymentStatus:'GetDeploymentDetailsofDeltacontrol',
   
}

    

import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
// import { deleteFile, documentsList, redisCommand} from '../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { GetIndividual, GetIndividualLogs, GetOperations, GetReqData, GetRunno, Operation, PrjSchemasListSelectData, SchemaListSelect, SchemaSelect, conList, deleteFile, deleteTableData, deleteTabledata, documentsList, fileStatus, projectConRunTblInsert, reqData, schemalist, setRedisCache1 } from '../../../../models/interfaces/types';
import { NgSelectModule } from '@ng-select/ng-select';
import { Title } from '@angular/platform-browser';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { CodeMigrationService } from '../../../../services/codeMigration.service';


declare let $: any;


@Component({

  selector: 'app-code-extraction',

  standalone: true,

  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, RouterOutlet, NgSelectModule],

  templateUrl: './code-extraction.component.html',

  styles: ``

})

export class CodeExtractionComponent {
  pageNumber: number = 1;
  //projectDocumentsDetailSelect
  fileData: any;
  userData: any = [];
  projectSrcF: any = [];
  secUserData: any = [];
  //getInfraSelect
  infraData: any = [];
  prjSrcTgtData: any = [];
  //GetReqData
  reqD: any = [];
  reqF: any = [];
  //getSchemasList
  schemaList: any = [];
  operation: any = [];
  //fetchExtractionFiles
  p: number = 1;
  p1: number = 1;
  p2: number = 1;
  //getProjectSrctgtconfSelect
  prjSrcTgt: any = {};
  prjSrcTgtD: any = {};
  ExeLog: any = {};
  prjSchmea: any = {};
  //getConList
  ConsList: any;
  //selectIteration
  ref_spin: boolean = false
  iterationselected: any
  //getUpdatedRunNumber
  getRunSpin: boolean = false;
  connectionType: any = [
    { values: 'F', option: 'File' },
    { values: 'D', option: 'Database' },
  ];
  //GetRunno
  runNoData: any = [];
  projectId: string = '';
  getRole: any;
  prjLogData: any = [];
  //selectOperation
  codeextractionForm: any;
  term: any;
  tenantId: any;
  subscriptionId: any;
  resourceGroup: any;
  location: any;
  vmName: string = '';
  dbName: string = '';
  filtered: any;
  dbNameFiltered: any;
  executeCommandObj: any = {};
  //getProjectSrctgtconfSelectD
  enableFileName: boolean = false;
  awrSchemaName: any;
  //getprojectDocumentsDetail
  showSchema: boolean = false;
  showSchema1: boolean = true;
  value: any;
  spin: boolean = false;
  project_name: any;
  //projectConRunTblInsert
  runinfospin: boolean = false;
  runinfospins: boolean = false;
  pi: number = 10;
  prjdatas: any;
  page1: number = 1;
  page: number = 1;
  dropdownList = [];
  selectedItems = [];
  schemaName: any = [];
  schemaname: any = [];
  disabledprevious: boolean = true;
  page2: number = 1;
  piA: number = 10;
  piB: number = 10;
  //searchFilter
  datachanges: any;
  datachange1: any;
  datachange2: any;
  datachange: any;
  grid_active: boolean = true;
  not_grid: boolean = true;
  //openModal
  selectedschemas: string = '';
  //selectOperation
  OpName: any;
  //gethrs
  hrs: any
  //downloadFile
  fileResponse: any
  spin_dwld: any;
  //GetIndividualLogs
  r_id: any;
  logdata: any;
  //deleteTableDatas
  deleteResponse: any
  tabledata: any = []
  i: any;
  //getreqTableData
  z: any;
  //getOperationList
  opeList: any;
  //getPrjRuninfoSelectAll
  runnoExtraction: any
  runnoForReports: any;
  //SelectObjectTypes
  objectType: any = [];
  obtypevalue: any
  //fetchExtractionFiles
  extractionFiles: any
  //selectedfiletype
  selectedConname: any;
  conId: any;
  conName: any;
  //projectConRunTblInsert
  prjdata: any;
  //downloadFile1
  validationreports: any;
  //setRedisCache
  redisResp: any
  //getRunNumber
  currentRunNumber: string = '';
  fileStatus: any;
  projectDocuments: any;
  projectDocumentFilter: any;

  pageName: string = ''
  migtypeid: any = ''
  schemalable: any = ''
  extractionOpeartion: any = []
  constructor(private titleService: Title,private common:CodeMigrationService, private toast: HotToastService, private assessmentService: AssessmentService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }

    this.extractionOpeartion = [{ option: 'Pre Extraction', value: 'Pre_Extraction' },
    { option: 'Code Extraction', value: 'Code_Extraction' },
    { option: 'Split Package', value: 'Split_Package' },
    { option: 'Conversion Dependent Files', value: 'Conversion_Dependent_Files' },
    ]
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.codeextractionForm = this.formBuilder.group({
      runNumber: ['', [Validators.required]],
      connectionType: [''],
      connectionName: ['', [Validators.required]],
      fileName: [''],
      exeop: [''],
      operation: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      refreshFlag: [''],
      objectType:['', [Validators.required]]
    });
    this.p1 = 1;
    this.page2 = 1;
    this.p = 1;
    this.p2 = 1;
    this.page = 1
    this.page2 = 1;
    //this.fetchExtractionFiles()
    this.GetConsList();
    this.getreqTableData();
    this.ref_spin = false
    this.getOperationList();
    // this.GetIndividualLogs("null");
    this.getPrjRuninfoSelectAll();



  }

  get f() {
    return this.codeextractionForm.controls;
  }
  openPopup() {
    this.schemaName = []
    this.selectedschemas = ''
    this.codeextractionForm.reset();
  }
  // operationSelect
  operationSelect(value: any) {

  }
  //changeFn
  changeFn(value: any) {
    if (value == "ALL") {
      this.schemaList.forEach((item: any) => {
        this.schemaName.push(item.schema_name)
      })
    }
    else{
    this.schemaName = value
    }
  }
  //getProjectSrctgtconfSelect
  getProjectSrctgtconfSelect() {
    this.prjSrcTgt.projectId = this.projectId.toString();
    this.prjSrcTgt.migsrcType = 'F';
    this.prjSrcTgt.connectionName = null;

  }
  //getProjectSrctgtconfSelectD
  getProjectSrctgtconfSelectD(migSrcType: any) {
    if (migSrcType == 'F') {
      this.enableFileName = true;
    } else {
      this.enableFileName = false;
    }
    this.prjSrcTgtD.projectId = this.projectId.toString();
    this.prjSrcTgtD.migsrcType = migSrcType;
    this.prjSrcTgtD.connectionName = 'null';
  }
  //getprojectDocumentsDetail
  getprojectDocumentsDetail(value: any) {
    this.value = value;
    let folderName = '';
    if (value == '4') {
      folderName = 'awr_report';
      this.awrSchemaName = 'awr';
      this.showSchema = true;
      this.showSchema1 = false;
    } else if (value == '5') {
      folderName = 'Infrastructure';
      this.showSchema = true;
      this.showSchema1 = true;
    } else {
      folderName = 'ProjectDocs';
      this.showSchema = false;
      this.showSchema1 = true;
    }
    const requestObj = {
      projectID: this.projectId.toString(),
      containerName: 'qmigratorfiles' + this.projectId,
      folderName: folderName,
    };
    if (value == '4' || value == '5') {
      this.assessmentService
        .projectDocumentsDetailSelect(requestObj)
        .subscribe((data) => {
          this.fileData = data;
        });
    }
  }
  //getPrjExeLogSelect
  getPrjExeLogSelect() {
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operation = 'TASK';
    this.ExeLog.pageNo = '1';
    this.assessmentService.PrjExeLogSelect(this.ExeLog).subscribe((data: any) => {
      this.userData = data['Table1'];
    });
  }


  //deleteTableDatas
  deleteTableDatas(request_id: any) {
    const obj: deleteTableData = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.assessmentService.deleteTableData(obj).subscribe((data: deleteTabledata) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData()
    })
  }

  /*--- Delete Project Document   ---*/

  deleteFiles(path: string) {
    this.assessmentService.deleteFile(path).subscribe({
      next: (data: deleteFile) => {
        this.fileStatus = data.message
        this.getDocuments();
        this.toast.success(this.fileStatus)
      },
      error: (error) => {
        this.toast.error(error.message)
      },
    });
  }

  /*--- Get Documents   ---*/

  getDocuments() {
    const path = "projectdoc/"
    this.assessmentService.getFiles(path).subscribe((data: documentsList) => {
      this.projectDocuments = data
      this.projectDocumentFilter = data
    })
  }


  //getreqTableData
  getreqTableData() {
    this.tabledata = []
    const obj1: GetReqData = {
      projectId: this.projectId,
      operationType: "Pre_Extraction"
    }
    const obj2: GetReqData = {
      projectId: this.projectId,
      operationType: "Code_Extraction"
    }
    const obj3: GetReqData = {
      projectId: this.projectId,
      operationType: "Split_Package"
    }
    const obj4: GetReqData = {
      projectId: this.projectId,
      operationType: "Conversion_Dependent_Files"
    }
    var obj = [obj1, obj2, obj3, obj4]
    this.ref_spin = true
    obj.forEach((el: GetReqData) => {
      this.assessmentService.GetReqData(el).subscribe((data: any) => {
        var temp = data['Table1']
        if (temp != undefined) {
          if (temp.length > 0) {
            temp.forEach((item: any) => {
              this.tabledata.push(item)
            })
          }

          this.tabledata = this.tabledata.sort((a: any, b: any) => {
            return b.request_id - a.request_id; // For descending order
          });
        }
      })
      this.ref_spin = false
    })



    // this.assessmentService.GetReqData(obj).subscribe((data: reqData) => {
    //   this.tabledata = data['Table1'];


    //   if (this.tabledata == undefined) {
    //     this.tabledata = []
    //   }
    //   else {
    //     this.ref_spin = false
    //     for (let k = 0; k < this.tabledata.length; k++) {
    //       if (this.tabledata[k].objecttype == "ALL") {
    //         this.tabledata[k].objecttype = ""
    //       }
    //     }
    //     if (this.tabledata != undefined) {
    //       for (this.z = 0; this.z < this.tabledata.length; this.z++) {
    //         for (let i = 0; i < this.ConsList?.length; i++) {
    //           if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
    //             this.tabledata[this.z].conname = this.ConsList[i].conname
    //           }
    //         }
    //       }
    //     }
    //     else {
    //       this.tabledata = []
    //     }
    //   }
    // })
  }
  //GetIndividualLogs
  GetIndividualLogs(action: any) {
    this.p2 = 1
    if (action == "null") {
      this.r_id = "null"
    }
    if (action == "previous") {
      this.r_id = this.logdata[0].exelog_id;
    }
    if (action == "next") {
      this.r_id = this.logdata[this.logdata.length - 1].exelog_id;
      this.disabledprevious = false;
    }
    if (action == '') {
      action = 'null'
    }
    const logobj: GetIndividualLogs = {
      projectId: this.projectId.toString(),
      operationType: "Extraction",
      action: 'null',
      row_id: this.r_id,
      runno: action,
      operationName: 'null'
    }
    this.assessmentService.GetIndividualLogs(logobj).subscribe((data: GetIndividual) => {
      this.logdata = data['Table1'];
    });
  }

  //getPrjRuninfoSelectAll
  getPrjRuninfoSelectAll() {
    this.assessmentService.GetRunno(this.projectId).subscribe((data: GetRunno) => {
      this.getRunSpin = false
      this.getRunSpin1 = false
      this.totalRunnumbers = data['Table1']
      this.runnoForReports = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoExtraction = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoForReports = this.runnoForReports.filter((data: any) => { return ((data.operation_name == "Extraction") && data.iteration != '') })
      this.runnoExtraction = this.runnoExtraction.filter((item1: any) => {
        if (item1.iteration == "") {
          item1.iteration = "New"
        }
        return (item1.operation_name == "Extraction") || item1.iteration == "New"
      })
      this.runNoData = data['Table1'];
      this.runNoData = this.runNoData.filter((item: any) => {
        if (item.iteration == "") {
          item.dbschema = "ALL"
        }
        return (item.operation_type == "Extraction") || item.iteration == ""
      })
      this.runNoData = this.filterList(this.runNoData)
      this.runnoForReports = this.filterList(this.runnoForReports)
      this.runnoExtraction = this.filterList(this.runnoExtraction)
    });
  }
  filterList(listData: any) {
    let uniqueNames: any = []
    for (let k = 0; k < listData.length; k++) {
      if (uniqueNames.length == 0) {
        uniqueNames.push(listData[k])
      }
      else {
        var abc = uniqueNames.filter((item: any) => {
          return item.iteration === listData[k].iteration
        })
        if (abc.length == 0) {
          uniqueNames.push(listData[k])
        }
      }
    }
    return uniqueNames;
  }
  //getUpdatedRunNumber
  getUpdatedRunNumber() {
    this.getRunSpin = true
    this.getPrjRuninfoSelectAll()
  }
  getRunSpin1: boolean = false;
  getUpdatedRunNumber1() {
    this.getRunSpin1 = true
    this.getPrjRuninfoSelectAll()
  }

  getPrjExeLogSelectTask(value: any) {
    if (value == "") {
      value = "null";
    }
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operation = "TASK";
    this.ExeLog.pageNo = "1";
    this.ExeLog.iteration = value;
    this.assessmentService.PrjExeLogSelectTask(this.ExeLog).subscribe((data: any) => {
      this.prjLogData = data['Table1'];
    })
  }

  //openModal
  openModal(value: any) {
    for (let i = 0; i < this.schemaName.length; i++) {
      this.selectedschemas = this.selectedschemas + this.schemaName[i];
      if (i < this.schemaName.length - 1) {
        this.selectedschemas = this.selectedschemas + ',';
      }
    }
    $('#demo').offcanvas('show');
  }
  //selectedfiletype

  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;
    this.getSchemasList(this.conId)
  }

  //selectOperation
  selectOperation(id: any) {
    // const op = this.operation.filter((item: any) => {
    //   return item.operation_id == id;
    // });
    this.OpName = id// op[0].operation_name;
    this.codeextractionForm.controls.schema.setValidators([Validators.required])
    this.codeextractionForm.controls.schema.updateValueAndValidity()
  }
  //getSchemasList
  getSchemasList(ConnectionId: any) {
    this.schemaList = []
    if (this.migtypeid == "31") {
      this.ConsList.forEach((item: any) => {
        if (item.Connection_ID === ConnectionId.toString()) {
          let ob = {
            schema_name: item.dbname
          }
          this.schemaList.push(ob);
        }
      })
    }
    else {
      const obj: SchemaListSelect = {
        projectid: this.projectId,
        connectioId: this.conId,
      };
      this.assessmentService.SchemaListSelect(obj).subscribe((data: any) => {
        this.schemaList = data && data['Table1'] ? data['Table1'] : [];
        this.schemaList = this.schemaList.filter((item:any) => item.schema_name !== 'ALL');
         this.schemaList.forEach((item: any) => {
        item.type = "ALL"
      })
      });
    }
  }

  //GetConsList
  condata: any = []
  GetConsList() {
    this.assessmentService.getConList(this.projectId.toString()).subscribe((data: conList) => {
      this.condata = data['Table1'];
      // this.ConsList = this.condata.filter((item: any) => {
      //   return item.migsrctgt == 'S' && item.conname != '';
      // });
    });
  }

  //getOperationList
  getOperationList() {
    const obj: GetOperations = {
      projectId: this.projectId,
      OperationType: 'Extraction',
    };
    this.assessmentService.GetOperations(obj).subscribe((data: Operation) => {
      this.operation = data['Table1'];
      this.opeList = this.operation;
    });
  }

  projectConRunTblInsert(data: any, value: any) {
    if (value) {
      data.status = 'P';
    } else {
      data.status = 'I';
    }
    const tableinsesrt: projectConRunTblInsert = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(data.operation),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: '',
      objectname: '',
      objecttype: '',
    };
    this.runinfospin = true;
    this.assessmentService.projectConRunTblInsert(tableinsesrt).subscribe(
      (data: any) => {
        this.prjdata = data['jsonResponseData']['Table1'];
        if (data.message == 'Success') {
          this.runinfospin = false;
          $('#demo').offcanvas('hide');
          this.toast.success('Successfully Inserted');
          this.getreqTableData();
        }
      },
      () => {
        this.spin = false;
        this.toast.error('Something happened');
      }
    );
  }
  //projectConRunTblInserts
  projectConRunTblInserts(data: any, value: any) {
    this.runinfospins = true;
    if (value) {
      data.status = 'P';
    } else {
      data.status = 'I';
    }
    const tableinsesrt = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(data.operation),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: '',
      objectname: '',
      objecttype: '',
    };
    this.selectedItems = []
    if (this.tabledata.length != 0) {
      const res = this.tabledata.filter((item: any) => {
        return item.connection_id == this.conId && item.schemaname == this.schemaName.toString() && item.status == "I" && item.operation_name == this.OpName
      })
      if (res.length >= 1) {
        this.runinfospins = false;
        $('#demo').offcanvas('hide');
        this.toast.error("Request already Added")
      }
      if (res.length == 0) {
        this.RedisCommand()
        this.getreqTableData();

      }
    }
    if (this.tabledata.length == 0) {
      this.RedisCommand()
      this.getreqTableData();


    }


  }
  //getRunNumber
  totalRunnumbers: any = []
  getRunNumber(data: any) {
    this.currentRunNumber = data;
    this.ConsList = []
    this.schemaList=[]
    if (data == "New") {
      this.ConsList = this.condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      
      this.clearValidators(['exeop'])
    } else if(this.totalRunnumbers){
      this.totalRunnumbers.filter((item: any) => {
        if (item.iteration == data) {
          this.schemaList.push({ schema_name: item.schemaname })
          this.schemaList = this.schemaList.filter((item: any) => item.schema_name !== 'ALL');
          this.schemaList.forEach((item: any) => {
            item.type = "ALL"
          })
          if (this.ConsList.length == 0) {
            this.ConsList.push({ Connection_ID: item.connection_id, conname: item.conname })
          }
        }
      })
    }
    else {
      // this.schemaList=[]
      this.ConsList = []
      this.currentRunNumber = data;
      this.runnoExtraction.filter((item: any) => {
        if (item.iteration == data) {
          // this.schemaList.push({schema_name:item.schemaname})
          if (this.migtypeid == "31") {
            var dbData = this.condata.filter((item1: any) => {
              return item1.Connection_ID == item.connection_id
            })
            this.ConsList.push({ Connection_ID: item.connection_id, conname: item.conname, dbname: dbData[0].dbname })
          }
          else {
            this.ConsList.push({ Connection_ID: item.connection_id, conname: item.conname })
          }
        }
      })
      this.setRequiredValidators(['exeop'])
    }
  }
  exeoperation: any
  selectExeOperation(value: any) {
    this.exeoperation = value

  }
  //RedisCommand
  RedisCommand() {
    this.runinfospins = true;
    const obj: setRedisCache1 = {
      projectId: this.projectId.toString(),
      option: 0,
      schema: this.schemaName.toString(),
      connection: this.conName,
      Object: this.OpName,
      srcConId: this.conId,
      jobName: "qmig-asses",
      iteration: this.currentRunNumber,
    }
    this.assessmentService.setRedisCache1(obj).subscribe((data: fileStatus) => {
      this.redisResp = data;
      this.runinfospins = false;
      this.schemaName = []
      this.selectedschemas = ''
      this.codeextractionForm.reset();
      $('#demo').offcanvas('hide');
      this.toast.success("Triggered Successfully")
    })
  }
  objCategory: any = [
    { values: 'all', option: 'ALL' },
    { values: 'storage', option: 'StorageObjects' },
    { values: 'Code_Objects', option: 'CodeObjects' } // CodeObjects
  ]
  //SelectObjectTypes
  SelectObjectTypes(value: any) {
    this.objectType = value;
  }
  //getFileFromList
  downloadFile1(fileInfo: any) {
    this.assessmentService.getFileFromList(fileInfo.filePath).subscribe((data: any) => {
      const base64content = data.message
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = ' data:application/octet-stream;base64,' + base64content
      downloadLink.download = fileName;
      downloadLink.click();
    })

  }
  //fetchExtractionFiles
  fetchExtractionFiles() {
    this.extractionFiles = []
    var basePath = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/"
    const paths = ['Pre_Extraction', 'Code_Extraction', 'Split_Packages', 'Conversion_Dependent_Files']
    paths.forEach((el: string) => {
      this.assessmentService.GetFilesFromDir(basePath + el).subscribe((data: any) => {
        data.forEach((item: any) => {
          this.extractionFiles.push(item)
        })
      })
    })

    this.extractionFiles.sort((a: any, b: any) => {
      const dateA = new Date(a.created_dt).getTime();
      const dateB = new Date(b.created_dt).getTime();
      return dateB - dateA; // For descending order
    });
  }
  //downloadFile
  downloadFile(fileInfo: any) {
    this.assessmentService.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  /*--- Download file   ---*/

  downloadFile2(title: any) {
    this.assessmentService.downloadFiles(title).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = title.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
    })
  }
  hours: any = [{
    option: "ALL", value: "0"
  },
  {
    option: "1 hr", value: "1"
  }, {
    option: "5 hrs", value: "5"
  }, {
    option: "10 hrs", value: "10"
  }, {
    option: "1 Day", value: "24"
  },
  ]
  //gethrs
  gethrs(value: any) {
    this.hrs = value
    this.filterReports()
  }
  //ReportsFilterByTime
  filterReports() {
    if (this.hrs == "0") {
      this.extractionFiles = []
      this.fetchExtractionFiles()
    }
    else {
      this.extractionFiles = []
      const obj = {
        path: "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Object_Count_Reports/",
        hrs: this.hrs,
        validationFlag: true
      }
      this.assessmentService.ReportsFilterByTime(obj).subscribe((data: any) => {
        this.extractionFiles = data;
      })
    }
  }
  //selectIteration
  selectIteration(value: any) {
    this.iterationselected = value;
    this.fetchExtractionFiles();
  }

  onKey() {
    this.page2 = 1;
    this.p = 1;
    this.p2 = 1;
  }
  refreshChecked: boolean = false
  getCheckValue($event: any): void {
    if ($event.target.checked == true) {
      this.refreshChecked = true
    }
    else {
      this.refreshChecked = false
    }
  }
  page3: number = 1
  datachangeLogs: any
  ExtractionResponseData: any = []
  ExecutionLogs: any = []
  iterationForLogs: any
  filterExecutionReports() {
    this.ExecutionLogs = []
    var basePath = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Execution_Logs/"
    const paths = ['Pre_Extraction', 'Code_Extraction', 'Split_Package', 'Conversion_Dependent_Files']
    paths.forEach((el: string) => {
      this.assessmentService.GetFilesFromDir(basePath + el).subscribe((data: any) => {
        data.forEach((item: any) => {
          this.ExecutionLogs.push(item)
        })
      })
    })

    this.ExecutionLogs.sort((a: any, b: any) => {
      const dateA = new Date(a.created_dt).getTime();
      const dateB = new Date(b.created_dt).getTime();
      return dateB - dateA; // For descending order
    });
  }
  selectIterforlogs(value: any) {
    this.iterationForLogs = value;
    this.filterExecutionReports()
  }
  AssessmentCommand() {
    this.runinfospins = true;
    var dbflag = ""
    this.ConsList.filter((item: any) => {
      if (item.Connection_ID == this.conId) {
        if (item.conname.toLowerCase().includes("dump")) {
          dbflag = "False"
        }
        else {
          dbflag = "True"
        }
      }
    })

    let obj: any = {
      sourceConnectionId: this.conId,
      projectId: this.projectId.toString(),
      dbFlag: dbflag,
      task: "Extraction",
      iteration: this.currentRunNumber,
      objectCategory: this.OpName,
      restartFlag: this.refreshChecked == true ? "True" : "False",
      processType: "",
      jobName: "qmig-asses",
      schema: this.schemaName.toString(),
      exeoperation: this.currentRunNumber == "New" ? "" : this.exeoperation,
      objectType:this.objtype.toString(),
      codeoption:true
    }
    console.log(obj)
    this.assessmentService.AssessmentCommad(obj).subscribe((data: any) => {
      this.ExtractionResponseData = data;
      this.runinfospins = false;
      $('#demo').offcanvas('hide');
      this.toast.success("Extraction Command Executed")
    },
      error => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong')
      })
  }
  selectedobjecttypes:any
  objtype:any
  selectObj(value: any) {
    this.objtype = value;
  }

  setRequiredValidators(controls: string[]) {
    controls.forEach(control => {
      (this.codeextractionForm.controls[control as keyof typeof this.codeextractionForm.controls] as FormControl).setValidators([Validators.required]);
    });
  }

  clearValidators(controls: string[]) {
    controls.forEach(control => {
      (this.codeextractionForm.controls[control as keyof typeof this.codeextractionForm.controls] as FormControl).clearValidators();
    });
  }
  uploadForm = this.formBuilder.group({
    file: ['', [Validators.required]],

  })
  fileName: string = '';
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
  }
  uploadfileSpin: boolean = false;
  upload_spin: boolean = false;
  selectFile: any;
  fileAdd: boolean = false;
  ExtractionCommandwithFile: any;
  buttonDisable: boolean = false;
  AssessmentCommandwithFile() {
    this.uploadfileSpin = true;

    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('projectId', this.projectId.toString());
    formData.append('fileName', this.selectFile.name);
    this.assessmentService.AssessmentCommandwithFile(formData).subscribe(
      (data: any) => {
        this.uploadfileSpin = false;
        this.uploadForm.controls.file.reset()
        this.fileName = ''
        this.toast.success('Data uploaded Successfully');
      },
      () => {
        this.uploadfileSpin = false;
        this.toast.error('Something went wrong');
      }
    );
  }
slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }
  openModal1(){
    
  }
  dep_objectType: any
  getAllObjectTypes() {
    const codeObj = {
      projectId: this.projectId,
      objectgroupname: "Code_Objects"
    };
  
    const storageObj = {
      projectId: this.projectId,
      objectgroupname: "Storage_Objects"
    };
    this.common.getObjectTypes(codeObj).subscribe((codeData: any) => {
      let codeList = codeData['Table1'] || [];
      if (this.migtypeid == "20") {
        codeList = codeList.filter((item: any) =>
          item.objecttype_name !== "Package_Function" &&
          item.objecttype_name !== "Package_Procedure" &&
          item.objecttype_name !== "Package"
        );
      }
      this.common.getObjectTypes(storageObj).subscribe((storageData: any) => {
        let storageList = storageData['Table1'] || [];
        this.objectType = [...codeList, ...storageList];
        this.objectType = this.objectType.filter((item:any) => item.object_type !== 'ALL');
        this.objectType.forEach((item: any) => {
       item.type = "ALL"
     })
        // this.objectType.unshift({
        //   object_type: "ALL",
        //   object_category: "Combined",
        //   object_type_id: "0"
        // });
      });
    });
  }
  
}






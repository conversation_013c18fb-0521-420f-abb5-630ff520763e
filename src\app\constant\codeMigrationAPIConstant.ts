import { trigger } from "@angular/animations";

export const codemigrationAPIConstant = {
    SchemaSelect: 'SchemaSelect?runno=',
    GetObjectType: 'GetObjTypes?runno=',
    RuninfoSelect: 'RuninfoSelect',
    getConList: 'GetConnectionList?projectId=',
    ObjectsSelect: 'ObjectsSelect',
    GetObjectCode: 'GetObjectCode',
    manualCodeUpdate: 'PrjTgtCodeUpdate',
    RedisCacheInsertion: 'RedisCacheInsertion',
    GetProjectTgtDeployStatusSelect: 'GetProjectTgtDeployStatusSelect',
    GetFilesFromDir: 'GetFilesFromDirectory?path=',
    updloadLargeFiles: 'UploadCloudFiles',
    getSingleFileFoders: 'GetSingleFileDirs?path=',
    getDeployOperations: 'GetDeployOperations',
    getDeploySequence: 'GetDeploySequence?DeployOperation=',
    DeleteSchema: 'DeleteSchema?schemaname=',
    getObjectTypes: 'GetObjectTypes?projectId=',
    prjSchemaListSelect: 'PrjSchemasListSelect',
    projectDocumentsDetailSelect: 'ARM/GetFiles/',
    UserAccessSelect: 'UserAccessSelect',
    PrjExeLogSelect: 'PrjExeLogSelect',
    getoperation: 'GetOperationList?projectId=',
    getobjname:'GetObjectNames',
    GetSchemasByRunId: 'GetSchemasByRunId?runid=',
    getobjtype:'GetObjectTypes',
    SchemaListSelect: 'GetSchemaList?projectId=',
    GetRunno: 'GetRunNumbers?projectId=',
    GetReqData: 'GetRequestTableData?projectId=',
    deleteTableData: 'DeleteRequestTableData?projectId=',
    GetIndividualLogs: 'GetIndividualLogs',
    ReportsFilterByTime: 'FilterReportsByTime',
    downloadLargeFiles: 'DownloadLargeFile?filePath=',
    setRedisCache: 'RedisCacheInsertion',
    OracleSchemas: 'GetSchema?Conid=',
    tgtSchemaSelect: 'GetPgSchemas?conid=',
    projectConRunTblInsert: 'RequestTableInsert',
    GetCommonFilesFromDirectory: 'GetCommonFilesFromDirectory?path=',
    GetDbnames: 'GetDbnames?ConId=',
    executeExtension: 'ExecuteExtension?Conid=',
    tgtSchemaSelectwithdb:'GetPgSchemaswithDb?conid=',
    getSchemas:'getSchemas?path=',
    executePgCheck:'ExecutePGCheck', 
    getProcs:'GetProcs?ConId=',
    createschema:'CreateSchema',
    dropschemaobjs:'DropObjects',
    uploadAndUnzip:'UnZipandUploadFolder',
    triggerDalCommand:'TriggerDalCommand',
    selectmigtype:'Selectmigtype?migid=',
    ConversionCommand:'ConversionCommand',
    getAgent:'conversion/conversion-agent',
    getAgentTargetStmt:'ConversionAgentDeploymentSelect?tgt_id=',
    getAgentDeploymentFullDataSelect:'ConversionAgentDeploymentFullDataSelect?tgt_id=',
    postReviewedDataUpdated:'ReviewedDataUpdated',
    getTargetFullCodeSelect:'GetTargetFullCodeSelect?tgt_id=',
    downloadFetchDependency:'Ora2PgConversion/ora2pg/GetDependentObjects',
    stage1MetadataGeneration:'conversion/stage1-metadata-generation',
    getPipelineList:'GetPipelineList?ConId=',
    getModuleSchemas: 'conversion_stage2/metadata/schemas?process_type=',
    getModuleObjectTypes: 'conversion_stage2/metadata/object-types?process_type=',
    getModuleObjects: 'conversion_stage2/metadata/object-names?process_type=',
    triggerAgent: 'conversion_stage2/conversion-agent-stage2',
    getModuleStatus: 'conversion_stage2/requests/recent?limit=50',
    downloadZip: 'conversion_stage2/download-file?migration_name=',
    getTargetStatements:'conversion_stage2/metadata/target-statements?process_type=',
    getStatements: 'conversion_stage2/metadata/statement-details?target_statement_number=',
    saveAgentLogs:'conversion_stage2/update-module-review-status',
    moveToRegistry: 'conversion_stage2/move-to-registry',
    mergeToQBook: 'conversion_stage2/merge-to-qbook',
}

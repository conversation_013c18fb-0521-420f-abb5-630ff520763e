<div class="v-pageName">{{pageName}}</div>

<!---Assessment List --->
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Code Scan
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="AssessmentForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Run No</label>
                                        <select class="form-select" formControlName="runNumber"
                                            (change)="getRunNumber(getrun.value)" #getrun>
                                            <option selected value="">Select a Run No</option>
                                            @for(list of runnoExtraction; track list;){
                                            <option value="{{ list.iteration }}">{{ list.iteration }}</option>
                                            }
                                        </select>
                                        @if ( f.runNumber.touched && f.runNumber.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.runNumber.errors.required) {Run No is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Connection
                                            Name</label>
                                        <select class="form-select" formControlName="connectionName" #Myselect (change)="
                                            selectedfiletype(Myselect.value);
                                                 " aria-label="Default select example">
                                            <option selected value="">Select a Connection Name</option>
                                            @for(ConsList of ConsList;track ConsList; ){
                                            <option value="{{ConsList.Connection_ID}}">{{ConsList.conname}}</option>
                                            }
                                        </select>
                                        @if ( f.connectionName.touched && f.connectionName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.connectionName.errors.required) {Connection Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Operation</label>
                                        <select class="form-select" formControlName="operation"
                                            (change)="selectOperation(MyOp.value)" #MyOp>
                                            <option selected value="">Select Operation</option>
                                            @for(list of operation;track list;){
                                            <option value="{{list.operation_category }}">
                                                {{ list.operation_category }}
                                            </option>
                                            }
                                        </select>
                                        @if ( f.operation.touched && f.operation.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.operation.errors.required) {Operation is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">{{schemalable}}
                                            Name</label>
                                        <ng-select (change)="changeFn(selectedItems)" formControlName="schema"
                                            [items]="schemaList" [multiple]="true" groupBy="type"
                                            [selectableGroup]="true" bindLabel="schema_name" [closeOnSelect]="false"
                                            bindValue="schema_name" [(ngModel)]="selectedItems">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schema_name}}
                                            </ng-template>
                                        </ng-select>
                                        @if ( f.schema.touched && f.schema.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.schema.errors.required) {{{schemalable}} Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <!-- <div class="col-md-4 col-xl-9"></div> -->
                                <div class="row">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <button class="btn btn-upload w-100 " [disabled]="AssessmentForm.invalid"
                                                (click)="openModal(this.AssessmentForm.value)"
                                                data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                                                    class="mdi mdi-checkbox-marked-circle-outline"
                                                    aria-hidden="true"></span>Check Details</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Execution Status
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-7 offset-5 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                            <button class="btn btn-sync mt-4" (click)="getreqTableData()">
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Run No</th>
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>{{schemalable}} Name</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of tabledata| searchFilter: datachange1| paginate:{ itemsPerPage: piA,
                                currentPage:
                                p1,
                                id:'second' };
                                track con;) {
                                <tr>
                                    <!-- <td>{{p1*piA+$index+1-piA}}</td> -->
                                    <td>{{con.iteration_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_category }}</td>
                                    <td>{{ con.schema_name }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        {{con.status}}
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p1 = $event" id="second"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Execution Logs
                </button>
            </h3>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-sm-6 col-md-3 col-xl-3 mt-3">
                            <div class="form-group">
                                <select class="form-select" #code (change)="selectIterforlogs(code.value)">
                                    <option selected value="">
                                        Select Run Number
                                    </option>
                                    @for( list of runnoForReports;track list;){
                                    <option value="{{ list.iteration}}">{{list.iteration}}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-3 col-xl-3"></div>
                        <div class="col-sm-6 col-md-6 col-xl-6 d-flex">
                            <div class="custom_search cs-r me-3 my-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="fetchExecutionLogs()">
                                @if(ref_spin1){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(exelogs of ExecutionLogs |searchFilter:
                            datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: page4, id:'five'};
                            track exelogs){
                            <tr>
                                <td>{{ page4*10+$index+1-10 }}</td>
                                <td>{{exelogs.fileName }}</td>
                                <td>{{exelogs.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(exelogs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page4 = $event" id="five">
                    </pagination-controls>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Reports
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <!-- <h3 class="main_h px-3 pt-3">Reports & Logs</h3> -->
                <!-- Assessment Logs -->
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-3 col-sm-3 col-md-3 col-xl-3 mt-3">
                            <div class="form-group">
                                <select class="form-select" (change)="selectIteration(iter.value)" #iter>
                                    <option selected value="">Select Run Number</option>
                                    @for( list of runnoForReports;track list;){
                                    <option value="{{ list.iteration }}">
                                        {{ list.iteration }}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-5 col-sm-2 col-md-2 col-xl-3 mt-3 pe-1"></div>
                        <div class="col-4 col-sm-7 col-md-7 col-xl-6 d-flex">
                            <div class="custom_search cs-r me-3 my-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Code Scan Reports" class="form-control"
                                    [(ngModel)]="datachange" (keyup)="onKey()">
                            </div>
                            <button class="btn btn-sync" (click)="getUpdatedRunNumber()">
                                @if(getRunSpin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>
                </div>
                <!-- downloadFile -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>File Name</th>
                                <th>Folder Name</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(validrepo of assessmentFiles |searchFilter: datachange|paginate:{
                            itemsPerPage: pi, currentPage: p, id:'third'};
                            track validrepo;){
                            <tr>
                                <td>{{p*pi+$index+1-pi}}</td>
                                <td>{{validrepo.fileName }}</td>
                                <td>Project Docs</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty list of Reports</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                </div>
            </div>
        </div>
        <!---Page-Activity Log-->
        <!-- <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    Page-Activity Log
                </button>
            </h3>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <!-- Run No -
                        <div class="col-sm-6 col-md-4 col-xl-2">
                            <div class="form-group ">
                                <select class="form-select" (change)="GetIndividualLogs(assessment.value)" #assessment>
                                    <option selected value="">Select Run Number</option>
                                    @for( list of runNoData;track list;){
                                    <option value="{{ list.iteration }}">
                                        {{ list.dbschema }}
                                    </option>
                                    }
                                </select>
                                <!-- <span class="ml-3 c-p pt-2" (click)="getUpdatedRunNumber()"><i
                                                *ngIf="!getRunSpin" class="fa fa-refresh "></i><i *ngIf="getRunSpin"
                                                class="fa fa-refresh fa-spin "></i></span> 
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-8 col-xl-10">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Activity Logs" class="form-control"
                                    [(ngModel)]="datachange2" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <!-- Table data -
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>Run No</th>
                                    <th>Date and Time</th>
                                    <th>Task</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(documents of logdata | searchFilter: datachange2 | paginate: {
                                itemsPerPage:
                                piB, currentPage: p2,id:'first' } ; track documents; ) {
                                <tr>
                                    <td>{{p2*piB+$index+1-piB}}</td>
                                    <td>{{ documents.iteration }}</td>
                                    <td>{{ documents.activity_date }}</td>
                                    <td>{{ documents.migtask }}</td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <!--- Pagination -
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p2 = $event" id="first"></pagination-controls>
                    </div>
                </div>
            </div>
        </div> -->
    </div>
    <!--- Assessment --->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Code Scan</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <!--- Connection Name --->
        <div class="offcanvas-body">
            <form class="form qmig-Form checkForm">
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Run Number</label>
                    : &nbsp;{{ currentRunNumber }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Connection Name</label>
                    : &nbsp;{{ conName }}
                </div>
                <!--- Operation--->
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Operation</label>
                    : &nbsp;{{ OpName }}
                </div>
                <!--- Schema Name--->
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Schema Name</label>
                    : &nbsp;{{ schemaName.toString() }}
                </div>
                <div class="form-group">
                    <div class="form-check form-check-flat form-check-primary mt-3">
                        <label class="form-check-label form-label">
                            <input type="checkbox" class="form-check-input" (click)="getCheckValue($event)"
                                formControlName="refreshFlag">
                            <i class="input-helper"></i> Restart
                        </label>
                    </div>
                </div>
                <!--- Execute--->
                <!-- projectConRunTblInserts(AssessmentForm.value, false)" -->
                <div class="form-group">
                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 me-1" (click)="AssessmentCommand()">
                            <span></span> Execute @if(runinfospins){<app-spinner />}</button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>
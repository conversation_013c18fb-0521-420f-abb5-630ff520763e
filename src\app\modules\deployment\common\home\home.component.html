<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
<div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
    <div class="accordion-item">
      <h2 class="accordion-header" id="headingOne">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
          Incremental-Upload
        </button>
      </h2>
      
      <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
        <div class="qmig-card-body">
            <div class="row">
                <form class="form add_form mt-3">
                    <div class="form-group" [formGroup]="incrementform">
                        <label for="formFile" class="form-label d-required">Upload File </label>
                        <div class="custom-file-upload">
                            <input class="form-control" type="file" id="formFile" formControlName="fileName" (change)="onFileSelected($event)">
                            <div class="file-upload-mask">
                                @if (fileName == '') { 
                                    <img src="assets/images/fileUpload.png" alt="img" />                                            
                                    <p>Drag and drop deployment file here or click add deployment file  </p>
                                    <button class="btn btn-upload"> Add File </button>
                                 } @else{
                                    <div class="d-flex justify-content-center align-items-center h-100 w-100"><p> {{ fileName }} </p></div>
                                 }
                            </div>                                        
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-xl-3 offset-md-9">
                            <div class="form-group">
                                <button class="btn btn-upload w-100" [disabled]="incrementform.invalid" (click)="uploadFile()"> <span class="mdi mdi-file-plus"  ></span>
                                    Upload@if(upload_spin){<app-spinner />}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
      </div>
    </div>
        <div class="accordion-item">
          <h2 class="accordion-header" id="headingTwo">
            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                Incremental-GetFiles
            </button>
          </h2>
          <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionExample">
            <div class="qmig-card-body">
                <div class="row" [formGroup]="incrementgetform">  
                    <div class="col-md-6 mt-4">
                        <div class="custom_search ms-0">
                            <span class="mdi mdi-magnify"></span>
                            <input type="text" placeholder="Search Status"  class="form-control" >
                        </div>
                    </div>              
                    <div class="col-md-3" [hidden]="!processBtn">
                        <div class="pt-1">
                            <label for="name" class="form-label">Connection Name</label>
                            <select class="form-select" formControlName="conname"
                                aria-label="Default select example">
                            <option selected disabled>Select Connection List</option>
                            <option>PRJ1048TGT</option>
                            <option>PRJ1059TGT2</option>
                            <option>PRJ1059TGT</option>
                        </select>
                        </div>
                    </div>  
                    <div class="col-md-3" [hidden]="!processBtn">
                        <div class=" mt-4">
                            <button class="btn btn-upload w-100" [disabled]="incrementgetform.invalid"> <span class="mdi mdi-cached"  ></span>
                                Process
                            </button>
                        </div>
                    </div>            
                </div>
        </div>
       
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>Select</th>
                        <th>Folder Name</th>
                        <th>File Name</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @for (con of LogsData |searchFilter: datachanges2| paginate:{
                    itemsPerPage:10,currentPage: p2 ,id:'second'} ;track con) {
                    <tr>
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="inlineCheckbox1" (click)="currentlyCheked($event)" value="option1">
                              </div>
                        </td>
                        <td>{{con.fileName }}</td>
                        <td>{{con.fileName }}</td>
                        <td>
                            <button class="btn btn-download"
                                (click)="downloadFile(con)">
                                <span class="mdi mdi-cloud-download-outline"></span>
                            </button>
                            <button class="btn btn-delete" >
                                <span class="mdi mdi-delete"></span>
                            </button>
                        </td>
                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="4">
                            <p class="text-center m-0 w-100">Empty</p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
          </div>
        </div> 
    </div> 
</div>
 


import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { environment } from '../environments/environment';
import { DeleteSchema, GetObjectCode, GetObjectType, GetProjectTgtDeployStatusSelect, ObjectCode, ObjectSelect, ObjectType, ObjectsSelect, RedisCacheInsertion, UpdateConnection, conList, fileStatus, getObjectTypes, manualSave, ob, obj, objtype, redis, redis1, redis2, schemalist } from '../models/interfaces/types';
import { codemigrationAPIConstant } from '../constant/codeMigrationAPIConstant';
import { GetIndividual, GetIndividualLogs, GetReqData, GetRunno, ReportsFilterByTime, SchemaListSelect1, SchemaSelect, deleteTableData, deleteTabledata, projectConRunTblInsert, reqData, setRedisCache } from '../models/interfaces/codemigrationTypes';
import { APIConstant } from '../constant/APIConstant';
import { blob } from 'stream/consumers';
import { HttpResponse } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class CodeMigrationService {

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl +'Conversion/'
  // SchemaSelect
  SchemaSelectURL = this.apiURL + codemigrationAPIConstant.SchemaSelect;
  SchemaSelect = (body: string): Observable<schemalist> => {
    return this.apiService.get(this.SchemaSelectURL + body)
  }

  //GetObjectType
  GetObjectTypeURL = this.apiURL + codemigrationAPIConstant.GetObjectType;
  GetObjectType = (body: GetObjectType): Observable<ObjectType> => {
    return this.apiService.get(this.GetObjectTypeURL + body.runno + '&schemaid=' + body.schema)
  }
  //Deploy
  GetObjectType1 = (body: objtype): Observable<objtype> => {
    return this.apiService.get(this.GetObjectTypeURL + body.runno + '&schemaid=' + body.schemaid)
  }
  GetObjectType2 = (body: objtype): Observable<objtype> => {
    return this.apiService.get(this.GetObjectTypeURL + body.runno + '&schemaid=' + body.schemaid)
  }

  //RuninfoSelect
  RuninfoSelectURL = this.apiURL + codemigrationAPIConstant.RuninfoSelect;
  RuninfoSelect = (): Observable<string> => {
    return this.apiService.get(this.RuninfoSelectURL)
  }

  //  //getConList
  getConListURL = this.apiURL + codemigrationAPIConstant.getConList;
  getConList = (body: string): Observable<conList> => {
    return this.apiService.get(this.getConListURL + body)
  }
  // executeExtension
  executeExtensionURL = this.apiURL + codemigrationAPIConstant.executeExtension;
  executeExtension = (body: any): Observable<conList> => {
    return this.apiService.get(this.executeExtensionURL + body.ConId+"&dbname="+body.dbname)
  }

  //ObjectsSelect
  ObjectsSelectURL = this.apiURL + codemigrationAPIConstant.ObjectsSelect;
  ObjectsSelect = (body: ObjectsSelect): Observable<ObjectSelect> => {
    //console.log(this.ObjectsSelectURL);
    return this.apiService.post(this.ObjectsSelectURL, body);
  }

  //deploy
  ObjectsSelect1 = (body: obj): Observable<obj> => {
    //console.log(this.ObjectsSelectURL);
    return this.apiService.post(this.ObjectsSelectURL, body);
  }
  ObjectsSelect2 = (body: ob): Observable<ob> => {
    //console.log(this.ObjectsSelectURL);
    return this.apiService.post(this.ObjectsSelectURL, body);
  }
  // executePgCheck
  executePgCheckURL = this.apiURL + codemigrationAPIConstant.executePgCheck;
  executePgCheck = (body: any): Observable<any> => {
    return this.apiService.post(this.executePgCheckURL, body);
  }

  //GetObjectCode
  GetObjectCodeURL = this.apiURL + codemigrationAPIConstant.GetObjectCode;
  GetObjectCode = (body: GetObjectCode): Observable<ObjectCode> => {
    return this.apiService.post(this.GetObjectCodeURL, body);
  }
  //GetDbnames
  GetDbnamesURL = this.apiURL + codemigrationAPIConstant.GetDbnames;
  GetDbnames = (body: any): Observable<any> => {
    return this.apiService.get(this.GetDbnamesURL + body)
  }
  // tgtSchemaSelectwithdb
   tgtSchemaSelectwithdbURL = this.apiURL + codemigrationAPIConstant.tgtSchemaSelectwithdb;
   tgtSchemaSelectwithdb= (body: any): Observable<conList> => {
    return this.apiService.get(this.tgtSchemaSelectwithdbURL + body.conid+"&dbname="+ body.dbname)
   }

 
  //manualCodeUpdate

  manualCodeUpdateURL = this.apiURL + codemigrationAPIConstant.manualCodeUpdate;
  manualCodeUpdate = (body: string): Observable<any> => {
    return this.apiService.post(this.manualCodeUpdateURL, body);
  }

  //RedisCacheInsertion
  RedisCacheInsertionURL = this.apiURL + codemigrationAPIConstant.RedisCacheInsertion;
  RedisCacheInsertion = (body: RedisCacheInsertion): Observable<fileStatus> => {
    return this.apiService.post(this.RedisCacheInsertionURL, body);
  }

  //deploy
  RedisCacheInsertion1 = (body: redis): Observable<fileStatus> => {
    return this.apiService.post(this.RedisCacheInsertionURL, body);
  }
  RedisCacheInsertion2 = (body: redis1): Observable<fileStatus> => {
    return this.apiService.post(this.RedisCacheInsertionURL, body);
  }
  RedisCacheInsertion3 = (body: redis2): Observable<fileStatus> => {
    return this.apiService.post(this.RedisCacheInsertionURL, body);
  }

  //GetProjectTgtDeployStatusSelect
  GetProjectTgtDeployStatusSelectURL = this.apiURL + codemigrationAPIConstant.GetProjectTgtDeployStatusSelect;
  GetProjectTgtDeployStatusSelect = (body: GetProjectTgtDeployStatusSelect): Observable<manualSave> => {
    return this.apiService.post(this.GetProjectTgtDeployStatusSelectURL, body);
  }

  //deploy
  //GetFilesFromDir

  GetFilesFromDirURL = this.apiURL + codemigrationAPIConstant.GetFilesFromDir;
  GetFilesFromDir = (body: any): Observable<string> => {
    return this.apiService.get(this.GetFilesFromDirURL + body)
  }
  // getProcs
  getProcsURL = this.apiURL + codemigrationAPIConstant.getProcs;
  getProcs = (body: any): Observable<string> => {
    return this.apiService.get(this.getProcsURL + body.conid+"&dbname="+body.dbname+"&schema="+body.schema)
  }
  //updloadLargeFiles

  updloadLargeFilesURL = this.apiURL + codemigrationAPIConstant.updloadLargeFiles;
  updloadLargeFiles = (body: any): Observable<any> => {
    return this.apiService.post(this.updloadLargeFilesURL, body);
  }

  //getSingleFileFoders
  getSingleFileFodersURL = this.apiURL + codemigrationAPIConstant.getSingleFileFoders;
  getSingleFileFoders = (body: any): Observable<string> => {
    return this.apiService.get(this.getSingleFileFodersURL + body)
  }

  //getDeployOperations
  getDeployOperationsURL = this.apiURL + codemigrationAPIConstant.getDeployOperations;
  getDeployOperations = (): Observable<string> => {
    return this.apiService.get(this.getDeployOperationsURL)
  }

  //getDeploySequence
  getDeploySequenceURL = this.apiURL + codemigrationAPIConstant.getDeploySequence;
  getDeploySequence = (body: any): Observable<string> => {
    return this.apiService.get(this.getDeploySequenceURL + body)
  }

  //DeleteSchema
  DeleteSchemaURL = this.apiURL + codemigrationAPIConstant.DeleteSchema;
  DeleteSchema = (body: DeleteSchema): Observable<DeleteSchema> => {
    return this.apiService.get(this.DeleteSchemaURL + body.schemaname + '&conId=' + body.conId)
  }
  //getObjectTypes
  getObjectTypesURL = this.apiURL + codemigrationAPIConstant.getObjectTypes;
  getObjectTypes = (body: getObjectTypes): Observable<getObjectTypes> => {
    return this.apiService.get(this.getObjectTypesURL + body.projectId + '&objectgroupname=' + body.objectgroupname)
  }
  //prjSchemaListSelect
  prjSchemaListSelectUrl = this.apiURL + codemigrationAPIConstant.prjSchemaListSelect
  prjSchemaListSelect = (body: schemalist): Observable<schemalist> => {
    return this.apiService.post(this.prjSchemaListSelectUrl, body);
  };
  //projectDocumentsDetailSelect
  projectDocumentsDetailSelectUrl = this.apiURL + codemigrationAPIConstant.projectDocumentsDetailSelect
  projectDocumentsDetailSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.projectDocumentsDetailSelectUrl, body);
  };
  //UserAccessSelect
  UserAccessSelectUrl = this.apiURL + codemigrationAPIConstant.UserAccessSelect
  UserAccessSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.UserAccessSelectUrl, body);
  };
  //PrjExeLogSelect
  PrjExeLogSelectUrl = this.apiURL + codemigrationAPIConstant.PrjExeLogSelect
  PrjExeLogSelect = (body: any): Observable<any> => {
    return this.apiService.post(this.PrjExeLogSelectUrl, body);
  };
  // Operation details
  getoperationurl = this.apiURL + codemigrationAPIConstant.getoperation
  getoperation = (data: any): Observable<any> => {
    return this.apiService.get(this.getoperationurl + data.projectId + '&OperationType=' + data.OperationType);
  };
  // getObjTypes
  getObjTypesurl = this.apiURL + codemigrationAPIConstant.getobjtype
  getObjTypes = (data: any): Observable<any> => {
    return this.apiService.post(this.getObjTypesurl ,data);
  };
  // getObjname
  getObjnameurl = this.apiURL + codemigrationAPIConstant.getobjname
  getObjNames = (data: any): Observable<any> => {
    return this.apiService.post(this.getObjnameurl ,data);
  };
  // GetSchemasByRunId
  GetSchemasByRunIdURL = this.apiURL + codemigrationAPIConstant.GetSchemasByRunId;
  GetSchemasByRunId = (body:string): Observable<any> => {
   return this.apiService.get(this.GetSchemasByRunIdURL + body)
  }
  // getSchemas
  getSchemasURL = this.apiURL + codemigrationAPIConstant.getSchemas;
  getSchemas = (body:string): Observable<any> => {
   return this.apiService.get(this.getSchemasURL + body)
  }
  // SchemaListSelect
  SchemaListSelectURL = this.apiURL + codemigrationAPIConstant.SchemaListSelect;
  SchemaListSelect1 = (body: SchemaListSelect1): Observable<SchemaSelect> => {
    return this.apiService.get(this.SchemaListSelectURL + body.projectId + '&ConId=' + body.connectionId)
  }
  // GetRunno
  GetRunnoURL = this.apiURL + codemigrationAPIConstant.GetRunno;
  GetRunno = (body:string): Observable<GetRunno> => {
    return this.apiService.get(this.GetRunnoURL + body);
  };
  // GetReqData
  GetReqDataURL = this.apiURL + codemigrationAPIConstant.GetReqData;
GetReqData = (body:GetReqData): Observable<reqData> => {
  return this.apiService.get(this.GetReqDataURL + body.projectId + '&operationType=' + body.operationType);
};
// deleteTableData
deleteTableDataURL = this.apiURL + codemigrationAPIConstant.deleteTableData;
 deleteTableData = (body:deleteTableData): Observable<deleteTabledata> => {
   return this.apiService.get(this.deleteTableDataURL + body.projectId + '&requestId=' + body.requestId);
 };
//  GetIndividualLogs
 GetIndividualLogsURL = this.apiURL + codemigrationAPIConstant.GetIndividualLogs
 GetIndividualLogs = (body: GetIndividualLogs): Observable<GetIndividual> => {
   return this.apiService.post(this.GetIndividualLogsURL, body);
 };
//  ReportsFilterByTime
 ReportsFilterByTimeURL = this.apiURL + codemigrationAPIConstant.ReportsFilterByTime
ReportsFilterByTime = (body: ReportsFilterByTime): Observable<any> => {
      return this.apiService.post(this.ReportsFilterByTimeURL, body);
};
// downloadLargeFiles
downloadLargeFilesURL = this.apiURL + codemigrationAPIConstant.downloadLargeFiles;
downloadLargeFiles = (body: string): Observable<any> => {
  return this.apiService.get(this.downloadLargeFilesURL + encodeURIComponent(body), { responseType: 'blob' as 'json' })
}
// setRedisCache
setRedisCacheURL = this.apiURL + codemigrationAPIConstant.setRedisCache;
setRedisCache = (body: setRedisCache): Observable<fileStatus> => {
  return this.apiService.post(this.setRedisCacheURL, body);
};
setRedisCache2 = (body: any): Observable<fileStatus> => {
  return this.apiService.post(this.setRedisCacheURL, body);
};
// OracleSchemas
OracleSchemasURL = this.apiURL + codemigrationAPIConstant.OracleSchemas
OracleSchemas = (body: any): Observable<any> => {
  return this.apiService.get(this.OracleSchemasURL + body);
}
 //tgtSchemaSelect
 tgtSchemaSelectURL = this.apiURL + codemigrationAPIConstant.tgtSchemaSelect;
 tgtSchemaSelect = (body: string): Observable<string> => {
   return this.apiService.get(this.tgtSchemaSelectURL + body)
 }
 //projectConRunTblInsert
 projectConRunTblInsertURL = this.apiURL + codemigrationAPIConstant.projectConRunTblInsert
 projectConRunTblInsert = (body: projectConRunTblInsert): Observable<any> => {
    return this.apiService.post(this.projectConRunTblInsertURL, body);
  };
   //GetCommonFilesFromDirectory
   GetCommonFilesFromDirectoryURL = this.apiURL + codemigrationAPIConstant.GetCommonFilesFromDirectory;
   GetCommonFilesFromDirectory = (body: any): Observable<any> => {
     return this.apiService.get(this.GetCommonFilesFromDirectoryURL + body)
   }

   SchemaListSelectUR = this.apiURL + codemigrationAPIConstant.SchemaListSelect;
   SchemaListSelect = (body: SchemaListSelect1): Observable<SchemaSelect> => {
     return this.apiService.get(this.SchemaListSelectUR + body.projectId + '&ConId=' + body.connectionId)
   }

   createschemaurl = this.apiURL + codemigrationAPIConstant.createschema
   createschema = (data: any): Observable<any> => {
     return this.apiService.post(this.createschemaurl,data);
   };
   dropschemaobjUrl = this.apiURL + codemigrationAPIConstant.dropschemaobjs
   dropschemaobjs = (data: any): Observable<any> => {
     return this.apiService.post(this.dropschemaobjUrl,data);
   };

   uploadunzipurl = this.apiURL + codemigrationAPIConstant.uploadAndUnzip
   uploadAndUnzip = (data: any): Observable<any> => {
     return this.apiService.post(this.uploadunzipurl,data);
   };

   triggerDalurl = this.apiURL + codemigrationAPIConstant.triggerDalCommand
   triggerdal = (data: any): Observable<any> => {
     return this.apiService.post(this.triggerDalurl,data);
   };
   selectmigtypeURL = this.apiURL + codemigrationAPIConstant.selectmigtype;
   selectmigtype = (body: string): Observable<string> => {
     return this.apiService.get(this.selectmigtypeURL + body)
   }
   ConversionCommandURL=this.apiURL+ codemigrationAPIConstant.ConversionCommand;
   ConversionCommand = (body: string): Observable<string> => {
     return this.apiService.post(this.ConversionCommandURL , body)
   }

   agentExecutionURL = environment.serviceUrl2 + codemigrationAPIConstant.getAgent;
   getAgentExecution = (body:FormData): Observable<any> => {
     return this.apiService.post(this.agentExecutionURL , body)
   }

   //stage1-metadata-generation
  stage1MetadataGenerationURL = environment.serviceUrl2 + codemigrationAPIConstant.stage1MetadataGeneration;
   stage1MetadataGeneration = (body: any): Observable<any> => {
      return this.apiService.post(this.stage1MetadataGenerationURL, body);
   }

   agentTargetStmtURL = this.apiURL + codemigrationAPIConstant.getAgentTargetStmt
   getAgentTargetStmtList = (body: string): Observable<string> => {
     return this.apiService.get(this.agentTargetStmtURL + body)
   }

  agentDeployFullDataURL = this.apiURL + codemigrationAPIConstant.getAgentDeploymentFullDataSelect
   agentDeployFullData = (body: any): Observable<any> => {
     return this.apiService.get(this.agentDeployFullDataURL + body.tgt_id + "&tgt_stmts_id=" + body.tgt_stmts_id)
   }

  TargetFullCodeSelectURL = this.apiURL + codemigrationAPIConstant.getTargetFullCodeSelect
  TargetFullCodeSelect = (body: string): Observable<string> => {
     return this.apiService.get(this.TargetFullCodeSelectURL + body)
   }

  ReviewedDataUpdatedURL = this.apiURL + codemigrationAPIConstant.postReviewedDataUpdated
  ReviewedDataUpdated = (body:any): Observable<any> => {
     return this.apiService.post(this.ReviewedDataUpdatedURL , body)
   }

    downloadZIPURL = environment.serviceUrl1 + APIConstant.common.getZipFiles
  downloadZip = (body:any): Observable<any> => {
     return this.apiService.get(this.downloadZIPURL + body,{ responseType: 'blob' as 'json' })
   }

   // Download Fetch Dependency file 
    downloadFetchDependencyURL = environment.serviceUrl1 + codemigrationAPIConstant.downloadFetchDependency;
    downloadFetchDependency = (body: any): Observable<any> => {
      return this.apiService.post(this.downloadFetchDependencyURL , body);
    }
    GetPipelineListtURL = this.apiURL + codemigrationAPIConstant.getPipelineList
    getPipelineList = (body: any): Observable<any> => {
     return this.apiService.get(this.GetPipelineListtURL + body)
   }

   // Module Agent Page 
   getModuleSchemaURL = environment.serviceUrl2 + codemigrationAPIConstant.getModuleSchemas;
   getModuleObjectTypesURL = environment.serviceUrl2 + codemigrationAPIConstant.getModuleObjectTypes;
   getModuleObjectsURL = environment.serviceUrl2 + codemigrationAPIConstant.getModuleObjects;
   getModuleStatusURL = environment.serviceUrl2 + codemigrationAPIConstant.getModuleStatus;
   triggerAgentURL = environment.serviceUrl2 + codemigrationAPIConstant.triggerAgent;
   downloadZipURL = environment.serviceUrl2 + codemigrationAPIConstant.downloadZip;
   getTargetStatementsURL = environment.serviceUrl2 + codemigrationAPIConstant.getTargetStatements;
   getStatementsURL = environment.serviceUrl2 + codemigrationAPIConstant.getStatements;
   saveAgentLogsURL = environment.serviceUrl2 + codemigrationAPIConstant.saveAgentLogs;
   moveToRegistryURL = environment.serviceUrl2 + codemigrationAPIConstant.moveToRegistry;
   mergeToQBookURL = environment.serviceUrl2 + codemigrationAPIConstant.mergeToQBook;

    getModuleSchemas = (body: any): Observable<any> => {
      return this.apiService.get(this.getModuleSchemaURL + body.process_type + '&migration_name=' + body.migration_name);
    }
    getModuleObjectTypes = (body: any): Observable<any> => {
      return this.apiService.get(this.getModuleObjectTypesURL + body.process_type + '&migration_name=' + body.migration_name + '&schema_name=' + body.schema_name);
    }
    getModuleObjectNames = (body: any): Observable<any> => {
      return this.apiService.get(this.getModuleObjectsURL + body.process_type + '&migration_name=' + body.migration_name + '&schema_name=' + body.schema_name + '&object_type=' + body.object_type);
    }
    triggerAgent = (body: any): Observable<any> => {
      return this.apiService.post(this.triggerAgentURL, body);
    }
    getModuleStatus = (): Observable<any> => {
      return this.apiService.get(this.getModuleStatusURL);
    }
    downloadFile = (body:any): Observable<HttpResponse<Blob>> => {
      return this.apiService.get(this.downloadZipURL + body.migration_name + '&schema_name=' + body.schema_name + '&object_type=' + body.object_type + '&object_name=' + body.object_name + '&cloud_category=' + body.cloud_category, {observe: 'response', responseType: 'blob' });
    }
        
    getTargetStatements = (body: any): Observable<any> => {
      return this.apiService.get(this.getTargetStatementsURL + body.process_type + '&migration_name=' + body.migration_name + '&schema_name=' + body.schema_name + '&object_type=' + body.object_type + '&object_name=' + body.object_name);
    }
    getStatements = (body: any): Observable<any> => {
      return this.apiService.get(this.getStatementsURL + body.target_statement_number + '&process_type=' + body.process_type + '&migration_name=' + body.migration_name + '&schema_name=' + body.schema_name + '&object_type=' + body.object_type + '&object_name=' + body.object_name);
    }
    saveAgentLogs = (body: any): Observable<any> => {
      return this.apiService.post(this.saveAgentLogsURL, body);
    }
    moveToRegistry = (body: any): Observable<any> => {
      return this.apiService.post(this.moveToRegistryURL, body);
    }
    mergeToQBook = (body: any): Observable<any> => {
      return this.apiService.post(this.mergeToQBookURL, body);
    }

}








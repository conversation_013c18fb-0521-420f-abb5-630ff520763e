<div class="v-pageName">{{pageName}}</div>

    <!--- connection details --->
    <div class="body-main mt-4">
        
        <div class="qmig-card">
            <div class="qmig-card-body">
                <form class="form qmig-Form"  [formGroup]="UserForm">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="targtetConnection">Connection name</label>
                                <select class="form-select" formControlName="connection" #ser
                                    (change)="Getdbname(ser.value)" formControlName="connection">
                                    <option selected value="">Select a connection name</option>
                                    @for(ConsList1 of ConsList;track ConsList1; ){
                                    <option value="{{ ConsList1.Connection_ID }}"> {{ ConsList1.conname }} </option>
                                    }
                                </select>
                                @if ( f.connection.touched && f.connection.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.connection.errors.required) {connection is Required }
                                    </p>
                                    }
                            </div>
                        </div>
                        <!-- Database details -->
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="Schema">Database</label>
                                <select class="form-select" formControlName="db" #db (change)="GetUsers(db.value)">
                                    <option selected value="">Select a database</option>
                                    @for(dbs of dblist;track dbs; ){
                                    <option value="{{ dbs.dbname }}"> {{ dbs.dbname }} </option>
                                    }
                                </select>
                                @if ( f.db.touched && f.db.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.db.errors.required) {Database is Required }
                                    </p>
                                    }
                            </div>
                        </div>

                        <!-- user details -->
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                            <div class="form-group">
                                <label class="form-label d-required" for="Schema">User name</label>
                                <ng-select  [items]="Userlist" [multiple]="true"
                                    bindLabel="username" groupBy="type" [selectableGroup]="true" formControlName="username" 
                                     (change)="selectUser(selectedItems)"  [(ngModel)]="selectedItems"
                                    [closeOnSelect]="false" bindValue="username" >
                                    <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                        <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected" 
                                            [ngModelOptions]="{ standalone : true }" /> {{item.username}}
                                    </ng-template>
                     
                                </ng-select>
                                @if ( f.username.touched && f.username.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.username.errors.required) {username is Required }
                                    </p>
                                    }
                            </div>
                        </div>

                        <!-- search user button -->
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                            <div class="form-group">
                                <button class="btn btn-upload w-100 mt-4" type="button" [disabled]="UserForm.invalid"
                                    (click)="GetSearchUsersInfo()"> <span class="mdi mdi-database-search"></span>
                                  Fetch @if(spinner){<app-spinner/>}  </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>


        <div class="body-main mt-4" [hidden]="!tableHide">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-md-3 col-xl-3 offset-md-9">
                            <button class="btn btn-upload w-100" (click)="exportexcel()">
                                <i class="mdi mdi-download "></i> Download <i *ngIf="excelSpin"
                                    class="fa fa-spinner fa-spin"></i> </button>
                        </div>            
                    </div>
                </div>
                <div class="table-responsive">
                <table class="table table-hover qmig-table">
                    <thead>
                        <tr>
                            <th>Users</th>
                            <th>Attributes</th>
                            <th>privileges</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (documents of searchUserlist | searchFilter: searchText1 |paginate: {itemsPerPage:50,currentPage: p2,id:'First'} ; track documents; let i = $index) {
                        <tr>
                            <!-- <td>{{ pi * (page1 - 1) + i + 1 }}</td>  -->
                            <td>{{documents.user}}</td>
                            <td>{{documents.attrbutes}}</td>
                            <td>{{documents.previllages.toString()}}</td>
                        </tr>
                        } @empty {
                        <tr>
                            <td colspan="4">
                                <p class="text-center m-0 w-100">No Results</p>
                            </td>
                        </tr>
                        }
                    </tbody>
                </table>
            </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="p2 = $event"
                id="First"></pagination-controls>
        </div>
    </div>
    </div>

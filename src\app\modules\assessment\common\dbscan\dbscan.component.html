<div class="v-pageName">{{pageName}}</div>

<!--- Bread Crumb --->
<div class="qmig-card mt-3">
  <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
    <div class="accordion-item">
      <h2 class="accordion-header" id="flush-heading">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapse1"
          aria-expanded="true" aria-controls="flush-collapse">
         SQL Script
        </button>
      </h2>
      <div id="flush-collapse1" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
        data-bs-parent="#accordionFlushExample">
        <div class="qmig-card">
          <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="sqlInsertForm">
              <div class="row">
                <div class="col-md-4 ">
                    <div class="form-group">
                        <label class="form-label d-required">Function Name</label>
                        <input type="text" placeholder="Function Name" class="form-control" id="txtfunction"
                            name="txtfunction" [(ngModel)]="defaultfuncValue"
                            formControlName="scriptfunctionControl">
                        @if ( f.scriptfunctionControl.touched && f.scriptfunctionControl.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.scriptfunctionControl.errors?.['required']) { Function Name is required }
                        </p>
                        }
                    </div>
                </div>
                <!-- <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label d-required">SQL Connection</label>
                        <select formControlName="ddlfunctionnames" [(ngModel)]="dbConnName" class="form-control"
                            #Myselect (change)="ddlcheckedChnaged(Myselect.value)"
                            aria-label="Default select example">
                            <option selected value="">Select Connection Type</option>
                            @for(list of targetData;track list;){
                            <option value="{{ list.Connection_ID }}">{{ list.conname}}</option>
                            }
                        </select>
                        @if ( f.ddlfunctionnames.touched && f.ddlfunctionnames.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.ddlfunctionnames.errors?.['required']) { SQL Connection is required }
                        </p>
                        }
                    </div>
                </div> -->
                <!-- <div class="col-md-4">
                                <div class="form-group">
                                    <h6>Categoty</h6>
                                    <input type="text" placeholder="Category Name" class="form-control" id="txtcategory"
                                        name="txtcategory" [(ngModel)]="defaultCateValue"
                                        formControlName="scriptCategoryControl">
                                </div>
                            </div> -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label d-required">Category</label>
                        <input type="text" placeholder="Category Name" class="form-control" id="txtfunction1"
                        name="txtfunction1" [(ngModel)]="defaultfuncValue"
                        formControlName="scriptCategoryControl">
                        <!-- <select class="form-control" #Myselect1 formControlName="scriptCategoryControl">
                            <!-- (change)="selectCategory(Myselect1.value)" 
                            <option selected value="">All Categories</option>
                            <!-- @for(miglist of categoryData;track ConsList;){
                            <option value="{{ miglist.scriptcategory }}">{{ miglist.scriptcategory}}
                            </option>
                            } 
                        </select> -->
                        <!-- @if ( f.scriptCategoryControl.touched && f.scriptCategoryControl.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.scriptCategoryControl.errors?.['required']) { Category is required }
                        </p>
                        } -->
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="form-label d-required">Script</label>
                        <textarea class="form-control" id="txtscript" name="txtscript" [(ngModel)]="defaultValue"
                            formControlName="scriptControl" placeholder="Script Function" rows="15"
                            cols="30"></textarea>
                        @if ( f.scriptControl.touched && f.scriptControl.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.scriptControl.errors?.['required']) { Script is required }
                        </p>
                        }
                    </div>
                </div>
                <div class="col-md-3 offset-md-9">
                    <button class="btn btn-sign w-100" data-bs-toggle="offcanvas"
                     (click)="insertScript(sqlInsertForm.value)" [disabled]="sqlInsertForm.invalid">save
                        <!-- 
                        &nbsp;
                        
                       
                        @if(add_spin){<app-spinner />} -->
                      </button>
                </div>
            </div>
            </form>
          </div>
        </div>
      </div>
    </div>

  
  </div>
  <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
    <div class="accordion-item">
      <h2 class="accordion-header" id="flush-heading">
        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#flush-collapse"
          aria-expanded="true" aria-controls="flush-collapse">
          DB Scan
        </button>
      </h2>
      <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
        data-bs-parent="#accordionFlushExample">
        <div class="qmig-card">
          <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="sqlscriptForm">
              <div class="row">
                <div class="col-md-3 col-xl-3">
                  <div class="form-group">
                    <label class="form-label d-required">DB Connection</label>
                    <select class="form-select" formControlName="ddlfunctionnames" 
                      (change)="ddlcheckedChnaged(Myselect.value)" #Myselect>
                      <option selected disabled>Select SQL Connection</option>
                      <option selected disabled>Select SQL Connection</option>
                      @for(list of targetData;track list;){
                      <option value="{{ list.conname }}">{{ list.conname}}</option>
                      }
                    </select>
                    @if ( f.ddlfunctionnames.touched && f.ddlfunctionnames.invalid) {
                    <p class="text-start text-danger mt-1">
                      @if (f.ddlfunctionnames.errors.required) {DB Connection is Required }
                    </p>
                    }
                  </div>
                </div>

                <div class="col-md-3 col-xl-3">
                  <div class="form-group">
                    <label class="form-label d-required" for="name">DB Category</label>
                    <select class="form-select" formControlName="ddlCategory" #Myselect1
                      (change)="ddlCategorycheckedChnaged(Myselect1.value);selectScript(Myselect1.value)">
                      <option selected disabled>All Categories</option>
                      @for(miglist of categoryData;track ConsList;){
                      <option value="{{ miglist.scriptcategory }}">{{ miglist.scriptcategory}}</option>
                      }
                    </select>
                    @if ( f.ddlCategory.touched && f.ddlCategory.invalid) {
                    <p class="text-start text-danger mt-1">
                      @if (f.ddlCategory.errors.required) {DB Category is Required }
                    </p>
                    }
                  </div>
                </div>
                <div class="col-md-2 col-xl-2 mt-4 " [hidden]="!valChecked">
                  <div class="body-header-button">
                    <button class="btn btn-upload w-100" (click)="prepareSQLQuerys()"> Execute
                      @if(executed){<app-spinner />}
                    </button>
                  </div>
                </div>
                <div class="col-md-6 col-xl-6 mt-3">
                  <div class="custom_search cs-r">
                    <span class="mdi mdi-magnify"></span>
                    <input type="text" placeholder="Search Reports" class="form-control" [(ngModel)]="datachanges1"
                      (keyup)="checked(datachanges1)">
                  </div>
                </div>
                <div class="row" [hidden]="!catSel">
                  <div class="table-responsive">
                    <table class="table table-hover qmig-table" id="example" style="width:100%">
                      <thead>
                        <tr>
                          <th>
                            <div class="form-check">
                              <input type="checkbox" id="inlineCheckbox1" value="m1" (change)="checkUncheckAll($event)"
                                class="form-check-input">
                            </div>
                          </th>
                          <th>Category</th>
                          <th>Function Name</th>
                        </tr>
                      </thead>

                      <tbody>
                        @for(list of FunctionalTableData |searchFilter: datachanges1 |paginate:{
                        itemsPerPage: 10, currentPage: pageNumber, id:'third'};
                        track list;){
                        <tr>
                          <td>
                            <div class="form-check">
                              <input type="checkbox" [checked]="list.isSelected" name="list_name" value="{{list.id}}"
                                (click)="onChange(list,list.id, $event)" #checkValue class="form-check-input">
                            </div>
                          </td>
                          <td>{{list.scriptcategory}}</td>
                          <td>{{list.scriptfunction}}</td>
                        </tr>
                        } @empty {
                        <tr>
                          <td colspan="4">
                            <p class="text-center m-0 w-100">Empty list of Reports</p>
                          </td>
                        </tr>
                        }

                      </tbody>
                    </table>
                  </div>
                  <div class="custom_pagination">
                    <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <div class="accordion-item">
      <h3 class="accordion-header" id="flush-headingTwo">
        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
          data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
          Page-Activity Log
        </button>
      </h3>
      <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
        data-bs-parent="#accordionFlushExample">
        <div class="qmig-card-body">
          <!-- <h3 class="main_h ">Reports</h3> -->
          <form class="form qmig-Form" [formGroup]="sqlForm">
            <div class="row">
              <div class="col-md-3 col-xl-3">
                <div class="form-group">
                  <label class="form-label d-required">DB Category</label>
                  <select class="form-select" formControlName="ddl"
                    (change)="ddlcheckedChnages(Myselectss.value)" #Myselectss>
                    <option selected disabled>Select SQL Connection</option>
                    <option selected disabled>Select SQL Connection</option>
                    @for(list of categoryDatas;track list;){
                    <option value="{{ list.scriptcategory }}">{{ list.scriptcategory}}</option>
                    }
                  </select>
                   @if(fc.ddl.touched && fc.ddl.invalid) {
                  <p class="text-start text-danger mt-1">
                  @if(fc.ddl.errors.required) {DB Category is Required }
                  </p>
                }
              </div>
            </div>
              <div class="col-md-3 col-xl-3">
                <div class="form-group">
                  <label class="form-label d-required" for="name">iteration</label>
                  <select class="form-select" formControlName="itr" #Myselect1
                    (change)="ddlIterationcheckedChnaged(Myselect.value)">
                    <option selected disabled>iteration</option>
                    <option selected disabled>iteration</option>
                    @for(ConsList of iterationData;track ConsList;){
                    <option value="{{ ConsList.iteration }}">{{ ConsList.iteration}}</option>
                    }
                  </select>
                   @if(fc.itr.touched && fc.itr.invalid) {
                <p class="text-start text-danger mt-1">
                  @if(fc.itr.errors.required) {Iteration is Required }
                </p>
                }
                </div>
              </div>
              <div class="col-md-2 col-xl-2 mt-4 ">
                <div class="body-header-button">
                  <button class="btn btn-upload w-100" [disabled]="sqlForm.invalid" (click)="downloadFile()"> Get Report
                    @if(spin_dwld){<app-spinner />}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
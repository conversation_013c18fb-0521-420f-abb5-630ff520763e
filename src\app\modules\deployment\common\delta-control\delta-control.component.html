<!--- Bread Crumb --->
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
  <div class="qmig-card-body">
    <form class="manual_form" [formGroup]="saveForm">
      <div class="row">
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
          <div class="form-group">
            <label class="form-label d-required" for="">Run Number</label>
            <select formControlName="runnumber" class="form-select" #schema (change)="getSchemas(schema.value)">
              <option selected value="" disabled>Select Run Number</option>              
                @for(list of runNumbers;track list;){
                <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                }
            </select>                               
            @if ( f.runnumber.touched && f.runnumber.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (f.runnumber.errors.required) { Run Number is required }
                </p>                                    
            }
          </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
          <div class="form-group">
            <label for="" class="form-label d-required">Schema</label>
            <select class="form-select" formControlName="schema" #sch (change)="getObjectNames(sch.value)">
              <option selected value="" disabled>Select Schema</option>
              @for(schema of schemaList;track schema; ){
              <option value="{{schema.schemaname}}"> {{schema.schemaname}} </option>
              }
            </select>                          
            @if ( f.schema.touched && f.schema.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (f.schema.errors.required) { Schema is required }
                </p>                                    
            }
          </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
          <div class="form-group">
            <label for="" class="form-label d-required">Object</label>
            <select class="form-select" formControlName="object" #obj (change)="getObjects(obj.value)">
              <option selected value="" disabled>Select Object</option>    
              @for(obj of objectNames;track obj; ){
                <option value="{{obj.objectname}}"> {{obj.objectname}} </option>
                }          
            </select>                   
            @if ( f.object.touched && f.object.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (f.object.errors.required) { Object is required }
                </p>                                    
            }
          </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
          <div class="form-group">
            <label for="" class="form-label d-required">View Type</label>
            <select class="form-select" formControlName="viewType" (change)="getView(view.value)" #view>
              <option selected value="" disabled>Select View Type</option>
              @for(list of viewData;track list; ){
                <option  value={{list.value}}>{{list.view}}</option>
                } 
            </select>                 
            @if ( f.viewType.touched && f.viewType.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (f.viewType.errors.required) { View Type is required }
                </p>                                    
            }
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

@if(this.saveForm.value.viewType == 'Grid_View'){
<div class="qmig-card mt-4">
  <div class="qmig-card-body">
    @for(statement of statementList;track statement; ){
      <div class="row statement-row"  [ngClass]="{'active': statement.mapping_id === statmentValue && statmentShow}">
        <div class="col-md-6 ps-0">
          <div class="stmt_card"> 
            @if(statmentShow && statement.mapping_id === statmentValue){
              <span class="stmt_number"> Statement Number   :   <b>{{statement.source_statemetnumber}}</b> |  Status : <b>{{statement.source_status}}</b> </span>  
            }
            <div class="stmt_block" [ngClass]="{'bg_active': statmentShow === true && statmentValue ===  statement.source_statemetnumber,'bg_modified': statement.source_status?.toUpperCase() === 'MATCHING', 'bg_new':statement.source_status?.toUpperCase() === 'NEW', 'bg_deleted': statement.source_status?.toUpperCase() === 'DELETED'}">        
              <div class="form-control" placeholder="Statement" id="{{'stmt' + statement.mapping_id}}" disabled> {{statement.sourcestatement_current}}</div>
            </div>
          </div>
        </div>
        <div class="delta-controls">
          <button (click)="statmentClick(statement.mapping_id)" class="btn btn-eye"><span class="mdi mdi-eye"></span></button>
          <button (click)="moveRight(statement.source_statemetnumber,statement.target_statemetnumber)" class="btn btn-eye"><span class="mdi mdi-chevron-double-right"></span></button>
        </div>
        <div class="col-md-6 pe-0">
          <div class="stmt_card"> 
            <div class="qmig_btn_group">
              <div class="btn-group">
                @if(statement.target_statemetnumber !== firstStmt || isMove){
                  <button class="btn btn-sign btn-cardAdd" (click)="stmtMoveUp(statement.target_statemetnumber)">
                    <i class="mdi mdi-chevron-double-up"></i>
                    <span>Move Up</span>
                  </button>
                }
                @if((statement.target_statemetnumber !== lastStmt) || isMove){
                  <button class="btn btn-sign btn-cardAdd" (click)="stmtMoveDown(statement.target_statemetnumber)">
                      <i class="mdi mdi-chevron-double-down"></i>
                      <span>Move Down</span>
                  </button>
                }
                @if((statement.target_statemetnumber !== firstStmt) || isMove){
                  <button class="btn btn-sign btn-cardAdd" (click)="stmtMoveSingleUp(statement.target_statemetnumber)">
                      <i class="mdi mdi-chevron-up"></i>
                      <span>Move Up</span>
                  </button>
                }
                @if((statement.target_statemetnumber !== lastStmt) || isMove){
                  <button class="btn btn-sign btn-cardAdd" (click)="stmtMoveSingleDown(statement.target_statemetnumber)">
                      <i class="mdi mdi-chevron-down"></i>
                      <span>Move Down</span>
                  </button>
                }
                @if(statement.target_statemetnumber && !statement.isDisabled){
                  <button class="btn btn-sign btn-cardAdd" (click)="addElement(statement.target_statemetnumber)">
                      <i class="mdi mdi-plus"></i>
                      <span>Add</span>
                  </button>
                }
              </div>
            </div>
            @if(statmentShow && statement.mapping_id === statmentValue){
              <span class="stmt_number"> Statement Number   :   <b>{{statement.target_statemetnumber}}</b> |  Status : <b>{{statement.target_status}}</b> </span>  
            }    
            <div class="stmt_block stmt-right" [ngClass]="
                {
                    'bg_modified': statement.target_status?.toUpperCase() === 'MATCHING_WITH_SOURCE' || statement.target_status?.toUpperCase() === 'MATCHING_WITH_SOURCE_NEW',
                    'bg_new':statement.target_status?.toUpperCase() === 'CONVERTED_FROM_SOURCE', 
                    'bg_deleted': statement.target_status?.toUpperCase() === 'MATCHING_WITH_SOURCE_DELETED',
                    'bg_only':statement.target_status?.toUpperCase() === 'ONLY_IN_TARGET',
                    'bg_not':statement.target_status?.toUpperCase() === 'NOT_IN_TARGET'
                }">  
                @for(stt of statement.targetstatement_current;track stt; let i = $index){
                  <div (input)="textareaChanged(statement.target_statemetnumber, $event,i)" contenteditable="true" class="form-control" id="{{'tgstmt' + statement.target_statemetnumber}}" placeholder="Statement"> {{stt.tgtSmt}}</div>
                }  
            </div>
          </div>
        </div>
      </div>
    } @empty {
      <p class="text-center m-0 w-100">Empty list of Documents</p>
      } 
      @if(statementList.length>0){
        <div class="row">
          <div class="col-md-2 offset-md-6">
            <button class="btn btn-default w-100 mt-4" (click)="saveData()" type="button"> <span class="mdi mdi-database-plus"></span> Save  &nbsp;  @if(save_spin){<app-spinner />} </button>
          </div>
          <div class="col-md-2">          
            <button class="btn btn-upload w-100 mt-4" (click)="GenerateFile()"> <span class="mdi mdi-database"></span> Generate  &nbsp;  @if(generate_spin){<app-spinner />} </button>
          </div>
          <div class="col-md-2">          
            <button class="btn btn-sign w-100 mt-4"><span class="mdi mdi-database-refresh"></span> Reset </button>
          </div>
        </div>
      }
      
      <div class="cc_Note mt-4">
        <h4>Note: Color Coding and its' values </h4>
        <ul>
            <li><span class="bg_new"></span> - Source <b>new</b> and Target <b>converted_from_source</b> </li>
            <li><span class="bg_not"></span> - Target <b>not_in_target</b></li>
            <li><span class="bg_only"></span> - Source <b>only in Source </b>and Target <b>only_In_target</b>
            </li>
            <li><span class="bg_deleted"></span> - Source <b>deleted</b> and Target
                <b>matching_with_source_deleted</b></li>
            <li><span class="bg_modified"></span> - Source <b>Matching</b> and Target
                <b>Matching_with_source</b></li>
        </ul>
    </div>
  </div>
</div>

}
@if(this.saveForm.value.viewType == 'Format_View'){
<div class="qmig-card mt-4">
  <div class="qmig-card-body">
    <div class="deltaComparison">
      <!-- <div class="line-numbers">
        <div *ngFor="let lineNumber of totalLinesText1Array">{{ lineNumber }}</div>
      </div> -->
      <div class="comparison-block">
        <div class="cr-header"> 
          <div>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" class="icon_small__n3B1T" viewBox="0 0 16 16" preserveAspectRatio="none"><circle cx="8" cy="8" r="7" fill="currentColor" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" opacity="0.2"></circle><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8H4"></path></svg> &nbsp; {{removalCount}} Removals 
          </div>
          <span>Total Lines : {{totalLinesText1}}</span></div>
        <hr/>
        <div class="comparison-content" contenteditable="true" (input)="onInput($event, 'sourceObject')" [innerHTML]="highlightedText1"></div>
      </div>
      <!-- <div class="line-numbers">
        <div *ngFor="let lineNumber of totalLinesText2Array">{{ lineNumber }}</div>
      </div> -->
      <div class="comparison-block">
        <div class="cr-header ca-header">
          <div>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" class="icon_small__n3B1T" viewBox="0 0 16 16" preserveAspectRatio="none"><circle cx="8" cy="8" r="7" fill="currentColor" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" opacity="0.2"></circle><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 4v4m0 4V8m0 0h4M8 8H4"></path></svg>  {{additionCount}} Additions 

          </div>
          <span>Total Lines : {{totalLinesText2}}</span></div>
        <hr/>
        <div class="comparison-content" contenteditable="true" (input)="onInput($event, 'targetObject')" [innerHTML]="highlightedText2"></div>
      </div>
    </div>
    <!-- <div class="text-center mt-3">
      <button class="btn btn-upload" (click)="compareTexts()"> &nbsp; Compare &nbsp; </button>
    </div> -->    
    <div class="row">
      <div class="col-md-2 offset-md-8">
        <button class="btn btn-default w-100 mt-4" (click)="saveOpData()" id="save_id"> <span class="mdi mdi-database-plus"></span> Save &nbsp;  @if(format_spin){<app-spinner />}</button>
      </div>
      <div class="col-md-2">          
        <button class="btn btn-upload w-100 mt-4"> <span class="mdi mdi-database-arrow-up"></span> Git Commit </button>
      </div>
    </div>
  </div>
</div>
}

@if(this.saveForm.value.viewType !== ''){
<div class="qmig-card mt-4">
  <div class="qmig-card-body">
      <form class="manual_form" [formGroup]="deployForm">
        <div class="row">
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              <label class="form-label d-required" for="name">Target Connection</label>
              <select class="form-select" formControlName="targetConnection">
                  <option selected value="" disabled>Select a Target Connection</option>
                  @for ( list of tgtlist; track list) {
                  <option value="{{ list.Connection_ID }}"> {{ list.conname }}</option>
                  }
              </select>                                         
              @if ( d.targetConnection.touched && d.targetConnection.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (d.targetConnection.errors.required) { Target Connection is required }
                </p>                                    
              }
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              <label class="form-label d-required" for="">Deploy Time (Minutes) </label>
              <input type="number" min="1" class="form-control" formControlName="deployTime" />
              @if(d.deployTime.touched && d.deployTime.invalid){
                <p class="text-start text-danger mt-1">
                  @if(d.deployTime.errors.required){ Deploy Time is Required }
                </p>
              }
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              <label class="form-label d-required" for="">Observations </label>
              <input type="text" class="form-control" formControlName="observation" />
              @if(d.observation.touched && d.observation.invalid){
                <p class="text-start text-danger mt-1"> @if(d.observation.errors.required){ Observations are required } </p>
              }
            </div>
          </div>          
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group mt-1">
              <button class="btn btn-upload w-100 mt-4" (click)="DeployFile()"  [disabled]='deployForm.invalid'> <span class="mdi mdi-database-arrow-up"></span> Deploy   &nbsp;  @if(deploy_spin){<app-spinner />}</button>
            </div>
          </div>
        </div>
      </form>

      <hr class="dash-dotted" />
      
      <form class="manual_form">
        <div class="row">
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              <div class="row">
                <div class="col-12 col-sm-6 col-md-8 col-lg-8 col-xl-7">
                  <label class="form-label d-required" for="">Deployed time stamp  </label>    
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-5">
                  <button class="btn btn-sync" (click)="getreqTableData()">
                    @if(deployTime_spin){
                    <app-spinner />
                    }@else{
                        <span class="mdi mdi-refresh"></span>
                    }
                  </button>
                </div>
              </div>          
              <select class="form-select" #Time (change)="selectTimestamp(Time.value)">
                <option selected value="" disabled>Select Time Stamp</option>
                @for(stamp of deployStatusList; track stamp){
                  <option value="{{stamp.activitytime}}">{{stamp.activitytime}}</option>
                }
              </select> 
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              <label class="form-label d-required" for="">Status Message </label>
              <input type="text" class="form-control"  value="{{statusMessages}}" disabled />
            </div>
          </div>
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group">
              <label class="form-label d-required" for="">Observation Text </label>
              <input type="text" class="form-control" value="{{observations}}" disabled/>
            </div>
          </div>          
          <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
            <div class="form-group mt-4 pt-1">
              <p class="mb-0">Deployed Status :  <b>{{deploystatus}}</b></p>
              <p>Deploy Time :  <b>{{deployTime}}</b></p>
            </div>
          </div>
        </div>
      </form>
  </div>
</div>
}
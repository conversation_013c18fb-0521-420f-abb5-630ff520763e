import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';

@Component({
  selector: 'delta-tabs',
  standalone: true,
  imports: [ RouterLink, RouterLinkActive, CommonModule],
  templateUrl: './tabs.component.html',
  styles: ``
})

export class TabsComponent {
 

  @Input() icon:string = 'mdi-database'
  @Input() pageName:string = 'Deployment'

  selectedscreen:any;
 constructor(){
  this.selectedscreen = localStorage.getItem("menuscreenname");
 }
}

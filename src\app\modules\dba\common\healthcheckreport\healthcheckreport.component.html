<div class="body-main mt-4">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="permissionForm">
                <div class="row">
                    <div class="col-md-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Connection
                                Name</label>
                                <select class="form-select" formControlName="tgtCon" #con
                                (change)="Getdbname(con.value)">
                                <option selected value="">Select a connection Name</option>
                                @for(ConsList1 of ConsList ;track ConsList1; ){
                                <option value="{{ ConsList1.Connection_ID  }}"> {{ ConsList1.conname }}
                                </option>
                                }
                            </select>
                            @if ( getControl.tgtCon.touched && getControl.tgtCon.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (getControl.tgtCon.errors.required) {Connection name is required }
                                </p>
                                }
                        </div>
                    </div>
                    <!-- Database details -->
                    <div class="col-md-3 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="Schema">Database</label>
                            <select class="form-select" formControlName="db" #db
                                (change)="selectDB(db.value);getSchemas();">
                                <option selected value="">Select a Database</option>
                                @for(dbs of dblist;track dbs; ){
                                <option value="{{ dbs.dbname }}"> {{ dbs.dbname }} </option>
                                }
                            </select>
                            @if ( getControl.db.touched && getControl.db.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (getControl.db.errors.required) {Database name is required }
                            </p>
                            }
                        </div>
                    </div>
                      <!-- schema details -->
                      <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="Schema">Schema</label>
                            <!-- <select class="form-select" #db1 formControlName="database">
                                <option selected value="">Select schema</option> -->
                                <!-- @for(dbs of dblist;track dbs; ){
                                <option value="{{ dbs.dbname }}"> {{ dbs.dbname }} </option>
                                } -->
                            <!-- </select> -->
                            <ng-select [items]="schemaList" [multiple]="true" bindLabel="schemaname"
                                groupBy="type" [selectableGroup]="true" formControlName="schemaname"
                            
                               
                                bindValue="schemaname"> 
                                 <ng-template ng-optgroup-tmp let-item="item" let-item$="item$"
                                    let-index="index">
                                    <input id="item-{{index}}" type="checkbox"
                                        [ngModel]="item$.selected" />
                                    {{item.type | uppercase}}
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                    let-index="index">
                                    <input id="item-{{index}}" type="checkbox"
                                        [(ngModel)]="item$.selected"
                                        [ngModelOptions]="{ standalone : true }" />
                                    {{item.schemaname}}
                                </ng-template>

                            </ng-select>
                            @if ( f.schemaname.touched && f.schemaname.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (f.schemaname.errors.required) {Schemaname is required }
                            </p>
                            }

                        </div>
                    </div>
                    <!-- Category details  -->
                     <!-- <div class="col-md-3"> 
                    <div class="form-group">
                        <label class="form-label d-required" for="Schema">Category</label>
                        <select class="form-select" #Cate formControlName="category">
                            <option selected value="">Select a Category</option>
                            <option value="0">User</option>
                            <option value="1">Role</option>
                        </select> -->
                        <!-- @if ( f.category.touched && f.category.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (f.category.errors.required) {category is Required }
                        </p>
                        } -->
                     <!-- </div>
                </div>  -->
                    <!-- <div class="row"> -->
                    <div class="col-md-8"></div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group mt-1" >
                            <button class="btn btn-upload w-100 mt-4" type="button"
                            (click)="downloadFile()" [disabled]="permissionForm.invalid">
                              <span class="mdi mdi-database-search"></span>
                                Generate health report
                                  </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { deleteFile, documentsList} from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { Title } from '@angular/platform-browser';

declare let $: any;

@Component({
  selector: 'app-reports-upload',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, RouterOutlet, NgSelectModule],
  templateUrl: './reports-upload.component.html',
  styles: ``
})
export class ReportsUploadComponent {

  searchText: string = '';

  /*-- Document--*/
  uploadForm = this.formBuilder.group({
    file: ['', [Validators.required]],    
  })
  selectFile: any;
  fileName: string = '';

  //for upload
  projectDocuments: any = [];
  projectDocumentFilter:any;
  uploadfileSpin: boolean = false;
  
  //for create file
  fileStatus: any;
  fileResponse: any;
  fileAdd: boolean = false;

/*--- Project Documents Pagination   ---*/  
 pageNumber: number = 1;
 color = "green";

pageName:string = ''
projectId:string = ''
spinDownload: boolean = false;

constructor(private titleService: Title,private toast: HotToastService, private assessmentService:AssessmentService, public formBuilder: FormBuilder, private route: ActivatedRoute){
  this.pageName = this.route.snapshot.data['name'];
  this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
}

ngOnInit(): void {
  this.titleService.setTitle(`QMigrator - ${this.pageName}`);
  this.fetchAssessmentFiles()
}

get f():any {
  return this.uploadForm.controls;
}

openPopup() {
  this.uploadForm.reset();
  this.fileName = ''
}

onKey() {
  this.pageNumber = 1;
}

assessmentFiles: any
fetchAssessmentFiles() {
  const path = "AssessmentReports/"
  this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
    this.assessmentFiles = data
  })
}

/*--- File upload    ---*/ 

  /*--- File upload    ---*/ 

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
  }
  uploadFile() {
    this.uploadfileSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', "AssessmentReports");
    this.assessmentService.uploadLargeDocument(formData).subscribe(
      (response:any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.fetchAssessmentFiles();
        this.uploadForm.controls.file.reset()
        this.fileName = ''
        this.toast.success(response.message)
        $('#demo').offcanvas('hide');
      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.uploadForm.controls.file.reset()
        this.fileName = ''
        this.toast.error('Something went wrong')
        this.openPopup()
        $('#demo').offcanvas('hide');
      }
    )
  }

  /*--- Download file   ---*/
  downloadFile(fileInfo: any) {
    this.assessmentService.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spinDownload = false

    })
  }

  /*--- Delete Project Reports   ---*/

  deleteFiles(path: string) {
    this.assessmentService.deleteFile(path).subscribe({
      next: (data: deleteFile) => {
        this.fileStatus = data.message
        this.fetchAssessmentFiles();
        this.toast.success(this.fileStatus)
      },
      error: (error) => {
        this.toast.error(error.message)
      },
    });
  }

}

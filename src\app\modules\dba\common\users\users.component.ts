import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonService } from '../../../../services/common.service';
import { DbaService } from '../../../../services/dba.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonModule } from '@angular/common';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import * as XLSX from 'xlsx';
import { ActivatedRoute } from '@angular/router';



@Component({
  selector: 'app-roles',
  standalone: true,
  imports: [NgSelectModule, FormsModule, CommonModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './users.component.html',
  styles: ``

})
export class UsersComponent {
  UserForm: any
  projectId: any
  ConsList: any
  tableHide: boolean = false
  pageNumber: number = 1;
  p2: number = 1;
  page1: number = 1;
  pi: number = 10;
  searchText1: string = '';

  pageName:string =''

  constructor(private fb: FormBuilder, private dba: DbaService,private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }
  ngOnInit(): void {

    this.GetConsList()
    this.UserForm = this.fb.group({
      connection: ['', [Validators.required]], 
      db: ['', [Validators.required]],
      username: ['',Validators.required],

    })
  }
  selectedItems = [];
  checkAll = [];
  schemaList = [];
  spinner: boolean = false
  get getroleControl() {
    return this.UserForm.controls;
  }
  // Connection details
  GetConsList() {
    this.dba.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }
   // Database details
   dblist: any
   connId: any
   Getdbname(conId: string) {
     this.dblist = []
     this.connId = conId
     this.dba.getdbnames(conId).subscribe((data: any) => {
       this.dblist = data
     });
   }

   // users details
   getusernames:any
   Userlist: any
  selecteddbname: any
  GetUsers(dbname: string) {
    this.selecteddbname = dbname
    let obj = {
      ConId: this.connId,
      dbname: dbname
    }
    this.dba.getusernames(obj).subscribe((data: any) => {
      this.Userlist = data
      this.Userlist.filter((item: any) => {
        item.type = "ALL"
      })
      //console.log(this.Userlist)
    });

  }
  selectedUser: string = ""
  selectUser(value: any) {
    var temp: any = []
    if (value.toString() == "ALL") {
      this.selectedUser = ""
      this.Userlist.filter((item: any) => {
        temp.push("'" + item.username + "'")
      })
      //console.log(temp)
      this.selectedUser = temp.toString()
    }
    else {
      value.filter((item: any) => {
        temp.push("'" + item + "'")
      })
      //console.log(temp)

      this.selectedUser = temp.toString()
    }

  }
//For validations 
  get f() {
    return this.UserForm.controls;
   
  }
  fileName = 'user.xlsx';
  testing: any = []
  excelSpin: any = false;
  exportexcel(): void {
    this.testing = []
    this.excelSpin = true
    var test = this.searchUserlist
    for (var el of test) {
      var newEle: any = {};
      newEle.user = el.user;
      newEle.attrbutes = el.attrbutes;
      newEle.previllages = el.previllages.toString();
      this.testing.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);

    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName);

  }

  // Search user button
  searchUserlist: any
  GetSearchUsersInfo() {
    this.UserForm.get('connection').disable();
    this.UserForm.get('db').disable();
    this.UserForm.get('username').disable();
    let obj = {
      ConId: this.connId,
      dbname: this.selecteddbname,
      user: this.selectedUser
    }
    this.spinner = true
    this.dba.GetUsersInfo(obj).subscribe((data: any) => {
      this.searchUserlist = data
     this.UserForm.get('connection').enable();
    this.UserForm.get('db').enable();
    this.UserForm.get('username').enable();
      if (this.searchUserlist.length > 0) {
        var i=1;
        this.searchUserlist.filter((item:any)=>{
          item.sno=i
          i++
        })
        //console.log(this.searchUserlist)
        this.tableHide = true
        this.spinner = false
      }
      
    });

  }
  
}

<!--- Bread Crumb --->
<div class="v-pageName">{{pageName}}</div>
<div class="body-header">
    <div class="row">
        <div class="col-md-6 col-xl-6 offset-md-6 mt-r-3">
            <div class="body-header-button">
                <button (click)="openPopup()" class="btn btn-upload" data-bs-toggle="offcanvas"
                    data-bs-target="#scriptModal" type="button"> <span class="mdi mdi-bash"></span>Script
                    Upload</button>
                <button (click)="openPopup()" class="btn btn-sign ms-1 " data-bs-toggle="offcanvas"
                    data-bs-target="#demo" type="button"> <span class="mdi mdi-format-page-break"></span>Template
                    Upload </button>
            </div>
        </div>
    </div>
</div>
<div class="qmig-card mt-3">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Execute Scripts
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <!-- <h3 class="main_h px-3 pt-3">Extract Table</h3>
                        <div class="qmig-card-body">
                            <form class="form qmig-Form" [formGroup]="extractForm">
                                <div class="row">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Source Connection Name</label>
                                            <select formControlName="sourceConnection" placeholder="Name" class="form-select"
                                                #Myselect (change)="
                                            getSchemasList(Myselect.value); ">
                                                <option disabled>Select a Source Connection</option>
                                                @for ( list of ConsList; track list) {
                                                <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                                }
                                            </select>
                                            @if ( f.sourceConnection.touched && f.sourceConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.sourceConnection.errors.required) {Source Connection is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Schema</label>
                                            <select formControlName="schema" class="form-select">
                                                <option disabled>Select a Schema</option>
                                                @for ( list of schemaList; track list) {
                                                <option value="{{ list.schemaname }}"> {{list.schemaname }}</option>
                                                }
                                            </select>
                                            @if ( f.schema.touched && f.schema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.schema.errors.required) {schema is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                        <div class="body-header-button">
                                            <button class="btn btn-upload w-100 " [disabled]="extractForm.invalid"
                                                (click)="extractValue(extractForm.value)">
                                                <span class="mdi mdi-cog-play-outline btn-icon-prepend"></span>Extract@if(extracttableSpin){<app-spinner />}</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <hr class="m-0"/> -->
                    <div class="qmig-card-body">
                        <!---Extract scripts Form --->
                        <form class="form qmig-Form" [formGroup]="executescriptsForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">GoldenGate Pod<span
                                                class="qmig-tooltip"><i>GoldenGate pods Deployed on
                                                    cluster</i></span></label>
                                        <select formControlName="podName" placeholder="Name" class="form-select"
                                            (change)="callShellScript(podVal.value)" #podVal>
                                            <option disabled>Select a GoldenGate Pod</option>
                                            @for ( list of podList; track list) {
                                            <option value="{{ list }}">{{list }}</option>
                                            }
                                        </select>
                                        @if ( fe.podName.touched && fe.podName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (fe.podName.errors.required) {POD Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Shell Script<span
                                                class="qmig-tooltip"><i>Run script on GoldenGate</i></span></label>
                                        <select formControlName="shellScript" class="form-select">
                                            <option disabled>Select a Shell Script</option>
                                            @for ( list of shellScriptList; track list) {
                                            <option value="{{ list.filePath }}"> {{list.fileName }}</option>
                                            }
                                        </select>
                                        @if ( fe.shellScript.touched && fe.shellScript.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (fe.shellScript.errors.required) { Shell Script is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Argument<span
                                                class="qmig-tooltip"><i>Additional arguments to script if
                                                    any</i></span></label>
                                        <input type="text" formControlName="argument" class="form-control" />
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100 " [disabled]="executescriptsForm.invalid"
                                            (click)="runScriptValue(executescriptsForm.value)">
                                            <span class="mdi mdi-cog-play-outline btn-icon-prepend"></span>Run
                                            Script@if(runScriptspin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <hr class="m-0" />
                    <h3 class="main_h px-3 pt-3">Create File from Template</h3>
                    <div class="qmig-card-body">
                        <!---Create File from Template Form --->
                        <form class="form qmig-Form" [formGroup]="fileandtemplateForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">GoldenGate Pod<span
                                                class="qmig-tooltip"><i>GoldenGate pods Deployed on
                                                    cluster</i></span></label>
                                        <select formControlName="podName" placeholder="Name" class="form-select"
                                            (change)="callShellScript(podVal.value)" #podVal>
                                            <option disabled>Select a GoldenGate Pod</option>
                                            @for ( list of podList; track list) {
                                            <option value="{{ list }}">{{list }}</option>
                                            }
                                        </select>
                                        @if ( ff.podName.touched && ff.podName.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.podName.errors.required) { POD Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Template Type</label>
                                        <select formControlName="template"
                                            (change)="getTemplateValue(templateType.value)" #templateType
                                            class="form-select">
                                            <option disabled>Select a Template</option>
                                            @for ( list of templateList; track list) {
                                            <option value="{{ list}}"> {{list }}</option>
                                            }
                                        </select>
                                        @if ( ff.template.touched && ff.template.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.template.errors.required) { Template is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Source Connection Name</label>
                                        <select formControlName="sourceConnection" placeholder="Name"
                                            class="form-select" #srcc (change)="
                                            getSchemasList(srcc.value); ">
                                            <option disabled>Select a Source Connection</option>
                                            @for ( list of ConsList; track list) {
                                            <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                            }
                                        </select>
                                        @if ( ff.sourceConnection.touched && ff.sourceConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.sourceConnection.errors.required) { Source Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Target Connection Name</label>
                                        <select formControlName="targetConnection" placeholder="Name"
                                            class="form-select">
                                            <option disabled>Select a Target Connection</option>
                                            @for ( list of tgtList; track list) {
                                            <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                            }
                                        </select>
                                        @if ( ff.targetConnection.touched && ff.targetConnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.targetConnection.errors.required) { Target Connection is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Schema Name</label>
                                        <select formControlName="schema" class="form-select" #getSchema
                                            (change)="getSchemaValue(getSchema.value)">
                                            <option disabled>Select a Schema</option>
                                            @for ( list of schemaList; track list) {
                                            <option value="{{ list.schemaname }}"> {{list.schemaname +' - '+list.schema_id}}</option>
                                            }
                                        </select>
                                        @if ( ff.schema.touched && ff.schema.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.schema.errors.required) { Schema is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                @if (templateType.value === 'Replicat') {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Table Name</label>
                                        <select formControlName="table" class="form-select">
                                            <option disabled>Select Method</option>
                                            @for ( list of tableList; track list) {
                                            <option value="{{ list.table_name }}"> {{list.table_name }}</option>
                                            }
                                        </select>
                                        @if ( ff.table.touched && ff.table.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.table.errors.required) { Table Name is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                }
                                @if (templateType.value === 'Extract') {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">GG Pod Host/IP<span
                                                class="qmig-tooltip"><i>GoldenGate (RMTHOST) Hostname/IP for sending
                                                    trail files</i></span></label>
                                        <input type="text" formControlName="podService" class="form-control" />
                                        @if ( ff.podService.touched && ff.podService.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.podService.errors.required) { GG Pod Host/IP is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                }
                                @if (templateType.value === 'Extract' || templateType.value === 'Globals' ||
                                templateType.value
                                === 'Mgr') {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Trail Path<span
                                                class="qmig-tooltip"><i>Path location of trail files on
                                                    RMTHOST</i></span></label>
                                        <input type="text" formControlName="trailPath" class="form-control" />
                                        @if ( ff.trailPath.touched && ff.trailPath.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.trailPath.errors.required) { Trail Path is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                }
                                @if (templateType.value === 'Extract') {
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Oracle Home<span
                                                class="qmig-tooltip"><i>Oracle Home Location on
                                                    Source</i></span></label>
                                        <input type="text" formControlName="oracleHome" class="form-control" />
                                        @if ( ff.oracleHome.touched && ff.oracleHome.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.oracleHome.errors.required) { Oracle Home is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                }
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">GoldenGate Home<span
                                                class="qmig-tooltip"><i>Home path location of
                                                    GoldenGate</i></span></label>
                                        <input type="text" formControlName="ggHome" class="form-control" />
                                        @if ( ff.ggHome.touched && ff.ggHome.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (ff.ggHome.errors.required) { Golden Gate Home is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"
                                    *ngIf="templateType.value !== 'Globals' && templateType.value !== 'Mgr' && templateType.value !== 'Odbc' && templateType.value !== 'Replicat'">
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"
                                    *ngIf="templateType.value === 'Extract'|| templateType.value === 'Odbc'">
                                </div>
                                <div class="" *ngIf="templateType.value === 'Globals'|| templateType.value === 'Mgr'">
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100 " [disabled]="fileandtemplateForm.invalid"
                                            (click)="createFile(fileandtemplateForm.value)">
                                            <span class="mdi mdi-file-cog-outline btn-icon-prepend"></span>Create
                                            File@if(createFileSpin){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Golden Gate Files
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-md-3 col-xl-3">
                                <h3 class="main_h mt-3">Golden Gate Files </h3>
                            </div>
                            <div class="col-md-6 col-xl-6">
                                <div class="custom_search mb-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Files" class="form-control"
                                        [(ngModel)]="searchText" (keyup)="onKey()">
                                </div>
                            </div>
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <select class="form-select" #templ (change)="getTempFiles(templ.value)">
                                        <option selected value="">Select a GoldenGate Pod</option>
                                        @for ( list of podList; track list) {
                                        <option value="{{ list }}">{{list }}</option>
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for ( validrepo of templateFiles | searchFilter: searchText | paginate: { itemsPerPage:
                                10,
                                currentPage: goldengatepageNumber } ; track validrepo; let i = $index) {
                                <tr>
                                    <td>{{i + 1}}</td>
                                    <td>{{validrepo.fileName }}</td>
                                    <td>Project Docs</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                            <span
                                                class="mdi mdi-cloud-download-outline"></span>@if(deleteSpin){<app-spinner />}
                                        </button>
                                        <button class="btn btn-delete" (click)="DeleteFiles(validrepo.filePath)">
                                            <span class="mdi mdi-delete"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="goldengatepageNumber = $event"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Template Files
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-md-6 col-xl-6">
                                <h3 class="main_h mt-3">Template Files </h3>
                            </div>
                            <div class="col-md-6 col-xl-6">
                                <div class="custom_search cs-r">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" (keyup)="onKey()" placeholder="Search Template Files"
                                        class="form-control" [(ngModel)]="searchText1">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for ( validrepo of configFiles | searchFilter: searchText1 | paginate: { itemsPerPage:
                                10,
                                currentPage: templatefilepageNumber, id: 'first' } ; track validrepo; let i = $index) {
                                <tr>
                                    <td>{{i + 1}}</td>
                                    <td>{{validrepo.fileName }}</td>
                                    <td>Project Docs</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                            <span
                                                class="mdi mdi-cloud-download-outline"></span>@if(deleteSpin){<app-spinner />}
                                        </button>
                                        <button class="btn btn-delete" (click)="DeleteFiles(validrepo.filePath)">
                                            <span class="mdi mdi-delete"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="templatefilepageNumber = $event"
                            id="first"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    Goldengate Template
                </button>
            </h2>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="templateForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">File Name</label>
                                    <select class="form-select" formControlName="filename">
                                        <option selected value="">Select File name </option>
                                        @for(ggt of configFiles; track ggt){
                                            <option value="{{ggt.fileName}}">{{ggt.fileName}}</option>
                                        }
                                    </select>
                                    @if(fg.filename.touched && fg.filename.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(fg.filename.errors.required) {File Name is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- Database -->
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Size</label>
                                    <input type="text" class="form-control" formControlName="size" placeholder="Size" />
                                    @if(fg.size.touched && fg.size.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(fg.size.errors.required) {Size is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Database Name</label>
                                    <input type="text" class="form-control" formControlName="databasename" placeholder="Database Name" />
                                    @if(fg.databasename.touched && fg.databasename.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(fg.databasename.errors.required) {Database Name is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Storage Class</label>
                                    <input type="text" class="form-control" formControlName="storageclass" placeholder="Storage Class" />
                                    @if(fg.storageclass.touched && fg.storageclass.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(fg.storageclass.errors.required) {Storage Class is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Image</label>
                                    <input type="text" class="form-control" formControlName="image" placeholder="Image" />
                                    @if(fg.image.touched && fg.image.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(fg.image.errors.required) {Image is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                                <div class="body-header-button">
                                    <button (click)="generateTemplate(templateForm.value)"
                                        [disabled]="this.templateForm.invalid" class="btn btn-upload w-100 mt-4">                                        
                                        Generate @if(exe_spin){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for ( gt of gtReports | searchFilter: searchText1 | paginate: { itemsPerPage:
                                10,
                                currentPage: templatefilepageNumber, id: 'first' } ; track gt; let i = $index) {
                                <tr>
                                    <td>{{i + 1}}</td>
                                    <td>{{gt.fileName }}</td>
                                    <td>Project Docs</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(gt)">
                                            <span
                                                class="mdi mdi-cloud-download-outline"></span>@if(deleteSpin){<app-spinner />}
                                        </button>
                                        <button class="btn btn-delete" (click)="DeleteFiles(gt.filePath)">
                                            <span class="mdi mdi-delete"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="templatefilepageNumber = $event"
                            id="first"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!---Create Template button --->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Create Template</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <div class="offcanvas-body">
            <form class="form qmig-Form" [formGroup]="createForm">
                <div class="form-group">
                    <label class="form-label d-required" for="dbName">Template</label>
                    <select formControlName="template" placeholder="Name" class="form-select">
                        <option selected value="">Select a Source Connection</option>
                        @for ( list of createTemplateList; track list) {
                        <option value="{{ list }}">{{list }}</option>
                        }
                    </select>
                    <div class="alert">
                        @if ( fc.template.touched && fc.template.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (fc.template.errors?.['required']) { Template is required }
                        </p>
                        }
                    </div>
                </div>
                <div class="form-group">
                    <div class="custom-file-upload">
                        <input class="form-control" type="file" id="formFile" formControlName="file"
                            (change)="uploadDocument($event)">
                        <div class="file-upload-mask">
                            @if (fileName == '') {
                            <img src="assets/images/fileUpload.png" alt="img" />
                            <p>Drag and drop deployment file here or click add deployment file </p>
                            <button class="btn btn-upload"> Add File </button>
                            }
                            <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                <p> {{ fileName }} </p>
                            </div>
                        </div>
                        <div class="alert">
                            @if ( fc.file.touched && fc.file.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (fc.file.errors?.['required']) { File is required }
                            </p>
                            }
                        </div>
                    </div>

                </div>

                <div class="form-group">

                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 " [disabled]="createForm.invalid"
                            (click)="createTemplate(createForm.value)"> <span></span> Upload Template
                            @if(createSpin){<app-spinner />}</button>

                    </div>
                </div>
            </form>
        </div>
    </div>
    <!---Script Upload button --->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="scriptModal">
        <div class="offcanvas-header">
            <h4 class="main_h">Script File Upload</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <div class="offcanvas-body">
            <form class="form qmig-Form" [formGroup]="scriptForm">
                <div class="form-group">
                    <label class="form-label d-required" for="name">GoldenGate Pod
                        <span class="qmig-tooltip"><i>GoldenGate pods Deployed on cluster</i></span>
                    </label>
                    <select formControlName="podName" placeholder="Name" class="form-select">
                        <option selected value="">Select a GoldenGate Pods</option>
                        @for ( list of podList; track list) {
                        <option value="{{ list }}">{{list }}</option>
                        }
                    </select>
                    <div class="alert">
                        @if ( fs.podName.touched && fs.podName.invalid) {
                        <p class="text-start text-danger mt-1">
                            @if (fs.podName.errors?.['required']) { POD Name is required }
                        </p>
                        }
                    </div>
                </div>
                <div class="form-group">
                    <div class="custom-file-upload">
                        <input class="form-control" type="file" id="formFile" formControlName="file"
                            (change)="sendScriptFile($event)">
                        <div class="file-upload-mask">
                            @if (filename == '') {
                            <img src="assets/images/fileUpload.png" alt="img" />
                            <p>Drag and drop deployment file here or click add deployment file </p>
                            <button class="btn btn-upload"> Add File </button>
                            }
                            <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                <p> {{ filename }} </p>
                            </div>
                        </div>
                        <div class="alert">
                            @if ( fs.file.touched && fs.file.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (fs.file.errors?.['required']) { File is required }
                            </p>
                            }
                        </div>
                    </div>

                </div>

                <div class="form-group">
                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 " [disabled]="scriptForm.invalid" (click)="uploadScript()">
                            <span></span>Upload Script @if(scriptSpin){<app-spinner />}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
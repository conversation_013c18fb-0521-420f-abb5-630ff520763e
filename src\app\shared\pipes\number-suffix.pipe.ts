import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'numberSuffix',
  standalone: true
})
export class NumberSuffixPipe implements PipeTransform {
  transform(value: number, precision: number = 1): string {
    if (value === null || value === undefined || isNaN(value)) return '';
    if (value < 1000) return value.toFixed(precision);

    const suffixes = ['K', 'M', 'B', 'T'];
    const exp = Math.floor(Math.log(value) / Math.log(1000));
    const suffix = suffixes[exp - 1];
    const shortValue = (value / Math.pow(1000, exp)).toFixed(precision);

    return `${shortValue} ${suffix}`;
  }
}

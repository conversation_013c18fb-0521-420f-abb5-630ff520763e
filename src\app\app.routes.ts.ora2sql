import { Routes } from '@angular/router';
import { ErrorComponent } from './shared/components/error/error.component';
import { LoginComponent } from './shared/components/login/login.component';
import { HelpCenterComponent } from './shared/components/help-center/help-center.component';
import { LayoutComponent } from './shared/components/layout/layout.component';

export const routes: Routes = [
    { path: '', redirectTo: 'login', pathMatch: 'full' },
    { path:'login', component:LoginComponent},
    { path:'help', component:HelpCenterComponent},
    { path:'assessment', loadChildren:() => import('./modules/assessment/assessment.module').then(a => a.AssessmentModule)},
    { path:'codeMigration', loadChildren:() => import('./modules/codeMigration/codeMigration.module').then(b => b.CodeMigrationModule)},
    { path:'dataMigration', loadChildren:() => import('./modules/dataMigration/dataMigration.module').then(c => c.DataMigrationModule)},
    { path:'dashboard', loadChildren:() => import('./modules/dashboard/dashboard.module').then(g => g.DashboardModule)},
    { path:'deployment', loadChildren:() => import('./modules/deployment/deployment.module').then(h => h.DeploymentModule)},
    { path:'sql',loadChildren:() => import('./modules/sql/sql.module').then(j=> j.SqlAssessmentModule)},
    { path: '**', component: ErrorComponent },
    { path: 'error', component: ErrorComponent },
];

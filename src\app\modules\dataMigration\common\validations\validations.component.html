<div class="v-pageName">{{pageName}}</div>

<!--- Reports & Logs  --->
<div class="body-main">
    <div class="qmig-card">
        <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-heading">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                        Create Configuration
                    </button>
                </h2>
                <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <div class="row">
                                <div class="col-12 col-md-6 col-xl-6 mt-4"></div>
                                <div class="col-12 col-md-3 col-xl-3 mt-4"></div>
                                <div class="col-12 col-md-3 col-xl-3 mt-4">
                                    <button class="btn btn-upload w-100 " (click)="onEditConfigClick()"
                                        data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                                            class="mdi mdi-checkbox-marked-circle-outline" aria-hidden="true"></span>Edit
                                        Configuration</button>
                                </div>
                            </div>
                            <form class="form qmig-Form" [formGroup]="validationForm">
                                <div class="row">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Operation</label>
                                            <select formControlName="operation" class="form-select">
                                                <option disabled value="">Select a Operation</option>
                                                @for ( op of operation; track op) {
                                                <option value="{{ op.value }}"> {{ op.option }}</option>
                                                }
                                            </select>
                                            @if ( f.operation.touched && f.operation.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.operation.errors.required) {Operation is Required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Source Connection</label>
                                            <select formControlName="connectionType" placeholder="Name"
                                                class="form-select" #Myselect (change)="
                                                selectedfiletype(Myselect.value);
                                                getSchemasList(Myselect.value,'S');onSourceChange(Myselect.value)
                                              ">
                                                <option disabled>Select a Source Connection</option>
                                                @for ( list of ConsList; track list) {
                                                <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                                }
                                            </select>
                                            @if ( f.connectionType.touched && f.connectionType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.connectionType.errors.required) {Source Connection is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    @if( migtypeid !="35")
                                    {
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Source {{schemalable}}
                                                Name</label>
                                            <!-- <select class="form-select" formControlName="schema" #sch
                                                (change)="selectSchema(sch.value,'S')">
                                                <option disabled selected value="">Select {{schemalable}} Name</option>
                                                @for ( slist of schemaList; track slist) {
                                                <option value="{{ slist.schema_name }}"> {{ slist.schema_name }}</option>
                                                }
                                            </select> -->
                                            <!-- @if ( f.schema.touched && f.schema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.schema.errors.required) {Schema is Required
                                                }
                                            </p>
                                            } -->
                                            <ng-select [placeholder]="'Select Schema Name'" [items]="schemaList"
                                                [selectableGroup]="true" (change)="selectSchema(selectedItems,'S')"
                                                [multiple]="true" formControlName="schema" bindLabel="schema_name"
                                                groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false"
                                                bindValue="schema_name" [(ngModel)]="selectedItems">
                                                <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                    let-index="index">
                                                    <input id="item-{{index}}" type="checkbox"
                                                        [(ngModel)]="item$.selected"
                                                        [ngModelOptions]="{ standalone : true }" /> {{item.schema_name}}
                                                </ng-template>
                                            </ng-select>

                                        </div>
                                    </div>
                                    }
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Target Connection</label>
                                            <select formControlName="targetConnection" class="form-select"
                                                #targetconnect
                                                (change)="selectTgtId(targetconnect.value);getSchemasList(targetconnect.value,'T');onTargetChange(targetconnect.value)">
                                                <option disabled value="">Select a Target Connection</option>
                                                @for ( list of tgtlist; track list) {
                                                <option value="{{ list.Connection_ID }}"> {{ list.conname }}</option>
                                                }
                                            </select>
                                            @if ( f.targetConnection.touched && f.targetConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.targetConnection.errors.required) {Target Connection is Required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    @if(!tableHide){
                                    @if(migtypeid!="40"){
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label " for="name">Target {{schemalable}}
                                                Name</label>
                                            <select class="form-select" #schtgt formControlName="tgtSchema"
                                                (change)="selectSchema(schtgt.value,'T')">
                                                <option disabled selected>Select {{schemalable}} Name</option>
                                                @for ( tgtlist of tgtschemaList; track tgtlist) {
                                                <option value="{{ tgtlist.schema_name }}"> {{ tgtlist.schema_name }}
                                                </option>
                                                }
                                            </select>
                                            <!-- <ng-select [placeholder]="'Select Schema Name'" [items]="tgtschemaList"
                                                        (change)="selectSchema(selectedItems)" [multiple]="true" bindLabel="schemaname"
                                                        groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false" bindValue="schemaname"
                                                        [(ngModel)]="selectedItems" [ngModelOptions]="{standalone: true}">
                                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                                [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                                        </ng-template>
                                                    </ng-select> -->

                                        </div>
                                    </div>
                                    }
                                    }

                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">File Name</label>
                                            <input type="text" formControlName="fileName" class="form-control"
                                                placeholder="fileName" id="fname" maxlength="15" />
                                            @if ( f.fileName.touched && f.fileName.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.fileName.errors.required) {File Name is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-xl-9 ">
                                        <div class="body-header-button">
                                            <button class="btn btn-upload w-100 "
                                                (click)="DataMigrationNewCommand(validationForm.value)"
                                                [disabled]="validationForm.invalid">
                                                <span class="mdi mdi-file-cog-outline btn-icon-prepend"></span>Create
                                                Config@if(createvalidationSpin){<app-spinner />}</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
           
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                        Configuration Files
                    </button>
                </h2>
                <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Source
                                        Connection Name</label>
                                    <select class="form-select" #srcFile2
                                        (change)="fetchSourceConfigFiles(srcFile2.value)">
                                        <option>Select a Source Connection</option>
                                        @for ( srccf of ConsList; track srccf) {
                                        <option value="{{ srccf.Connection_ID }}">{{ srccf.conname }}</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                <div class="form-group">
                                    <label class="form-label" for="targtetConnection">Target Connection Name</label>
                                    <select class="form-select" #tgtFile2
                                        (change)="fetchTargetConfigFiles(tgtFile2.value)">
                                        <option>Select Target Connection</option>
                                        @for(tgt of tgtlist;track tgt; ){
                                        <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-4 col-md-6 col-lg-6 d-flex">
                                <div class="custom_search cs-r my-3 me-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Reports" class="form-control"
                                        [(ngModel)]="searchText" (keyup)="onKey()">
                                </div>
                                <button class="btn btn-sync" (click)="getUpdatedRunNumber()">
                                    @if(getRunSpin){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.NO</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (validrepo of configFiles | searchFilter: searchText | paginate: {
                                itemsPerPage: pageNumberitems, currentPage: reportspageNumber,id:'first' } ; track
                                validrepo; let i
                                =
                                $index) {
                                <tr>
                                    <td>{{reportspageNumber*pageNumberitems+i+1-pageNumberitems}}</td>
                                    <td>{{validrepo.fileName }}</td>
                                    <td>{{validrepo.created_dt}}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                        <button class="btn btn-delete" (click)="deleteAirflowFiles(validrepo.fileName)">
                                            <span class="mdi mdi-delete"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="reportspageNumber = $event" id="first"></pagination-controls>
                    </div>
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingTest">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                        Dag Execution
                    </button>
                </h2>
                <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <form class="form qmig-Form" [formGroup]="executeForm">
                                <div class="row">
                                    <!-- <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="operation">Operation</label>
                                            <select formControlName="operation" class="form-select" #MyOp (change)="
                                                selectOperation(MyOp.value);
                                              ">
                                                <option disabled>Select a Operation</option>
                                                @for (list of operation; track list) {
                                                <option value="{{ list.value }}">{{ list.option }}</option>
                                                }
                                            </select>
                                        </div>
                                    </div> -->
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 ">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="targtetConnection">Source
                                                Connection Name</label>
                                            <select class="form-select" #srcFile
                                                (change)="fetchSourceConfigFiles1(srcFile.value)">
                                                <option>Select a Source Connection</option>
                                                @for ( src of ConsList; track src) {
                                                <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 ">
                                        <div class="form-group">
                                            <label class="form-label" for="targtetConnection">Target Connection
                                                Name</label>
                                            <select class="form-select" #tgtFile
                                                (change)="fetchTargetConfigFiles1(tgtFile.value)">
                                                <option>Select Target Connection</option>
                                                @for(tgt of tgtlist;track tgt; ){
                                                <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="fileNameExecute">File Name</label>
                                            <select formControlName="fileNameExecute" class="form-select" #sf
                                                (change)="selectDag(sf.value);selectFname(sf.value);refreshdags()">
                                                <option selected value="">Select a File Name</option>
                                                @for ( file of configFilesExe; track file) {
                                                <option value="{{file.fileName }}"> {{ file.fileName }}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                        <div class="body-header-button">
                                            <button class="btn btn-upload w-100 " [disabled]="!isCheckBoxSel"
                                                (click)="executeDags()">
                                                <span class="mdi mdi-cog-play-outline btn-icon-prepend"></span>Execute
                                                @if(executeSpin){<app-spinner />}</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    @if(dagSelected)
                    {
                    <div class="body-main mt-4">
                        <div class="qmig-card">
                            <div class="qmig-card-body">
                                <div class="row">
                                    <div class="col-12 col-md-7 offset-5 d-flex">
                                        <div class="custom_search cs-r me-3 my-3">
                                            <span class="mdi mdi-magnify"></span>
                                            <input type="text" placeholder="Search Dags" class="form-control"
                                                [(ngModel)]="searchText1" (keyup)="onKey()">
                                        </div>
                                        <button class="btn btn-sync" (click)="refreshdags()">
                                            @if(reff_spin){
                                            <app-spinner />
                                            }@else{
                                            <span class="mdi mdi-refresh"></span>
                                            }
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- Table details -->
                            <div class="table-responsive">
                                <table class="table table-hover qmig-table" id="example" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th style="width: 50px;">
                                                <div class="form-check m-0">
                                                    <label class="form-check-label">
                                                        <input type="checkbox" (click)="selectAll($event)"
                                                            class="form-check-input" [checked]="showCheckStatus"
                                                            [disabled]="isDisableAllCheck"> <i class="input-helper"></i>
                                                    </label>
                                                </div>
                                            </th>

                                            <th>Dag</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- For pagination -->
                                        @for(dag of dagsInfo | searchFilter: searchText1 |paginate:
                                        {itemsPerPage:10,currentPage:
                                        p,id:'First'};track dag){
                                        <tr>
                                            <td>
                                                <div class="form-check mt-33">
                                                    <label class="form-check-label">
                                                        <input type="checkbox" class="form-check-input" #chck
                                                            (click)="checkboxselect($event, chck.value)"
                                                            value="{{dag.dag_id}}" [checked]='dag.isSelected'
                                                            [disabled]="dag.isDagAvailable==false">
                                                        <i class="input-helper"></i>
                                                    </label>
                                                </div>
                                            </td>
                                            <td>{{dag.dag_id}}</td>
                                            @if(dag.isDagAvailable==true)
                                            {<td><i class="mdi mdi-check green"></i>
                                            </td>
                                            }
                                            @if(dag.isDagAvailable==false)
                                            {<td><i class="mdi mdi-close red"></i>
                                            </td>
                                            }
                                        </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            <div class="custom_pagination">
                                <pagination-controls (pageChange)="p = $event" id="First"></pagination-controls>
                            </div>
                        </div>
                    </div>
                    }
                </div>
            </div>
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne1" aria-expanded="false" aria-controls="flush-collapseOne">
                        Reports
                    </button>
                </h2>
                <div id="flush-collapseOne1" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <!--- Reports List --->
                    <div class="body-main mt-4">
                        <div class="qmig-card">
                            <!-- <h3 class="main_h px-3 pb-1 pt-3">List of Reports</h3> -->

                            <div class="qmig-card-body">
                                <div class="row">
                                    <div class="col-12 col-sm-3 col-md-3 col-lg-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="targtetConnection">Source
                                                Connection Name</label>
                                            <select class="form-select" #srcFile21
                                                (change)="selectSrc(srcFile21.value)">
                                                <option>Select a Source Connection</option>
                                                @for ( srccf of ConsList; track srccf) {
                                                <option value="{{ srccf.Connection_ID }}">{{ srccf.conname }}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-3 col-md-3 col-lg-3">
                                        <div class="form-group">
                                            <label class="form-label" for="targtetConnection">Target Connection
                                                Name</label>
                                            <select class="form-select" #tgtFile22
                                                (change)="selectTgt(tgtFile22.value)">
                                                <option>Select Target Connection</option>
                                                @for(tgt of tgtlist;track tgt; ){
                                                <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>

                                    <!-- <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                        <div class="form-group">
                                            <label class="form-label" for="targtetConnection">FileName</label>
                                            <select class="form-select" #tgtFile21
                                                (change)="fetchNewValidationFiles(tgtFile21.value)">
                                                <option>Select Validation</option>
                                                @for(tgtfolder of foldersList;track tgtfolder; ){
                                                <option value="{{tgtfolder.folderpath}}">{{tgtfolder.folderName}}
                                                </option>
                                                }
                                            </select>
                                        </div>
                                    </div> -->
                                    <div class="col-12 col-sm-6 col-md-6 col-lg-6 d-flex">
                                        <div class="custom_search cs-r me-3 my-3">
                                            <span class="mdi mdi-magnify"></span>
                                            <input type="text" placeholder="Search Reports" class="form-control"
                                                [(ngModel)]="searchText" (keyup)="onKey()">
                                        </div>
                                        <button class="btn btn-sync" (click)="fetchNewValidationFiles1()">
                                            @if(getRunSpin11){
                                            <app-spinner />
                                            }@else{
                                            <span class="mdi mdi-refresh"></span>
                                            }
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- <div class="row">
                                <div class="col-sm-6 col-md-3 col-xl-3">
                                    <!-- <h3 class="main_h pt-3">Validation files  
                                        <button class="btn btn-sync"
                                            (click)="fetchAirflowFiles()">Reload
                                            @if(refSpin){
                                            <app-spinner />
                                            }@else{
                                            <span class="mdi mdi-refresh"></span>
                                            }
                                        </button>
                                    <!-- </h3> 
                                </div>-
                                <div class="col-sm-6 col-md-6 col-xl-6 mt-r-3 offset-md-6 mb-3">
                                    <div class="custom_search cs-r">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Airflow Reports" class="form-control"
                                            [(ngModel)]="searchText1" (keyup)="onKey()">
                                    </div>
                                </div>
                            </div> -->
                            <div class="row">
                                <div class="table-responsive">
                                    <table class="table table-hover qmig-table">
                                        <thead>
                                            <tr>
                                                <th>S.NO</th>
                                                <th>File Name</th>
                                                <th>Created Date</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for (Reports1 of NewValidationFiles | searchFilter: searchText1 | paginate:
                                            {
                                            itemsPerPage: pi1,
                                            currentPage: pageNumber1 , id:'second'} ; track Reports1; let i = $index) {
                                            <tr>
                                                <td>{{pageNumber1*pi+i+1-pi}}</td>
                                                <td>{{Reports1.fileName }}</td>
                                                <td>{{Reports1.created_dt}}</td>
                                                <td>
                                                    <button class="btn btn-download" (click)="downloadFile(Reports1)">
                                                        <span
                                                            class="mdi mdi-cloud-download-outline"></span>@if(spinDownload){<app-spinner />}
                                                    </button>
                                                    <button class="btn btn-delete"
                                                        (click)="deleteFiles(Reports1.filePath)">
                                                        <span class="mdi mdi-delete"></span>
                                                    </button>
                                                </td>
                                            </tr>
                                            } @empty {
                                            <tr>
                                                <td colspan="4">
                                                    <p class="text-center m-0 w-100">Empty list of Reports</p>
                                                </td>
                                            </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="custom_pagination">
                                    <pagination-controls (pageChange)="pageNumber1 = $event"
                                        id="second"></pagination-controls>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="offcanvas offcanvas-end" tabindex="-1" id="audit">
        <div class="offcanvas-header">
            <h4 class="main_h">Validation</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <div class="offcanvas-body">
            <form class="form qmig-Form">
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Connection Name</label>
                    : &nbsp;{{ conName }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Operation</label>
                    : &nbsp;{{ OpName }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Schema Name</label>
                    : &nbsp;{{ selectedschemas }}
                </div>
                <div class="form-group">
                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 me-1" (click)="
                            projectConRunTblInsert(validationForm.value, true)">
                            <span></span> Save & Execute later @if(runinfospin){<app-spinner />}</button>
                    </div>
                </div>
                <div class="form-group">
                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 me-1" (click)="
                        executeDags()">
                            <span></span> Execute @if(runinfospins){<app-spinner />}</button>
                    </div>
                </div>
                <div class="form-group">
                    <div class="body-header-button">

                        <button class="btn btn-upload w-100 me-1">
                            <span></span> Cancel </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Json Details</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <!--- Connection Name --->
        <div class="offcanvas-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="form-label d-required" for="name">Json Data </label>
                    <textarea class="form-control" type="text" id="read" placeholder="data" rows="15" cols="30"
                        [(ngModel)]="readDataString"></textarea>
                </div>
            </div>
            <div class="body-header-button mt-1">
                <button (click)="Update()" class="btn btn-upload w-100 "> Save
                    @if(uploadSpin){<app-spinner />}</button>
            </div>
        </div>
    </div>
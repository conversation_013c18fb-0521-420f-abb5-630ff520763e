import { Component, Renderer2 } from '@angular/core';
import { HeaderComponent } from '../header/header.component';
import { RouterOutlet } from '@angular/router';

@Component({
  selector: 'app-layout',
  standalone: true,
  imports: [HeaderComponent,  RouterOutlet],
  templateUrl: './layout.component.html',
  styles: ``
})
export class LayoutComponent {
  constructor(
    private renderer: Renderer2,
  ) {
    }
  toggle() {
    if (document.body.classList.contains("sidebar-icon-only")) {
      this.renderer.removeClass(document.body, "sidebar-icon-only")
    }
    else {
      this.renderer.addClass(document.body, "sidebar-icon-only")
    }
  }
}

import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { ActivatedRoute } from '@angular/router';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { deleteTableData, deleteTabledata, GetExceution, GetReqData } from '../../../../models/interfaces/types';

declare let $: any;

@Component({
  selector: 'app-server-parameters',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './server-parameters.component.html',
  styles: ``
})

export class ServerParametersComponent {
  pageName: string = ''
  projectId: any;
  datachange1: any;
  p: number = 1;
  p1: number = 1;
  p2: number = 1;
  page2: number = 1;
  p3: number = 1;
  ref_spin: boolean = false;
  ServerReports: any;
  piB: number = 1;
  status_spin: boolean = false;
  tabledata: any;
  piC: number = 10;
  codeextractionForm: any;

  constructor(private titleService: Title, private toast: HotToastService, private assessmentService: AssessmentService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.codeextractionForm = this.formBuilder.group({
      connectionName: ['', [Validators.required]],
    });
    this.GetConsList();
    this.getreqTableData();
    this.filterExecutionReports1()
    this.fetchExecutionLogs();
  }
  onKey() {
    this.page2 = 1;
    this.p = 1;
    this.p2 = 1;
  }
  get f() {
    return this.codeextractionForm.controls;
  }
  openPopup() {

    this.codeextractionForm.reset();
  }
  openModal(value: any) {
    $('#demo').offcanvas('show');
  }
  userData: any = [];
  selectedConname: any;
  conName: any;
  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;

  }
  GetConsList() {
    this.assessmentService.getConList(this.projectId.toString()).subscribe((data: any) => {
      const condata = data['Table1'];
      this.ConsList = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
    });
  }
  runinfospins: boolean = false;
  ConsList: any;
  conId: any;
  currentRunNumber: string = '';
  OpName: any;
  refreshChecked: boolean = false;
  schemaName: any = [];
  exeoperation: any;
  ExtractionResponseData: any = []

  AssessmentCommand() {
    this.runinfospins = true;
    var dbflag = ""
    this.ConsList.filter((item: any) => {
      if (item.Connection_ID == this.conId) {
        if (item.conname.toLowerCase().includes("dump")) {
          dbflag = "False"
        }
        else {
          dbflag = "True"
        }
      }
    })

    let obj: any = {
      sourceConnectionId: this.conId,
      projectId: this.projectId.toString(),
      dbFlag: dbflag,
      task: "Server_Parameters",
     // iteration: this.currentRunNumber,
     // objectCategory: this.OpName,
      restartFlag: this.refreshChecked == true ? "True" : "False",
      processType: "",
      jobName: "qmig-asses",
     // schema: this.schemaName.toString(),
     // exeoperation: this.currentRunNumber == "New" ? "" : this.exeoperation
    }
    this.assessmentService.AssessmentCommad(obj).subscribe((data: any) => {
      this.ExtractionResponseData = data;
      this.runinfospins = true;
      $('#demo').offcanvas('hide');
      this.toast.success("Extraction Command Executed")
    },
      error => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong')
      })
  }
  //fetchExtractionFiles
  extractionFiles: any;
  datachange: any;
  pi: number = 1;
  filterExecutionReports1() {
    this.ref_spin = true
    var path = "PRJ" + this.projectId + "SRC/Server_Parameter_Reports/" 
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ServerReports = data
      this.ref_spin = false
    })
  }
  runnoExtraction: any;
  condata: any = []
 
  //downloadFile
  fileResponse: any
  spin_dwld: any;
  downloadFile(fileInfo: any) {
    this.assessmentService.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  i: string = '';
  z: number = 0;
  getreqTableData() {
    this.status_spin = true
    const obj = {
      projectId: this.projectId,
      operationType: 'Server_Parameters',
    };

    this.assessmentService.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      this.status_spin = false
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {

        for (let k = 0; k < this.tabledata.length; k++) {
          if (this.tabledata[k].status == "C") {
            this.tabledata[k].statusfull = "Completed"
          }
          else if (this.tabledata[k].status == "I") {
            this.tabledata[k].statusfull = "Initialize"
          }
          else if (this.tabledata[k].status == "P") {
            this.tabledata[k].statusfull = "Pending"
          }
          else {

          }

          if (this.tabledata[k].objecttype == "ALL") {
            this.tabledata[k].objecttype = ""
          }
        }
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList?.length; i++) {
              if (
                this.tabledata[this.z].connection_id ==
                this.ConsList[i].Connection_ID
              ) {
                this.tabledata[this.z].conname = this.ConsList[i].conname;
              }
            }
          }
        }
        else {
          this.tabledata = []
        }
      }
    });
  }
  deleteResponse: any;
  deleteTableDatas(request_id: any) {
    const obj: deleteTableData = {
      projectId: this.projectId,
      requestId: request_id,
    };
    this.assessmentService.deleteTableData(obj).subscribe((data: deleteTabledata) => {
      //this.deleteResponse = data['Table1'];
      this.getreqTableData();
    });
  }
  page3:number=1
  datachangeLogs:any;
  ExecutionLogs:any=[]
  page4:number=1
  fetchExecutionLogs() {
    const path = "PRJ" + this.projectId + "SRC/Server_Parameter_Reports/Execution_Logs"
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecutionLogs = data
    })
  }
}

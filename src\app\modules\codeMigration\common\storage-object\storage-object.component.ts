import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { GetIndividual, GetIndividualLogs, SchemaListSelect1 } from '../../../../models/interfaces/types';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
declare let $: any;
@Component({
  selector: 'app-storage-object',
  standalone: true,
  imports: [NgSelectModule, FormsModule, CommonModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './storage-object.component.html',
  styles: ``
})
export class StorageObjectComponent {
  migrationSource = [{ values: 'D', option: 'Database' }];
  selectedCar: number | undefined;
  selectedPeople = [];
  selectedItems = [];
  selectedItemstgt=[];
  data: any = [];

  people$: Observable<any> | undefined;
  //pagination
  searchText: string = '';
  searchText1: string = '';
  projectId: string = '';
  ref_spin2: boolean = false;
  ref_spin1: boolean = false;
  //getConsLIst
  schemaList: any;
  obj: any;
  conList: any;
  datachangeLogs: any
  datachangeLogs1: any
  //getschemaList
  tgtList: any;
  userData: any;
  selectedConname: any;
  //connection
  conId: any;
  conName: any;
  tgtId: any;
  //pagination
  datachange: any;
  datachange1: any;
  datachange2: any;
  datacahnge3: any;
  pageNumber: number = 1;
  pageNumber1: number = 1;
  p: number = 1;
  page: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  // piB: number = 10;
  // piA: number = 10;
  // pi: number = 10;
  r_id: any;
  //page activity
  logdata: any;
  disabledprevious: boolean = false;
  tgtValue: string = '';
  //object type
  objectType: any;
  project: any;
  objtype: any;
  //validations
  value: any;
  airflowResponse: any
  execProjectForm: any;
  prjSchmea: any = {};
  getRunSpin: boolean = false;
  //get run number
  runNoData: any;
  runnoForReports: any;
  ref_spin: boolean = false
  tabledata: any;
  z: any;
  //iteration
  deleteResponse: any
  iterationForLogs: any
  //obj
  selectedObjType: any
  depLogs: any = []
  //schema
  schemaName: any = [];
  assessmentFiles: any
  ExeLog: any = {};
  ExecututionFiles: any;
  objType: any = [];
  obtypevalue: any
  operation: any
  selectedOperation: any
  prjdata: any
  //hrs
  hrs: any
  fileResponse: any;
  spin_dwld: any;
  runinfospin: boolean = false;
  runinfo: boolean = false;
  //open model details
  prjdatas: any
  opId: any
  iterationselected: any
  selectedschemas: string = ""
  migtypeid:string=""
  pageName:string = ''
  schemalable:string=""
  constructor(private titleService: Title, private FormBuilder: FormBuilder, private common: CodeMigrationService, private toast: HotToastService, private route:ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    if(this.migtypeid=="31"){
      this.schemalable="Database"
    } else{
      this.schemalable="Schema"
    }
  }
  ngOnInit(): void {
this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList()
    this.getObjectTypes();
    this.getPrjRuninfoSelectAll()
    // this.getrunno()
    this.getreqTableData();
    this.execProjectForm = this.FormBuilder.group({
      runNo:['',[Validators.required]],
      connectionName: ['', Validators.required],
      schema: ['', Validators.required],
      targetconnection:[''],
      operation: [''],
      objectType: ['', Validators.required],
      targetSchema:[''],
      deployConnection:[''],
      refreshFlag: ['']
    });
    this.pageNumber = 1;
    this.page2 = 1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3 = 1;
    this.page = 1;
  }


  //for validations
  get f() {
    return this.execProjectForm.controls;

  }
  // select schema data
  selectschema(data: any) {
    this.schemaName = data.toString();
    this.showTargetSchemaAndTables = data.length === 1;
    this.isAll = data.toString();
    this.selectedsrcschema = [];
    this.selectedtgtScema = "";
    const targetSchemaControl = this.execProjectForm.get('targetSchema');
    this.selectedtgtScema = targetSchemaControl?.value;
  
    if (data === 'ALL' || (Array.isArray(data) && data.includes('ALL'))) {
      this.schemaList.forEach((item: any) => {
        if (this.migtypeid == "31") {
          this.selectedsrcschema.push(item.schemaname)
        }
        else {
          this.selectedsrcschema.push(item.schema_name);
        }
      });
    } else if (Array.isArray(data)) {
      this.selectedsrcschema = data.filter((schema: any) => schema !== 'ALL');
    } else {
      this.selectedsrcschema = [data];
    }
    console.log("Selected Schemas →", this.selectedsrcschema);
   
  }
  // onkey event
  onKey() {
    this.pageNumber = 1;
    this.page2 = 1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3 = 1;
    this.page = 1;
  }
  // redis details
  redisResp: any
  ObjectCategory: any = "Storage_Objects"
  currentRunNumber: string = '';
  // get runnumber details
  getRunNumber(data: any) {

    this.schemaList=[]
    this.conList=[]
    this.currentRunNumber = data;
    this.totalRunnumbers.filter((item:any)=>{
      if(item.iteration==data){
        this.schemaList.push({schema_name:item.schemaname})
        this.schemaList = this.schemaList.filter((item: any) => item.schema_name !== 'ALL');
        this.schemaList.forEach((item: any) => {
          item.type = "ALL"
        })
        if(this.conList.length==0){
        this.conList.push({Connection_ID:item.connection_id,conname:item.conname})
        }
      }
    })
    // this.currentRunNumber = data;
    // this.schemasbyiteration(data)
    //console.log(data)
  }
  schemasbyiteration(value: string) {
    this.common.GetSchemasByRunId(value).subscribe((data: any) => {
      this.schemaList = data['Table1'];
    })
  }

  get getControl() {
    return this.execProjectForm.controls;
  }
  //GetSchemasLiist
  getSchemasList(connectionId: any) {
    this.tgtSchemaList=[]
    if(this.migtypeid=="31"){
      this.tgtList.filter((item: any) => {
        if (item.Connection_ID == connectionId) {
          let ob={
            schema_name:item.dbname
          }
          this.tgtSchemaList.push(ob);
        }
      })
    }
    else{
    const obj: SchemaListSelect1 = {
      projectId: this.projectId,
      connectionId: connectionId
    }
    this.common.SchemaListSelect1(obj).subscribe((data: any) => {
      this.tgtSchemaList = data['Table1']|| [];
      this.tgtSchemaList=this.tgtSchemaList.filter((item:any)=>{
        return item.schemaname !="ALL"
      });
      // this.schemaList = data['Table1'];
    })
  }
  }
  //GetconnectionList
  GetConsList() {
    this.common.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.conList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
    })
  }
  //selected file type
  selectedfiletype(value: any) {
    const selectedconname = this.conList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.userData = [];
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;

  }
  //Get terget connection

  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
  }
  //fetch obj details
  selectObj(value: any) {
    this.objtype = value
    this.execProjectForm.controls.schema.setValidators([Validators.required])
    this.execProjectForm.controls.schema.updateValueAndValidity()
  }
  depObjTypes: any=[]
  //Get object types
  getObjectTypes() {
    const obj = {
      projectId: this.projectId,
      objectgroupname: "Storage_Objects"
    }
    this.common.getObjectTypes(obj).subscribe((data: any) => {
      this.objectType = data['Table1'];
      this.depObjTypes = this.objectType.filter((item: any) => {
        return item.object_category != "ALL"
      })

      var ob={object_type : "ALL",object_category: "Storage_Objects",
        object_type_id:"0"}
      this.objectType.unshift(ob)
    })
  }
  tgtSchemaList: any=[]
  selectedtgtScema: string = ""
  selectTgtSchema(value: any) {
    this.selectedtgtScema = value
  }
  //update run number
  getUpdatedRunNumber() {
    this.getRunSpin = true
    this.getPrjRuninfoSelectAll()
    this.getreqTableData()

  }
  //get run no details
  // getrunno() {
  //   this.common.GetRunno(this.projectId).subscribe((data) => {
  //     this.runNoData = data['Table1'];
  //     this.runNoData = this.runNoData.filter((item: any) => {
  //       if (item.iteration == "") {
  //         item.dbschema = "ALL"
  //       }
  //       return (item.operation_type == "Extraction") 
  //     })
  //     this.runNoData=this.filterList(this.runNoData)
  //   });
  // }

  totalRunnumbers:any=[]
  getPrjRuninfoSelectAll() {
    this.common.GetRunno(this.projectId).subscribe((data: any) => {
      this.getRunSpin = false
      this.totalRunnumbers=data['Table1']
      this.runNoData = data['Table1'];
      this.runnoForReports = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoForReports= this.runnoForReports.filter((item: any) => {
        return item.iteration!=""
      })
     // this.runnoForReports = this.runnoForReports.filter((data: any) => { return ((data.operation_type == "Extraction") && data.iteration != '') })
      // this.runNoData = this.runNoData.filter((item: any) => {
      //   if (item.iteration == "") {
      //     item.dbschema = "ALL"
      //   }
      //   return (item.operation_name == "Storage_Objects" && item.operation_type == "Conversion") || item.iteration == ""
      // })
      this.runnoForReports = this.filterList(this.runnoForReports)
      this.runNoData = this.filterList(this.runNoData)
    });
  }
  //filter list
  filterList(listData: any) {
    let uniqueNames: any = []
    for (let k = 0; k < listData.length; k++) {
      if (uniqueNames.length == 0) {
        uniqueNames.push(listData[k])
      }
      else {
        var abc = uniqueNames.filter((item: any) => {
          return item.iteration === listData[k].iteration
        })
        if (abc.length == 0) {
          uniqueNames.push(listData[k])
        }
      }
    }
    return uniqueNames;
  }

  //Get request table data
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.common.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            if (this.tabledata[this.z].status == "C") {
              this.tabledata[this.z].statusfull = "Completed"
            }
            else if (this.tabledata[this.z].status == "I") {
              this.tabledata[this.z].statusfull = "Initialize"
            }
            else if (this.tabledata[this.z].status == "P") {
              this.tabledata[this.z].statusfull = "Pending"
            }
            else {

            }

            for (let i = 0; i < this.conList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.conList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.conList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_category == "Storage_Objects"
        })
      }
    })
    //console.log(this.tabledata)



  }
  // delete request table data

  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.common.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData()
    })
  }
  //**package activity log*/
  GetIndividualLogs(action: any) {
    this.page = 1
    if (action == "null") {
      this.r_id = "null"
    }
    if (action == "previous") {
      this.r_id = this.logdata[0].exelog_id;
    }
    if (action == "next") {
      this.r_id = this.logdata[this.logdata.length - 1].exelog_id;
      this.disabledprevious = false;
    }
    if (action == '') {
      action = 'null'
    }
    const logobj = {
      projectId: this.projectId.toString(),
      operationType: "Conversion",
      operationName: "Storage_Objects",
      action: 'null',
      row_id: this.r_id,
      runno: action
    }
    this.common.GetIndividualLogs(logobj).subscribe((data) => {
      this.logdata = data['Table1'];
    });
  }
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value
  }
  //select object type
  selectObjType(value: any) {
    this.selectedObjType = value
    this.fetchLogs();
  }
  selectObjType1(value:any){
    this.selectedObjType = value;
    this.fetchLogs1();
  }
  //fetch logs details
  fetchLogs() {
    this.depLogs = [];
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Deployment_Logs/" + this.selectedObjType + "/";
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.depLogs = data;
      if (this.depLogs.length == 0) {
        let ob = {
          fileName: "No Files Created"
        }
        this.depLogs.push(ob)
      }
      //console.log(this.depLogs)
    })
  }
  depLogs1:any
  fetchLogs1() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs1 + "/Failed_Objects/Storage_Objects/";
    this.depLogs1 = []
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.depLogs1 = data;
    })
  }
  iterationForLogs1:any;
  selectIterForLogs1(value: any) {
    console.log('Selected iteration:', value);
    this.iterationForLogs1 = value;
    this.fetchLogs1();
  }
  //select iteration
  selectIteration(value: any) {
    this.iterationselected = value
    this.fetchAssessmentFiles()
  }
  //fetch assessment files details
  fetchAssessmentFiles() {
    this.assessmentFiles = []
    const path = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Validation/"
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.assessmentFiles = data.filter((item: any) => {
        return item.fileName.includes("_Storage_Objects_Validation")
      })
    })
  }
  //hrs
  hours: any = [{
    option: "ALL", value: "0"
  },
  {
    option: "1 hrs", value: "1"
  }, {
    option: "5 hrs", value: "5"
  }, {
    option: "10 hrs", value: "10"
  }, {
    option: "1 Day", value: "24"
  },
  ]
  //ge hrs details
  gethrs(value: any) {
    this.hrs = value
    //console.log(this.hrs)
    this.filterReports()
  }
  //filter reports
  filterReports() {
    if (this.hrs == "0") {
      this.assessmentFiles = []
      this.fetchAssessmentFiles()
    }
    else {
      const obj = {
        path: "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Validation/",
        hrs: this.hrs,
        validationFlag: false
      }
      this.common.ReportsFilterByTime(obj).subscribe((data: any) => {
        this.assessmentFiles = data
      })
    }
  }
  //download files
  downloadFile(fileInfo: any) {
    this.common.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  //open model details

  openModal(value: any) {
    // for (let i = 0; i < this.schemaName.length; i++) {
    //   this.selectedschemas = this.selectedschemas + this.schemaName[i]
    //   if (i < this.schemaName.length - 1) {
    //     this.selectedschemas = this.selectedschemas + ","
    //   }
    // }
    this.selectedschemas = this.schemaName.toString()
    $('#demo').offcanvas('show');
  }
  //open popup
  openPopup() {
    this.schemaName = []
    this.selectedschemas = ''
    this.execProjectForm.reset();
  }

  //project connection run table inserts
  projectConRunTblInserts(data: any, value: any) {
    this.runinfo = true;
    this.opId = data.operation
    if (value == true) {
      data.status = "P"
    }
    else {
      data.status = "I"
    }
    if (data.pattern != "") {
      var objName = data.pattern
    }
    else {
      objName = ""
    }
    if (data.objectType != "") {
      var objType = data.objectType
    }
    else {
      objType = ""
    }
    // const tableinsesrt = {
    //   projectId: this.projectId.toString(),
    //   connection_id: parseInt(this.conId),
    //   operationId: parseInt(this.operation[0].operation_id),
    //   schema: this.schemaName.toString(),
    //   status: data.status,
    //   remarks: "",
    //   objectname: objName,
    //   objecttype: objType
    // }
    if (this.tabledata.length != 0) {
      const res = this.tabledata.filter((item: any) => {
        return item.connection_id == this.conId && item.schemaname == this.schemaName.toString() && item.status == "I" && item.operation_name == "Storage_Objects"
      })
      if (res.length >= 1) {
        this.runinfo = false;
        $('#demo').offcanvas('hide');
        this.toast.error("Request already Added")
      }
      if (res.length == 0) {

        this.RedisCommand()
        this.getreqTableData();
        //this.runinfo = false;
        // $('#updateOrgModal').modal('hide');
      }
    }
    if (this.tabledata.length == 0) {
      this.RedisCommand()
      this.getreqTableData();
      // this.runinfo = false;
      //$('#updateOrgModal').modal('hide');

    }

  }
  //redis command details
  RedisCommand() {
    this.runinfo = true;
    const obj:any = {
      projectId: this.projectId.toString(),
      option: 3,
      schema: this.schemaName.toString(),
      connection: this.conName,
      Object: this.ObjectCategory,
      srcConId: this.conId,
      tgtConId: this.tgtId,
      objectType: this.objtype,
      jobName: "qmig-convs",
      iteration: this.currentRunNumber,
      targetSchema:this.selectedtgtScema
    }
    //console.log(obj);
    this.common.setRedisCache(obj).subscribe((data: any) => {
      this.redisResp = data;
      this.runinfo = false;
      this.schemaName = []
      this.selectedschemas = ''
      this.execProjectForm.reset();
      $('#demo').offcanvas('hide');
      this.toast.success("Triggered Successfully")
    })
  }
  //get exe logs
  getPrjExeLogSelect() {
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operation = 'TASK';
    this.ExeLog.pageNo = '1';
    this.common.PrjExeLogSelect(this.ExeLog).subscribe((data) => {
      this.userData = data['Table1'];
    });
  }
  //post user access select
  postUserAccessSelect() {
    const obj = {};
    this.common.UserAccessSelect(obj).subscribe(() => {
    });
  }
  //project connection run  tables
  projectConRunTblInsert(data: any, value: any) {
    if (value == true) {
      data.status = "P"
    }
    else {
      data.status = "I"
    }
    if (data.pattern != "") {
      var objName = data.pattern
    }
    else {
      objName = ""
    }
    if (data.objectType != "") {
      var objType = data.objectType
    }
    else {
      objType = ""
    }

    const tableinsesrt = {
      projectId: this.projectId.toString(),
      connection_id: parseInt(this.conId),
      operationId: parseInt(this.operation[0].operation_id),
      schema: this.schemaName.toString(),
      status: data.status,
      remarks: "",
      objectname: objName,
      objecttype: objType
    }

    this.runinfospin = true;
    this.common.projectConRunTblInsert(tableinsesrt).subscribe((data: any) => {
      this.prjdata = data['jsonResponseData']['Table1'];
      if (data.message == "Success") {
        this.runinfospin = false;
        $('#demo').offcanvas('hide');
        this.toast.success('Successfully Inserted');
        this.getreqTableData()
      }
    },
      () => {
        this.runinfospin = false;
        $('#demo').offcanvas('hide');
        this.toast.error('Something happened');
      });
  }
  //select operation
  selectOpertion(id: any) {
    const op = this.operation.filter((item: any) => {
      return item.operation_id == id
    })
    this.selectedOperation = op[0].operation_name
  }
  //select object type
  SelectObjectTypes(value: any) {
    this.objType = value;
  }
  //select iitreation
  selectIterforlogs(value: any) {
    this.iterationForLogs = value
    this.filterExecutionReports()
  }
  //filter execution reports
  filterExecutionReports() {
    this.ExecututionFiles = []
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Execution_Logs/Conversion/"
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data.filter((item: any) => {
        return item.fileName.includes("_Storage_Objects")
      })
    })
  }
  ConversionResponseData: any = []
  ConversionCommand() {
    let obj: any = {
      sourceConnectionId: this.conId,
      targetConnectionId:this.tgtId,
      projectId: this.projectId.toString(),
      task: "Conversion",
      iteration: this.currentRunNumber,
      objectCategory:this.ObjectCategory,
      objectType: this.migtypeid=="20" ? (this.objtype == 'Package' ? 'Package_Function,Package_Procedure' : this.objtype) :  this.objtype,
      schema: this.selectedsrcschema.join(','),
      targetSchema:this.selectedtgtScema || this.selectedsrcschema.toString() ,
      deployConnectionId:this.deployCon,
      processType:"",
      restartFlag: this.refreshChecked==true?"True":"False",
      jobName: "qmig-convs",
    }
    console.log(obj)
    this.common.ConversionCommand(obj).subscribe((data: any) => {
      this.ConversionResponseData = data;
      $('#demo').offcanvas('hide');
      this.toast.success("Storage Conversion Command Executed")
    },
      error => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong')
      })
  }
  refreshChecked: boolean = false
  getCheckValue($event: any): void {
    //console.log($event.target.checked)
    if ($event.target.checked == true) {
      this.refreshChecked = true
    }
    else {
      this.refreshChecked = false
    }
  }
  deployCon:any;
  drconName:any;
  drconId:any;
  selectDeployCon(value:any){
    // if(this.tgtId == value){
    //   this.toast.warning("Target and Deploy Connection Can not be Same ");
    // }
    // else{
      const selectedconname = this.tgtList.filter((item: any) => {
        return item.Connection_ID === value;
      });
      this.deployCon = value;
      this.drconId = selectedconname[0].Connection_ID;
      this.drconName = selectedconname[0].conname;
     // this.deployCon=value
    // }
  }
  selectedsrcschema: any = [];
  selectedtgtschema: any
  schemavalue: any
  showschemaa: boolean = false
  isAll: any;
  showTargetSchemaAndTables: boolean = true;
  validationstable() {
    const tgtschemaControl = this.execProjectForm.get('targetSchema');
    if (this.isAll === "ALL") {
      tgtschemaControl?.clearValidators();
      tgtschemaControl?.setValue(null);
    }
    else if (Array.isArray(this.selectedItems) && this.selectedItems?.length === 1) {
      tgtschemaControl?.clearValidators();
      tgtschemaControl?.setValue(null);
    }
    else {
      tgtschemaControl?.clearValidators();
      tgtschemaControl?.setValue(null);
    }
    tgtschemaControl?.updateValueAndValidity();
  }
}


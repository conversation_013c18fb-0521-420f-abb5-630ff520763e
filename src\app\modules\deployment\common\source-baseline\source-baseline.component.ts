import { Component } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { TabsComponent } from '../tabs/tabs.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { DeploymentService } from '../../../../services/deployment.service';
import { DatePipe } from '@angular/common'
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-source-baseline',
  standalone: true,
  imports: [NgSelectModule, DatePipe, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, TabsComponent],
  templateUrl: './source-baseline.component.html',
  styles: ``
})
export class SourceBaselineComponent {
  pi: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;
  fileData: any;
  datachange: any;
  TgtUpdateTgt: any;
  logfile: any;
  fileName: any;
  fileResult: any;
  project_name: any
  projectId: any
  getRole: any;
  Spin: any
  hide_files: boolean = true;
  hide_db: boolean = true;
  isGit: boolean = true;
  uploadedData: any
  spin_process: any;
  prjSrcTgtData: any;
  infraData: any;
  filtered: any;
  selectExtraction: any;
  datachanges: any;
  datachanges1: any;
  datachanges2: any;
  datachanges3: any;
  pageNumber: number = 1;
  p: number = 1;
  page: number = 1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  datachange1:any;
  pages: number = 1;
  sorcedeltaform: any;
  // @ViewChild(PlyrComponent)
  // plyr: PlyrComponent | undefined;

  // // or get it from plyrInit event
  // player: Plyr | undefined;
  // poster = 'assets/videos/qmig_ui.jpg';


  schemaName: any = [];
  UserInfo: any;
  pageName: string = '';


  constructor(public fb: FormBuilder,
    public deployment: DeploymentService,
    private toast: HotToastService,
    private route: ActivatedRoute,

  ) {
    this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    let userJson = localStorage.getItem('userData') as string;
    this.UserInfo = JSON.parse(userJson);
    this.pageName = this.route.snapshot.data['name'];

  }
  sorceform: any;

  ngOnInit(): void {
    this.sorceform = this.fb.group({
      runnumber: ['', [Validators.required]],
      sourceext: ['', [Validators.required]],
      sourcecon: ['', [Validators.required]],
      scheman: [''],
     // date: [null],
      sourcefile: [''],
      objectt: ['', [Validators.required]],
    });
    this.sorcedeltaform = this.fb.group({
      scrun: ['', [Validators.required]],
      scschema: ['', [Validators.required]],
    });
    this.getInfraSelect();
    this.getBlobfileData();
    this.getRunNumber();
    this.getRunNumbers();
    this.getFiles();
    this.getPrjExeLogSelectTask("null");
    this.GetRequestTableData();

  }

  get validate() {
    return this.sorceform.controls;
  }
  blobfile: any;
  getBlobfileData() {
    // this.project.GetBlobfileByFileName("source_baseline.mp4").subscribe((data: any) => {
    //   this.blobfile = data.fileUrl;
    // })
  }

  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }
  TgtCodeShow() {
    this.TgtUpdateTgt = true;
  }
  TgtCodeShows() {
    this.TgtUpdateTgt = false;
  }

  onKey() {
    this.pageNumber = 1;
    this.page2=1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3=1;
  }
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all') {
      this.pi = this.uploadedData.length;
      this.p = 1;

    }
    else if (value == '20') {
      this.p = 1;
    }
    else if (value == '50') {
      this.p = 1;
    }
    else if (value == '100') {
      this.p = 1;
    }
  }
  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }
  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }

  selectedDropdown(data: any) {
    if (data == 'Git Repo') {
      this.hide_files = true;
      this.hide_db = true;
      this.isGit = false;

    }
    if (data == 'Database') {
      this.hide_files = true;
      this.hide_db = false;
      this.isGit = true;
      this.sorceform.controls['runnumber'].setValidators([Validators.required]);
      this.sorceform.controls['sourceext'].setValidators([Validators.required]);
      this.sorceform.controls['sourcecon'].setValidators([Validators.required]);
      this.sorceform.controls['scheman'].setValidators([Validators.required]);
     //  this.sorceform.controls['date'].clearValidators();
      this.sorceform.controls['objectt'].setValidators([Validators.required]);
      this.sorceform.controls['sourcefile'].clearValidators();
      this.GetDBConnections();
    }
    if (data == "Fileshare") {
      this.hide_files = false;
      this.hide_db = true;
      this.isGit = true;
      this.sorceform.controls['runnumber'].setValidators([Validators.required]);
      this.sorceform.controls['sourceext'].setValidators([Validators.required]);
      this.sorceform.controls['sourcecon'].setValidators([Validators.required]);
      this.sorceform.controls['objectt'].setValidators([Validators.required]);
      this.sorceform.controls['scheman'].clearValidators();
      // this.sorceform.controls['date'].clearValidators();
      this.sorceform.controls['sourcefile'].setValidators([Validators.required]);
      this.getuploadedFiles();
      this.GetDBConnections();
    }
    else {
      this.selectExtraction = data;
      this.sorceform.controls['runnumber'].updateValueAndValidity();
      this.sorceform.controls['sourceext'].updateValueAndValidity();
      this.sorceform.controls['sourcecon'].updateValueAndValidity();
      this.sorceform.controls['scheman'].updateValueAndValidity();
      this.sorceform.controls['objectt'].updateValueAndValidity();
      // this.sorceform.controls['date'].updateValueAndValidity();
      this.sorceform.controls['sourcefile'].updateValueAndValidity();
    }
  }

  selectedItems = [];
  // select schema data
  selectschema(data: any) {
    this.schemaName = data
    //console.log(data)
  }

  getuploadedFiles() {
    let requestObj = {
      projectID: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: "Code",
      subFolderName: "Archive",
      subFolderName1: "Oracle",
    };
    // this.project.Files(requestObj).subscribe((data) => {
    //   this.uploadedData = data;
    // });
  }

  getFiles() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iteration + "/Target_Current_Zip"
    };
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.uploadedData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }

  sourceext: any;
  sourcecon: any;
  scheman: any;
  date: any;
  selecteddate:any;
  DeltaCommand(value: any) {

    // if (value.date ===null) {
    //   this.selecteddate= value.date;
    // }
    // else
    // {
    //   this.selecteddate = this.deployment.formatDate(value.date);
    // }
    // console.log(this.selecteddate)
    
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.sourceext,
      srcCon: value.sourcecon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      // date:this.selecteddate,
      extraction_Category: "Source_Baseline",
      task: "Source_Baseline",
      fileName: "",
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      debugger;
      this.Deltabaseline = data;
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data.message);
      }
    })
  }
  Deltabaseline: any;
  Deltabaselineextract:any;

  DeltaCommandextract(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.sourceext,
      srcCon: value.sourcecon,
      ObjectCategory: value.objectt,
      schema: value.scheman.toString(),
      extraction_Category: "Source_Baseline",
      task: "Source_Baseline",
      fileName: value.sourcefile,
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      this.Deltabaselineextract = data
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data);
      }
    })
  }
  SourceCo:any;
  GetDBConnections() {
    const projectId = this.projectId.toString();
    this.deployment.getConList(projectId).subscribe((data) => {
      this.prjSrcTgtData = data['Table1'];
      this.SourceCo = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "S" && item.migsrctype == "D";

      })
    })
  }
  runNumbers: any;
  getRunNumber() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbers = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });

  }

  SourceConn: any;
  schemaList: any = []
  prjSchemaList(sourceConection: any) {
    this.SourceConn = sourceConection;
    (this.SourceConn)
    let obj = {
      projectId: this.projectId.toString(),
      dbConnection: 'null',
      sourceConnection: sourceConection.toString()
    }
    this.deployment.getschemaList(obj).subscribe((data) => {
      this.schemaList = data['Table1'];
    });
  }
  conid: any;
  prjSchemaLists(conid: any) {
    this.SourceConn = conid;
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnection: conid.toString()
    }
    this.deployment.getschemaList(obj).subscribe((data) => {
      this.schemaList = data['Table1'];
    });
  }
  getInfraSelect() {
    let id = this.projectId;
    // this.project.InfraSelect(id).subscribe((data) => {
    //   this.infraData = data['jsonResponseData']['Table1'];
    //   this.filtered = this.infraData.filter((element: any) => {
    //     return element.active == 'True';
    //   });
    // });
  }
  isLoading: any = [];
  isload(value: any) {
    this.isLoading[value] = true
  }


  iteration: any
  selectIteration(iter: any) {
    this.iteration = iter;
    this.getFiles();
  }
  // ExecuteCommandOnVM(filename: any) {
  //   this.spin_process = true
  //   let obj = {
  //     projectID: this.projectId.toString(),
  //     command: 'delta_source_extraction.sh',
  //     ObjectType: this.UserInfo.email.toString(),
  //     id: this.projectId.toString(),
  //     filename: filename,
  //     Schema: this.SourceConn,//Connection 
  //     ConnectionName: this.iteration,//iteration or run no.
  //     Connection: this.schemaName.toString()
  //   }

  // this.project.ExecuteCommandOnVM(obj).subscribe(
  //   (data: any) => {
  //     this.spin_process = false;
  //     if ((data.message == 'Command Executed Successfully')) {
  //       this.alertService.success(data.message);
  //       localStorage.setItem('Sourcefilename', filename);
  //     }
  //   },
  //   (error) => {
  //     this.spin_process = false;
  //     this.alertService.danger(
  //       'Something Went Wrong please try Again Later!'
  //     );
  //   }
  // );
  // }



  onItemSelect(item: any) {
    this.schemaName.push(item.schemaname);
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.schemaname);
    this.schemaName.splice(inde, 1);
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.schemaname);
    });
  }
  onDeSelectAll(items: any) {
    this.schemaName = [];
  }
  ExeLog: any = {};
  prjLogData: any;
  selectedrun: any;
  NoDataHide: boolean = false;
  getPrjExeLogSelectTask(value: any) {
    this.selectedrun = value;
    if (value == "") {
      value = "null";
    }
    this.prjLogData = [];
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operationType = "Source_Baseline";
    this.ExeLog.action = "null";
    this.ExeLog.row_id = "null";
    this.ExeLog.runno = value;
    this.ExeLog.operationName = "null";
    this.deployment.PrjExelogSelectTask(this.ExeLog).subscribe((data: any) => {
      this.prjLogData = data['Table1'];
      this.NoDataHide = true;
    })
  }
  runNumbersData: any;
  getRunNumbers() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbersData = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }
  RequestTableData: any;
  GetRequestTableData() {
    let obj = {
      projectId: this.projectId,
      operationType: 'Source_Baseline'
    }
    this.ref_spin=true;
    this.deployment.GetRequestTableData(obj).subscribe((data: any) => {
      this.RequestTableData = data['Table1'];
      this.ref_spin=false;
    });
  }
  ref_spin: boolean = false;
  deleteResponse: any;
  // delete request table data
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.deployment.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      if (data['Table1'][0].v_status) {
        this.toast.success(data['Table1'][0].v_status);
      }
      else {
        this.toast.error(data);
      }
	  this.GetRequestTableData();
    })
  }
  refresh()
  {
    this.GetRequestTableData();
    this.getRunNumbers();
  }
  LogsData: any;
  GetFilesExecutionLogsFromDirectory() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iterationForLogs + "/Execution_Logs/Deployment/Source_Baseline"
    };
    this.LogsData=[];
    this.deployment.getFiles(requestObj).subscribe((data) => {
      this.LogsData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }

  iterationForLogs: any;
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value;
    this.GetFilesExecutionLogsFromDirectory();
  }

  fileResponse: any;
  spin_dwld: any;
  //download files
  downloadFile(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  GetSourceFilePathData:any;
  GetSourceFilePath()
  {
    const obj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.run + "/Source/Database_Baseline/"+this.deltaschema.toString()+"/Procedure/"
    }
    this.GetSourceFilePathData=[];
    this.deployment.getFiles(obj).subscribe((data: any) => {
      this.GetSourceFilePathData = data; 
      for (const element of this.GetSourceFilePathData) {
        let schemaobjtype = element.baseline_filepath.split('/')[6] + "/" + element.baseline_filepath.split('/')[7]
        element.baselineFile = element.objectname + "_baseline.sql"
        element.currentFile = element.objectname + "_current.sql"
        element.htmlFile = element.objectname + ".html"
        element.schemaobj = schemaobjtype
        element.lastmodified = ""
        element.basefilename = element.objectname
      }
    });
  }
  downloadFiles(path:string,filename:string) {
    var pth=path.split("/mnt/pypod/")[1]
    pth="/mnt/eng/"+pth
    this.deployment.downloadLargeFiles(pth).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = filename;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
   // sc schemalist
   schemaNamess:any;
   GetSCSchemaList(runno: any) {
     this.schemaNamess = []
     let obj = {
       projectId: this.projectId,
       runno: runno
     }
     this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
       this.schemaNamess = data['Table1'];
     });
   }
   deltaschema:any;
  sourceSelected: boolean = false
  sourceSel(value: string) {
    value != '' ? this.sourceSelected = true : this.sourceSelected = false;
    this.deltaschema=value;
    this.GetSourceFilePath();
  }
  run:any;
  selectRunnumber(itera: any) {
    this.run = itera;
    this.GetSCSchemaList(itera);
  }
  get validates() {
    return this.sorcedeltaform.controls;
  }


  downloadFiless(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
}

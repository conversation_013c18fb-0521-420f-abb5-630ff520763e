<delta-tabs icon="mdi-database-import"></delta-tabs>
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button " type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Source current Extraction
                </button>
            </h3>
            <div id="flush-collapseOne" class="accordion-collapse collapse show" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="sourceDelta">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Run No</label>
                                        <select formControlName="runnumber" class="form-select" #run
                                            (change)="selectIteration(run.value)">
                                            <option selected disabled>Select Run Number</option>
                                            @for(list of runNumbers;track list;){
                                            <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validate.runnumber.touched && validate.runnumber.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.runnumber.errors?.['required']) { Run Number is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Source Extraction</label>
                                        <select class="form-select" formControlName="sourceext" #drpdwn
                                            (change)="selectedDropdown(drpdwn.value)">
                                            <option selected> select Source Extraction</option>
                                            <option value="Database">Database</option>
                                            <option value="Fileshare">Fileshare</option>
                                        </select>
                                        <div class="alert">
                                            @if(validate.sourceext.touched && validate.sourceext.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.sourceext.errors?.['required']) { Source Extraction is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label">Source Connection</label>
                                        <select class="form-select" formControlName="sourcecon" #src
                                            (change)="prjSchemaLists(src.value)" aria-label="Default select example">
                                            <option selected disabled>Select Source List</option>
                                            @for(list of SourceCo;track list;){
                                            <option value="{{ list.Connection_ID }}">{{ list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validate.sourcecon.touched && validate.sourcecon.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.sourcecon.errors?.['required']) { Source Connection is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label d-required">Schema Name</label>
                                        <ng-select [placeholder]="'Select Schema Type'" [items]="schemaList"
                                            [multiple]="true" bindLabel="schemaname" groupBy="gender"
                                            [selectableGroup]="true" [closeOnSelect]="false" bindValue="schemaname"
                                            [(ngModel)]="selectedItems" formControlName="scheman"
                                            (change)="selectschema(selectedItems)">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index" clearAllText="Clear" [clearSearchOnAdd]="true">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                            <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.schemaname}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if(validate.scheman.touched && validate.scheman.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.scheman.errors?.['required']) { Schema Name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label d-required">Object Category</label>
                                        <select class="form-select" aria-label="Default select example"
                                            aria-placeholder="Select Object List" #object formControlName="objectt">
                                            <option selected> Object Category</option>
                                            <option value="All">All</option>
                                            <option value="Storage_Objects">Storage_Objects</option>
                                            <option value="Code_Objects">Code_Objects</option>
                                        </select>
                                        <div class="alert">
                                            @if(validate.objectt.touched && validate.objectt.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.objectt.errors?.['required']) {Object Category is required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                @if(hide_files){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label">Upload Files</label>
                                        <select class="form-select" formControlName="sourcefile"
                                            aria-label="Default select example">
                                            <option selected disabled>Select Fiels</option>
                                            @for(list of uploadedData;track list;){
                                            <option value="{{ list.fileName }}">{{ list.fileName}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if(validate.sourcefile.touched && validate.sourcefile.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.sourcefile.errors?.['required']) { File Name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                @if(hide_files){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                    <button type="button" #extract class="btn btn-upload w-100 "
                                        [disabled]="sourceDelta.invalid"
                                        (click)="DeltaCommandExtract(sourceDelta.value)"> <span
                                            class="mdi mdi-cloud-sync"></span>
                                        Extract </button>
                                </div>
                                }
                                @if(hide_files){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-12 col-xl-12 mt-3">
                                    <div class="form-group" [formGroup]="sourceFileUpload">
                                        <label for="formFile" class="form-label d-required">Upload File </label>
                                        <div class="custom-file-upload">
                                            <input class="form-control" type="file" id="formFile"
                                                formControlName="fileName" (change)="onFileSelected($event)">
                                            <div class="file-upload-mask">
                                                @if (fileName == '') {
                                                <img src="assets/images/fileUpload.png" alt="img" />
                                                <p>Drag and drop deployment file here or click add deployment file </p>
                                                <button class="btn btn-upload"> Add File </button>
                                                } @else{
                                                <div
                                                    class="d-flex justify-content-center align-items-center h-100 w-100">
                                                    <p> {{ fileName }} </p>
                                                </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3 col-xl-3 offset-md-9">
                                            <div class="form-group">
                                                <button class="btn btn-upload w-100"
                                                    [disabled]="sourceFileUpload.invalid" (click)="uploadFile()"> <span
                                                        class="mdi mdi-file-plus"></span>
                                                    Upload
                                                    @if(Deltacom_Spin){
                                                        <app-spinner />
                                                        }
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                }

                                @if(hide_db){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label">Date</label>
                                        <input type="date" formControlName="date" id="selectedDate"
                                            class="form-control">
                                        <div class="alert">
                                            @if(validate.date.touched && validate.date.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.date.errors?.['required']) {Date is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- @if(hide_db){
                        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" >
                            <div class="row">
                                <div class="col-md-12 pt-2">
                                    <div class="form-check form-check-flat form-check-primary mt-4">
                                        <label class="form-check-label form-label">
                                            <input type="checkbox" class="form-check-input" > <i class="input-helper"></i><i
                                                class="input-helper"></i> List From
                                            Target
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    } -->
                                @if(hide_db){
                                <div class="col-md-3"></div>
                                <div class="col-md-3 text-right">
                                    <button type="button" class="btn btn-upload w-100 mt-4"
                                        [disabled]="sourceDelta.invalid" (click)="DeltaCommand(sourceDelta.value)">
                                        <span class="mdi mdi-cloud-sync"></span> Extraction @if(Delta_Spin){
                                            <app-spinner />
                                            }</button>
                                </div>
                                }
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
            <div class="accordion-item">
                <h2 class="accordion-header" id="headingFive">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                        Source Current - Files
                    </button>
                </h2>
                <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive"
                    data-bs-parent="#accordionExample">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="sorcedeltaform">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Run No</label>
                                        <select formControlName="scrun" class="form-select" #scrun
                                        (change)="selectRunnumber(scrun.value)">
                                        <option selected disabled>Select SQL Connection</option>
                                @for(list of runNumbersDatas;track list;){
                                <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                }
                                    </select>
                                        <div class="alert">
                                            @if(validates.scrun.touched && validates.scrun.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validates.scrun.errors?.['required']) {Run Number is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label d-required">Schema Name</label>
                                        <select class="form-select" aria-label="Default select example"
                                            aria-placeholder="Select Schema Name" #scschemaa
                                            (change)="sourceSel(scschemaa.value)" formControlName="scschema">
                                            <option selected>Select Schema</option>
                                            @for(list of schemaNamess;track list;){
                                        <option value="{{ list.schemaname }}">{{ list.schemaname}}</option>
                                        }
                                        </select>
                                        <div class="alert">
                                            @if(validates.scschema.touched && validates.scschema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validates.scschema.errors?.['required']) {Schema Name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- <div class="col-md-3 offset-3 mt-4" [hidden]="!sourceSelected">
                                    <button class="btn btn-upload w-100" [disabled]="sorcedeltaform.invalid"
                                        (click)="getconsoledatadelta(sorcedeltaform.value)">
                                        <span class="mdi mdi-cloud-download-outline"></span> Download Delta
                                        Objects</button>
                                </div> -->

                            </div>
                        </form>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Sno</th>
                                    <th>File Name</th>
                                    <th>Created Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (con of GetSourceFilePathData |searchFilter: datachange1| paginate: { itemsPerPage: 10,
                                currentPage: pages ,id:'Third'} ;track con) {
                                <tr>
    
                                    <td>{{p*10+$index+1-10}}</td>
                                    <td>{{ con.fileName }}</td>
                                    <td>{{ con.created_dt }}</td>
                                    <td>
                                        <button class="btn btn-download"
                                            (click)="downloadFiless(con)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                    <td>
    
                                </tr>
                                } @empty {
                                <!-- <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr> -->
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pages = $event" id="Third">
                        </pagination-controls>
                    </div>
                </div>
            </div>
        <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
            <div class="accordion-item">
                <h3 class="accordion-header" id="flush-headingTwo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                        Source Current Status
                    </button>
                </h3>
                <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                    data-bs-parent="#accordionFlushExample">
                    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 mt-3  text-end">
                        
                </div>
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-6">
                        <h3 class="main_h py-4 ps-3">
                            Source Current Status
                            <button class="btn btn-sync " (click)="refresh()">
                                @if(ref_spin){
                                    <app-spinner />
                                    }@else{
                                        <span class="mdi mdi-refresh"></span>
                                    }
                            </button>
                        </h3>
                    </div>
                    <!-- search status of storage obj migration -->
                    <div class="col-12 col-sm-6 col-md-6">
                        <div class="custom_search cs-r my-3 me-3">
                            <span class="mdi mdi-magnify"></span>
                            <input type="text" placeholder="Search Status" class="form-control" [(ngModel)]="datachanges"
                                (keyup)="onKey()">
                        </div>
                    </div>
                </div>
                    <div class="table-responsive mt-2">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>Run No</th>
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>Schema Name</th>
                                    <th>Object Type</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (con of RequestTableData |searchFilter: datachanges| paginate: { itemsPerPage: 10,
                                currentPage: page2 ,id:'second'} ;track con) {
                                <tr>
                                    <td>{{p*10+$index+1-10}}</td>
                                    <td>{{con.run_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_name }}</td>
                                    <td>{{ con.schemaname }}</td>
                                    <td>{{ con.objecttype }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        @switch (con.status) {
                                        @case ('I') {
                                        <span>Initialize</span>
                                        }
                                        @case ('P') {
                                        <span>Pending</span>
                                        }
                                        @default {
                                        <span>Completed</span>
                                        }
                                        }
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                </div>
            </div>
            <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
                <!-- <div class="accordion-item">
                    <h3 class="accordion-header" id="flush-headingThree">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#flush-collapseThree" aria-expanded="false"
                            aria-controls="flush-collapseThree">
                            Page-Activity Log
                        </button>
                    </h3>
                    <div id="flush-collapseThree" class="accordion-collapse collapse"
                        aria-labelledby="flush-headingThree" data-bs-parent="#accordionFlushExample">
                        <div class="qmig-card">
                            <div class="qmig-card-body">
                                <div class="row">
                                    <div class="col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="Schema">Run No</label>
                                            <select formControlName="runnumber" class="form-select"
                                                (change)="getPrjExeLogSelectTask(runnum.value)" #runnum>
                                                <option selected disabled>Select Run Number</option>
                                                @for(list of runNumbersData;track list;){
                                                <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                                }
                                            </select>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table class="table table-hover qmig-table">
                                            <thead>
                                                <tr>
                                                    <th>S.No</th>
                                                    <th>Run No</th>
                                                    <th>Date and Time</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for (con of prjLogData |searchFilter: datachanges1| paginate: {
                                                itemsPerPage:
                                                10,currentPage: page , id: 'first'} ;track con; ) {
                                                <tr>
                                                    <td>{{p*10+$index+1-10}}</td>
                                                    <td>{{ con.iteration }}</td>
                                                    <td>{{ con.activity_date }}</td>
                                                    <td>{{ con.migtask }}</td>
                                                </tr>
                                                } @empty {
                                                <tr>
                                                    <td colspan="4">
                                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                                    </td>
                                                </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                     pagination
                                    <div class="custom_pagination">
                                        <pagination-controls (pageChange)="page = $event" id="first">
                                        </pagination-controls>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
                    <div class="accordion-item">
                        <h3 class="accordion-header" id="flush-headingFour">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                data-bs-target="#flush-collapseFour" aria-expanded="false"
                                aria-controls="flush-collapseFour">
                                Execution Logs
                            </button>
                        </h3>
                        <div id="flush-collapseFour" class="accordion-collapse collapse"
                            aria-labelledby="flush-headingFour" data-bs-parent="#accordionFlushExample">
                            <div class="qmig-card">
                                <div class="qmig-card-body">
                                    <div class="row">
                                        <div class="col-sm-6 col-md-4 col-lg-4 col-xl-4">
                                            <div class="form-group">
                                                <label class="form-label d-required" for="Schema">Run No</label>
                                                <select class="form-select" (change)="selectIterForLogs(exelog.value)"
                                                    #exelog>
                                                    <option selected disabled>Select Run Number</option>
                                                    @for(list of runNumbersData;track list;){
                                                    <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-hover qmig-table">
                                                <thead>
                                                    <tr>
                                                        <th>S.No</th>
                                                        <th>File Name</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @for (con of LogsData |searchFilter: datachanges2| paginate:{
                                                    itemsPerPage:10,currentPage: p2 ,id:'second'} ;track con) {
                                                    <tr>
                                                        <td>{{ page3*10+$index+1-10 }}</td>
                                                        <td>{{con.fileName }}</td>
                                                        <td>
                                                            <button class="btn btn-download"
                                                                (click)="downloadFile(con)">
                                                                <span class="mdi mdi-cloud-download-outline"></span>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    } @empty {
                                                    <tr>
                                                        <td colspan="4">
                                                            <p class="text-center m-0 w-100">Empty</p>
                                                        </td>
                                                    </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                        <!-- pagination -->
                                        <div class="custom_pagination">
                                            <pagination-controls (pageChange)="p2 = $event" id="second">
                                            </pagination-controls>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





<!-- @if(hide_files){
        <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="custom_search cs-r">
                <span class="mdi mdi-magnify"></span>
                <input type="text" placeholder="Search Files" aria-controls="example">
            </div>
        </div>
    <div class="table-responsive">
        <table class="table table-hover qmig-table" id="example"
            style="width: 100%">
            <thead>
                <tr>
                    <th>Sr.No</th>
                    <th>Folder Name</th>
                    <th>File Name</th>
                     <th>Action</th> 
                </tr>
            </thead>
            <tbody>                
            <tr>
                <td>
                    1
                </td>
                <td> Code </td>
                <td>Source_3_2_2023.zip</td>               
                <td>
                    <button type="button" class="btn btn-upload btn-small"> Extract </button>
                </td>
            </tr>
            <tr>
                <td>
                    2
                </td>
                <td> Code </td>
                <td>DB_16_08_reg_Backup_Till_June1st_extraction_code.zip</td>               
                 <td>
                    <button type="button" class="btn btn-upload btn-small"> Extract </button>
                </td> 
            </tr>
            </tbody>
        </table>
    </div>
</div>
    } -->
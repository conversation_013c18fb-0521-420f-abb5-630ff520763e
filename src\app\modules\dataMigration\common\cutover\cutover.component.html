<div class="v-pageName">{{pageName}}</div>

<!--- Documents List --->
<div class="body-main">
    <div class="col-6 col-md-6 col-xl-6 offset-md-6">
        <div class="body-header-button">
            <button class="btn btn-upload" data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                    class="mdi mdi-account-plus-outline"></span> Add Owner </button>
            <button class="btn btn-sign ms-1" data-bs-toggle="offcanvas" data-bs-target="#addProcess"> <span
                    class="mdi mdi-file-document-plus-outline"></span> Add Process Step </button>
        </div>
    </div>
    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="processTableForm">
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Process Step</label>
                            <select class="form-select" formControlName="processStep">
                                <option selected disabled>Select a Process</option>
                            </select>
                            @if ( pt.processStep.touched && pt.processStep.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (pt.processStep.errors.required) {Process Step is Required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="owner">Owner</label>
                            <select class="form-select" formControlName="owner">
                                <option selected disabled>Select a Process</option>
                            </select>
                            @if ( pt.owner.touched && pt.owner.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (pt.owner.errors.required) {Owner is Required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="status">Status</label>
                            <input type="text" formControlName="status" placeholder="status" class="form-control" />
                            @if ( pt.status.touched && pt.status.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (pt.status.errors.required) {Status is Required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="timetaken">Time Taken</label>
                            <input type="text" formControlName="timetaken" placeholder="timetaken"
                                class="form-control" />
                            @if ( pt.timetaken.touched && pt.timetaken.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (pt.timetaken.errors.required) {Time Taken is Required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="parallel">Sequence/Parallel</label>
                            <input type="text" formControlName="parallel" placeholder="Sequence/Parallel"
                                class="form-control" />
                            @if ( pt.parallel.touched && pt.parallel.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (pt.parallel.errors.required) {Sequence/Parallel is Required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="remarks">Remarks</label>
                            <input type="text" formControlName="remarks" placeholder="Remarks" class="form-control" />
                            @if ( pt.remarks.touched && pt.remarks.invalid) {
                            <p class="text-start text-danger mt-1">
                                @if (pt.remarks.errors.required) {Remarks is Required }
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 pt-4 mt-1 offset-md-3">
                        <button class="btn btn-upload w-100" [disabled]="processTableForm.invalid"> <span
                                class="mdi mdi-file-plus"></span> Trigger Task
                            @if(addprocessTableSpin){<app-spinner />}</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!--- Upload Documnets  --->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Add User</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <form class="form qmig-Form" [formGroup]="addUserForm">
                <div class="form-group">
                    <label class="form-label d-required" for="userName">User Name</label>
                    <input type="text" formControlName="userName" placeholder="Name" class="form-control" />
                    @if ( f.userName.touched && f.userName.invalid) {
                    <p class="text-start text-danger mt-1">
                        @if (f.userName.errors.required) {User Name is Required }
                    </p>
                    }
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="userEmail">User Email</label>
                    <input type="text" formControlName="userEmail" placeholder="Name" class="form-control" />
                    @if ( f.userEmail.touched && f.userEmail.invalid) {
                    <p class="text-start text-danger mt-1">
                        @if (f.userEmail.errors.required) {User Email is Required }
                    </p>
                    }
                </div>
                <div class="form-group">
                    <button class="btn btn-upload w-100" [disabled]="addUserForm.invalid"> <span
                            class="mdi mdi-file-plus"></span> Add User @if(adduserSpin){<app-spinner />}</button>
                </div>
            </form>
        </div>
    </div>

    <!--- Upload Documnets  --->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="addProcess">
        <div class="offcanvas-header">
            <h4 class="main_h">Add Process</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body">
            <form class="form qmig-Form" [formGroup]="addProcessForm">
                <div class="form-group">
                    <label class="form-label d-required" for="processName">Process Name</label>
                    <input type="text" formControlName="processName" placeholder="Name" class="form-control" />
                    @if ( p.processName.touched && p.processName.invalid) {
                    <p class="text-start text-danger mt-1">
                        @if (p.processName.errors.required) {Process Name is Required }
                    </p>
                    }
                </div>
                <div class="form-group">
                    <button class="btn btn-upload w-100" [disabled]="addProcessForm.invalid"> <span
                            class="mdi mdi-file-plus"></span> Add Process @if(addprocessSpin){<app-spinner />}</button>
                </div>
            </form>
        </div>
    </div>
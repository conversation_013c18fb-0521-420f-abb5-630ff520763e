export const dataMigrationAPIConstant = {
    GetConfigFilesFromExpath: 'GetGGPDConfigFiles?configtype=',
    downloadLargeFiles: 'DownloadLargeFile?filePath=',
    SchemaListSelect: 'GetSchemaList?projectId=',
    airflowValidationCommand: 'AirFlowValidarionCommand',
    triggerValidationDags: 'TriggerValidationDags?operation=',
    GetCommonFilesFromDirectory: 'GetCommonFilesFromDirectory?path=',
    GetRunno: 'GetRunNumbers?projectId=',
    GetReqData: 'GetRequestTableData?projectId=',
    getConList: 'GetConnectionList?projectId=',
    PrjSchemasListSelectData: 'PrjSchemasListSelectData?projectId=',
    projectConRunTblInsert: 'RequestTableInsert',
    setRedisCache: 'RedisCacheInsertion',
    tgtSchemaSelect: 'GetPgSchemas?conid=',
    deleteValidationFiles: 'DeleteValidationFiles?fileName=',
    UploadDagFiles: 'UploadDagFiles',
    getSchemas: 'getSchemas?path=',
    dataLoadStatus: 'DataLoadStatus?schemaname=',
    GetConfigData: 'GetConfigData',
    GetDagsExecutionLog: 'GetDagsExecutionLogData?configid=',
    FetchdynamoTables: 'Dynamo/FetchTables?conId=',
    triggerDataValidation: "DataValidation",
    fetchTablesFromDB:"GetTablesFromDB",
    fetchFilesFromDB:"GetFilenamesFromDB?tablename=",
    ReplaceFileName: 'UploadAirflowCmd?fileName=',
    deleteAirflowFiles: 'DeleteAirflowFiles?fileName=',
    getoperation: 'GetOperationList?projectId=',
    GetFilesFromEx: 'GetFilesFromDirectoryEx?path=',
    getExcelDags: 'getExcelDagsNew?path=',
    getggpdDags: 'GetGGPDDags?path=',
    getggpdDagsNew: 'GetGGPDDagsNew?path=',
    getDags: 'GetDagsList',
    GetAirflowDirs: 'GetAirflowDirectories?path=',
    GetAirflowLogs: 'GetAirflowLogs?path=',
    ModifyDagRun: 'UpdatDagRun?dagId=',
    getScnNumber: 'GetScn?ConId=',
    TriggerMultiDagsWithCDC: 'TriggerMultiDagsWithCDC',
    getDagStatus: 'GetDagStatus?dagId=',
    getDagRuns: 'GetDagRuns?dagId=',
    ScheduleDags: 'scheduleDags',
    OracleSchemas: 'Oracle/GetSchema?Conid=',
    GetConfigMenu: 'GetConfigTypes?migid=',
    GGPDCrateConifg: 'GGPDCrateConifg',
    Getpods: 'mixAirPod',
    deletePods: 'deletePods',
    clearTask: 'clearTaskRun?inlcude_downstream=',
    markTaskRun: 'markTaskRun',
    getValidationDags: 'GetValidationDags?fileName=',
    replicationSlot:'GetPgSlotname?conid=',
    getPgTables:'GetPgtables?conid=',
    FetchMongoTables:'Oracle2MongoMigration/ora2mon/FetchMongoTables?conid=',
    getAuditDagsByConnection: 'Common/Migration/GetAuditDagsByConnection?srcId=',
    GetAuditDagsStatus: 'GetAuditDagsStatus?srcId=',
    GetAuditJobTrackStatus: 'GetAuditJobAgentStatus?srcId=',
    deleteJobTrackStatus: 'DeleteJobAgentStatus?srcId=',
    getOracleTables:'ora2pgMigration/ora2pg/FetchSourceTables?conid=',
    getDb2Tables:'db2fabricMigration/db2fab/GetDB2Tables?conid=',
    getSqlTables:'sql2fabricMigration/sql2fab/GetSqlTables?conid=',
    getSqlServerTables:'Sql2OracleMigration/sql2ora/GetSqlServerTables?conid=',
    getFolders:'GetFolders?path=',
    UpdateJsonConfig:'UpdateJsonConfig',
    ReadJsonConfig:'ReadJsonConfig?path=',    
    getConfigData:'GetConfigJobAgentData?srcid=',
    updateAgentStatus:'UpdateAgentStatus'
}
import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { HotToastService } from '@ngxpert/hot-toast';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { ActivatedRoute } from '@angular/router';
import { SpinnerComponent } from "../../../../shared/components/spinner/spinner.component";
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { TestingService } from '../../../../services/testing.service';
import { AssessmentService } from '../../../../services/automation.service';
declare let $: any;
@Component({
  selector: 'app-automation-test',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './automation-test.component.html',
  styles: ``
})
export class AutomationTestComponent {
  datachange1: any;
  pageName: string = '';
  getForm: FormGroup;
  projectId: any;
  migtypeid: any;
  constructor(private titleService: Title, private assessmentService: AssessmentService, private toast: HotToastService, private testing: TestingService, private datamigrationservice: DataMigrationService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.pageName = this.route.snapshot.data['name'];
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.getForm = this.formBuilder.group({
      file: [null, Validators.required]
    });
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getautomationreports();
    this.selectOperation();
  }

  onKey() {

  }
  getSpin: boolean = false;
  selFile: any;
  // fileName: string = ''
  uploadFile() {
    this.getSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', "pyparamfiles/param_" + this.migtypeid);
    //formData.append('option',"True");
    //console.log(formData)
    this.testing.UploadDagFiles(formData).subscribe(
      (response: any) => {
        this.getSpin = false
        //this.fileAdd = false
        // this.getDocuments();
        this.getForm.reset();
        this.fileName = ''
        this.toast.success("File uploaded successfully")
      },
      error => {
        this.getSpin = false
        // this.fileAdd = false
        this.getForm.reset();
        this.fileName = ''
        this.toast.warning("Error uploading file")
      }
    );
  }

  selectFile: any;
  fileName: string = '';
  onFileSelected1(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
  }
  uploadSpin: boolean = false;
  selectedParamFile: string = '';
  TriggerButton() {
    let obj = {
      modules: "",
      mig_path: "ora2pg",
      type: "Test-UI",
      projectId: this.projectId.toString(),
      migration_id: this.migtypeid.toString(),
      paramfile: "",//"param_"+ this.migtypeid//this.selectedParamFile
    }
    this.testing.Triggerbutton(obj).subscribe({
      next: () => {
        this.getSpin = false;
        this.toast.success("Updated Successfully");
        $('#demo').offcanvas('hide');
      },
      error: (err) => {
        console.error('API Error:', err);
        this.getSpin = false;
        this.toast.error('Update failed. Please try again.');
      }
    });
    this.getautomationreports()
  }
  operation: any
  selectOperation() {
    let obj = {
      path : "pyparamfiles/param_" + this.migtypeid
    }
    this.testing.getFilesFromDirectory1(obj).subscribe((data: any) => {
      this.operation = data;

    })
  }
  searchText: string = '';
  pageNumber: number = 1;
  pi: number = 10;
  automationReports: any;
  path: any
  //automationreports
  getautomationreports() {
    this.testing.automationreports().subscribe((data: any) => {
      this.automationReports = data;

    })
  }
  spin_dwld: boolean = false;
  fileResponse: string = '';
  downloadFile(fileInfo: any) {
    this.datamigrationservice.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  deleteResponse: any;
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id,
    };
    this.datamigrationservice.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getautomationreports();
    });
  }
}

import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { AssessmentService } from '../../../../services/assessment.service';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-e2e-migration',
  standalone: true,
  imports: [FormsModule, ReactiveFormsModule,],
  templateUrl: './e2e-migration.component.html',
  styles: ``
})
export class E2eMigrationComponent {

  execProjectForm: any;
  pageName: string = ''
  projectId: string = ""
  constructor(private titleService: Title, private FormBuilder: FormBuilder, private toast: HotToastService, private route: ActivatedRoute,
    private assessmentService: AssessmentService,
  ) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit() {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList()
    this.getObjectTypes()
    this.execProjectForm = this.FormBuilder.group({
      connectionName: ['', Validators.required],
      schema: ['', Validators.required],
      tgtschema: ['', Validators.required],
      targetconnection: ['', [Validators.required]],
      objectType: ['', Validators.required],
    });
  }

  get f() {
    return this.execProjectForm.controls;
  }
  srcConsList: any
  tgtConsList: any
  srcConId:any
  tgtConid:any
  GetConsList() {
    this.assessmentService.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.srcConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }
  srcschemaList: any
  tgtschemaList: any
  getSchemasList(ConnectionId: any, value: any) {
    const obj: any = {
      projectid: this.projectId,
      connectioId: ConnectionId,
    };
    this.assessmentService.SchemaListSelect(obj).subscribe((data: any) => {
      if (value == "0") {
        this.srcConId=ConnectionId
        this.srcschemaList = data['Table1'];
      } else {
        this.tgtConid=ConnectionId
        this.tgtschemaList = data['Table1'];
      }
    });
  }
  objectType:any
  getObjectTypes() {
    const obj = {
      projectId: this.projectId,
      objectgroupname: "Storage_Objects"
    }
    this.assessmentService.getObjectTypes(obj).subscribe((data: any) => {
      this.objectType = data['Table1'];
      //console.log(this.objectType)
    })
  }
  triggerresponse:any
  triggere2eCOmmand(formdata:any)
  {
    let obj={
      srcConId:this.srcConId,
      tgtConId:this.tgtConid,
      projectId:this.projectId.toString(),
      operation:"Extraction",
      schema:formdata.schema,
      targetschema:formdata.tgtschema,
      Object:"Storage_Objects"
    }
    this.assessmentService.TriggerE2eCommand(obj).subscribe((data: any) => {
      this.triggerresponse = data;
      this.toast.success(this.triggerresponse.message)
      this.execProjectForm.reset()
    })
  }
}

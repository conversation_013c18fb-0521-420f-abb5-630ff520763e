import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { AutomationTestComponent } from './common/automation-test/automation-test.component';

const routes: Routes = [
   {path:'', component:LayoutComponent, children:[
      {path:'automationTest', component:AutomationTestComponent,data:{name:'Automation Testing'}},
      
  
    ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AutomationTestingRoutingModule { }

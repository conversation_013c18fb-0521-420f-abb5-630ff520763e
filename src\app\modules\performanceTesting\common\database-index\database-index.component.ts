import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { PerofmanceTestingService } from '../../../../services/performanceTesting.service';
import { ActivatedRoute, Router,RouterLink, RouterOutlet } from '@angular/router';
import * as XLSX from 'xlsx';


declare let $: any;
@Component({
  selector: 'app-database-index',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, RouterOutlet, RouterLink],

  templateUrl: './database-index.component.html',
  styles: ``
})

export class DatabaseIndexComponent {
  perForm:any = this.formBuilder.group({
    tgtconnection: ['', Validators.required],
    index: ['', Validators.required],
    fromDate: ['', Validators.required],
    toDate: ['', Validators.required],
  });

  hidedata: boolean = true;
  data: boolean = false;
  projectId: string;
  ref_spin: boolean = true;
  tabledata: any;
  schemaName: any = [];
  pageNumber: number = 1;
  exe: any;
  contype: string = ""
  dup: string = ""
  mon: string = ""
  tgtId: any
  tgtValue: string = ''
  z: any;
  i: any;
  conName: any
  conId: any;
  ConsList: any;
  tgtList: any
  hideD: boolean = false
  value: any;
  perSchema: any;
  TableData: boolean = false;
  Monspinner: boolean = false;
  excelSpin: boolean = false;
  pageName: string = ''
  indexMonitor: any;
  Monitor: boolean = false;
  testing: any;
  uploadForm: any;
  ConId: any;
  hidedate: boolean = false;
  Monitdate: boolean = false;
  excelSpinn: boolean = false;
  backT: boolean = false;

  constructor(private toast: HotToastService, private performanceservices: PerofmanceTestingService, public formBuilder: FormBuilder, private route: ActivatedRoute, private router: Router,) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    
    
    
  }

  /*--- Page Title ---*/
  pageTitle: string = "Documents"
  pageIcon: string = "assets/images/documents.svg"


  ngOnInit(): void {
    this.GetConsList();
    this.pageName = this.route.snapshot.data['name'];
  }
  

  
  /*--- Validation ---*/
  get f() {
    return this.perForm.controls;
  }

  /*--- Schema   ---*/
  GetperfSchemas() {
    const obj = {
      conId: this.tgtId,
    }
    this.performanceservices.GetperfSchemas(obj).subscribe((data: any) => {
      this.perSchema = data;
    }) 
  }

  /*--- SelectContype   ---*/
  selectContype(value: string) {
    this.contype = value;
    if (value == "0") {
      this.hidedata = true;
      this.data = false;
    }
    else {
      this.hidedata = false;
      this.data = true;
    }
  }

  

  

  /*--- Get Operation List   ---*/
  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
  }

  

  /*--- GetReqTableData   ---*/
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.performanceservices.GetReqData(obj)?.subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.ConsList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_name == "Storage_Objects"
        })
        //console.log(this.tabledata)
      }
    })
  }
  
  ftsFiles: any
  /*--- GetConsList ---*/
  GetConsList() {
    this.performanceservices.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.getreqTableData()
      this.ref_spin = false
    })
  }

    /*--- Select Option ---*/
    selectedOption:string=""
  selectOption(value:any)
  {
  this.selectedOption=value
  if (value == "2") {
    this.hidedate = true;
    this.data = false;
  }
  else {
    this.hidedate = false;
    this.data = true;
  }
  }
  /*--- GetPerfIndexMonitorDate  ---*/
  GetPerfIndexMonitorData(value:any) {

    this.Monitdate = true;
    var obj = {};
    if (this.selectedOption == "2") {
    obj = {
        conId: this.tgtId,
        fromDate:value.fromDate,
        toDate:value.toDate,
        operation:this.selectedOption
    };
    }
    else{
      obj = {
        conId: this.tgtId,
        operation:this.selectedOption
    };
    }
    this.Monspinner = true;
 
    this.performanceservices.GetPerfIndexMonitorData(obj).subscribe((data: any) => {
        this.indexMonitor = data;
       this.indexMonitor = this.indexMonitor.sort((a:any, b:any) => a.indexname.localeCompare(b.indexname));
        this.Monspinner = false;
        
    });
  }


  formatDate(date: string): string{
    let m = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
      , o = new Date(date)
      , S = o.getDate()
      , x = m[o.getMonth()]
      , u = o.getFullYear();
    return `${u}-${x}-${S}`
  }

  /*--- exportExcelMonitor ---*/
  filesName = 'NewlyCreatedIndexes.xlsx';
  exportexcelMonitor(): void {
    this.testing = []
    this.excelSpinn = true;
    var test = this.indexMonitor
    for (var el of test) {
      var newEle: any = {};
      newEle.Type = el.type;
      newEle.SchemaName = el.schemaname;
      //newEle.IndexColumns=el.indexColumns;
      newEle.TableName = el.tablename;
      newEle.IndexName = el.indexname;
      newEle.IndexDefinition = el.indexdefinition;
      newEle.Logdate = el.logdate;
      this.testing.push(newEle);
    }
   
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.filesName);
    this.excelSpinn = false;
  }

  
  HomeReouting()

  {
    this.backT = true;
  }
}
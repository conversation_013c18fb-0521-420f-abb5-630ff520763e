<div class="v-pageName">{{pageName}}</div>

<!-- POD details -->
<div class="body-main">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <div class="row">
                <div class="col-12 col-md-6">
                    <h3 class="main_h pt-3">
                        Pods List
                        <button class="btn btn-sync" (click)="refreshdags()">
                            @if(refresh_spin){
                            <app-spinner />
                            }@else{
                                <span class="mdi mdi-refresh"></span>
                            }
                        </button>
                    </h3>
                </div>
                <div class="col-12 col-md-6">
                    <div class="custom_search me-0">
                        <span class="mdi mdi-magnify"></span>
                        <input type="text" placeholder="Search Pods" class="form-control" [(ngModel)]="searchText"
                            (keyup)="onKey()">
                    </div>
                </div>
            </div>
        </div>
        <!-- Table details --> 
        <div class="row ms-1">
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-3 pe-1">
                        <div class="form-group mb-0">
                            <select class="form-select form-small" (change)="getPodsBasedonTime(time.value)" #time>
                                <option selected value="">Last Age</option>
                                <option value="30mins">Last 30 Mins</option>
                                <option value="1hour">Last 1 Hour</option>
                                <option value="3hour">Last 3 Hour's</option>
                                <option value="6hour">Last 6 Hour's</option>
                                <option value="12hour">Last 12 Hour's</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 px-1">
                        <div class="form-group mb-0">
                            <select class="form-select form-small" (change)="filterPhase($event)">
                                <option selected value="">Dag Status</option>
                                <option value="success">success</option>
                                <option value="running">running</option>
                                <option value="failed">failed</option>
                                <option value="queued">queued</option>
                                <option value="up_for_retry">up_for_retry</option>
                                <option value="skipped">skipped</option>
                                <option value="others">others</option>
                                <option value="all">all</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 px-1">
                        <div class="form-group mb-0">
                            <select class="form-select form-small" (change)="filterPod($event)">
                                <option selected value="">Pod Phase</option>
                                <option value="Succeeded">Succeeded</option>
                                <option value="Running">Running</option>
                                <option value="Failed">Failed</option>
                                <option value="Pending">Pending</option>
                                <option value="Unknown">Unknown</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3 ps-1">
                        <div class="form-group mb-0">
                            <select class="form-select form-small" (change)="filterNode($event)">
                                <option selected value="">Node Name</option>
                                @for(node of nodelist | searchFilter: searchText | paginate: {itemsPerPage:10,currentPage:
                                    p,id:'First'};track node){
                                        <option value={{node}}>{{node}}</option>
                                    }
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-3 pe-1 offset-md-5">
                        <div class="form-group mb-0">
                            <select class="form-select form-small" (change)="actionsPost(val.value)" #val [disabled]="!isCheckBoxSel">
                                <option selected value="">Actions</option>
                                <option value="mark_failed">Mark Failed</option>
                                <option value="mark_success">Mark Success</option>
                                <option value="mark_skipped">Mark Skipped</option>
                                <option value="clear">Clear</option>
                                <option value="clear_with">Clear with downstream</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 ps-1">             
                        <button class="btn btn-sign btn-small w-100 mt-1" [disabled]="!isCheckBoxSel" (click)="openConfirm()">
                            <span class="mdi mdi-delete"></span>
                            Delete Pods </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-responsive mt-3">
            <table class="table table-hover qmig-table" id="example" style="width:100%">
                <thead>
                    <tr>
                        <th style="width: 50px;">
                            <div class="form-check m-0">
                                <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input" (click)="selectAll($event)" [checked]="showCheckStatus">
                                    <i class="input-helper"></i>
                                </label>
                            </div>
                        </th>
                        <th>Dag Name</th>
                        <th>Task ID</th>
                        <th>Dag Status </th>
                        <th>Duration</th>
                        <th>Try Number</th>
                        <th>Host Name</th>
                        <th>Pod Phase</th>
                        <th>Age</th>
                        <th>Node Name</th>
                        <th>Status</th>
                        <th>Events</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- For pagination -->
                    @for(dag of podList | searchFilter: searchText | paginate: {itemsPerPage:10,currentPage:
                    p,id:'First'};track dag; let i= $index){
                    <tr>
                        <td>
                            <div class="form-check mt-33">
                                <label class="form-check-label">
                                    <input type="checkbox" class="form-check-input" (click)="checkboxselect($event)" value="{{i}}" [checked]='dag.isSelected'>
                                    <i class="input-helper"></i>
                                </label>
                            </div>
                        </td>
                        <td>{{dag.taskinstance.dag_id}}</td>
                        <td><span title={{dag.taskinstance.task_id}}>{{dag.taskinstance.task_id | slice:0:15}}</span></td>
                        <td>
                            @if(dag.taskinstance.state=='success'){
                            <span class="badge badge-csuccess">success</span>
                            }@else if(dag.taskinstance.state == 'running'){
                            <span class="badge badge-cwarning">running</span>
                            }@else if(dag.taskinstance.state == 'skipped'){
                            <span class="badge badge-cwarning">skipped</span>
                            }@else if(dag.taskinstance.state == 'queued'){
                            <span class="badge badge-clower">queued</span>
                            }@else{
                            <span class="badge badge-cdanger">{{dag.taskinstance.state}}</span>
                            }
                        </td>
                        <td>{{dag.taskinstance.duration }}</td>
                        <td>{{dag.taskinstance.try_number}}</td>
                        <td><span title={{dag.taskinstance.hostname}}>{{dag.taskinstance.hostname | slice:0:15}}</span></td>
                        <td>{{dag.basicPods == null ? '' : dag.basicPods.phase}}</td>
                        <td>{{dag.basicPods == null ? '' : dag.basicPods.age}}</td>
                        <td>{{dag.basicPods == null ? '' : dag.basicPods.node_name}}</td>
                        <td>{{dag.basicPods == null ? '' : dag.basicPods.status}}</td>
                        <td>{{dag.basicPods == null ? '' : dag.basicPods.events}}</td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="p = $event" id="First"></pagination-controls>
        </div>
    </div>
</div>
<div class='dialog-ovelay fadeIn customConfirm' id="confirmBox">
    <div class='dialog zoomIn'>
        <div class="dialog_h">
            <h3 class="main_h"><span class="mdi mdi-information"></span> Confirm Delete</h3>
        </div>
        <div class='dialog-msg'>
            <p>Are you sure you want to <b>Delete POD's</b></p>
        </div>
        <footer>
            <div class='controls'>
                <button class='btn btn-upload' (click)="closeConfirm()">
                    Cancel
                </button>
                <button class='btn btn-danger mx-3' (click)="deletePOD()">
                    Delete <span class="mdi mdi-delete"></span>
                </button>
            </div>
        </footer>
    </div>
</div>
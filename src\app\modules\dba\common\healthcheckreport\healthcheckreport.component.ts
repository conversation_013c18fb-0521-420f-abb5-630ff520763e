import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DbaService } from '../../../../services/dba.service';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { HotToastService } from '@ngxpert/hot-toast';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { ActivatedRoute } from '@angular/router';
import { sequence } from '@angular/animations';

declare let $: any;

@Component({
  selector: 'app-permissions',
  standalone: true,
  imports: [CommonModule, NgSelectModule, FormsModule, CommonModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './healthcheckreport.component.html',
  styles: ``
})
export class HealthcheckreportComponent {
  Cons:any
  con:any
  roleForm: any
  projectId: any
  ConsList: any
  ConsListForPermissions: any
  tableHide: boolean = false
  pageNumber: number = 1;
  p2: number = 1;
  page1: number = 1;
  pi: number = 10;
  searchText1: string = '';
  fileResponse: any;
  permissionForm: any
  exeForm: any
  disablecheckbox: boolean = true
  spinner_exe: boolean = false
  pageName:string = ''
  

  constructor(private fb: FormBuilder, private dba: DbaService, private toast: HotToastService,private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }
  ngOnInit(): void {

    this.GetConsList(),
    this.permissionForm = this.fb.group({
      tgtCon: ['', [Validators.required]],
      schemaname: ['', Validators.required],
      objName: [''],
      objType: [''],
      usr_rol: [''],
      // category: ['', Validators.required],
      db: ['', Validators.required],
    })
    this.exeForm = this.fb.group({
      db: ['', [Validators.required]],
      tgtCon: ['', [Validators.required]],
    })
  }
  selectedItems = [];
  selectedItems1 = [];
  checkAll = [];
  schemaList: any = [];
  spinner: boolean = false
  get getControl() {
    return this.exeForm.controls;
  }
  get f() {
    return this.permissionForm.controls;

  }
  // Connection details
  GetConsList() {
    this.dba.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      this.ConsListForPermissions = this.ConsList
    });
  }

  // Database details
  
  dblist: any
  connId: any
  Getdbname(conId: string) {
    this.dblist = []
    this.connId = conId
    this.dba.getdbnames(conId).subscribe((data: any) => {
      this.dblist = data
    });
  }

  selectDB(data: any) {
    this.selecteddbname = data.toLowerCase()
  }
  
  
  selectedTgtCon: any
  selecteddbname: any
  getSchemas() {
    let obj = {
      conid: this.connId,
      dbname: this.selecteddbname.toLowerCase()
    }
    this.dba.tgtSchemaSelectwithdb(obj).subscribe((data: any) => {
      this.schemaList = data
      this.schemaList.sort((a:any, b:any) => a.schemaname.localeCompare(b.schemaname));
      this.schemaList.filter((item: any) => {
        item.type = "ALL"
      })
    })
  }
  selectedcategory: string = ""
  selectcategory(value: string) {
    this.selectedcategory = value
  }
  objectlist:any
  selectedschema: string = ""
  selectschema(value: any) {
    var temp: any = []
    if (value.toString() == "ALL") {
      this.selectedschema = ""
      this.schemaList.filter((item: any) => {
        temp.push("'" + item.schemaname.toUpperCase() + "'")
      })
      this.selectedschema = temp.toString()
      //this.GetObjects()
    }
    else if (value.length > 0) {
      value.filter((item: any) => {
        temp.push("'" + item.toLowerCase() + "'")
      })
      this.selectedschema = temp.toString()
      //this.GetObjects()
    }
    else if (value.length == 0) {
      this.objectlist = []
    }

  }
  
  spin_dwld:any
  downloadFile() {
    var filePath="/mnt/eng/AssessmentReports/HealthCheckReport/pgmetrics_sample_report.txt"
    var filename="pgmetrics_sample_report.txt"
    this.dba.downloadLargeFiles(filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName =filename;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
}


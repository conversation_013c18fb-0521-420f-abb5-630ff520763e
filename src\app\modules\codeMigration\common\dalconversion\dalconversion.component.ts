import { Component, OnInit } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { Title } from '@angular/platform-browser';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-dalconversion',
  templateUrl: './dalconversion.component.html',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  styles: ``
})
export class DalconversionComponent implements OnInit {

  pageName: string = ''
  projectId: string = ''
  migtypeid: string = ''

  /*--- Dag File Form ---*/
  getForm = this.formBuilder.group({
    document: ['', [Validators.required]],
  })
  getSpin: boolean = false;
  selFile: any;
  fileName: string = ''

  /*--- Dag File Form ---*/
  dalConvForm = this.formBuilder.group({
    migrationName: ['', [Validators.required]],
    iterationID: ['', [Validators.required]],
  })
  dal_spin: boolean = false;

  /*--- DAL Status ---*/
  ref_spin: boolean = false
  datachange: string = ''
  tabledata: any;
  page1: number = 1;
  iterationselected: any
  runnoForReports: any;


  getRunSpin: boolean = false
  datachange1: string = ''
  assessmentFiles: any;
  page2: number = 1


  datachange2: string = ''
  depLogs: any
  page3: number = 1

  datachange3: string = ''
  ExecututionFiles: any
  page4: number = 1

  datachange4: string = ''
  logdata: any
  page5: number = 1

  iteration_spin:boolean = false
  reports_spin:boolean = false

  constructor(private titleService: Title, private toast: HotToastService, private common: CodeMigrationService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.pageName = this.route.snapshot.data['name'];
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
  }
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getPrjRuninfoSelectAll()
    this.getmigType()
    this.getreqTableData()
    this. GetIndividualLogs("null")
   // this.filterExecutionReports()
    //this.fetchLogs()
  }

  //for validations
  get f(): any { return this.dalConvForm.controls; }

  onKey() {
    this.page1 = 1
    this.page2 = 1
    this.page3 = 1
    this.page4 = 1
    this.page5 = 1
  }

  selectIteration(value: any) {
    this.iterationselected = value
    this.filterExecutionReports()
  }
  logIteration:any
  selectIterationForLogs(value: any) {
    this.logIteration = value
    this.fetchLogs()
  }

  getPrjRuninfoSelectAll() {
    this.iteration_spin = true
    this.reports_spin = true
    this.common.GetRunno(this.projectId).subscribe((data: any) => {
      this.runnoForReports = JSON.parse(JSON.stringify(data['Table1']));
      this.iteration_spin = false
      this.reports_spin = false
      //this.runnoForReports = this.runnoForReports.filter((data: any) => { return ((data.operation_type == "Extraction") && data.iteration != '') })
    });
  }
  migtype: any
  getmigType() {
    this.common.selectmigtype(this.migtypeid).subscribe((data: any) => {
      this.migtype = data['Table1'];//[0].migtype
      //console.log(this.migtype)
    })
  }
  selectFile: any
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
  }
  uploadFile() {
    // let obj = {
    //   projectId: this.projectId,
    //   iteration: "null",
    //   schemaid: "null",
    //   operationid: "37",
    //   objecttypeid: "33",
    //   pattern: "null"
    // }
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('projectId', this.projectId);
    formData.append('iteration', "null");
    formData.append('schemaid', "null");
    formData.append('operationid', "37");
    formData.append('objecttypeid', "33");
    formData.append('pattern', "null");

    this.common.uploadAndUnzip(formData).subscribe((data: any) => {
      var res = data.message
      this.getForm.reset()
      this.toast.success(res)
    })
  }
  triggerDal(formData: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: formData.iterationID,
      jobName: "qmig-convs",
      nameSpace:"qmig-ns8"
    }
    this.common.triggerdal(obj).subscribe((data: any) => {
      var res = data.message
      this.toast.success(res)
    })
  }
   //Get request table data
   z:any
   getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "DAL_Conversion"
    }
    this.ref_spin = true
    this.common.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            if (this.tabledata[this.z].status == "C") {
              this.tabledata[this.z].statusfull = "Completed"
            }
            else if (this.tabledata[this.z].status == "I") {
              this.tabledata[this.z].statusfull = "Initialize"
            }
            else if (this.tabledata[this.z].status == "P") {
              this.tabledata[this.z].statusfull = "Pending"
            }
            else {
  
            }
            
            // for (let i = 0; i < this.conList.length; i++) {
            //   if (this.tabledata[this.z].connection_id == this.conList[i].Connection_ID) {
            //     this.tabledata[this.z].conname = this.conList[i].conname
            //   }

            // }
          }
        }
        else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_name == "Code_Objects"
        })
        //console.log(this.tabledata)
      }
    })
  }
  // delete request table data
  deleteResponse:any
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.common.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData()
    })
  }
  //**package activity log*/
  page:any
  r_id:any
  disabledprevious:any
  GetIndividualLogs(action: any) {
    this.page = 1
    if (action == "null") {
      this.r_id = "null"
    }
    if (action == "previous") {
      this.r_id = this.logdata[0].exelog_id;
    }
    if (action == "next") {
      this.r_id = this.logdata[this.logdata.length - 1].exelog_id;
      this.disabledprevious = false;
    }
    if (action == '') {
      action = 'null'
    }
    const logobj = {
      projectId: this.projectId.toString(),
      operationType: "DAL",
      operationName: "Code_Objects",
      action: 'null',
      row_id: this.r_id,
      runno: action
    }
    this.common.GetIndividualLogs(logobj).subscribe((data: any) => {
      this.logdata = data['Table1'];
    });
  }
  //filter logs
  iterationForLogs:any
  selectIterForLogs(value: any) {
    this.iterationForLogs = value
  }
  filterExecutionReports() {
    var path = "PRJ" + this.projectId + "SRC/"+this.iterationselected+"/Conversion"
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
      // this.ExecututionFiles=this.ExecututionFiles.forEach((item:any)=>{
      //   return item.fileName.includes('.zip')
      // })
    })
  }
  fetchLogs() {
    var path = "PRJ" + this.projectId + "SRC/" + this.logIteration + "/Execution_Logs/Conversion";
    this.common.GetFilesFromDir(path).subscribe((data: any) => {
      this.depLogs = data;
      if (this.depLogs.length == 0) {
        let ob = {
          fileName: "No Files Created"
        }
        this.depLogs.push(ob)
      }
      //console.log(this.depLogs)
    })
  }
   //download files
   fileResponse:any
   spin_dwld:boolean=false
   downloadFile(fileInfo: any) {
    this.common.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
}


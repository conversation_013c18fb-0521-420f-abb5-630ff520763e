import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { TestingService } from '../../../../services/testing.service';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-usertest-cases',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule,SearchFilterPipe],  
  templateUrl: './usertest-cases.component.html',
  styles: ``
})
export class UsertestCasesComponent {

  project_name:string='';
  getRole:string;
  projectId: string; 
  schemaId: number
  batch_id:any;
  fileData: any;
  Spin: boolean = false;
  Add_Spin: boolean = false;
  TestCaseUploadForm: any;
  datachange: any;
  pi: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;
  p: number = 1;
  testCaseNamesResponse: any;
  databases: any = [
    { value: 'S', option: 'Source' },
    { value: 'T', option: 'Target' },
  ];
  testCaseResponse: any;
  SelectedModule: string='';
  selectedBatchId: string='';
  selectedTestCaseId: string='';
  moduleResponse: any
  batchIdResponse: any
  pageNumber: number = 1;
  searchText:string='';

  pageName:string=''

  constructor(private toast: HotToastService,  public formBuilder: FormBuilder,public testingService:TestingService,private route:ActivatedRoute){
    this.project_name = JSON.parse(localStorage.getItem('project_name') || '{}'); 
    this.batch_id=JSON.parse((localStorage.getItem('batch_id') as string)); 
    this.schemaId=JSON.parse(localStorage.getItem('schema_id')as string) 
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') || 'null');
    this.pageName = this.route.snapshot.data['name'];

  }  

  ngOnInit(): void {
    this.TestCaseUploadForm = this.formBuilder.group({
      testcaseId: ['', [Validators.required]],
      batchId: ['', [Validators.required]],
      moduleName: ['', [Validators.required]],
    });
    this.getModules()
  }
  
/* Fetching Batch Ids  */ 
  get f():any{
    return this.TestCaseUploadForm.controls;
  }
   
  getBatchIds(moduleName: any) {
    let obj = {
      projectId:null,
      schemaId: 193 ,//this.schema_id,
      ModuleName: moduleName,
    };
    this.SelectedModule = moduleName;
    this.schemaId = 193;
    this.testingService.getBatchIds(obj).subscribe((data: any) => {
      this.batchIdResponse = data['Table1'];
    })
  }
  

   /* Fetching ModulesList */
  getModules() {
    this.batch_id = 747;
    this.testingService.getModules(this.batch_id).subscribe((data: any) => {
      this.moduleResponse = data['Table1'];
      //console.log(this.moduleResponse)
    })
  }

  /* Assigning selectedTestCaseIds to local variable */
  SelectTid(tid: any) {
    this.selectedTestCaseId = tid
  }

  /*  Fetching TestCase Ids */
  // getTestCaseIds(batchId: any) {
    
  //   const obj:any = {
  //     //projectId:null,
  //     batchId: batchId,
  //   };
  //   this.selectedBatchId = batchId
  //   this.testingService.getTestcases(obj).subscribe((data: any) => {
  //     this.testCaseResponse = data['Table1'];
  //   })
  // }

  getTestCaseIds(batchId: any) {
    this.selectedBatchId = batchId
    this.testingService.getTestcases(batchId).subscribe((data: any) => {
      this.testCaseResponse = data['Table1'];
    })
  }

  /* Fetching TestCaseNames */
  getTestCaseNames() {
    const obj = {
      //projectId: this.projectId.toString(),
      moduleName: this.SelectedModule,
      batchId: this.selectedBatchId,
      testcaseId: this.selectedTestCaseId
    }
    this.testingService.getTestcaseNames(obj).subscribe((data: any) => {
      this.testCaseNamesResponse = data['Table1'];
    })
  }
}

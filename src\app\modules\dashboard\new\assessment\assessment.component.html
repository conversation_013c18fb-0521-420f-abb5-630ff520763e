<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-12">
            <dash-tabs></dash-tabs>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Migration Type</p>
                            <h6 class="mt-1 mb-0">Oracle to Postgres</h6>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Source Version</p>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/chart.svg" alt="db" />
                        </div>
                    </div>
                    <h6 class="mt-1  mb-0">Oracle Database 11 g Enterprise</h6>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Schema</p>
                            <h4 class="mt-1">HR PAY</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/chart1.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Total Migration </p>
                            <h4 class="mt-1">4.5 TB</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/percentage.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Efforts Save</p>
                            <h4 class="mt-1">68.68 %</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/chart1.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-9">
                            <p>Total Saved</p>
                            <h4 class="mt-1">267</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/percentage.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-md-4 pe-1">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="dash-card">
                        <h3 class="main_h">Code Object</h3>
                        <div class="dash-content">
                            <div class="dash-cItem">
                                <div>
                                    <h3>36</h3>
                                    <p>Object Count</p>
                                </div>
                            </div>
                            <div class="dash-cItem">
                                <div>
                                    <h3>164.04</h3>
                                    <p>Manual Estimate Hrs</p>
                                </div>
                            </div>
                            <div class="dash-cItem">
                                <div>
                                    <h3>72.88</h3>
                                    <p>Estimate Hrs with QMigrator</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 px-1">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="dash-card">
                        <h3 class="main_h">Storage Object</h3>
                        <div class="dash-content">
                            <div class="dash-cItem">
                                <div>
                                    <h3>929</h3>
                                    <p>Object Count</p>
                                </div>
                            </div>
                            <div class="dash-cItem">
                                <div>
                                    <h3>224.40</h3>
                                    <p>Manual Estimate Hrs</p>
                                </div>
                            </div>
                            <div class="dash-cItem">
                                <div>
                                    <h3>48.79</h3>
                                    <p>Estimate Hrs with QMigrator</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 ps-1">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <div class="dash-card">
                        <h3 class="main_h">Summary</h3>
                        <div class="dash-content">
                            <div class="dash-cItem">
                                <div>
                                    <h3>964</h3>
                                    <p>Object Count</p>
                                </div>
                            </div>
                            <div class="dash-cItem">
                                <div>
                                    <h3>388.44</h3>
                                    <p>Manual Estimate Hrs</p>
                                </div>
                            </div>
                            <div class="dash-cItem">
                                <div>
                                    <h3>121.67</h3>
                                    <p>Estimate Hrs with QMigrator</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-3 dashCH">
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Code Object Migration Estimate Hours By Object Type</h5>
                    <div style="display: block;">
                        <canvas width="400" height="250" baseChart #chart1="base-chart" [datasets]="cObjectChartData"
                            [labels]="cObjectChartLabels" [options]="cObjectChartOptions" [legend]="cObjectChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Storage Object Migration Estimate Hours By Object Type</h5>
                    <div style="display: block;">
                        <canvas width="400" height="250" baseChart #chart1="base-chart" [datasets]="sObjectChartData"
                            [labels]="sObjectChartLabels" [options]="sObjectChartOptions" [legend]="sObjectChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Migration Estimate Hours By Object Type</h5>
                    <div style="display: block;">
                        <canvas width="400" height="250" baseChart #chart1="base-chart" [datasets]="MigrationChartData"
                            [labels]="MigrationChartLabels" [options]="MigrationChartOptions"
                            [legend]="MigrationChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-3 dashCH">
        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total CPU vs Used Max CPU</h5>
                    <div style="display: block;">
                        <canvas id="totalMaxCPUChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="totalMaxChartData" [options]="totalMaxChartOptions"
                            [legend]="totalMaxChartLegend" [plugins]="totalMaxChartPlugins">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total CPU vs Used Avg CPU</h5>
                    <div style="display: block;">
                        <canvas id="totalAvgCPUChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="totalAvgChartData" [options]="totalAvgChartOptions"
                            [legend]="totalAvgChartLegend" [plugins]="totalAvgChartPlugins">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total Memory vs Used Max Memory</h5>
                    <div style="display: block;">
                        <canvas id="totalMemCPUChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="totalMemChartData" [options]="totalMemChartOptions"
                            [legend]="totalMemChartLegend" [plugins]="totalMemChartPlugins">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Total Memory vs Used Avg Memory</h5>
                    <div style="display: block;">
                        <canvas id="totalMAvgCPUChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="totalMAvgChartData" [options]="totalMAvgChartOptions"
                            [legend]="totalMAvgChartLegend" [plugins]="totalMAvgChartPlugins">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="row  dashCH">
                <div class="col">
                    <h5>1,498.32</h5>
                    <p class="mb-0">IO Request Avg Per Sec</p>
                </div>
                <div class="col">
                    <h5>9,396.60</h5>
                    <p class="mb-0">IO Request Max Per Sec</p>
                </div>
                <div class="col">
                    <h5>1,278.77</h5>
                    <p class="mb-0">Read IO Request Avg Per Sec</p>
                </div>
                <div class="col">
                    <h5>8,178.70</h5>
                    <p class="mb-0">Read IO Request Max Per Sec</p>
                </div>
            </div>
        </div>
    </div>

    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="row  dashCH">
                <div class="col">
                    <h5>117.14</h5>
                    <p class="mb-0">Read Throughput Avg (Mib/s)</p>
                </div>
                <div class="col">
                    <h5>775.40</h5>
                    <p class="mb-0">Read Throughput Max (Mib/s)</p>
                </div>
                <div class="col">
                    <h5>207.11</h5>
                    <p class="mb-0">Throughput Avg (Mib/s)</p>
                </div>
                <div class="col">
                    <h5>1241.40</h5>
                    <p class="mb-0">Throughput Max (Mib/s)</p>
                </div>
            </div>
        </div>
    </div>

    <div class="qmig-card mt-3">
        <div class="qmig-card-body">
            <div class="row  dashCH">
                <div class="col">
                    <h5>219.55</h5>
                    <p class="mb-0">Write IO Request Avg Per Sec</p>
                </div>
                <div class="col">
                    <h5>1217.90</h5>
                    <p class="mb-0">Write IO Request Max Per Sec</p>
                </div>
                <div class="col">
                    <h5>89.97</h5>
                    <p class="mb-0">Write Throughput Avg (Mib/s)</p>
                </div>
                <div class="col">
                    <h5>466</h5>
                    <p class="mb-0">Write Throughput Max (Mib/s)</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3 dashCH">
        <div class="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-6">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>During Migration Estimates for Kotak (Monthly)</h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart [datasets]="sourceVSChartData"
                            [labels]="sourceVSChartLabels" [options]="sourceVSChartOptions" [legend]="sourceVSChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-6">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Post Migration Azure price Estimates for Kotak (Monthly)</h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart #chart1="base-chart" [datasets]="longRunningChartData"
                            [labels]="longRunningChartLabels" [options]="longRunningChartOptions" [legend]="longRunningChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="qmig-card mt-3 dashCH">
        <div class="qmig-card-body">
            <h5>Migration Timelines</h5>
            <div style="display: block;">
                <canvas width="600" height="400" baseChart [datasets]="migrationTimelineChartData"
                    [labels]="migrationTimelineChartLabels" [options]="migrationTimelineChartOptions" [legend]="migrationTimelineChartLegend">
                </canvas>
            </div>
        </div>
    </div>
</section>
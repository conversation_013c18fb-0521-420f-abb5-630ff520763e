<div class="v-pageName">{{pageName}}</div>

<!--- Bread Crumb --->
<div class="qmig-card mt-3">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse1" aria-expanded="true" aria-controls="flush-collapse">
                    D<PERSON> Script
                </button>
            </h2>
            <div id="flush-collapse1" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="DBAInsertForm">
                            <div class="row">
                                <div class="col-md-3 ">
                                    <div class="form-group">
                                        <label class="form-label d-required">Category Name</label>
                                        <select formControlName="categoryn1" #cate  class="form-select" (change)="categorySelect(cate.value)">
                                            <option value="" disabled selected>Select Category Name</option>
                                            @for(lists of DBAtaskDataWithNew;track lists; ){
                                            <option value="{{ lists.category }}"> {{ lists.category }} </option>
                                            }
                                        </select>
                                        @if ( validate.categoryn1.touched && validate.categoryn1.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (validate.categoryn1.errors?.['required']) { Category Name is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                @if(DBAInsertForm.value.categoryn1 == "New" ){
                                <div class="col-md-4 " >
                                    <div class="form-group">
                                        <label class="form-label d-required">Add Category Name</label>
                                        <input type="text" placeholder="Category Name" class="form-control"
                                            id="txtfunction" name="txtcategory" formControlName="categoryname">
                                        @if ( validate.categoryname.touched && validate.categoryname.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (validate.categoryname.errors?.['required']) { Categoryname Name is
                                            required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            }
                                <div class="col-md-4 ">
                                    <div class="form-group">
                                        <label class="form-label d-required">Name</label>
                                        <input type="text" placeholder="Name" class="form-control" id="txtfunction"
                                            name="txtfunction" formControlName="scriptfunctionname">
                                        @if ( validate.scriptfunctionname.touched &&
                                        validate.scriptfunctionname.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (validate.scriptfunctionname.errors?.['required']) { Function Name is
                                            required }
                                        </p>
                                        }
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label d-required">Script</label>
                                        <textarea class="form-control" id="txtscript" name="txtscript"
                                            formControlName="scriptquery" placeholder="Queries" rows="15"
                                            cols="30"></textarea>
                                        @if ( validate.scriptquery.touched && validate.scriptquery.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (validate.scriptquery.errors?.['required']) { Script is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-md-3 offset-md-9">
                                    <button class="btn btn-sign w-100" data-bs-toggle="offcanvas"
                                        (click)="DBAScriptInsertion(DBAInsertForm.value)"
                                        [disabled]="DBAInsertForm.invalid">save
                                        &nbsp;
                                        @if(add_spin){<app-spinner />}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="headingthree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapsethree" aria-expanded="false" aria-controls="collapsethree">
                    Execution
                </button>
            </h2>
            <div id="collapsethree" class="accordion-collapse collapse" aria-labelledby="headingthree"
                data-bs-parent="#accordionExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="DBAExecuteForm">
                            <div class="row">
                                <div class="col-md-4 ">
                                    <div class="form-group">
                                        <label class="form-label d-required">Connection Name</label>
                                        <select formControlName="tgtconnection" #Myselect2 (change)="
                          selTgtId(Myselect2.value)" class="form-select">
                                            <option value="" disabled selected>Select Connection Name</option>
                                            @for(list of tgtList;track list; ){
                                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                            }
                                        </select>
                                        @if ( validates.tgtconnection.touched && validates.tgtconnection.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (validates.tgtconnection.errors?.['required']) { Connection Name is
                                            required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-md-4 ">
                                    <div class="form-group">
                                        <label class="form-label d-required">Category Name</label>
                                        <select formControlName="categoryn" #Myselect3 (change)="
                        selCategory(Myselect3.value)" class="form-select">
                                            <option value="" disabled selected>Select Category Name</option>
                                            @for(lists of DBAtaskData;track lists; ){
                                            <option value="{{ lists.category }}"> {{ lists.category }} </option>
                                            }
                                        </select>
                                        @if ( validates.categoryn.touched && validates.categoryn.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (validates.categoryn.errors?.['required']) { Category Name is required }
                                        </p>
                                        }
                                    </div>
                                </div>

                            </div>
                        </form>
                    </div>
                    <div [hidden]="CategoryLists?.length <= 0">
                        <div class="row">
                            <hr class="dash-dotted" />
                            <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3">
                            </div>
                            <div class="col-12 col-sm-6 col-md-6">
                                <div class="custom_search cs-r my-3 me-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search Name" class="form-control"
                                        [(ngModel)]="searchText" (keyup)="onKey()">
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3 pe-3 mt-3 mb-3"
                                [hidden]="!executeBtn">
                                <button class="btn btn-upload w-100" (click)="insertScripts()">
                                    <span class="mdi mdi-arrow-up-thin"></span>Execute
                                    @if(queryspin){<app-spinner />}
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive mt-3">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>S.No</th>
                                        <th>Name</th>
                                        <th>
                                            <div class="form-check">
                                                <input type="checkbox" [(ngModel)]="selectAll" class="form-check-input"
                                                    (change)="toggleAllSelection();currentlyChecked($event)" />
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (con of CategoryLists | searchFilter: searchText
                                    | paginate: {
                                    itemsPerPage: 10, currentPage: pageNumber2 } ; track con; )
                                    {
                                    <tr>
                                        <td>{{p1*10+$index+1-10}}</td>
                                        <td>{{con.name}}</td>
                                        <td>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input"
                                                    [(ngModel)]="con.isSelected"
                                                    (change)="onCheckboxChange(con);currentlyChecked($event)"
                                                    [value]="con.script" />
                                               
                                            </div>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="8">
                                            <p class="text-center m-0 w-100">Empty list </p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pageNumber2 = $event"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingtwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapsetwo" aria-expanded="false" aria-controls="collapsetwo">
                    Reports
                </button>
            </h2>
            <div id="collapsetwo" class="accordion-collapse collapse" aria-labelledby="headingtwo"
                data-bs-parent="#accordionExample">
                <div class="accordion-body">
                    <form [formGroup]="DBAReports">
                    <div class="row">
                        <div class="col-md-3 ">
                            <div class="form-group">
                                <label class="form-label d-required">Connection Name</label>
                                <select formControlName="tgtconnection" #con (change)="
                                selectTGTCon(con.value)" class="form-select">
                                    <option value="" disabled selected>Select Connection Name</option>
                                    @for(list of tgtList;track list; ){
                                    <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                    }
                                </select>
                                @if ( validates1.tgtconnection.touched && validates1.tgtconnection.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (validates1.tgtconnection.errors?.['required']) { Connection Name is
                                    required }
                                </p>
                                }
                            </div>
                        </div>
                        <div class="col-md-3 ">
                            <div class="form-group">
                                <label class="form-label d-required">Category Name</label>
                                <select formControlName="categoryn" #cate1 (change)="
                                selctCategoryName(cate1.value);DBACategoryList()" class="form-select">
                                    <option value="" disabled selected>Select Category Name</option>
                                    @for(lists of DBAtaskData;track lists; ){
                                    <option value="{{ lists.category }}"> {{ lists.category }} </option>
                                    }
                                </select>
                                @if ( validates1.categoryn.touched && validates1.categoryn.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (validates1.categoryn.errors?.['required']) { Category Name is required }
                                </p>
                                }
                            </div>
                        </div>
                        <div class="col-md-3 ">
                            <div class="form-group">
                                <label class="form-label d-required">Script Name</label>
                                <select formControlName="scriptquery" #scr (change)="
                                selectTaskName(scr.value);GetDBARunIds()" class="form-select">
                                    <option value="" disabled selected>Select Category Name</option>
                                    @for(lists of CategoryLists;track lists; ){
                                    <option value="{{ lists.name }}"> {{ lists.name }} </option>
                                    }
                                </select>
                                @if ( validates1.scriptquery.touched && validates1.scriptquery.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (validates1.scriptquery.errors?.['required']) { Script Name is required }
                                </p>
                                }
                            </div>
                        </div>
                    
                        <div class="col-md-3 ">
                            <div class="form-group">
                                <label class="form-label d-required">Run Number</label>
                                <select formControlName="runnumber" class="form-select" #run
                                    (change)="GetDBAExecResults(run.value)">
                                    <option value="" disabled selected>Select Run Number</option>
                                    @for(list of dbarunids;track list; ){
                                    <option value="{{ list.run_no }}"> {{ list.run_no }} </option>
                                    }
                                </select>
                                @if ( validates1.runnumber.touched && validates1.runnumber.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (validates1.runnumber.errors?.['required']) { Run Number Name is required }
                                </p>
                                }
                            </div>
                        </div>
                       
                        <div class="col-md-4 mt-4 pe-4" [hidden]="!hidelabels">
                            <div class="form-group">
                                <p class="m-0">Executed Date : <b>{{executedDate}}</b>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6" ></div>
                        <div class="col-md-2 mt3" [hidden]="!hidelabels">
                            <button class="btn btn-sign w-100 mb-3" (click)="exportexcel()" type="button"><i
                                    class="mdi mdi-cloud-upload-outline btn-icon-prepend"></i>&nbsp; Export
                                Excel </button>
                        </div>

                    </div>
                </form>

                    <div class="table-responsive" id="tblexample">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Si.No</th>
                                    @for(key of keys ;track key){
                                    <th>{{key}}</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @for(exec of jobj |searchFilter:
                                datachange4|paginate:{
                                itemsPerPage: 10, currentPage: p3, id:'fourth'};
                                track exec; let i=$index){
                                <tr>
                                    <td>{{10*p3+i+1-10}}</td>
                                    @for(col of keys;track col){
                                    <td>{{exec[col]}}</td>
                                    }
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Reports</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p3 = $event" id="fourth"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
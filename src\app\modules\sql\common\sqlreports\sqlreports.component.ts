import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HotToastService } from '@ngxpert/hot-toast';
import { SqlService } from '../../../../services/sqlService.service';
import { CommonService } from '../../../../services/common.service';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import html2canvas from 'html2canvas';
import jspdf from 'jspdf';

@Component({
  selector: 'app-sqlreports',
  standalone: true,
  imports: [FormsModule,CommonModule,ReactiveFormsModule],
  templateUrl: './sqlreports.component.html',
  styles: ``
})

export class SqlreportsComponent {
  asessment_id: any;
  getRole: any;
  ConsList: any;
  project_name: any;
  projectId: any;
  categoryData: any = [];
  iterationData: any = [];
  selectedIteration: any;
  dbConnName: any;
  targetData: any = [];
  pdfspin: boolean = false;
  prjSrcTgt: any = {};
  tablecount: any;
  add_spin: boolean = false;
  sqlresult: any = [];
  sqlresult1: any = [];
  sqlresult2: any = [];
  sqlresult3: any = [];
  sqlresult4: any = [];
  sqlresult5: any = [];
  sqlresult6: any = [];
  sqlresult7: any = [];
  sqlresult8: any = [];
  sqlresult9: any = [];
  sqlresult10: any = [];
  sqlresult11: any = [];

  sqlresult12: any = [];
  sqlresult13: any = [];
  sqlresult14: any = [];
  sqlresult15: any = [];
  sqlresult16: any = [];
  sqlresult17: any = [];
  sqlresult18: any = [];

  sqlresult19: any = [];
  sqlresult20: any = [];

  sqlresult21: any = [];
  sqlresult22: any = [];
  sqlresult23: any = [];
  sqlresult24: any = [];
  sqlresult25: any = [];
  sqlresult26: any = [];
  sqlresult27: any = [];
  sqlresult28: any = [];
  sqlresult29: any = [];
  sqlresult30: any = [];

  sqlresult31: any = [];
  sqlresult32: any = [];
  sqlresult33: any = [];
  sqlresult34: any = [];
  sqlresult35: any = [];
  sqlresult36: any = [];
  sqlresult37: any = [];
  sqlresult38: any = [];
  sqlresult39: any = [];
  sqlresult40: any = [];

  sqlresult41: any = [];
  sqlresult42: any = [];
  sqlresult43: any = [];
  sqlresult44: any = [];
  sqlresult45: any = [];
  sqlresult46: any = [];
  sqlresult47: any = [];
  sqlresult48: any = [];
  sqlresult49: any = [];
  sqlresult50: any = [];

  sqlHeader: any = [];
  sqlHeader1: any = [];
  sqlHeader2: any = [];
  sqlHeader3: any = [];
  sqlHeader4: any = [];
  sqlHeader5: any = [];
  sqlHeader6: any = [];
  sqlHeader7: any = [];
  sqlHeader8: any = [];
  sqlHeader9: any = [];
  sqlHeader10: any = [];
  sqlHeader11: any = [];
  sqlHeader12: any = [];
  sqlHeader13: any = [];
  sqlHeader14: any = [];
  sqlHeader15: any = [];
  sqlHeader16: any = [];
  sqlHeader17: any = [];
  sqlHeader18: any = [];
  sqlHeader19: any = [];
  sqlHeader20: any = [];
  sqlHeader21: any = [];
  sqlHeader22: any = [];
  sqlHeader23: any = [];
  sqlHeader24: any = [];
  sqlHeader25: any = [];
  sqlHeader26: any = [];
  sqlHeader27: any = [];
  sqlHeader28: any = [];
  sqlHeader29: any = [];
  sqlHeader30: any = [];
  sqlHeader31: any = [];
  sqlHeader32: any = [];
  sqlHeader33: any = [];
  sqlHeader34: any = [];
  sqlHeader35: any = [];
  sqlHeader36: any = [];
  sqlHeader37: any = [];
  sqlHeader38: any = [];
  sqlHeader39: any = [];
  sqlHeader40: any = [];
  sqlHeader41: any = [];
  sqlHeader42: any = [];
  sqlHeader43: any = [];
  sqlHeader44: any = [];
  sqlHeader45: any = [];
  sqlHeader46: any = [];
  sqlHeader47: any = [];
  sqlHeader48: any = [];
  sqlHeader49: any = [];
  sqlHeader50: any = [];

  sqlTitle: any = [];
  sqlTitle1: any = [];
  sqlTitle2: any = [];
  sqlTitle3: any = [];
  sqlTitle4: any = [];
  sqlTitle5: any = [];
  sqlTitle6: any = [];
  sqlTitle7: any = [];
  sqlTitle8: any = [];
  sqlTitle9: any = [];
  sqlTitle10: any = [];
  sqlTitle11: any = [];
  sqlTitle12: any = [];
  sqlTitle13: any = [];
  sqlTitle14: any = [];
  sqlTitle15: any = [];
  sqlTitle16: any = [];
  sqlTitle17: any = [];
  sqlTitle18: any = [];
  sqlTitle19: any = [];
  sqlTitle20: any = [];
  sqlTitle21: any = [];
  sqlTitle22: any = [];
  sqlTitle23: any = [];
  sqlTitle24: any = [];
  sqlTitle25: any = [];
  sqlTitle26: any = [];
  sqlTitle27: any = [];
  sqlTitle28: any = [];
  sqlTitle29: any = [];
  sqlTitle30: any = [];
  sqlTitle31: any = [];
  sqlTitle32: any = [];
  sqlTitle33: any = [];
  sqlTitle34: any = [];
  sqlTitle35: any = [];
  sqlTitle36: any = [];
  sqlTitle37: any = [];
  sqlTitle38: any = [];
  sqlTitle39: any = [];
  sqlTitle40: any = [];
  sqlTitle41: any = [];
  sqlTitle42: any = [];
  sqlTitle43: any = [];
  sqlTitle44: any = [];
  sqlTitle45: any = [];
  sqlTitle46: any = [];
  sqlTitle47: any = [];
  sqlTitle48: any = [];
  sqlTitle49: any = [];
  sqlTitle50: any = [];


  showTable1: boolean = true;
  showTable2: boolean = true;
  showTable3: boolean = true;
  showTable4: boolean = true;
  showTable5: boolean = true;
  showTable6: boolean = true;
  showTable7: boolean = true;
  showTable8: boolean = true;
  showTable9: boolean = true;
  showTable10: boolean = true;
  showTable11: boolean = true;
  showTable12: boolean = true;
  showTable13: boolean = true;
  showTable14: boolean = true;
  showTable15: boolean = true;
  showTable16: boolean = true;
  showTable17: boolean = true;
  showTable18: boolean = true;
  showTable19: boolean = true;
  showTable20: boolean = true;
  showTable21: boolean = true;
  showTable22: boolean = true;
  showTable23: boolean = true;
  showTable24: boolean = true;
  showTable25: boolean = true;
  showTable26: boolean = true;
  showTable27: boolean = true;
  showTable28: boolean = true;
  showTable29: boolean = true;
  showTable30: boolean = true;
  showTable31: boolean = true;
  showTable32: boolean = true;
  showTable33: boolean = true;
  showTable34: boolean = true;
  showTable35: boolean = true;
  showTable36: boolean = true;
  showTable37: boolean = true;
  showTable38: boolean = true;
  showTable39: boolean = true;
  showTable40: boolean = true;
  showTable41: boolean = true;
  showTable42: boolean = true;
  showTable43: boolean = true;
  showTable44: boolean = true;
  showTable45: boolean = true;
  showTable46: boolean = true;
  showTable47: boolean = true;
  showTable48: boolean = true;
  showTable49: boolean = true;
  showTable50: boolean = true;
  showTable51: boolean = true;
  selectedCategoryName: string = "";
  sqlresults: any[] = [];
  sqlHeaders: any[] = [];
  sqlTitles: any[] = [];
  showTables: boolean[] = [];
  
  enableExcel:boolean=true
  constructor(private titleService: Title,private toast: HotToastService, 
                                                   
     private project: SqlService,
    private common: CommonService,
    private route: ActivatedRoute,
     public formBuilder: FormBuilder) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.asessment_id = JSON.parse((localStorage.getItem('assessment_id') as string));
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    this.project_name = localStorage.getItem('project_name');
  }
  ngOnInit(): void {
//this.titleService.setTitle(`QMigrator - ${this.pageName}`);
  
    this.getProjectSrctgtconfSelect();
    this.getCategorySelect();
    this.getIterationSelect();
  }
  getIterationSelect() {
    const req = {
      projectId: this.projectId.toString()
    };
    this.project.PrjIterationSelect(req).subscribe((data) => {
      const obj = data['Table1'];
      this.iterationData = obj;
      this.selectedIteration = (obj[0]['iteration']).toString();
    });
  }
  getProjectSrctgtconfSelect() {
    this.prjSrcTgt.projectId = this.projectId.toString();
    this.prjSrcTgt.migsrcType = 'D';
    this.prjSrcTgt.connectionName = 'null';
    this.project.ProjectSrctgtconfSelect(this.prjSrcTgt).subscribe((data) => {
      this.targetData = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.migsrctype == 'D';
      });
    });
  }

  ddlCategorycheckedChnaged(value: any) {
    this.selectedCategoryName = value.toString();
    if(this.selectedCategoryName=="Features not supported in AWS RDS")
    {
      this.enableExcel=false
    }
    this.getIterationByCategorySelect(value);
  }
  getIterationByCategorySelect(value: any) {
    const req = {
      projectId: this.projectId.toString(),
      categoryName: value.toString()
    };
    this.project.PrjIterationByCategorySelect(req).subscribe((data) => {
      const obj = data['Table1'];
      this.iterationData = obj;
      this.selectedIteration = (obj[0]['iteration']).toString();
    });
  }
 
  getCategorySelect() {
    const req = {
      projectId: this.projectId.toString()
    };
    this.project.PrjSqlScriptCategorySelect(req).subscribe((data) => {
      this.categoryData = data['Table1'];
    });
  }
  ddlIterationcheckedChnaged(value: any) {
    this.selectedIteration = value;
  }

  getSqlData() {
    const data = {
      projectId: this.projectId.toString(),
      iteration: this.selectedIteration.toString()
    };
    this.project.PrjSqlScriptcycleSelectwithIteration(data).subscribe((resdata: any) => {
      if (resdata) {

        this.tablecount = parseInt(resdata['TableCount'][0]['TableCount']);
        this.sqlresult = resdata['Table0'];
        this.showTable1 = false;
        this.sqlHeader = resdata['Table0'][0];
        this.sqlTitle = (resdata['TableHeader'][0]['FunctionName']).toString();
        if (this.tablecount >= 1) {
          this.sqlresult1 = resdata['Table1'];
          this.sqlHeader1 = resdata['Table1'][0];
          this.showTable2 = false;
          this.sqlTitle1 = (resdata['TableHeader'][1]['FunctionName']).toString();
        }
        if (this.tablecount >= 2) {
          this.sqlresult2 = resdata['Table2'];
          this.sqlHeader2 = resdata['Table2'][0];
          this.showTable3 = false;
          this.sqlTitle2 = (resdata['TableHeader'][2]['FunctionName']).toString();
        }
        if (this.tablecount >= 3) {
          this.sqlresult3 = resdata['Table3'];
          this.sqlHeader3 = resdata['Table3'][0];
          this.showTable4 = false;
          this.sqlTitle3 = (resdata['TableHeader'][3]['FunctionName']).toString();
        }
        if (this.tablecount >= 4) {
          this.sqlresult4 = resdata['Table4'];
          this.sqlHeader4 = resdata['Table4'][0];
          this.showTable5 = false;
          this.sqlTitle4 = (resdata['TableHeader'][4]['FunctionName']).toString();
        }
        if (this.tablecount >= 5) {
          this.sqlresult5 = resdata['Table5'];
          this.sqlHeader5 = resdata['Table5'][0];
          this.showTable6 = false;
          this.sqlTitle5 = (resdata['TableHeader'][5]['FunctionName']).toString();
        }
        if (this.tablecount >= 6) {
          this.sqlresult6 = resdata['Table6'];
          this.sqlHeader6 = resdata['Table6'][0];
          this.showTable7 = false;
          this.sqlTitle6 = (resdata['TableHeader'][6]['FunctionName']).toString();
        }
        if (this.tablecount >= 7) {
          this.sqlresult7 = resdata['Table7'];
          this.sqlHeader7 = resdata['Table7'][0];
          this.showTable8 = false;
          this.sqlTitle7 = (resdata['TableHeader'][7]['FunctionName']).toString();
        }
        if (this.tablecount >= 8) {
          this.sqlresult8 = resdata['Table8'];
          this.sqlHeader8 = resdata['Table8'][0];
          this.showTable9 = false;
          this.sqlTitle8 = (resdata['TableHeader'][8]['FunctionName']).toString();
        }
        if (this.tablecount >= 9) {
          this.sqlresult9 = resdata['Table9'];
          this.sqlHeader9 = resdata['Table9'][0];
          this.showTable10 = false;
          this.sqlTitle9 = (resdata['TableHeader'][9]['FunctionName']).toString();
        }
        if (this.tablecount >= 10) {
          this.sqlresult10 = resdata['Table10'];
          this.sqlHeader10 = resdata['Table10'][0];
          this.showTable11 = false;
          this.sqlTitle10 = (resdata['TableHeader'][10]['FunctionName']).toString();
        }

        if (this.tablecount >= 11) {
          this.sqlresult11 = resdata['Table11'];
          this.sqlHeader11 = resdata['Table11'][0];
          this.showTable12 = false;
          this.sqlTitle11 = (resdata['TableHeader'][11]['FunctionName']).toString();
        }
        if (this.tablecount >= 12) {
          this.sqlresult12 = resdata['Table12'];
          this.sqlHeader12 = resdata['Table12'][0];
          this.showTable13 = false;
          this.sqlTitle12 = (resdata['TableHeader'][12]['FunctionName']).toString();
        }
        if (this.tablecount >= 13) {
          this.sqlresult13 = resdata['Table13'];
          this.sqlHeader13 = resdata['Table13'][0];
          this.showTable14 = false;
          this.sqlTitle13 = (resdata['TableHeader'][13]['FunctionName']).toString();
        }
        if (this.tablecount >= 14) {
          this.sqlresult14 = resdata['Table14'];
          this.sqlHeader14 = resdata['Table14'][0];
          this.showTable15 = false;
          this.sqlTitle14 = (resdata['TableHeader'][14]['FunctionName']).toString();
        }

        if (this.tablecount >= 15) {
          this.sqlresult15 = resdata['Table15'];
          this.sqlHeader15 = resdata['Table15'][0];
          this.showTable16 = false;
          this.sqlTitle15 = (resdata['TableHeader'][15]['FunctionName']).toString();
        }
        if (this.tablecount >= 16) {
          this.sqlresult16 = resdata['Table16'];
          this.sqlHeader16 = resdata['Table16'][0];
          this.showTable17 = false;
          this.sqlTitle16 = (resdata['TableHeader'][16]['FunctionName']).toString();
        }
        if (this.tablecount >= 17) {
          this.sqlresult17 = resdata['Table17'];
          this.sqlHeader17 = resdata['Table17'][0];
          this.showTable18 = false;
          this.sqlTitle17 = (resdata['TableHeader'][17]['FunctionName']).toString();
        }

        if (this.tablecount >= 18) {
          this.sqlresult18 = resdata['Table18'];
          this.sqlHeader18 = resdata['Table18'][0];
          this.showTable19 = false;
          this.sqlTitle18 = (resdata['TableHeader'][18]['FunctionName']).toString();
        }
        if (this.tablecount >= 19) {
          this.sqlresult19 = resdata['Table19'];
          this.sqlHeader19 = resdata['Table19'][0];
          this.showTable20 = false;
          this.sqlTitle19 = (resdata['TableHeader'][19]['FunctionName']).toString();
        }
        if (this.tablecount >= 20) {
          this.sqlresult20 = resdata['Table20'];
          this.sqlHeader20 = resdata['Table20'][0];
          this.showTable21 = false;
          this.sqlTitle20 = (resdata['TableHeader'][20]['FunctionName']).toString();
        }
        if (this.tablecount >= 21) {
          this.sqlresult21 = resdata['Table21'];
          this.sqlHeader21 = resdata['Table21'][0];
          this.showTable22 = false;
          this.sqlTitle21 = (resdata['TableHeader'][21]['FunctionName']).toString();
        }
        if (this.tablecount >= 22) {
          this.sqlresult22 = resdata['Table22'];
          this.sqlHeader22 = resdata['Table22'][0];
          this.showTable23 = false;
          this.sqlTitle22 = (resdata['TableHeader'][22]['FunctionName']).toString();
        }

        if (this.tablecount >= 23) {
          this.sqlresult23 = resdata['Table23'];
          this.sqlHeader23 = resdata['Table23'][0];
          this.showTable24 = false;
          this.sqlTitle23 = (resdata['TableHeader'][23]['FunctionName']).toString();
        }
        if (this.tablecount >= 24) {
          this.sqlresult24 = resdata['Table24'];
          this.sqlHeader24 = resdata['Table24'][0];
          this.showTable25 = false;
          this.sqlTitle24 = (resdata['TableHeader'][24]['FunctionName']).toString();
        }
        if (this.tablecount >= 25) {
          this.sqlresult25 = resdata['Table25'];
          this.sqlHeader25 = resdata['Table25'][0];
          this.showTable26 = false;
          this.sqlTitle25 = (resdata['TableHeader'][25]['FunctionName']).toString();
        }

        if (this.tablecount >= 26) {
          this.sqlresult26 = resdata['Table26'];
          this.sqlHeader26 = resdata['Table26'][0];
          this.showTable27 = false;
          this.sqlTitle26 = (resdata['TableHeader'][26]['FunctionName']).toString();
        }
        if (this.tablecount >= 27) {
          this.sqlresult27 = resdata['Table27'];
          this.sqlHeader27 = resdata['Table27'][0];
          this.showTable28 = false;
          this.sqlTitle27 = (resdata['TableHeader'][27]['FunctionName']).toString();
        }
        if (this.tablecount >= 28) {
          this.sqlresult28 = resdata['Table28'];
          this.sqlHeader28 = resdata['Table28'][0];
          this.showTable29 = false;
          this.sqlTitle28 = (resdata['TableHeader'][28]['FunctionName']).toString();
        }

        if (this.tablecount >= 29) {
          this.sqlresult29 = resdata['Table29'];
          this.sqlHeader29 = resdata['Table29'][0];
          this.showTable30 = false;
          this.sqlTitle29 = (resdata['TableHeader'][29]['FunctionName']).toString();
        }
        if (this.tablecount >= 30) {
          this.sqlresult30 = resdata['Table30'];
          this.sqlHeader30 = resdata['Table30'][0];
          this.showTable31 = false;
          this.sqlTitle30 = (resdata['TableHeader'][30]['FunctionName']).toString();
        }
        if (this.tablecount >= 31) {
          this.sqlresult31 = resdata['Table31'];
          this.sqlHeader31 = resdata['Table31'][0];
          this.showTable32 = false;
          this.sqlTitle31 = (resdata['TableHeader'][31]['FunctionName']).toString();
        }
        if (this.tablecount >= 32) {
          this.sqlresult32 = resdata['Table32'];
          this.sqlHeader32 = resdata['Table32'][0];
          this.showTable33 = false;
          this.sqlTitle32 = (resdata['TableHeader'][32]['FunctionName']).toString();
        }
        if (this.tablecount >= 33) {
          this.sqlresult33 = resdata['Table33'];
          this.sqlHeader33 = resdata['Table33'][0];
          this.showTable34 = false;
          this.sqlTitle33 = (resdata['TableHeader'][33]['FunctionName']).toString();
        }
        if (this.tablecount >= 34) {
          this.sqlresult34 = resdata['Table34'];
          this.sqlHeader34 = resdata['Table34'][0];
          this.showTable35 = false;
          this.sqlTitle34 = (resdata['TableHeader'][34]['FunctionName']).toString();
        }

        if (this.tablecount >= 35) {
          this.sqlresult35 = resdata['Table35'];
          this.sqlHeader35 = resdata['Table35'][0];
          this.showTable36 = false;
          this.sqlTitle35 = (resdata['TableHeader'][35]['FunctionName']).toString();
        }
        if (this.tablecount >= 36) {
          this.sqlresult36 = resdata['Table36'];
          this.sqlHeader36 = resdata['Table36'][0];
          this.showTable37 = false;
          this.sqlTitle36 = (resdata['TableHeader'][36]['FunctionName']).toString();
        }
        if (this.tablecount >= 37) {
          this.sqlresult37 = resdata['Table37'];
          this.sqlHeader37 = resdata['Table37'][0];
          this.showTable38 = false;
          this.sqlTitle37 = (resdata['TableHeader'][37]['FunctionName']).toString();
        }

        if (this.tablecount >= 38) {
          this.sqlresult38 = resdata['Table38'];
          this.sqlHeader38 = resdata['Table38'][0];
          this.showTable39 = false;
          this.sqlTitle38 = (resdata['TableHeader'][38]['FunctionName']).toString();
        }
        if (this.tablecount >= 39) {
          this.sqlresult39 = resdata['Table39'];
          this.sqlHeader39 = resdata['Table39'][0];
          this.showTable40 = false;
          this.sqlTitle39 = (resdata['TableHeader'][39]['FunctionName']).toString();
        }
        if (this.tablecount >= 40) {
          this.sqlresult40 = resdata['Table40'];
          this.sqlHeader40 = resdata['Table40'][0];
          this.showTable41 = false;
          this.sqlTitle40 = (resdata['TableHeader'][40]['FunctionName']).toString();
        }
        if (this.tablecount >= 41) {
          this.sqlresult41 = resdata['Table41'];
          this.sqlHeader41 = resdata['Table41'][0];
          this.showTable42 = false;
          this.sqlTitle41 = (resdata['TableHeader'][41]['FunctionName']).toString();
        }
        if (this.tablecount >= 42) {
          this.sqlresult42 = resdata['Table42'];
          this.sqlHeader42 = resdata['Table42'][0];
          this.showTable43 = false;
          this.sqlTitle42 = (resdata['TableHeader'][42]['FunctionName']).toString();
        }

        if (this.tablecount >= 43) {
          this.sqlresult43 = resdata['Table43'];
          this.sqlHeader43 = resdata['Table43'][0];
          this.showTable44 = false;
          this.sqlTitle43 = (resdata['TableHeader'][43]['FunctionName']).toString();
        }
        if (this.tablecount >= 44) {
          this.sqlresult44 = resdata['Table44'];
          this.sqlHeader44 = resdata['Table44'][0];
          this.showTable45 = false;
          this.sqlTitle44 = (resdata['TableHeader'][44]['FunctionName']).toString();
        }
        if (this.tablecount >= 45) {
          this.sqlresult45 = resdata['Table45'];
          this.sqlHeader45 = resdata['Table45'][0];
          this.showTable46 = false;
          this.sqlTitle45 = (resdata['TableHeader'][45]['FunctionName']).toString();
        }

        if (this.tablecount >= 46) {
          this.sqlresult46 = resdata['Table46'];
          this.sqlHeader46 = resdata['Table46'][0];
          this.showTable47 = false;
          this.sqlTitle46 = (resdata['TableHeader'][46]['FunctionName']).toString();
        }
        if (this.tablecount >= 47) {
          this.sqlresult47 = resdata['Table47'];
          this.sqlHeader47 = resdata['Table47'][0];
          this.showTable48 = false;
          this.sqlTitle47 = (resdata['TableHeader'][47]['FunctionName']).toString();
        }
        if (this.tablecount >= 48) {
          this.sqlresult48 = resdata['Table48'];
          this.sqlHeader48 = resdata['Table48'][0];
          this.showTable49 = false;
          this.sqlTitle48 = (resdata['TableHeader'][48]['FunctionName']).toString();
        }

        if (this.tablecount >= 49) {
          this.sqlresult49 = resdata['Table49'];
          this.sqlHeader49 = resdata['Table49'][0];
          this.showTable50 = false;
          this.sqlTitle49 = (resdata['TableHeader'][49]['FunctionName']).toString();
        }
        if (this.tablecount >= 50) {
          this.sqlresult50 = resdata['Table50'];
          this.sqlHeader50 = resdata['Table50'][0];
          this.showTable51 = false;
          this.sqlTitle50 = (resdata['TableHeader'][50]['FunctionName']).toString();
        }


        this.toast.success('Data Received  Successfully');
      } else {
        this.toast.error('Data Received  Failed!');
      }
    });

  }
  generatePDF() {
    //let html1 = document.getElementById('print.html')as HTMLCanvasElement;
    const html1 = document.getElementById('pdf') as HTMLCanvasElement;

    this.pdfspin = true;
    html2canvas(html1).then(canvas => {
      const pdf = new jspdf('p', 'pt', [canvas.width, canvas.height]);
      const imgData = canvas.toDataURL("image/jpeg", 1.0);
      pdf.addImage(imgData, 0, 0, canvas.width, canvas.height);
      const docname = "SqlExecutedData_" + this.projectId.toString();
      pdf.save(docname + '.pdf');
      if (html1) {
        this.pdfspin = false;
      }
    },
      (error) => {
        this.pdfspin = false;
      });
  }
  spin_dwld:boolean=false
  downloadFile() {
    this.spin_dwld=true;
    const path='/mnt/eng/PRJ'+this.projectId+'SRC/AWS_Reports/Sql_Server_Feature_Assessment.xlsx';
    // title="DB_Reg.zip"
    this.project.downloadLargeFiles(path).subscribe((blob: any) => {
      //this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = "Sql_Server_Feature_Assessment.xlsx";
      downloadLink.href = url;
      //console.log('hi');
      //downloadLink.href = 'data:application/octet-stream;base64,' + this.fileResponse;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
}

export const testingAPIConstant = {
    Files:'GetFiles',
    deleteFiles:'DeleteFiles?Path=',
    selecttestcaseRange:'selecttestcaseRange',
    getFiles:'GetFilesFrsomDirectoryEx?path=',
    uploadDocuments:'UploadExtraFiles',
    downloadFiles:'DownloadLargeFile?filePath=',
    deleteFile:'DeleteFiles?path=',
    tgtCycltestcasehdrSelect:'PrjTgttestcasehdrSelect',
    getConList:'GetConnectionList?projectId=',
    GetReqData:'GetRequestTableData?projectId=',
    GetDataByBatchId:'GetDataByBatchId',
    testPrjSrcfeaturesSelect:'testPrjSrcfeaturesSelect',
    testPrjFeaturesGroupLeadSelect:'testPrjFeaturesGroupLeadSelect',
    testPrjObjectsfeaturesSelect:'testPrjObjectsfeaturesSelect',
    testPrjSrcFeaturesEstTimeCountSelect:'testPrjSrcFeaturesEstTimeCountSelect',
    testPrjSrcfeaturesAssignSelect:'testPrjSrcfeaturesAssignSelect',
    testPrjRuninfoIteration:'testPrjRuninfoIteration?projectId=',
    testPrjFeatureGroupSelect:'testPrjFeatureGroupSelect',
    GetExecutionLog:'GetExecutionLog',
    PrjsrcObjectsSelectCDC:'PrjsrcObjectsSelectCDC',
    PrjtgtObjectsSelectCDC:'PrjtgtObjectsSelectCdc',
    TestCaseDetailUpdate:'TestCaseDetailUpdate',
    PrjTgttestcasedtlSelect:'PrjTgttestcasedetailSelect',
    getTestCaseBatches:'GetTestcaseBatchIds?tgt_testid=',
    PrjTgtTestCaseUpdate:'PrjTgtTestCaseUpdate',
    PrjTgttestcasehdrSelect:'PrjTgttestcasehdrSelect',
    getRunNumbers:'GetRunNumbers?projectId=',
    PrjSchemasListSelectData:'PrjSchemasListSelectData?projectId=',
    InfraSelect:'GetInfraSelect?projectId=',
    ProjectSrctgtconfSelect:'GetProjectSrctgtconfSelect',    
    TestCaseFileSelect:'GetPrjTestCasefileSelect?projectId=',
    GetConnectionList:'GetConnectionList?projectId=',       
    prjSchemasListSelect:'PrjSchemasListSelect',
    UploadBinaryFileToShare:'ARM/UpLoadBinaryFileToShare', 
    prjTestCaseFileInsert:'PrjTestCaseFileInsert',
    getExecutionLog:'GetExecutionLog',
    getTestRunInfo:'testPrjRuninfoIteration?projectId=',        
    GetProjectSrctgtconfSelect:'GetProjectSrctgtconfSelect',
    testPrjSrcObjectsSelect:'testPrjSrcObjectsSelect',     
    prjsrcObjectsSelectCDC:'PrjsrcObjectsSelectCDC',  
    testPrjTgtObjectsSelect  :'testPrjTgtObjectsSelect',  
    prjtgtObjectsSelectCdc  :'PrjtgtObjectsSelectCdc' , 
    //testPrjSrcfeaturesSelect:'testPrjSrcfeaturesSelect',
    //testPrjFeaturesGroupLeadSelect:'testPrjFeaturesGroupLeadSelect',
    secUserSelectByProject:'SecUserSelectByProject?projectId=',  
    testPrjTgtDeploystatusSelect  : 'testPrjTgtDeploystatusSelect', 
    getSecUserProjectMenu  : 'GetSecUserProjectMenu?projectId=77', 
    getInfraSelect  : 'GetInfraSelect?projectId=',
    getSchemaList  : 'SchemaSelect?runno=',
    //getSchemaList  : 'GetSchemaList?projectId=',
    //getRunNumbers  : 'GetRunNumbers?projectId=',
    getObjectTypes  : 'GetObjectTypes?projectId=',
    getBatchIds  : 'GetBatchIds?projectId=',
    getModules  : 'GetModules?batch_id=',
    getTestcases  : 'GetTestcases?batchId=',
    getTestcaseNames  : 'GetTestcaseNames',
    testCaseWrkLoadCmd:'TestCaseWrkLoadCmd',
    uploadCloudFiles:'UploadCloudFiles',   
    getFilesFromDirectory:'GetFilesFromDirectory?path=', //need to test if this api is correct
    //prjTestCaseFileInsert:'PrjTestCaseFileInsert',  
    GetFileContent:'GetFileContent' ,
    GetFilesFromDir:'GetFilesFromDirectory?path=',
    GetProjectsrctestcasehdrSelect:'GetProjectsrctestcasehdrSelect',
    TestCycleCmd:'TestCycleCmd',
    testTgtCodeUpdateLocal:'testTgtCodeUpdate', 
    deleteTableData:'DeleteRequestTableData?projectId=',
    GetIndividualLogs:'GetIndividualLogs', 
    GetRunno:'GetRunNumbers?projectId=',
    downloadLargeFiles:'DownloadLargeFile?filePath=',
    testPrjSrcFeaturesAssignInsert:'testPrjSrcFeaturesAssignInsert',
    testPrjSrcFeaturesInsert:'testPrjSrcFeaturesInsert',
    testPrjObjectFeaturesInsert:'testPrjObjectFeaturesInsert',
    testPrjObjectFeatureInsertNew:'testPrjObjectFeaturesInsert',
    GetProjectObjectFeaturesSelectCdc:'GetProjectObjectFeaturesSelectCdc',
    GetProjectObjectFeaturesSelectCdcNew:'GetProjectObjectFeaturesSelectCdcURL',
    ObjectFeaturesInserCdc:'PrjObjectFeaturesInsertCdc',
    ObjectFeaturesInserCdcNew:'PrjObjectFeaturesInsertCdc',
    createWorkItem:'CreateWorkItem',
    testTgtCodeUpdate:'testTgtCodeUpdate',
    GetTestObjTypes:'GetTestObjTypes?runno=',
    TestObjectsSelect:'TestObjectsSelect',
    webTestCommand:'WebTestCommand',
    baselineCommand:'BaselineCommand',
    SchemaListSelect:'GetSchemasFromPG?conid=',
    TablesSelect:'GetTablesFromPgSchema?conid=',
    getFilesFromDirectory1:'GetFilesFromDirectory?path=',
    uploadCloudFiles1:'UploadCloudFiles',

}
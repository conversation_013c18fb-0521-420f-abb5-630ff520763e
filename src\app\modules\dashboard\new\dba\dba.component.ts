import { Component } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { DashboardService } from '../../../../services/dashboard.service';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartOptions, ChartType } from 'chart.js';

@Component({
  selector: 'app-dba',
  standalone: true,
  imports: [TabsComponent, NgChartsModule],
  templateUrl: './dba.component.html',
  styles: ``
})
export class NewDbaComponent {

    /*--- No issues Vs Resolved Issues by Log date Chart Data ---*/

    public NORIssuesChartLabels = ['11:00', '11:20 ', '11:30','11:40', '11:50 ', '12:00', '12:20 ', '12:30','12:40', '12:50 ', '13:00'];
    public NORIssuesChartLegend = true;
    public NORIssuesChartData = [
      {
        data: [50, 45, 40, 58, 70, 75, 65, 60, 55, 50, 45, 40, 35, 30, 25,50, 45, 40, 58, 70, 75, 65, 60, 55, 50, 45, 40, 35, 30, 25],
        label: ' Maximum ',
        borderColor:'#ff7900',
        backgroundColor: 'rgb(255 121 0 / 10%)',
        hoverBackgroundColor: '#4c00b5',
        pointRadius: 1,
        tension: 0.4,
      },    
      {
        data: [48, 43, 35, 49, 56, 62, 54, 52, 50, 48, 46, 44, 42, 40, 38,48, 43, 35, 49, 56, 62, 54, 52, 50, 48, 46, 44, 42, 40, 38],
        label: ' Average ',
        borderColor:'#a25eff',
        backgroundColor: 'rgb(162 94 255 / 20%)',
        hoverBackgroundColor: '#8b36ff',
        pointRadius: 1,
        tension: 0.4,
      }
    ];
    public NORIssuesChartOptions: ChartOptions<'line'> = {
      responsive: true,
      interaction: {
        mode:'index',
        intersect: false,
      },
      scales: {
        x: {
          beginAtZero: false,
          ticks: {
            maxTicksLimit: 4, // Limit the number of ticks on the x-axis
            autoSkip: true, // Automatically skip ticks to avoid overlap
            maxRotation: 0, // Prevent rotation of labels
            minRotation: 0
          },
          grid: {
            display: true, // Show grid lines for x-axis
            color: 'rgba(200, 200, 200, 0.2)' // Light gray grid lines
          }
        },
        y: {
          beginAtZero: false,
          ticks: {
            stepSize: 10, // Set step size for y-axis ticks
            maxTicksLimit: 6 // Limit the number of ticks on the y-axis
          },
          grid: {
            display: true, // Show grid lines for y-axis
            color: 'rgba(200, 200, 200, 0.2)' // Light gray grid lines
          }
        },
      },
      plugins: {
        legend: {
          display: true  // This line disables the legend
        },
        tooltip: {
          enabled: true,
          usePointStyle: true,
          titleSpacing: 5,
          backgroundColor: '#ffffff', // White background
          titleColor: '#333333', // Dark title color
          bodyColor: '#666666', // Lighter body text color
          borderColor: '#cccccc', // Light gray border
          borderWidth: 1, // Thin border
          cornerRadius: 8, // Rounded corners
          padding: 10, // Padding inside the tooltip
          callbacks: {
          }
        }
      }
    };
}

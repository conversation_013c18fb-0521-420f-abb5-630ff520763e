import { Component, ElementRef, Renderer2, ViewChild } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { GetObjectCode, GetObjectType, GetProjectTgtDeployStatusSelect, ObjectCode, ObjectType, ObjectsSelect, RedisCacheInsertion, deleteFile, documentsList, ob, obj, objtype, projectConRunTblInsert, redis1, redisCommand, setRedis, setRedisCache, setRedisCache1, setredis } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { Observable } from 'rxjs';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-deploy',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './deploy.component.html',
  styles: ``
})
export class DeployComponent {
  projectId: any;
  getRole: any;
  ExeLog: any = {};
  prjLogselect: any = [];
  prjSrcTgt: any = {};

  targetList: any
  infraSelectData: any = [];
  spin: boolean = false;
  add_spin: boolean = false;
  fileName: string = '';
  fileResult: any;
  filtered: any;
  tenantId: any;
  subscriptionId: any;
  resourceGroup: any;
  location: any;
  vmName: any;
  deployForm: any;
  project_name: any;


  createFileForm: any;
  createForm: any;
  schemaName: any = [];
  selectedItems = [];

  objectTypes: any = [];
  selectedObjItems = [];
  selectedObjItem = [];
  objectType: any = [];

  selectedObjItems1 = [];
  deployFormSingle: any

  runNumbersList: { iteration_id: string }[] = [];
  schemaList: { schema_name: string, connection_id: string, schema_id: string, tgt_schemaname: string, tgt_connection_id: string, tgt_schema_id: string }[] = [];
  tgtSchemaList: { schemaname: string }[] = [];
  currentRunNumber: string = '';
  currentSchema: string = '';
  objectTypeList: ObjectType[] = [];
  deleteForm: any
  p: number = 1;
  pi: number = 10;
  datachangeLogs: any
  prjLogData: any = [];
  migtypeid: string = ""

  // createTargetForm
  createTargetForm: any;
  pageName: string = ''
  schemalable: string = ''
  constructor(private titleService: Title, private toast: HotToastService, private codemigrationservice: CodeMigrationService, public formBuilder: FormBuilder, private el: ElementRef, private renderer: Renderer2, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }
  }
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.createTargetForm = this.formBuilder.group({
      ctTargetConnection: ['', [Validators.required]],
      ctSchema: ['', [Validators.required]],
    })
    this.createFileForm = this.formBuilder.group({
      iteration: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      objectType: ['', [Validators.required]],
      auditLogs: [''],
    });
    this.p = 1;
    this.createForm = this.formBuilder.group({
      targetConnection: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      objectType: ['', [Validators.required]],
    });
    this.deployForm = this.formBuilder.group({
      targetConnection: ['', [Validators.required]],
      formFileName: ['', [Validators.required]],
      deployOp: ['', [Validators.required]],
      fname: ['', [Validators.required]]

    });
    this.deployFormSingle = this.formBuilder.group({
      targetConnection: ['', [Validators.required]],
      iteration: ['', [Validators.required]],
      tgtschema: ['', [Validators.required]],
      objTye: ['', [Validators.required]],
      objnames: ['', [Validators.required]],
    });
    if (this.migtypeid == "30") {
      this.deleteForm = this.formBuilder.group({
        targetConnection: ['', [Validators.required]],
        schema: ['', [Validators.required]],
        objTye: ['', [Validators.required]],
        objnames: ['', [Validators.required]],
      });
    } else {
      this.deleteForm = this.formBuilder.group({
        targetConnection: ['', [Validators.required]],
        schema: ['', [Validators.required]],
      });
    }
    this.fetchLogs()
    this.getSingleFileFolders()
    this.getDeployOperations()
    this.GetConsList();
    this.getRunNumber();
    this.getObjectTypes()
  }
  get getControl() {
    return this.deployForm.controls;
  }
  get ffs() {
    return this.createForm.controls;
  }
  get fs() {
    return this.createFileForm.controls;
  }
  get fsd() {
    return this.deployFormSingle.controls;
  }
  get f() {
    return this.deleteForm.controls;
  }

  get fc() {
    return this.createTargetForm.controls;
  }

  runnoForReports: any
  getRunNumber() {
    this.getRunSpin = false
    this.codemigrationservice.RuninfoSelect().subscribe((data: any) => {
      this.runNumbersList = data['Table1']
      this.runnoForReports = data['Table1']
    });
  }
  schemaSingletgt: any
  runidSingle: any
  getSchema(value: string, check: any) {
    this.schemaList = []
    this.currentRunNumber = value;
    this.codemigrationservice.SchemaSelect(value).subscribe((data: any) => {
      if (check == "single") {
        this.runidSingle == ""
        this.schemaSingletgt = data['Table1'];
        this.runidSingle = value
      }
      else {
        this.schemaList = data['Table1'];
      }

      //console.log(this.schemaList)
    });
  }

  ConsList: any;
  GetConsList() {
    this.codemigrationservice.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.targetList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
    })
  }

  getPrjExeLogSelect(value: any) {
    if (value == "") {
      value = "null";
    }
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operation = "TASK";
    this.ExeLog.pageNo = value;
    this.codemigrationservice.PrjExeLogSelect(this.ExeLog).subscribe((data: any) => {
      this.prjLogselect = data['Table1'];
    });
  }


  selectedFileName: any

  sendValue($event: any): void {
    this.readThis($event.target);
  }

  readThis(inputValue: any): void {
    const file: File = inputValue.files[0];
    this.fileName = inputValue.files[0].name;
    const myReader: FileReader = new FileReader();
    myReader.onloadend = (e) => {
      this.fileResult = myReader.result;
    };
    myReader.readAsDataURL(file);
  }


  tableFiles: any = []
  disableDeploy: boolean = false
  fetchTableFiles(Path: any) {
    var fldr = this.objFolders.filter((item: any) => {
      if (item.fileName.toLowerCase() == Path.toLowerCase()) {
        //console.log(item.fileName.toLowerCase())
        //console.log(Path.toLowerCase())

        Path = item.fileName
      }
    })
    this.tableFiles = []
    this.disableDeploy = false
    const path1 = "PRJ" + this.projectId + "SRC/Single_File_Output/" + Path + "/" // Path.replace("/mnt/eng/", "")
    this.codemigrationservice.GetFilesFromDir(path1).subscribe((data: any) => {
      this.tableFiles = data
      //console.log(data)
      if (this.tableFiles == null) {
        this.tableFiles = []
        let ob = {
          fileName: "No Files Available",
          filePath: ""
        }
        this.disableDeploy = true
        //console.log(ob)
        this.tableFiles.push(ob);
      }
    },
      (nocontent) => {
        //console.log(this.tableFiles)
        if (this.tableFiles == undefined) {
          let ob = {
            fileName: "No Files Available",
            filePath: ""
          }
          this.tableFiles = this.tableFiles.push(ob);
        }
      }
    )
  }


  selFile: any
  onFileSelected1(event: any) {
    const file: File = event.target.files[0];
    this.selFile = file
    this.fileName = file.name;
    //this.uploadFile(file);
  }
  upload_spin: boolean = false
  uploadFile() {
    this.upload_spin = true
    const path = "PRJ" + this.projectId + "SRC/Single_File_Output/User_Created_Files/"
    const formData: FormData = new FormData();
    formData.append('file', this.selFile, this.selFile.name);
    formData.append('path', path)
    //console.log(formData)

    this.codemigrationservice.updloadLargeFiles(formData).subscribe(
      response => {
        this.upload_spin = false
        this.toast.success(response.message);
        this.fetchTableFiles("PRJ" + this.projectId + "SRC/Single_File_Output")
        //console.log('File uploaded successfully', response);
      },
      error => {
        this.upload_spin = false
        console.error('Error uploading file', error);
      }
    );
  }



  SourceConn: any;
  selectSourceConnection(scon: any) {
    this.SourceConn = scon;
  }

  GetDBConnections() {
    const obj = {
      projectId: this.projectId.toString(),
      migsrcType: "D",
      connectionName: 'null'
    }
  }
  tgtId: any
  filePath: any
  selectTarget(value: any) {
    this.tgtId = value
  }
  tgtIdForSIngle: any
  selectTargetsingle(value: any) {
    this.tgtIdForSIngle = value
  }
  selectPath(value: any) {
    this.filePath = value.replace("/mnt/eng/", "")
    this.substring = this.getsubstring(this.filePath, '_~_')
    // //console.log(this.substring);
  }
  redisResp: any
  RedisCommand() {
    this.spin = true
    var srcSchema
    var tgtSchema
    if (this.migtypeid == "22") {
      srcSchema = (<HTMLInputElement>document.getElementById("srcSchema")).value
      tgtSchema = (<HTMLInputElement>document.getElementById("tgtSchema")).value
      if (tgtSchema != "" || srcSchema != "") {
        if (tgtSchema == "") {
          this.toast.error("Please Enter Target Schema")
        }
        if (srcSchema == "") {
          this.toast.error("Please Enter Source Schema")
        }
      }
    }
    else {
      srcSchema = this.substring
      tgtSchema = this.seltargetschema
    }

    const obj = {
      projectId: this.projectId.toString(),
      option: 4,
      tgtConId: this.tgtId,
      filePath: this.filePath,
      schema: srcSchema,
      targetSchema: tgtSchema,
      jobName: "qmig-convs"

    }
    this.codemigrationservice.setRedisCache2(obj).subscribe((data: any) => {
      this.redisResp = data;
      this.spin = false
      this.toast.success("Executed Successfully")
      this.deployFormSingle.reset()
    })
    this.deleteForm.reset();
  }
  tgtid: string = ""
  tgtschname: string = ""
  getObjectType(value: string) {
    //console.log(value)
    this.objectTypeList = []
    const newObjectType: objtype = {
      runno: this.currentRunNumber,
      schemaid: value
    }
    this.currentSchema = value;
    this.codemigrationservice.GetObjectType1(newObjectType).subscribe((data: any) => {
      this.objectTypeList = data['Table1']
      var det
      if (this.migtypeid == "30") {
        det = this.schemaList.filter((item: any) => {
          return item.schema_id == value
        })
      }
      else {
        det = this.schemaList.filter((item: any) => {
          return item.schema_id == value
        })
      }
      //console.log(det)
      if (det.length > 0) {
        if (this.migtypeid == "30") {
          this.tgtid = det[0].connection_id
          this.tgtschname = det[0].schema_name
        } else {
          this.tgtid = det[0].connection_id
          this.srcId = det[0].connection_id
        }
      }
    })

    this.createFileForm.controls.objectType.setValidators([Validators.required])
    this.createFileForm.controls.objectType.updateValueAndValidity()

  }

  onObjItemSelect(item: any) {
    this.objectTypes.push(item.objecttype_name);
  }
  onObjItemDeSelect(item: any) {
    this.objectTypes.pop(item.objecttype_name);
  }
  onObjSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.objectTypes.push(dd.objecttype_name);
    });
  }
  onObjDeSelectAll(items: any) {
    this.objectTypes = [];
  }

  singleValue: string = ''
  singleFilevalue(data: any) {
    if (data.target.checked) {
      this.singleValue = 'True'
    } else {
      this.singleValue = 'False'
    }
  }
  file_iter_spin: boolean = false
  createFile(data: any) {
    var sch: any
    if (this.migtypeid != "30") {
      sch = this.schemaList.filter((item: any) => {
        return item.schema_id == this.currentSchema
      })
    }

    this.file_iter_spin = true
    const Obj: any = {
      projectId: this.projectId.toString(),
      option: 5,
      deployOption: 'Iteration',
      objectType: data.objectType.toString(),
      single_Check: this.singleValue,
      iteration: this.currentRunNumber,
      jobname: "qmig-convs",
      srcConId: this.srcId,
    }
    if (this.migtypeid == "30" || this.migtypeid == "20") {
      Obj["tgtConId"] = this.tgtid,
        Obj["targetSchema"] = this.tgtschname
    }
    else {
      Obj['schema'] = sch[0].schemaname
    }
    this.codemigrationservice.RedisCacheInsertion1(Obj).subscribe((data: any) => { this.file_iter_spin = false; data.message == "Command Inserted" ? this.toast.success("Command Inserted") : this.toast.warning("Something went wrong") })
    this.createFileForm.reset();
  }

  /*---- Create Deployment file with Target Connection ---*/

  currentTargetConnection: string = '';
  getTgtSchema(data: string) {
    this.tgtSchemaList = [];
    this.currentTargetConnection = data;
    if (this.migtypeid == "31") {
      this.targetList.filter((item: any) => {
        if (item.Connection_ID == data) {
          this.tgtSchemaList.push({ schemaname: item.dbname })
        }
      })
    }
    else if (this.migtypeid == "30") {
      this.codemigrationservice.OracleSchemas(data).subscribe((data: any) => {
        this.tgtSchemaList = data;
      });
    }
    else {
      this.codemigrationservice.tgtSchemaSelect(data).subscribe((data: any) => {
        this.tgtSchemaList = data;
      });
    }

  }
  srcId: any
  currentTargetSchema: string = ''
  getTgtObject(data: string) {
    this.currentTargetSchema = data
    this.createForm.controls.objectType.setValidators([Validators.required])
    this.createForm.controls.objectType.updateValueAndValidity()
  }
  getObjectTypes() {
    const obj = {
      projectId: this.projectId,
      objectgroupname: "Storage_Objects"
    }
    this.codemigrationservice.getObjectTypes(obj).subscribe((data: any) => {
      this.objectType = data['Table1'];
    })
  }

  objectTypeForDB: any = []
  onItemSelect(item: any) {
    this.objectTypeForDB.push(item.objecttype_name);
    //console.log(this.objectTypeForDB.toString())
  }
  onItemDeSelect(item: any) {
    this.objectTypeForDB.pop(item.objecttype_name);
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.objectTypeForDB.push(dd.objecttype_name);
    });
  }
  onDeSelectAll(items: any) {
    this.objectTypeForDB = [];
  }

  singleTgtValue: string = ''
  singleTgtFilevalue(data: any) {
    if (data.target.checked) {
      this.singleTgtValue = 'True'
    } else {
      this.singleTgtValue = 'False'
    }
  }
  file_db_spin: boolean = false
  createFileByDB(data: any) {
    this.file_db_spin = true
    const Obj: redis1 = {
      projectId: this.projectId.toString(),
      option: 5,
      deployOption: 'Database',
      target_schema: this.currentTargetSchema,
      objectType: data.objectType.toString(),
      single_Check: this.singleTgtValue,
      tgtConId: this.currentTargetConnection,
      jobName: "qmig-convs",
      schema: ""
    }
    this.codemigrationservice.RedisCacheInsertion2(Obj).subscribe((data: any) => { this.file_db_spin = false; data.message == "Command Inserted" ? this.toast.success("Command Inserted") : this.toast.warning("Something went wrong"); this.createForm.reset() })
  }
  objFolders: any
  getSingleFileFolders() {
    const path = "PRJ" + this.projectId + "SRC/Single_File_Output/"
    this.codemigrationservice.getSingleFileFoders(path).subscribe((data: any) => {
      this.objFolders = data;
    })
  }
  depOperations: any
  getDeployOperations() {
    this.codemigrationservice.getDeployOperations().subscribe((data: any) => {
      this.depOperations = data['Table1'];
    })
  }
  depSequence: any
  getDeploySequence(value: any) {
    this.tableFiles = []
    this.codemigrationservice.getDeploySequence(value).subscribe((data: any) => {
      this.depSequence = data['Table1'];
    })
  }
  targetschema: any
  gettgtSchema(value: any) {
    this.targetschema = value
  }
  spin_delete: boolean = false
  DeleteSchema() {
    this.spin_delete = true
    if (this.migtypeid == "30") {
      this.DropObjects()
    }
    else {
      let obj = {
        schemaname: this.targetschema,
        conId: this.currentTargetConnection
      }
      this.codemigrationservice.DeleteSchema(obj).subscribe((data: any) => {
        if (data.message.includes("does not exist")) {
          this.spin_delete = false
          this.toast.error("DataBase/Schema Not Found")
        }
        else {
          this.spin_delete = false
          this.toast.success(data.message)
        }
      },
        (error) => {
          this.spin_delete = false
          this.toast.error(error.message);
        })
      this.deleteForm.reset();
    }
  }
  DeploySingleObj() {
    var dt = this.objectTypeListForDeploy.filter((item: any) => {
      return item.objecttype_id == this.objtypid
    })
    this.file_db_spin = true
    const Obj = {
      projectId: this.projectId.toString(),
      option: 7,
      iteration: this.runidSingle,
      schema: this.singleSchemaname,
      targetSchema: this.selSingletargetschema,//this.currentSchema,
      objectType: dt[0].objecttype_name,
      tgtConId: this.deployFormSingle.value.targetConnection,
      objectName: this.deployFormSingle.value.objnames.toString() + " ",
      jobName: "qmig-convs",
    }
    this.codemigrationservice.RedisCacheInsertion3(Obj).subscribe((data: any) => { this.file_db_spin = false; data.message == "Command Inserted" ? this.toast.success("Command Inserted") : this.toast.warning("Something went wrong") })
  }
  objectTypeListForDeploy: any
  singleSchemaname: any
  getObjectTypeForDeploy(value: string) {
    var schname = this.schemaSingletgt.filter((item: any) => {
      return item.schema_id == value
    })
    if (schname.length != 0) {
      this.singleSchemaname = schname[0].schemaname;
    }
    this.objectTypeList = []
    const newObjectType: objtype = {
      runno: this.currentRunNumber,
      schemaid: value
    }
    this.currentSchema = value;
    this.codemigrationservice.GetObjectType2(newObjectType).subscribe((data: any) => {
      this.objectTypeListForDeploy = data['Table1']

    })
    // this.createFileForm.controls.objectType.setValidators([Validators.required])
    // this.createFileForm.controls.objectType.updateValueAndValidity()

  }
  objectNames: any
  objtypid: any
  fetchObjectNames(objTypeId: any) {
    this.objtypid = objTypeId
    const Obj: obj = {
      runno: this.runidSingle,
      schemaid: this.currentSchema,
      objTypeId: objTypeId,
      deployed: 'true'
    }
    this.codemigrationservice.ObjectsSelect1(Obj).subscribe((data: any) => {
      this.objectNames = data['Table1']
      const Ob: ob = {
        runno: this.runidSingle,
        schemaid: this.currentSchema,
        objTypeId: objTypeId,
        deployed: 'false'
      }
      this.codemigrationservice.ObjectsSelect2(Ob).subscribe((data: any) => {
        var objnames = data['Table1']
        objnames.forEach((item: any) => {
          this.objectNames.push(item);
          this.objectNames.sort();
        })
      });
    });
  }

  objnames: any = []
  onObjItemSelect1(item: any) {
    this.objnames.push(item.object_name);
  }
  onObjItemDeSelect1(item: any) {
    this.objnames.pop(item.object_name);
  }
  onObjSelectAll1(items: any) {
    items.forEach((dd: any) => {
      this.objnames.push(dd.object_name);
    });
  }
  onObjDeSelectAll1(items: any) {
    this.objnames = [];
  }
  iterationForLogs: any
  selectIterForLogs(value: any) {
    this.iterationForLogs = value
  }
  selectedObjType: any
  selectObjType(value: any) {
    this.selectedObjType = value
    this.fetchLogs()
  }
  getDeploySpin: boolean = false;
  depLogs: any
  fetchLogs() {
    var path = "PRJ" + this.projectId + "SRC/" + "/Deployment_Logs/";
    this.codemigrationservice.GetFilesFromDir(path).subscribe((data: any) => {
      this.depLogs = data;
      this.getDeploySpin = false
      //console.log(this.depLogs)
    })
  }
  fetchDeployLogs() {
    this.getDeploySpin = true
    this.fetchLogs()
  }
  getRunSpin: boolean = false;
  getUpdatedRunNumber() {
    this.getRunSpin = true
    this.getRunNumber()
  }
  fileResponse: any
  spin_dwld: any;
  downloadFile(fileInfo: any) {
    this.codemigrationservice.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  onKey() {
    this.p = 1;
  }
  grid_active: boolean = false;
  not_grid: boolean = false;
  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }
  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all') {
      this.p = 1;
      this.pi = this.prjLogData.length;
    }
    else if (value == '20') {
      this.p = 1;
    }
    else if (value == '50') {
      this.p = 1;
    }
    else if (value == '100') {
      this.p = 1;
    }
  }
  objectTypesList: any;
  getObjecttypes() {
    let obj = {
      Conid: this.currentTargetConnection,
      schemaname: this.targetschema
    }
    this.codemigrationservice.getObjTypes(obj).subscribe((data: any) => {
      this.objectTypesList = data
    })
  }
  selectedtgtObjType: string = ""
  selectObjectType(value: any) {
    this.selectedtgtObjType = value
  }
  ObjectNamesList: any
  getObjectNames() {
    let obj = {
      Conid: this.currentTargetConnection,
      schemaname: this.targetschema,
      objectType: this.selectedtgtObjType
    }
    this.codemigrationservice.getObjNames(obj).subscribe((data: any) => {
      this.ObjectNamesList = data
      this.ObjectNamesList.filter((item: any) => {
        item.type = "ALL"
      })
    })
  }
  selectedobjctName: any
  selectObjNames(value: any) {
    this.selectedobjctName = value
  }
  dropresponse: string = ""
  spin_drop: boolean = false
  DropObjects() {
    this.spin_drop = true
    var selobjnames: any = []
    this.selectedobjctName = this.selectedobjctName.forEach((item: any) => {
      if (item == "ALL") {
        selobjnames.push(item)
      }
      else {
        selobjnames.push("'" + item + "'")
      }
    })
    let obj = {
      Conid: this.currentTargetConnection,
      schemaname: this.targetschema,
      objectType: this.selectedtgtObjType,
      objectName: selobjnames.toString()
    }
    this.codemigrationservice.dropschemaobjs(obj).subscribe((data: any) => {
      this.dropresponse = data.message
      this.spin_drop = false
      this.deleteForm.reset()
      this.toast.success(this.dropresponse)
    })
  }
  createschemaresponse: string = ""
  spin_createschema: boolean = false
  createSchema(formData: any) {
    this.spin_createschema = true
    let obj = {
      Conid: formData.ctTargetConnection,
      schemaname: formData.ctSchema
    }
    this.codemigrationservice.createschema(obj).subscribe((data: any) => {
      this.createschemaresponse = data.message;
      this.spin_createschema = false
      this.createTargetForm.reset();
      this.toast.success(this.createschemaresponse)
    })
  }
  targetSchemaList: any = []
  singletargetSchemaList: any = []
  //GetSchemasLiist
  getSchemasList(connectionId: any, value: any) {
    this.tgtSchemaList = []
    if (this.migtypeid == "31") {
      this.targetList.filter((item: any) => {
        if (item.Connection_ID == connectionId) {
          let ob = {
            schema_name: item.dbname
          }
          if (value == 0) {
            this.targetSchemaList = []
            this.targetSchemaList.push(ob);
          }
          else {
            this.singletargetSchemaList = []
            this.singletargetSchemaList.push(ob);
          }
        }
      })
    }
    else {
      const obj = {
        projectId: this.projectId,
        connectionId: connectionId
      }
      this.codemigrationservice.SchemaListSelect(obj).subscribe((data: any) => {
        // this.schemaList = data['Table1'];
        if (value == 0) {
          this.targetSchemaList = data['Table1'];
          this.targetSchemaList = this.targetSchemaList.filter((item: any) => {
            return item.schemaname != "ALL"
          });
        } else {
          this.singletargetSchemaList = data['Table1'];
          this.singletargetSchemaList = this.singletargetSchemaList.filter((item: any) => {
            return item.schemaname != "ALL"
          });
        }
      })
    }
  }
  seltargetschema: string = ""
  selSingletargetschema: string = ""

  selectTargetschema(value: any, value1: any) {
    if (value1 == 0) {
      this.seltargetschema = value
    } else {
      this.selSingletargetschema = value
    }
  }
  substring: string = ""

  getsubstring(str: string, delimiter: string): string {
    const firstIndex = str.indexOf(delimiter);
    const lastIndex = str.lastIndexOf(delimiter);

    if (firstIndex !== -1 && lastIndex !== -1 && firstIndex !== lastIndex) {
      return str.substring(firstIndex + delimiter.length, lastIndex);
    }

    return ''; // return an empty string if the conditions are not met
  }
  ConversionResponseData: any = []
  ConversionCommand(value: any) {
    var commonObj=[]
    var deployOp = ""

    if (value == 0) {
      var srcSchema
      var tgtSchema
      if (this.migtypeid == "22") {
        srcSchema = (<HTMLInputElement>document.getElementById("srcSchema")).value
        tgtSchema = (<HTMLInputElement>document.getElementById("tgtSchema")).value
        if (tgtSchema != "" || srcSchema != "") {
          if (tgtSchema == "") {
            this.toast.error("Please Enter Target Schema")
          }
          if (srcSchema == "") {
            this.toast.error("Please Enter Source Schema")
          }
        }
      }
      else {
        srcSchema = this.substring
        tgtSchema = this.seltargetschema
      }
      let deployFileobj: any = {
        targetConnectionId: this.tgtId,
        task: "Deploy_File",
        deployFilePath: this.filePath,
        targetSchema: tgtSchema,
        schema: srcSchema,
      }
      commonObj=deployFileobj
    }
    else if (value == 1) {
      let createfileobj: any = {
        targetConnectionId: this.tgtId,
        task: "Deployment_File",
        deployFileCreateOption: "Iteration",
        iteration: this.currentRunNumber,
        objectType: this.selectedObjItems.toString(),
        targetSchema: this.tgtschname,
        deployFileCheck: this.singleValue,
      }
      commonObj=createfileobj
    }
    else if (value == 2) {
      let createfilewithdb: any = {
        targetConnectionId: this.currentTargetConnection,
        task: "Deployment_File",
        deployFileCreateOption: "Database",
        iteration: "",
        objectType: this.selectedObjItems.toString(),
        targetSchema: this.currentTargetSchema,
        deployFileCheck: this.singleTgtValue,
      }
      commonObj=createfilewithdb
    }
    else if (value == 3) {
      var dt = this.objectTypeListForDeploy.filter((item: any) => {
        return item.objecttype_id == this.objtypid
      })
      let deploysingleobj: any = {
        targetConnectionId: this.deployFormSingle.value.targetConnection,
        task: "Deploy_Objects",
        iteration: this.currentRunNumber,
        objectType: dt[0].objecttype_name,
        schema: this.singleSchemaname,
        targetSchema: this.selSingletargetschema,
        objectName: this.deployFormSingle.value.objnames.toString() + " ",
      }
      commonObj=deploysingleobj
    }
    commonObj["jobName"]="qmig-convs"
    commonObj["processType"]=""
    commonObj["projectId"]=this.projectId.toString()

    this.codemigrationservice.ConversionCommand(commonObj).subscribe((data: any) => {
      this.ConversionResponseData = data;
      this.toast.success("Deploy Command Executed")
    },
      error => {
        this.toast.error('Something went wrong')
      })
  }

}

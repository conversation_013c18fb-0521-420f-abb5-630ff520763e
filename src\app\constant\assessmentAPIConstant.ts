export const assessmentAPIConstant = {    
    uploadDocuments: 'UploadExtraFiles',
    fileDownload: 'DownloadLargeFile?filePath=',
    getFiles: 'GetFilesFromDirectoryEx?path=',
    InsertSchemaCommand: 'SchemaInsertion',
    getConList: 'GetConnectionList?projectId=',
    projectMigDetailSelect: 'GetProjectMigDetailSelect',
    GetBlobFiles: 'GetBlobFiles',
    projectDocumentsDetailSelect: 'GetFiles/',
    deployBtn: 'CommonDbOPs',
    ExeBtn: 'ExecuteDbQuery',
    UpLoadCommonBinaryFileToBlob: 'UpLoadCommonBinaryFileToBlob',
    UploadBinaryFileToShare: 'UpLoadBinaryFileToShare',
    addPrjConnection: 'AddDbConnection',
    updatePrjConnection: 'UpdateDbConnection',
    DeleteprjDbConnection: 'DeleteprjDbConnection?conId=',
    selectmigtype: 'Selectmigtype?migid=',
    addProjectObject: 'AddConnectionwithUserId',
    UserAccessSelect: 'Client/UserAccessSelect',
    InfraSelect: 'GetInfraSelect?projectId=',
    PrjExeLogSelect: 'PrjExeLogSelect',
    getRolesDetails: 'Admin/SecRolesSelect',
    GetDBConnections: 'GetDBConnections?projectId=',
    deleteDbConnection: 'DeleteDBConnection?projectId=',
    UpdateConnection: 'UpdateConnectionwithUserId',
    testSourceConn: 'Ora2PgAssessment/ora2pg/TestOracleConnection',
    testTargetConn: 'PgTestConnection',
    addfiletype: 'ProjectConFileTypeInsert',
    getfiletype: 'GetFileTypes?projectId=',
    GetFileConnections: 'GetFileConnections?projectId=',
    srctgtConfInsert: 'srcTgtConfSelectInsert',
    deleteFileShareFile: 'DeleteFilefromFileShareList',
    deleteFileCon: 'DeleteFileConection?projectId=',
    SchemaListSelect: 'GetSchemaList?projectId=',
    testMySqlConn: 'SQL2OracleAssessment/sql2ora/TestSqlConnection',
    testorclConn: 'SQL2OracleAssessment/sql2ora/TestOracleConnection',
    GetReqData: 'GetRequestTableData?projectId=',
    deleteTableData: 'DeleteRequestTableData?projectId=',
    GetIndividualLogs: 'GetIndividualLogs',
    GetRunno: 'GetRunNumbers?projectId=',
    PrjExeLogSelectTask: 'PrjExelogSelectTask',
    PrjSchemasListSelectData: 'PrjSchemasListSelectData?projectId=',
    GetOperations: 'GetOperationList?projectId=',
    projectConRunTblInsert: 'RequestTableInsert',
    assessmentsetRedisCache: 'RedisCacheInsertion',
    setRedisCache: 'RedisCacheInsertion',
    getFileFromList: 'GetFileFromList?path=',
    ReportsFilterByTime: 'FilterReportsByTime',
    GetFilesFromExpath: 'GetFilesFromDirectoryEx?path=',
    GetCommonFilesFromDirectory: 'GetCommonFilesFromDirectory?path=',
    GetSchemasByRunId: 'GetSchemasByRunId?runid=',
    GetFilesFromDir: 'GetFilesFromDirectory?path=',
    downloadLargeFiles: 'DownloadLargeFile?filePath=',
    GetInventoryFolders:'SqlGetInventoryFolders',
    getsqlServerKey:'getsqlServerKey',
    downloadInventoryZip:'downloadInventoryZip?serverIp=',
    InsertInventorDetalis:'InsertInventorDetalis',
    downloadScript:'SqldownloadScript',
    GetServerlist:'SqlGetServerlist',
    PrjSQLRuninfoIteration:'PrjSQLRuninfoIteration?projectId=',
    PrjSqlScriptFunctionSelect:'PrjSqlScriptFunctionSelect?projectId=',
    InsertSQLMiCommand:'InsertSQLMiCommand',
    PrjSqlQueryExecute:'PrjSqlQueryExecute',
    PrjSqlScriptcycleSelect:'PrjSqlScriptcycleSelect?projectId=',
    PrjSqlScriptCategorySelect:'PrjSqlScriptCategorySelect?projectId=',
    GetExceution:'TriggerAwr',
    uploadDocument:'UploadCloudFiles',
    CreatePGSchema:'CreatePGSchema',
    createschema:'SQL2OracleAssessment/sql2ora/CreateSchema',
    dropschemaobjs:'DropObjects',
    dropsql2oraObjects:'SQL2OracleAssessment/sql2ora/DropObjects',
    OracleSchemas: 'GetSchema?Conid=',
    OraSchemas: 'SQL2OracleAssessment/sql2ora/GetOracleSchema?Conid=',
    tgtSchemaSelect: 'GetPgSchemas?conid=',
    DeleteSchema: 'DeleteSchema?schemaname=',
    getobjtype:'SQL2OracleAssessment/sql2ora/GetObjectTypes',
    getobjname:'SQL2OracleAssessment/sql2ora/GetObjectNames',
    getObjectTypes: 'GetObjectTypes?projectId=',
    e2emigration: 'TriggetEtoECommand',
    e2emigrationNew: 'TriggerE2EMigration',
    uploadLargeFiles: 'UploadCloudFiles',
    InsertScript: 'InsertScript',
    SelectScriptByCategory:'SelectScriptByCategory?category=',
    CreateSqlSchema:'Ora2SqlAssessment/ora2sql/CreateSqlSchema?schemaname=',
    dropsqlschemobjects:'Ora2SqlAssessment/ora2sql/DropSqlSchemaObjects',
    getSqlSchema:'Ora2SqlAssessment/ora2sql/GetSqlSchemas?conid=',
    getSqlObjects:'Ora2SqlAssessment/ora2sql/GetSqlObjectNames',
    getSqlObjectTypes:'Ora2SqlAssessment/ora2sql/GetSqlObjectTypes?conid=',
    testSqlConnection:'Ora2SqlAssessment/ora2sql/TestSqlConnection',
    getStorageObjects:'GetAssessmentStorageObjectData?projectName=',
    getCodeObjects:'GetAssessmentCodeObjectData?projectName=',
    getSummaryReport:'GetAssessmentSummaryReportData?projectName=',
    e2emigrationAll: 'Redis/TriggetEtoECommand',
    GetConfigMenu:'Migration/GetConfigTypes?migid=',
    DataMigrationCommand:'Common/Migration/DataMigrationCommand',
    mariae2emigrationURL:'Maria2MysqlAssessment/maria2mysql/TriggerMariaEtoECommand',
    GetPgDatabase:'GetPgDatabases?conid=',
    GetMySqlTables:'Maria2MysqlAssessment/maria2mysql/GetMySqlTables?conid=',
    DropMySqlTables:'Maria2MysqlAssessment/maria2mysql/DropMySqlTables?conid=',
    AssessmentCommand:'AssessmentCommand',
    ConversionCommand:'ConversionExtractionCommand',
    getOracleTables:'ora2pgMigration/ora2pg/FetchSourceTables?conid=',
    getDb2Tables:'db2fabricMigration/db2fab/GetDB2Tables?conid=',
    ExtractionFile:'AssessmentCommandwithFile',
    testRedisConn: 'TestRedisConnection',
}
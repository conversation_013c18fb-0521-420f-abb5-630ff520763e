<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <h3 class="main_h px-3 pt-3">  DataBase Index Monitoring  </h3>
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="perForm">
            <div class="row">
                <!--- Category  --->
                <div class="col-md-3 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="targtetConnection"> Connection
                            Name</label>
                        <select formControlName="tgtconnection" #Myselect2 (change)="
                        selTgtId(Myselect2.value);GetperfSchemas() " class="form-select">
                        <option value="" disabled selected>Select Connection Name</option>
                            @for(list of tgtList;track list; ){
                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                            }
                        </select>
                        <div class="alert">
                            @if(f.tgtconnection.touched && f.tgtconnection.invalid) {
                            <p class="text-start text-danger mt-1">Connection Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <!--- Database index Validation --->
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="indexType"> Index Reports</label>
                        <select class="form-select" #mon (change)="selectOption(mon.value)" formControlName="index">
                            <option value="" selected>Select Index</option>
                            <!-- <option value="0">Created Indexes</option>
                            <option value="1">Dropped Indexes</option> -->
                            <option value="2">Full Index Report</option>
                        </select>
                        <div class="alert">
                            @if(f.index.touched && f.index.invalid){
                                <p class="text-start text-danger mt-1">Index Type is required</p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="fromDate"> From Date</label>
                        <input type="datetime-local" class="form-control" formControlName="fromDate" id="selectedFroDate">
                        <div class="alert">
                            @if(f.fromDate.touched && f.fromDate.invalid){
                                <p class="text-start text-danger mt-1"> From Date is required</p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="toDate"> To Date</label>
                        <input type="datetime-local" class="form-control" formControlName="toDate" id="selectedToDate">
                        <div class="alert">
                            @if(f.toDate.touched && f.toDate.invalid){
                                <p class="text-start text-danger mt-1">To Date is required</p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-xl-3 offset-md-9">
                    <div class="">
                        <button class="btn btn-upload w-100" (click)="GetPerfIndexMonitorData(perForm.value)" [disabled]="perForm.invalid" >Fetch Data
                            @if(Monspinner){<app-spinner />}</button>
                    </div>
                </div>

            </div>
        </form>
    </div>
    <div [hidden]="!Monitdate">
        <hr class="dash-dotted" />
        <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-md-9 ps-2 pe-3">
            <button class="btn btn-sign w-100" (click)="exportexcelMonitor()">
                <span class="mdi mdi-arrow-up-thin"></span>Export
                @if(excelSpinn){<app-spinner />}
            </button>
        </div>
        <div class="table-responsive mt-3">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>S.No</th>
                        <th>Type</th>
                        <th>SchemaName</th>
                        <th>TableName</th>
                        <th>IndexName</th>
                        <th>LogDate</th>
                    </tr>
                </thead>
                <tbody>
                    @for (documents of indexMonitor | paginate: { itemsPerPage: 10, currentPage:
                    pageNumber } ; track documents;) {
                    <tr>
                        <td>{{pageNumber*10+$index+1-10}}</td>
                        <td>{{documents.type}}</td>
                        <td>{{documents.schemaname}}</td>
                        <td>{{documents.tablename}}</td>
                        <td>{{documents.indexname}}</td>
                        <td>{{documents.logdate}}</td>
                    </tr>
                    } @empty {
                    <tr>
                        <td colspan="6">
                            <p class="text-center m-0 w-100">No Results</p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
    </div>
</div>
import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, input, Output, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MarkdownModule } from 'ngx-markdown';

interface ChatEntry {
  question: string;
  answer: string;
}

interface TargetConnections {  
Connection_ID: string;
conname: string
dbname:string
}
@Component({
  selector: 'app-aiagent',
  standalone: true,
  imports: [CommonModule,FormsModule,MarkdownModule],
  templateUrl: './aiagent.component.html',
  styles: `
    .btn-clear{
      border: 1px solid #e2e8f0;
      font-size: 14px;
      border-radius: 0.8rem;
      padding: 10px 20px;
      display: flex;
      gap: 5px;
      letter-spacing: 0.5px;
      &:hover{
        background:#e2e8f0
      }
      img {
          width: 18px;
          height: 20px;
      }
    }
    .chatInterface {
        border: 1px solid #e2e8f0;
        border-width: 1px 0px;
        padding: 15px 0px;
        margin: 15px 0px;
    }
        
  .question {
    align-self: flex-end;
    background-color: #7926e8;
    color: white;
    padding: 10px 14px;
    border-radius: 16px 16px 0 16px;
    max-width: 75%;
    word-wrap: break-word;
    font-size: 0.95rem;
  }

  .answer {
    align-self: flex-start;
    background-color: #f6f0ff;
    color: #333;
    padding: 10px 14px;
    border-radius: 16px 16px 16px 0;
    max-width: 75%;
    word-wrap: break-word;
    font-size: 0.95rem;

  }
  .chat-entry {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .chatBox {
    background: #fff;
    padding: 15px;
    border-radius: .8rem;
    margin-bottom: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
    scroll-behavior: smooth;
      max-height: 468px;
      min-height: 56vh;
  }
    .chatHeight{
      max-height: 468px;
      min-height: 50vh;
    }
    .ChatBoxWidth{
      max-width: 1100px;
      margin: 20px auto;
    }
      .chatFooter {
        padding: 0px 20px;
    }
      .form-box {
        display: flex;
        gap: 15px;
        .btn-sign {
            min-width: 130px;
        }
    }

    .bouncing-dots {
  display: flex;
  gap: 0.25rem; /* equivalent to Tailwind's space-x-1 */
}

.dot {
  width: 0.5rem; /* Tailwind w-2 */
  height: 0.5rem; /* Tailwind h-2 */
  background-color: #a855f7; /* Tailwind bg-purple-500 */
  border-radius: 9999px; /* Tailwind rounded-full */
  animation: bounce 1s infinite;
}

.delay-1 {
  animation-delay: 0.1s;
}

.delay-2 {
  animation-delay: 0.2s;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
}

  `
})
export class AIagentComponent {

  // Target Connection Variables 
  targetConnections = input<TargetConnections[]>();
  History = input<ChatEntry[]>();

  // Form 
  question: string = '';
  loading: boolean = false;

  @Output() submitQuestion = new EventEmitter<{question: string;targetConnection: string;}>();
  @Output() clearHistory = new EventEmitter<void>();

  @ViewChild('chatEnd') chatEndRef!: ElementRef;

  selectedTargetConnection:string = ''

  constructor(private cdr: ChangeDetectorRef) {}

  ngAfterViewChecked() {
    this.scrollToBottom();
  }

  scrollToBottom() {
    if (this.chatEndRef) {
      this.chatEndRef.nativeElement.scrollIntoView({ behavior: 'smooth' });
    }
  }
  Getdbname(targetConection:string){
    this.selectedTargetConnection = targetConection
  }

  handleSubmit(): void {
    if(this.selectedTargetConnection == ''){
      alert("Please Select Target Connection and submit")
      return
    }
    const trimmed = this.question.trim();
    if (trimmed) {
      this.submitQuestion.emit({
        question: trimmed,
        targetConnection: this.selectedTargetConnection
      });
      this.question = '';
    }
    this.cdr.detectChanges();  // optional, can help force re-check
  }

  onClearHistory(): void {
    this.clearHistory.emit();
  }

}

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { TestCycleComponent } from '../testing/common/test-cycle/test-cycle.component';
import { WorkLoadComponent } from '../testing/common/work-load/work-load.component';

const routes: Routes = [
  {path:'', component:LayoutComponent, children:[
    {path:'testCycle', component:TestCycleComponent, data: { name: 'Test Cycle'}},
    {path:'workLoad', component:WorkLoadComponent, data: { name: 'Work Load'}}
  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SitRoutingModule { }

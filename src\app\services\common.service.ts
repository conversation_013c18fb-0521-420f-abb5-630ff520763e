import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { APIConstant } from '../constant/APIConstant'
import { environment } from '../environments/environment';
import { GetRunno } from '../models/interfaces/types';

@Injectable({
  providedIn: 'root'
})
export class CommonService {

  constructor(private apiService: ApiService) { }

  apiURL = environment.serviceUrl

  /*--- Get Local Storgae ---*/

  localStorageURL = this.apiURL + APIConstant.login.getLocalStorage;
  getLocalStorage = (body: any): Observable<any> => {
    return this.apiService.get(this.localStorageURL)
  }

  /*--- Check login ---*/
  checkURL = this.apiURL + APIConstant.login.checkLogin
  checkLogin = (body: any): Observable<any> => {
    return this.apiService.post(this.checkURL, body);
  };

  /*--- Check Kuber<PERSON>es login ---*/
  kubeLoginURL = this.apiURL + APIConstant.login.verifywithEmail
  checkKubeLogin = (body: any): Observable<any> => {
    return this.apiService.post(this.kubeLoginURL, body);
  };

  /*--- Check Kubernetes File ---*/
  qubeFileURL = this.apiURL + APIConstant.login.checkQubeFile
  checkQubeFile = (): Observable<any> => {
    return this.apiService.get(this.qubeFileURL)
  }
  //getProjectMenu

  getProjectMenuURL = this.apiURL + APIConstant.header.getProjectMenu
  getProjectMenu = (body: any): Observable<any> => {
    return this.apiService.get(this.getProjectMenuURL + body)
  }

  //GetRunno
  GetRunnoURL = this.apiURL + APIConstant.common.GetRunno;
  GetRunno = (body: any): Observable<GetRunno> => {
    return this.apiService.get(this.GetRunnoURL + + body.projectId + '&migsrcType=' + body.migsrcType);
  };

  //GetSchemasByRunId
  GetSchemasByRunIdURL = this.apiURL + APIConstant.common.GetSchemasByRunId;
  GetSchemasByRunId = (body: string): Observable<any> => {
    return this.apiService.get(this.GetSchemasByRunIdURL + body)
  }

  buildUrl = (base: string, path: string): string => 
    `${environment.serviceUrl2}${base}${path}`;

  postToApi = (url: string, body: any): Observable<any> => 
    this.apiService.post(url, body);

  // Define constants for folder paths
  DBA = 'dba/';
  PerformanceURL = 'performance/';

  // Define API calls using helper functions
  getAgent = (body: any, type: 'DBA' | 'Performance'): Observable<any> => {
    const basePath = type === 'DBA' ? this.DBA : this.PerformanceURL;
    const url = this.buildUrl(basePath, type === 'DBA' ? APIConstant.common.getAgent : APIConstant.common.getPerAgent);
    return this.postToApi(url, body);
  };

  clearHistory = (body: any, type: 'DBA' | 'Performance'): Observable<any> => {
    const basePath = type === 'DBA' ? this.DBA : this.PerformanceURL;
    const url = this.buildUrl(basePath, APIConstant.common.clearChatHistory);
    return this.postToApi(url, body);
  };


  getconnectionurl = this.apiURL +  APIConstant.common.getConnections
  getConList = (body: any): Observable<any> => {
    return this.apiService.get(this.getconnectionurl + body);
  };

}

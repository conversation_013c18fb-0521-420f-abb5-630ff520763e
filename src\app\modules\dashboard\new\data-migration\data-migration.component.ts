import { Component } from '@angular/core';
import { TabsComponent } from '../tabs/tabs.component';
import { DashboardService } from '../../../../services/dashboard.service';
import { BaseChartDirective, NgChartsModule } from 'ng2-charts';
import { ChartConfiguration, ChartDataset, ChartOptions, ChartType } from 'chart.js';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { AssessmentService } from '../../../../services/assessment.service';
import { PercentPipe } from '@angular/common';
import { SpinnerComponent } from "../../../../shared/components/spinner/spinner.component";
import { HotToastService } from '@ngxpert/hot-toast';
declare let $: any;

interface OperationList {
  iteration: string,
  conname: string,
  schemaname: string,
  total_count: string,
  converted: string,
  percentage_passed: number
}

@Component({
  selector: 'app-data-migration',
  standalone: true,
  imports: [TabsComponent, NgChartsModule, PercentPipe, SpinnerComponent],
  templateUrl: './data-migration.component.html',
  styles: ``
})
export class NewDataMigrationComponent {

  connectionsList: Array<String> = [];
  schemaList: any = [];
  iterationList: Array<String> = [];
  operationsList: OperationList[] = [];
  operationsCopy: OperationList[] = this.operationsList
  operationsTable: OperationList[] = this.operationsList

  connectionID: string = '';
  connectionValue: string = '';
  schemaId: string = '';
  schemaName: string = '';
  iterationId: string = ''


  /*---- Search bar ---*/
  searchText: string = '';

  /*---- line Charts ----*/


  // Custom plugin for displaying percentage in the center
  public centerTextPlugin = {
    id: 'centerTextPlugin',
    afterDraw: (chart: any) => {
      if (chart.canvas.id === 'myDoughnutChart') {
        const { ctx, chartArea: { width, height } } = chart;

        ctx.save();
        const percentage = ((this.TGtnumRows / this.numRows) * 100).toFixed(2);

        ctx.font = 'bold 25px Geist';
        ctx.fillStyle = '#333';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        const centerX = width / 2;
        const centerY = height / 2 + 20;

        ctx.fillText(percentage, centerX, centerY);
        ctx.restore();
      }
    }
  };


  public lineChartData: ChartConfiguration<'doughnut'>['data'] = {
    labels: [
      'Source Count',
      'Target Count',
    ],
    datasets: [
      {
        data: [],
        label: 'Row Migration',
        circumference: 180,
        rotation: 270,
        backgroundColor: [
          '#8b36ff',
          '#f0f0f0'
        ],
        hoverBackgroundColor: ['#4c00b5', '#dab1fd'],
        borderWidth: 0,

      }
    ]
  };
  public lineChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 42,
    rotation: 1 * Math.PI,
    circumference: 1 * Math.PI,
    spacing: 5,
    elements: {
      arc: {
        borderWidth: 2,
        borderColor: '#fff',
        borderRadius: 10 // Adjust for rounded edges
      }
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }

  };
  public lineChartLegend = false;
  public lineChartPlugins = [this.centerTextPlugin];


  /*--- source vs extraction % ---*/
  public sourceVSChartLabels: string[] = [];
  public sourceVSChartType = 'bar';
  public sourceVSChartLegend = true;
  public sourceVSChartData: {
    data: number[];
    label: string;
    [key: string]: any;
  }[] = [
      {
        data: [],
        label: 'Source & Target %',
        borderColor: '#a25eff',
        backgroundColor: '#a25eff',
        hoverBackgroundColor: '#8b36ff',
        barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
        maxBarThickness: 70, // Set max bar thickness if auto-calculating
      },
      // {
      //   data: [],
      //   label: 'Total Target Tablesize',
      //   borderColor: '#4c00b5',
      //   backgroundColor: '#4c00b5',
      //   hoverBackgroundColor: '#4c00b5',
      //   barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      //   maxBarThickness: 70, // Set max bar thickness if auto-calculating
      // }
    ];
  public sourceVSChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
          label: (tooltipItem) => {
            const dataPoint = tooltipItem.formattedValue;
            // Return multiple labels for the tooltip
            return [
              " Count" + ': ' + dataPoint,  // Main dataset label
            ];
          }
        }
      }
    }
  };

  // CPU
  public CPUsourceVSChartLabels: string[] = [];
  public CPUsourceVSChartType = 'bar';
  public CPUsourceVSChartLegend = true;
  public CPUsourceVSChartData: {
    data: number[];
    label: string;
    [key: string]: any;
  }[] = [
      {
        data: [],
        label: ' CPUPercentage',
        borderColor: '#a25eff',
        backgroundColor: '#a25eff',
        hoverBackgroundColor: '#8b36ff',
        barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
        maxBarThickness: 70, // Set max bar thickness if auto-calculating
      },
      // {
      //   data: [89],
      //   label: ' Total Target CPU',
      //   borderColor: '#4c00b5',
      //   backgroundColor: '#4c00b5',
      //   hoverBackgroundColor: '#4c00b5',
      //   barThickness: 20, // Set exact bar thickness (smaller number for thinner bars)
      //   maxBarThickness: 70, // Set max bar thickness if auto-calculating
      // }
    ];
  public CPUsourceVSChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'point'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
        callbacks: {
          label: (tooltipItem) => {
            const dataPoint = tooltipItem.formattedValue;
            // Return multiple labels for the tooltip
            return [
              " CPU%" + ': ' + dataPoint,  // Main dataset label
            ];
          }
        }
      }
    }
  };
  /*--- Long Running Queries Chart Data ---*/

  public sObjectChartLabels = [''];
  public sObjectChartType = 'bar';
  public sObjectChartLegend = true;
  public sObjectChartData: {
    data: number[];
    label: string;
    [key: string]: any;
  }[] = [
      {
        data: [],
        label: ' memoryPercentage ',
        borderColor: '#a25eff',
        backgroundColor: '#a25eff',
        hoverBackgroundColor: '#8b36ff',
        maxBarThickness: 70,
      }
    ];
  public sObjectChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    scales: {
      x: {
        stacked: true
      },
      y: {
        stacked: true
      },
    },
    interaction: {
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true  // This line disables the legend
      },
      tooltip: {
        enabled: true,
        usePointStyle: true,
        titleSpacing: 5,
        backgroundColor: '#ffffff', // White background
        titleColor: '#333333', // Dark title color
        bodyColor: '#666666', // Lighter body text color
        borderColor: '#cccccc', // Light gray border
        borderWidth: 1, // Thin border
        cornerRadius: 8, // Rounded corners
        padding: 10, // Padding inside the tooltip
      }
    }
  };
  projectId: any;

  constructor(private toast: HotToastService, private dbService: DashboardService, private datamigration: DataMigrationService, private assessmentService: AssessmentService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
  }

  ngOnInit(): void {
    this.GetConsList();
   // this.getThroughput();
  }
  ConsList: any = [];
  tgtList: any
  GetConsList() {
    this.datamigration.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }
  /*--- Fetch Iteration ---*/
  gb: any;
  numRows: number = 0;
  extractedTablesCount: number = 0;
  totalTablesCount: number = 0;
  getOperationsList(Conid: any, Schema: any, TableName: any) {
    const obj = {
      Conid: Conid,
      schema: Schema,
    }
    this.dbService.GetSourceCountslist(obj).subscribe((data: any) => {
      // this.operationsList = data;
      if (Array.isArray(data) && data.length > 0) {
        this.gb = data.reduce((sum: number, item) => {
          const gbValues = item.gb.split(/\s+/).map(Number);
          return sum + gbValues.reduce((gbSum: number, value: number) => gbSum + value, 0);
        }, 0).toFixed(2);
        this.numRows = data.reduce((sum: number, item) => {
          const numRowsValues = item.num_Rows.split(/\s+/).map(Number);
          return sum + numRowsValues.reduce((rowSum: number, value: number) => rowSum + value, 0);
        }, 0);
        this.totalTablesCount = Array.isArray(data)
          ? data.filter((item: any) => item.table_name).length
          : 0;
      }

      (this.gb)
      const filteredData = data.filter((item: any) => item.table_name === TableName);

      if (filteredData.length > 0) {
        this.gb = filteredData[0].gb;
        this.numRows = filteredData[0].num_Rows;
      }
      this.updateChartData();
      // this.updateDoughnutChart();
    });

  }

  /*--- Fetch Schemas ---*/
  getSchema(value: string) {
    this.dbService.getSchemalist(value).subscribe((data: any) => {
      this.schemaList = data
    })
  }
  selectedConId: any;
  selectedSchema: any;
  onSourceConnectionChange(conId: any) {
    this.selectedConId = conId;
    this.getSchema(conId); // Fetch schemas
    this.schemaList = [];
    this.ObjectNamesList = [];
  }

  onSchemaChange(schema: string) {
    this.selectedSchema = schema;
    (this.selectedSchema)
    if (this.selectedConId && this.selectedSchema) {
      this.getOperationsList(this.selectedConId, this.selectedSchema, null);
      this.getTableDetails(this.selectedConId, this.selectedSchema);
    }

  }
  tgtSchemaList: any
  getTgtSchema(tgt: string) {
    this.assessmentService.tgtSchemaSelect(tgt).subscribe((data: any) => {
      this.tgtSchemaList = data;
    });
  }
  //getThroughPutList
  troughput: any
  getThroughput() {
    // this.dbService.getThroughPutList().subscribe((data: any) => {
    //   this.troughput = data['Table1'];
    //   this.troughput = data?.Table1?.[0]?.thoughput || '0';
    // });
  }
  selectedTable: string = '';
  ObjectNamesList: any;
  getTableDetails(conid: any, sch: any) {
    let obj = {
      Conid: conid,
      objectType: "TABLE",
      schemaname: sch
    }
    this.assessmentService.getObjNames(obj).subscribe((data: any) => {
      this.ObjectNamesList = data
    })
  }

  onTableChange(tab: any) {
    this.selectedTable = tab;
    if (this.selectedConId && this.selectedSchema && this.selectedTable) {
      this.getOperationsList(this.selectedConId, this.selectedSchema, this.selectedTable);
    }
    if (this.selectedtgtConId && this.selectedtgtSchema) {
      this.getTgtData(this.selectedtgtConId, this.selectedtgtSchema, this.selectedTable);
    }
    this.onTableSelect(this.selectedTable);
  }
  selectedtgtConId: any;
  ontargetConnectionChange(conId: any) {
    this.selectedtgtConId = conId;
    this.getTgtSchema(conId); // Fetch schemas
    //this.getTgtData(conId, null, null);
    this.tgtSchemaList = [];
    // this.ObjectNamesList = [];
  }
  selectedtgtSchema: any;
  ontgtSchemaChange(schema: string) {
    this.selectedtgtSchema = schema;
    if (this.selectedtgtConId && this.selectedtgtSchema) {
      this.getTgtData(this.selectedtgtConId, this.selectedtgtSchema, null)
    }

  }
  tgtDataList: any;
  Tgtgb: any;
  TGtnumRows: any;
  getTgtData(tgt: any, schema: any, TableName: any) {
    const obj = {
      Conid: tgt,
      schema: schema
    }
    this.dbService.getTgtDatat(obj).subscribe((data: any) => {
      this.tgtDataList = data;

      if (Array.isArray(data) && data.length > 0) {
        const normalizedSchema = schema?.toUpperCase();
        const normalizedTable = TableName?.toUpperCase();
        if (TableName) {
          const filteredData = data.filter((item: any) =>
            item.schema &&
            item.table_count &&
            item.schema?.toUpperCase() === normalizedSchema &&
            item.table_count?.toUpperCase() === normalizedTable
          );
          if (filteredData.length > 0) {
            this.Tgtgb = parseFloat(filteredData[0].total_size_gb);
            this.TGtnumRows = parseInt(filteredData[0].row_count, 10);
          }
          this.extractedTablesCount = Array.isArray(data)
            ? data.filter((item: any) => item.table_count).length
            : 0;
        } else {
          this.Tgtgb = data.reduce((sum: number, item: any) => {
            return sum + (parseFloat(item.total_size_gb) || 0);
          }, 0).toFixed(2);

          this.TGtnumRows = data.reduce((sum: number, item: any) => {
            return sum + (parseInt(item.row_count, 10) || 0);
          }, 0);
          this.extractedTablesCount = Array.isArray(data)
            ? data.filter((item: any) => item.table_count).length
            : 0;
        }
      }
      this.updateChartData();
      //this.updateDoughnutChart();
    });
  }

  onTableSelect(tableName: string) {
    const normalizedTable = tableName.toUpperCase();
    const normalizedSchema = this.selectedSchema?.toUpperCase(); // assumed

    const match = this.tgtDataList.find((item: any) =>
      item.schema === normalizedSchema &&
      item.table_count === normalizedTable
    );

    if (match) {
      this.Tgtgb = match.total_size_gb;
      this.TGtnumRows = match.row_count;
    } else {
      this.Tgtgb = 0;
      this.TGtnumRows = 0;
    }

    this.updateChartData();
    // this.updateDoughnutChart();
  }
  CpuDetails: any;
  totalCpuCores: number = 0;
  utilizedCpu: number = 0;
  totalMemoryGb: number = 0;
  memoryUtilizationGb: number = 0;
  selectedConnectionName: any;
  getCPUData(tgt: string) {
    this.dbService.getCPUData(tgt).subscribe((data: any) => {
      this.CpuDetails = data['Table1'];

      if (Array.isArray(this.CpuDetails) && this.CpuDetails.length > 0) {
        const cpu = this.CpuDetails[0];
        this.totalCpuCores = parseFloat(cpu.total_cpu_cores);
        this.utilizedCpu = parseFloat(cpu.utilized_cpu);
        this.totalMemoryGb = parseFloat(cpu.total_memory_gb);
        this.memoryUtilizationGb = parseFloat(cpu.memory_utilization_gb);

        const CPUPercentage = parseFloat(cpu.cpu_utilization_percentage);
        this.CPUsourceVSChartLabels = [this.selectedConId];
        this.CPUsourceVSChartData[0].data = [CPUPercentage];

        const mem = this.CpuDetails[0];
        const memoryPercentage = parseFloat(mem.memory_utilization_percentage);
        this.sObjectChartLabels = [this.selectedConId];
        this.sObjectChartData[0].data = [memoryPercentage];
      }
    });
  }
  gbConversionPercent: any
  updateChartData() {
    // this.sourceVSChartLabels = [this.selectedConId, this.selectedtgtConId];
    // this.sourceVSChartData[0].data = [this.gb, this.Tgtgb];
    // console.log(this.gb)
    const gbConversionPercent = this.gb > 0 ? ((this.Tgtgb / this.gb) * 100).toFixed(2) : '0.00';
    this.sourceVSChartLabels = [`${this.selectedConId} (${this.selectedtgtConId})`];
    this.sourceVSChartData[0].data = [parseFloat(gbConversionPercent)];

  }
  percentage: any;
  openModal() {
    $('#demo').offcanvas('show');
  }
  openPopup() {
    // this.schemaName = []
    // this.selectedschemas = ''
    // this.AssessmentForm.reset();
  }
  selsrcId:any;
  seltgtId:any;
  onSourceChange(event: any) {
    this.selsrcId = event.target?.value;
  }
  
  onTargetChange(event: any) {
    this.seltgtId = event.target?.value;
  }
  //datamigrationNewCommand
  runinfospins: boolean = false;
  DatamigrationCommand() {
    let obj = {
      task: "CPU_Memory_Utilization",
      projectId: this.projectId.toString(),
      sourceConnectionId: this.selsrcId,
    }
    this.dbService.datamigrationNewCommand(obj).subscribe((data: any) => {
      this.CpuDetails = data['Table1'];
      this.runinfospins = false;
      $('#demo').offcanvas('hide');
      this.toast.success("Successfully triggered the CPU utilization Pod")
    },
      error => {
        $('#demo').offcanvas('hide');
        this.toast.error('Something went wrong')
      })
  }
}

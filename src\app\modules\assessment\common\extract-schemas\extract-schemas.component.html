<!--- Bread Crumb --->
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    {{schemalable}} Extraction
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="extractschemaForm">
                        <div class="row">
                            @if(this.migtypeid == '48'){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Category">Category</label>
                                    <select class="form-select" formControlName="Category">
                                        <option disabled>Select a Source Connection</option>
                                        <option value="schema">Schema</option>
                                        <option value="pipeline">Pipeline</option>
                                    </select>
                                </div>
                            </div>
                            }@else{}
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Connection Type</label>
                                    <select class="form-select" #srctype
                                        (change)="selectFilesAndDatabase(srctype.value)">
                                        <option selected disabled>Select a Connection Type</option>
                                        @for(list1 of databases;track list1; ){
                                        <option value="{{ list1.value }}"> {{ list1.option }} </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!sourcedata">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Source
                                        Connection</label>
                                    <select class="form-select" #org (change)="getDb(org.value)"
                                        formControlName="dataBase">
                                        <option disabled>Select a Source Connection</option>
                                        @for ( list of ConsList; track list) {
                                        <option value="{{ list.Connection_ID }}">{{ list.conname }}</option>
                                        }
                                    </select>
                                    @if ( fs.dataBase.touched && fs.dataBase.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (fs.dataBase.errors.required) {Source Connection is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            @if(migtypeid!="40"){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3" [hidden]="!targetdata">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Target
                                        Connection</label>
                                    <select class="form-select" #tgtext (change)="getgtId(tgtext.value)"
                                        formControlName="tgtdatabase">
                                        <option disabled>Select a Target Connection</option>
                                        @for ( tgtlist of targetList; track tgtlist) {
                                        <option value="{{ tgtlist.Connection_ID }}">{{ tgtlist.conname }}</option>
                                        }
                                    </select>
                                    @if ( fs.tgtdatabase.touched && fs.tgtdatabase.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (fs.tgtdatabase.errors.required) {Target Connection is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            }
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                <div class="body-header-button">
                                    @if(this.migsrcTypevalue=='S'){
                                    <button class="btn btn-upload w-100 " [disabled]="extractschemaForm.invalid"
                                        (click)="AssessmentCommand('S')"> <span class="mdi mdi-cog-play"></span> Execute
                                        @if(getSpin_S){<app-spinner />}</button>
                                    }@else {
                                    <button class="btn btn-upload w-100 " [disabled]="extractschemaForm.invalid"
                                        (click)="AssessmentCommand('T')">
                                        <span class="mdi mdi-cog-play"></span> Execute
                                        @if(getSpin_T){<app-spinner />}</button>
                                    }
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        @if(migtypeid!="40"){
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Create Target {{schemalable}}
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="createTargetForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Target
                                        Connection</label>
                                    <select formControlName="ctTargetConnection" placeholder="Name" class="form-select">
                                        <option disabled>Select a Target Connection</option>
                                        @for ( role of targetList;track role;) {
                                        <option value="{{ role.Connection_ID }}">{{role.conname }}</option>
                                        }
                                    </select>
                                    @if(fc.ctTargetConnection.touched && fc.ctTargetConnection.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(fc.ctTargetConnection.errors.required) {Target Connection is required}
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">{{schemalable}}</label>
                                    <input type="text" class="form-control" formControlName="ctSchema" />
                                    @if(fc.ctSchema.touched && fc.ctSchema.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(fc.ctSchema.errors.required) {{{schemalable}} is required}
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="body-header-button">
                                    <button [disabled]="createTargetForm.invalid" class="btn btn-upload w-100 mt-4"
                                        (click)="createSchema(createTargetForm.value)">
                                        <span class="mdi mdi-plus"></span>
                                        Create Schema @if(spin_createschema){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                    <!--- source and target code details --->
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    {{dropschema}}
                </button>
            </h3>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="deleteForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Target
                                        Connection</label>
                                    <select formControlName="targetConnection" placeholder="Name" class="form-select"
                                        #tgtCon (change)="getTgtSchema(tgtCon.value)">
                                        <option disabled>Select a Target Connection</option>
                                        @for ( role of targetList;track role;) {
                                        <option value="{{ role.Connection_ID }}">{{role.conname }}</option>
                                        }
                                    </select>
                                    @if(f.targetConnection.touched && f.targetConnection.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(f.targetConnection.errors.required) {Target Connection is required}
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">{{schemalable}}</label>
                                    <ng-select groupBy="type" [selectableGroup]="true"
                                        (change)="getObjecttypes(schemaName);getMySqlTables(schemaName)"
                                        formControlName="schema" [items]="tgtSchemaList" [multiple]="true"
                                        bindLabel="schemaname" [closeOnSelect]="false" bindValue="schemaname"
                                        [(ngModel)]="schemaName">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" value={{item.schemaname}} />
                                            {{item.schemaname}}
                                        </ng-template>
                                    </ng-select>
                                    @if(f.schema.touched && f.schema.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(f.schema.errors.required) {{{schemalable}} is required}
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">{{schemalable}}</label>
                                    <select formControlName="schema" class="form-select" [(ngModel)]="schemaName" #sche
                                        (change)="getObjecttypes(sche.value);getMySqlTables(sche.value)">
                                        <option disabled>Select a Schema</option>
                                        @for ( schema of tgtSchemaList; track schema) {
                                        <option value="{{ schema.schemaname }}"> {{schema.schemaname }}</option>
                                        }
                                    </select>
                                    @if(f.schema.touched && f.schema.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(f.schema.errors.required) {{{schemalable}} is required}
                                    </p>
                                    }
                                </div>
                            </div> -->
                            @if(migtypeid=="30" || migtypeid=="39"|| migtypeid=="28"){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Object
                                        Type</label>
                                    <select formControlName="objTye" class="form-select" #obty
                                        (change)="selectObjectType(obty.value);getObjectNames()">
                                        <option disabled>Select Object Type</option>
                                        @for ( objtype of objectTypesList; track objtype) {
                                        <option value="{{ objtype.objectType }}"> {{objtype.objectType }}</option>
                                        }
                                    </select>
                                    @if(f.objTye.touched && f.objTye.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(f.objTye.errors.required) {Object Type is required}
                                    </p>
                                    }
                                </div>
                            </div>
                            }
                            @if(migtypeid=="30"|| migtypeid=="28"|| migtypeid == "31"|| migtypeid=="39" ){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Object Name</label>
                                    <!-- <select formControlName="objnames" class="form-select">
                                            <option disabled>Select Object Name</option>
                                            @for ( objnames of ObjectNamesList; track objnames) {
                                            <option value="{{ objnames.objectName }}"> {{objnames.objectName }}</option>
                                            }
                                        </select> -->
                                    <ng-select [items]="ObjectNamesList" [multiple]="true" bindLabel="objectName"
                                        groupBy="type" [selectableGroup]="true" formControlName="objnames"
                                        (change)="selectObjNames(selectedItems)" [closeOnSelect]="false"
                                        bindValue="objectName" [(ngModel)]="selectedItems">
                                        <ng-template ng-optgroup-tmp let-item="item" let-item$="item$"
                                            let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [ngModel]="item$.selected" />
                                            {{item.type | uppercase}}
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" /> {{item.objectName}}
                                        </ng-template>

                                    </ng-select>
                                    @if(f.objnames.touched && f.objnames.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(f.objnames.errors.required) {Object Name is required}
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-6"></div>
                            }
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4">
                                <div class="body-header-button">
                                    <button [disabled]="deleteForm.invalid" class="btn btn-upload w-100"
                                        (click)="DeleteSchema()">
                                        <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        {{dropschema}} @if(spin_drop){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                    <!--- source and target code details --->
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTestEx" aria-expanded="false" aria-controls="flush-collapse">
                    Execution Status
                </button>
            </h2>
            <div id="flush-collapseTestEx" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-7 offset-5 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                            <button class="btn btn-sync" (click)="getreqTableData()">
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <!-- <th>Run No</th> -->
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of tabledata| searchFilter: datachange1| paginate:{ itemsPerPage: piA,
                                currentPage:
                                p1,
                                id:'second' };
                                track con;) {
                                <tr>
                                    <!-- <td>{{p1*piA+$index+1-piA}}</td> -->
                                    <!-- <td>{{con.run_id}}</td> -->
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_category }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        {{con.status}}
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="p1 = $event" id="second"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Execution Logs
                </button>
            </h3>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-7 offset-5 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="filterExecutionReports()">
                                @if(ref_spin_repo){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                        <!-- <div class="col-sm-6 col-md-4 col-xl-2">
                            <button class="btn btn-sync mt-2" (click)="filterExecutionReports()">Reload
                                @if(ref_spin_repo){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                        <div class="col-sm-6 col-md-8 col-xl-10">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                        </div> -->
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(logs of ExecututionLogs |searchFilter:
                            datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: page3, id:'Four'};
                            track logs){
                            <tr>
                                <td>{{ page3*10+$index+1-10 }}</td>
                                <td>{{logs.fileName }}</td>
                                <td>{{logs.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(logs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page3 = $event" id="Four">
                    </pagination-controls>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseFour" aria-expanded="false" aria-controls="flush-collapseFour">
                    Execution Reports
                </button>
            </h3>
            <div id="flush-collapseFour" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-7 offset-5 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="filterExecutionReports()">
                                @if(ref_spin_repo){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                        <!-- <div class="col-sm-6 col-md-4 col-xl-2">
                            <button class="btn btn-sync mt-2" (click)="filterExecutionReports()">Reload
                                @if(ref_spin_repo){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div> -->
                        <!-- <div class="col-sm-6 col-md-8 col-xl-10">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                        </div> -->
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(exefiles of ExecututionFiles |searchFilter:
                            datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: page4, id:'five'};
                            track exefiles){
                            <tr>
                                <td>{{ page4*10+$index+1-10 }}</td>
                                <td>{{exefiles.fileName }}</td>
                                <td>{{exefiles.created_dt }}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(exefiles)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page4 = $event" id="five">
                    </pagination-controls>
                </div>
            </div>
        </div>
        }
    </div>
    <!--- extract Form Details --->
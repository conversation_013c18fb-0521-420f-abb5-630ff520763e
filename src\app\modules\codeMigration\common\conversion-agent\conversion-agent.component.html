<!--- Bread Crumb --->
<div class="v-pageName">{{pageName}} </div>

<!--- Manual conversion form  --->
<div class="body-main">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="manualForm">
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Run Number</label>
                            <select formControlName="runNumber" placeholder="Name" class="form-select" #run
                                (change)="getSchema(run.value)">
                                <option disabled value="">Select a Run Number</option>
                                @for ( list of runNumbersList; track list) {
                                <option value="{{ list.iteration_id }}">{{list.iteration_id }}</option>
                                }
                            </select>
                            @if(ff.runNumber.touched && ff.runNumber.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.runNumber.errors.required) {Run No is required}
                            </p>
                            }
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Source Schema</label>
                            <select formControlName="schema" class="form-select" #sche
                                (change)="selectSrcSchema(sche.value)">
                                <option disabled value="">Select a Source Schema</option>
                                @for ( schema1 of srcSchemaList; track schema1) {
                                <option value="{{ schema1.schema_id }}"> {{schema1.schema_name }}</option>
                                }
                            </select>
                            @if(ff.schema.touched && ff.schema.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.schema.errors.required) {schema is required}
                            </p>
                            }
                        </div>
                    </div>
                    @if(migtypeid !="33"){
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Target Schema</label>
                            <select formControlName="targetSchema" class="form-select" [(ngModel)]="schemaName" #schema
                                (change)="getObjectType(schema.value)">
                                <option disabled value="">Select a Target Schema</option>
                                @for ( schema of tgtSchemaList1; track schema) {
                                <option value="{{ schema.schema_id }}"> {{schema.schema_name }}</option>
                                }
                            </select>
                            @if(ff.targetSchema.touched && ff.targetSchema.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.targetSchema.errors.required) {Target schema is required}
                            </p>
                            }
                        </div>
                    </div>
                    }
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Type</label>
                            <select formControlName="objectType" class="form-select"
                                (change)="getobjtypeid(object.value)" #object>
                                <option value="">Select a Object Type</option>
                                @if(migtypeid=="33"){
                                <option value ="" selected>DAL</option>
                                }
                                @else{
                                @for ( objects of objectTypeList; track objects) {
                                <option value="{{objects.object_type_id }}"> {{objects.object_type }}</option>
                                }
                                }
                            </select>
                            @if(migtypeid!="33"){
                            @if(ff.objectType.touched && ff.objectType.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.objectType.errors.required) {Object Type is required}
                            </p>
                            }
                        }
                        </div>
                    </div>
                    @if(migtypeid != "33"){
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Status</label>
                            <select formControlName="objectStatus" class="form-select" #status
                                (change)="getObjectName(status.value)">
                                <option disabled value="">Select a Object Status</option>
                                <option value="true">Deployed</option>
                                <option value="false">Non Deployed</option>
                            </select>
                            @if(ff.objectStatus.touched && ff.objectStatus.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.objectStatus.errors.required) {Object Status is required}
                            </p>
                            }
                        </div>
                    </div>
                    }
                    @if(this.migtypeid== "33")
                    { <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Name </label>
                            <select formControlName="objectName" class="form-select"
                                (change)="getObjectStatus(obj.value);getObjectCode()" formControlName="objectName" #obj>
                                <option disabled value="">Select a Object Name</option>
                                @for ( objects of objectNames; track objects) {
                                <option value="{{ objects.object_name }}"> {{objects.object_name }}</option>
                                }
                            </select>
                            @if(ff.objectName.touched && ff.objectName.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.objectName.errors.required) {Object Name is required}
                            </p>
                            }
                        </div>
                    </div>}
                    @else{
                    <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 px-1">
                        <div class="form-group">
                            <label class="form-label d-required" for="name">Object Name @if(hidedata){( <small>{{
                                    ff.objectStatus.value == 'true' ? 'count :' : 'count : ' }}
                                    @if(objectNames){{{objectNames.length}}}</small>)}</label>
                            <select formControlName="objectName" class="form-select"
                                (change)="getObjectStatus(obj.value);getObjectCode()" formControlName="objectName" #obj>
                                <option disabled value="">Select a Object Name</option>
                                @for ( objects of objectNames; track objects) {
                                <option value="{{ objects.object_name }}"> {{objects.object_name }}</option>
                                }
                            </select>
                            @if(ff.objectName.touched && ff.objectName.invalid){
                            <p class="text-start text-danger mt-1">
                                @if(ff.objectName.errors.required) {Object Name is required}
                            </p>
                            }
                        </div>
                    </div>
                    }



                </div>
            </form>
            <!--- source and target code details --->
            @if(objectSelected){
            <div class="row">
                <div class="col-md-6">
                    <p><b>Source Code </b> </p>
                </div>
                <div class="col-md-6">
                    <p> <b>Target Code </b></p>
                </div>
                <div class="stmt_list">
                    <form>
                        @for (statement of statementList; track statement) {
                        <div class="row mx-0">
                            <div class="col-md-6 border_right pl-0">
                                <div class="stmt_card">
                                    <div class="stmt_block">
                                        @if(!statementShow && statement.tgt_id == currentStmt){
                                        <p>
                                            <a type="button" class="btn btn-stmt"
                                                (click)="convertCode(statement.tgt_id)">
                                                Conversion Statement
                                            </a>
                                        </p>
                                        }
                                        <textarea #div1 (keyup)="autoGrowTextZone($event)" id="div1"
                                            class="form-control gb_text" placeholder="Statement" disabled
                                            value="{{statement.src_code}}"> {{statement.src_code}}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 border_left">
                                <div class="stmt_card">
                                    <div class="stmt_block">
                                        <textarea #div2 id="div2"
                                            (change)="textareaChanged(statement.tgt_id, div2.value)"
                                            (keyup)="autoGrowTextZone($event)" class="form-control gb_text"
                                            placeholder="Statement"
                                            value="{{statement.updated_code}}">{{statement.updated_code}}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        }

                    </form>
                </div>
            </div>
            }
            @if(objectSelected){
            <hr/>
            <div class="row">
                <form class="form qmig-Form" [formGroup]="deploymentForm">
                    <div class="row">
                        @if(!displayTargetConnection){
                        <div class="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-6">
                            <div class="row">                                
                            <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-4 pl-1">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Target Connection</label>
                                    <select placeholder="Target Connection" formControlName="conname" class="form-select"
                                        #Myselect2 (change)=" selTgtId(Myselect2.value)">
                                        <option disabled>Select a Target Connection</option>
                                        @for ( list of targetList; track list) {
                                        <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                        }
                                    </select>
                                    @if ( f.conname.touched && f.conname.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.conname.errors.required) { Target Connection is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-4 px-1">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Time Taken To Fix</label>
                                    <input type="text" id="timetaken" formControlName="time_taken_min" placeholder="Time"
                                        class="form-control" />
                                    @if ( f.time_taken_min.touched && f.time_taken_min.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.time_taken_min.errors.required) { Time Taken is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-4 ps-1">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Observation Text</label>
                                    <input type="text" id="deployeeText" formControlName="observations" placeholder="Observation Text"
                                        class="form-control" />
                                    @if ( f.observations.touched && f.observations.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.observations.errors.required) { Observation Text is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            </div>
                        </div>
                        }
                        <div class="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-6" [ngClass]="displayTargetConnection ?'offset-6' : ''">
                            <div class="row mt-3 pt-2">
                                <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-4">
                                    <button class="btn btn-upload w-100" [disabled]="buttonDisable" (click)="saveData()">
                                        <span class="mdi mdi-content-save-edit btn-icon-prepend"></span>
                                        Save@if(saveSpin){<app-spinner />}</button>
                                </div>
                                <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-4">
                                    <button class="btn btn-upload w-100" [disabled]="deploymentForm.invalid"
                                        (click)="ConversionCommand(deploymentForm.value)">
                                        <span class="mdi mdi-cog-play btn-icon-prepend"></span>
                                        Deploy@if(deploySpin){<app-spinner />}</button>
                                </div>
                                <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-4">
                                    <button class="btn btn-upload w-100" [disabled]="buttonDisable"
                                        [hidden]="displayTargetConnection" (click)="Reset()">
                                        <span class="mdi mdi-content-save-off-outline btn-icon-prepend"></span>
                                        Reset</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            }

        </div>
    </div>
</div>
<!--- Deployment log details --->
@if(objectSelected){
<div class="qmig-card my-3">
    <h3 class="main_h px-3 pt-3">Logs & Agent Execution</h3>
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Deployment Log
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionPanelsStayOpenExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="deploymentLog">
                        <div class="row">
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Deployed time
                                        stamp</label> @if(!deploystatusSpin){
                                    <button class="btn" (click)="GetDeployStatus()">
                                        <span class="mdi mdi-sync"></span>
                                    </button>
                                    }
                                    <select class="form-select" #Time (change)="filterDeployData(Time.value)" formControlName="timeStamp" [ngModel]="DeployCreateddata">
                                        <option selected value="">Select Deploy time stamp</option>
                                        @for ( stamp of TgtDeployData; track stamp) {
                                        <option value="{{stamp.created_dt}}"> {{stamp.created_dt }}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Deployed
                                        Status</label>
                                    <input type="text" class="form-control" value="{{deploystatuss}}"
                                        placeholder="Status" formControlName="status" />
                                </div>
                            </div>
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Deploy Time </label>
                                    <input type="text" class="form-control" value="{{deployTime}}"
                                        placeholder="Time" formControlName="time" />
                                </div>
                            </div>
                            <div class="col-md-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Deploy Cumulative
                                        Time </label>
                                    <input type="text" class="form-control" value="{{totaldeploy}}"
                                        placeholder="cumulative Time" formControlName="cumulativeTime" />
                                </div>
                            </div>
                            <div class="col-md-6 col-xl-6">
                                <div class="form-group">
                                    <h6>Status Message</h6>
                                    <textarea id="w3review" name="w3review" class="form-control"
                                        placeholder="Status Message" value="{{statusMessages}}"
                                        formControlName="statusMessage" rows="5" cols="50"></textarea>
                                </div>
                            </div>
                            <div class="col-md-6 col-xl-6">
                                <div class="form-group">
                                    <h6>Observation Text</h6>
                                    <textarea id="w3review" name="w3review" class="form-control"
                                        placeholder="Status Message" value="{{observations}}"
                                        formControlName="observationTime" rows="5" cols="50">
                                </textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne1">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne1" aria-expanded="false" aria-controls="flush-collapseOne1">
                    Conversion Agent Execution
                </button>
            </h2>
            <div id="flush-collapseOne1" class="accordion-collapse collapse" aria-labelledby="flush-headingOne1"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="AgentExecutionForm">
                        <div class="row">                                
                            <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Target Connection</label>
                                    <select placeholder="Target Connection" formControlName="targetConnection" class="form-select"  (change)="getAgentTargetStatments(atgtc.value)" #atgtc>
                                        <option disabled value="">Select a Target Connection</option>
                                        @for ( list of targetList; track list) {
                                        <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                        }
                                    </select>
                                    @if ( af.targetConnection.touched && af.targetConnection.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (af.targetConnection.errors.required) { Target Connection is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="row">
                                    <div class="col-12 col-sm-9 col-md-9 col-lg-9 col-xl-9">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Deployment Error</label>
                                            <textarea id="w3review" name="w3review" formControlName="deploymentError"
                                                class="form-control" item id="deployeeText" placeholder="Deployment Error" rows="4"
                                                cols="20"></textarea>
                                            @if ( af.deploymentError.touched && af.deploymentError.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (af.deploymentError.errors.required) { File is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3"> 
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Max Attempt (per statement)</label>
                                            <input type="text" value="5" class="form-control" formControlName="maxAttempts" />
                                        </div>
                                        <div class="form-group">
                                            <button class="btn btn-upload w-100" type="button" [disabled]="AgentExecutionForm.invalid" (click)="startAgent(AgentExecutionForm.value)">Start Agent</button>
                                        </div>                                        
                                    </div>
                                </div>
                            </div> 
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Conversion Agent Status
                </button>
            </h3>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-sm-6 col-md-4 col-xl-2">
                            <button class="btn btn-sync mt-2" (click)="getreqTableData()">Reload
                                @if(ref_spin_repo){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                        <div class="col-sm-6 col-md-8 col-xl-10">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Conversion Agent Status" aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                                <tr>
                                    <th>Run No</th>
                                    <th>Connection</th>
                                    <th>Category</th>
                                    <th>{{schemalable}} Name</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                        </thead>
                        <tbody>
                            @for(con of ExecututionLogs |searchFilter:
                            datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: page3, id:'Four'};
                            track con){
                                <tr>
                                    <!-- <td>{{page2*piA+$index+1-piA}}</td> -->
                                    <td>{{con.iteration_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_category }}</td>
                                    <td>{{ con.schema_name }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        {{con.status =='Error'?con.error:con.status}}
                                    </td>

                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                        <button class="btn btn-download" (click)="downloadFile()">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>

                                </tr>
                            } @empty {
                            <tr>
                                <td colspan="9">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page3 = $event" id="Four">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
</div>
        
    <div class="qmig-card mt-3">          
        <div class="row">
            <div class="col-md-6 col-xl-6">
                <h3 class="main_h px-3 pt-3">Agent Review @if(selectedAgentTGTID){ / Selected Target Statement - {{selectedAgentTGTID}} }</h3>
            </div>
            <div class="col-md-3 col-xl-3 offset-md-3 px-3 pt-1">
                <div class="row">
                    <div class="col-12 col-sm-9 col-md-9 col-lg-9 col-xl-9 px-1">
                        <select class="form-select" (change)="getAgentDeployFullData(atgt.value)" #atgt>
                            <option selected value="">Select target statement</option>
                            @for ( list of TargetStatmentList; track list) {
                            <option value="{{ list.tgt_statements_id }}">Target Statement - #{{list.tgt_statements_id }}</option>
                            }
                        </select>
                    </div>
                    <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 px-1">
                        <button class="btn btn-sync mt-2" (click)="getAgentTargetStatments(atgt.value)">
                            @if(getRunSpin){
                            <app-spinner />
                            }@else{
                                <span class="mdi mdi-refresh"></span>
                            }
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion accordion-flush qmig-accordion mt-3" id="accordionPanelsStayOpenExample">     
            @for(agent of agentLogs; track agent.deployment_id; let i = $index){   
             <div class="accordion-item">
                <h2 class="accordion-header" [id]="'flush-headingOne' + agent.deployment_id">
                <button
                    class="accordion-button collapsed"
                    type="button"
                    data-bs-toggle="collapse"
                    [attr.data-bs-target]="'#flush-collapseOne' + agent.deployment_id"
                    aria-expanded="false"
                    [attr.aria-controls]="'flush-collapseOne' + agent.deployment_id">
                    Attempt {{ agent.attempt }}
                </button>
                </h2>
                <div
      [id]="'flush-collapseOne' + agent.deployment_id"
      class="accordion-collapse collapse"
      [attr.aria-labelledby]="'flush-headingOne' + agent.deployment_id"
      data-bs-parent="#accordionPanelsStayOpenExample">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form">
                            <div class="row mt-3">
                                <div class="col-md-4 col-xl-4">
                                    <label class="form-label" [for]="'source_' + agent.deployment_id">Source Statement</label>
                                    <textarea 
                                        class="form-control" 
                                        rows="7" 
                                        readonly 
                                        disabled 
                                        [id]="'source_' + agent.deployment_id">{{ agent.source_statement }}</textarea>
                                </div>
                                <div class="col-md-4 col-xl-4">
                                    <label class="form-label" [for]="'target_' + agent.deployment_id">Target Statement</label>
                                    <textarea 
                                        class="form-control" 
                                        rows="7" 
                                        readonly 
                                        disabled 
                                        [id]="'target_' + agent.deployment_id">{{ agent.target_statement }}</textarea>
                                </div>
                                <div class="col-md-4 col-xl-4">
                                    <label class="form-label" [for]="'converted_' + agent.deployment_id">Converted Statement</label>
                                    <textarea 
                                        class="form-control" 
                                        rows="7" 
                                        [(ngModel)]="agent.converted_statement" 
                                        [id]="'converted_' + agent.deployment_id"
                                        name="converted_{{ agent.deployment_id }}"></textarea>
                                </div>
                                <div class="col-md-4 col-xl-4 mt-3">
                                    <label class="form-label" [for]="'error_' + agent.deployment_id">Error</label>
                                    <textarea 
                                        class="form-control" 
                                        rows="4" 
                                        readonly 
                                        disabled 
                                        [id]="'error_' + agent.deployment_id">{{ agent.error }}</textarea>
                                </div>
                                @if (i === 0) {
                                <div class="col-md-4 col-xl-4 mt-3">
                                    <label class="form-label" [for]="'comments_' + agent.deployment_id">Reviewer Comments</label>
                                    <textarea 
                                        class="form-control" 
                                        rows="4" 
                                        [(ngModel)]="agent.reviewer_comments" 
                                        [id]="'comments_' + agent.deployment_id"
                                        name="comments_{{ agent.deployment_id }}"></textarea>
                                </div>                                
                                    <div class="col-md-4 col-xl-4 mt-4 pt-3">
                                        <div class="cagent_status">
                                            <p>AI Deployment Status: @if(agent.is_deployed =="True"){ <span style="color: #13cd68;">Success</span> } @else { <span style="color: #ff3b56;">Failed</span> }</p>
                                            <p>Reviewer Status: @if(agent.review_status =="True"){ <span style="color: #13cd68;">Reviewed</span> } @else { <span style="color: #ff3b56;">Not yet reviewed</span>  }</p>
                                            <p>Deployment Verified by Reviewer : @if(agent.is_manually_deployed !== ""){ <span style="color: #13cd68;">Yes, I Deployed This</span> }</p>
                                            @if(agent.is_manually_deployed === ""){ 
                                                <div class="form-check">
                                                    <label class="form-check-label">
                                                        <span style="color: #ff3b56;">Mark My Deployment</span> 
                                                        <input type="checkbox"  [checked]="isManualDeployed"  class="form-check-input" (click)="onCheckboxChange($event)">
                                                    </label>
                                                </div>
                                            }
                                            <button 
                                                class="btn btn-upload w-100" 
                                                (click)="saveAgentLogs(agent.deployment_id)"> &nbsp; Save &nbsp; 
                                            </button>
                                        </div>
                                    </div>
                                }
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            }@empty {
            <p class="text-center py-3">No Logs Found</p>
            }
        </div>
    </div>
    <div class="row justify-content-center">
        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 mt-3">
            <div class="form-group">
                <button class="btn btn-upload w-100" (click)="openModal()" data-bs-toggle="offcanvas"
                    data-bs-target="#demo"> Final Code </button>
            </div>
        </div>
        @if(agentReviewerStatus){
        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 mt-3 ">
            <div class="form-group">
                <button class="btn btn-sign w-100" (click)="proceedToStage()" [disabled]="finalCodeShow"> Proceed to Stage2 @if(finalCodeShow){<app-spinner />} </button>
            </div>
        </div>
        }
    </div>
}

<!--- Assessment --->
<div class="offcanvas offcanvas-end offcanvas-custom" tabindex="-1" id="demo">
    <div class="offcanvas-header">
        <h4 class="main_h">Final Code</h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <!--- Connection Name --->
    <div class="offcanvas-body pt-0">
        <textarea class="form-control" rows="30">{{FinalCode}}</textarea>
        <div class="text-end mt-3">
            <button class="btn btn-sync" (click)="copyCode()">copy</button>
            <button class="btn btn-sync" (click)="downloadCode()">Download</button>
        </div>
    </div>
</div>

<app-confirm-dialog
  [confirmVal]="'Are You Sure want to Mark it as Deployment Verified by Reviewer?'"
  [message]="'Are You Sure want to Mark it as Deployment Verified by Reviewer?'"
  (confirmResponse)="handleConfirm($event)">
</app-confirm-dialog>

<app-confirm-dialog
    #confirmDialog1
  [confirmVal]="'Do you want to proceed to Stage2 using Stage1 Data?'"
  [message]="'confirmDialog1'"
  (confirmResponse)="proceedConfirm($event)">
</app-confirm-dialog>
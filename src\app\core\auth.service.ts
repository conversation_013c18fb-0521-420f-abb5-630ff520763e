import { Injectable } from '@angular/core';
import { Router } from '@angular/router';

import { HotToastService } from '@ngxpert/hot-toast';
import { BehaviorSubject, Observable } from 'rxjs';

import { CommonService } from '../services/common.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  getdataToken: any;
  tokenstring: any;
  public currentUserSubject: BehaviorSubject<any>;
  public currentUser: Observable<any>;
  userData: any;
  modules: Array<any> = [];
  decodeToken: any;
  getModule: any;
  localStorgaeDD: any;

  private LoginData: BehaviorSubject<any> = new BehaviorSubject<any>(null) ;
  public LoginResponse:Observable<any> = this.LoginData.asObservable();

  constructor(
    private commonService: CommonService,
    private toast: HotToastService,
    private router: Router
  ) {
    this.currentUserSubject = new BehaviorSubject(
      localStorage.getItem('currentUser') || 'null'
    );
    this.currentUser = this.currentUserSubject.asObservable();
  }

  public get currentUserValue(): any {
    return this.currentUserSubject.value;
  }

  isAuthenticate(): boolean {
    const userData = localStorage.getItem('currentUser');
    if (userData && userData.length > 0) {
      return true;
    } else {
      return false;
    }
  }
  setSession(data:any){
        this.LoginData.next(true);
        this.getdataToken=data['token']
        localStorage.setItem('currentUser', this.getdataToken);
        localStorage.setItem('project_id', data['project_id'])
        const tokens = this.getdataToken.split('.');
        this.decodeToken = JSON.parse(atob(tokens[1]));
        localStorage.setItem('migtypeid', this.decodeToken["unique_name"]);
        this.router.navigate(['/dashboard']);
        this.toast.success("Welcome back to QMigrator");
      }

  loginAction(formData: any) {
    const fordTy = typeof (formData);
    if (fordTy == 'string') {
      this.commonService.getLocalStorage(formData).subscribe((data: any) => {
        this.router.navigate(['/dashboard']);
        this.localStorgaeDD = data['jsonResponseData']['Table1'];
        this.getdataToken = this.localStorgaeDD[0].token;
        localStorage.setItem('project_id', this.localStorgaeDD[0].project_id);
        localStorage.setItem('role_id', '8');
        this.tokenstring = localStorage.setItem('currentUser', this.getdataToken);
        this.decodeToken = JSON.parse(atob((this.getdataToken.split('.'))[1]));
        localStorage.setItem('userData', JSON.stringify(this.decodeToken));
        this.getModule = this.decodeToken['Module'];
        localStorage.setItem('module_item', JSON.stringify(this.getModule));
      });
    } else {
      this.commonService.checkLogin(formData).subscribe(
        (res) => {
          if (res.message == 'Login Successes') {
            this.router.navigate(['home']);
            this.getdataToken = res['jwtToken'];
            this.tokenstring = localStorage.setItem(
              'currentUser',
              JSON.stringify(this.getdataToken)
            );
            this.decodeToken = JSON.parse(atob((this.getdataToken.split('.'))[1]));
            localStorage.setItem('userData', JSON.stringify(this.decodeToken));
            this.getModule = this.decodeToken['Module'];
            localStorage.setItem('module_item', JSON.stringify(this.getModule));
            this.currentUserSubject.next(this.getdataToken);
          } else {
            this.toast.error('invalid credentials');
          }
        },
        (err) => {
          if (err == '') {
            this.toast.error('invalid credentials');
          }
        }
      );
    }
  }

  accountMatch(allowedAccounts: any) {
    let isMatch = false;
    const module_items: any = [];
    const modules_value = JSON.parse(
      localStorage.getItem('module_item') || 'null'
    );
    if (typeof modules_value === 'object') {
      modules_value.forEach((element: any) => {
        module_items.push(element.slice(2).toLowerCase());
      });
    } else {
      module_items.push(modules_value.slice(2).toLowerCase());
    }
    if (module_items.includes(allowedAccounts.toString())) {
      isMatch = true;
    }
    return isMatch;
  }
  KubeLogInResponse: any
  kubeLogin(formaValue: any, fileContent: any) {
    const obj = {
      content: formaValue.key,
      signContent: fileContent,
      email: formaValue.email
    }
    this.commonService.checkKubeLogin(obj).subscribe((data: any) => {
      this.KubeLogInResponse = data
      if (data.tokenRes == "License Validated") {
        this.LoginData.next(true);
        localStorage.setItem('project_id', this.KubeLogInResponse.projectId);
        this.tokenstring = localStorage.setItem('currentUser', this.KubeLogInResponse.token);
        this.getdataToken=data['token']
        const tokens = this.getdataToken.split('.');
        this.decodeToken = JSON.parse(atob(tokens[1]));
        localStorage.setItem('migtypeid', this.decodeToken["unique_name"]);
        // this.getModule = this.decodeToken['Module'];
        // localStorage.setItem('module_item', JSON.stringify(this.getModule));
        this.router.navigate(['/dashboard']);
        this.toast.success("Welcome back to QMigrator");
      }
      else {
        this.LoginData.next(false);
        this.toast.error(data.tokenRes);
      }
    })
  }
}

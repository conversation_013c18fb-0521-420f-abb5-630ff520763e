import { Component } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { TabsComponent } from '../tabs/tabs.component';
import { DeploymentService } from '../../../../services/deployment.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-target-current',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, TabsComponent],
  templateUrl: './target-current.component.html',
  styles: ``
})
export class TargetCurrentComponent {
  pi: number = 10;
  grid_active: boolean = false;
  not_grid: boolean = false;
  fileData: any;
  datachange: any;
  TgtUpdateTgt: any;
  logfile: any;
  project_name: any
  projectId: any
  getRole: any;
  isGit: boolean = true;
  UserInfo: any;
  pageName: string = '';
  datachanges: any;
  datachanges1: any;
  datachanges2: any;
  datachanges3: any;
  pageNumber: number = 1;
  p: number = 1;
  page: number = 1;
  pag2: number = 1;
  page2: number = 1;
  page3: number = 1;
  p1: number = 1;
  p2: number = 1;
  TargetConn: any;
  SourceCo:any;
  datachange1:any;
  pages: number = 1;
  deltaform: any;
  constructor(public fb: FormBuilder,
    public deployment: DeploymentService,
    private toast: HotToastService,
    private route: ActivatedRoute,
  ) {
    this.pageName = this.route.snapshot.data['name'];
    this.project_name = localStorage.getItem('project_name');
    let getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    this.getRole = JSON.parse(localStorage.getItem('role_id') ?? 'null');
    let userJson = localStorage.getItem('userData') as string;
    this.UserInfo = JSON.parse(userJson);
  }
  dropdownList = [];
  selectedItems = [];
  targetcurrentform: any;
  targetFileUpload: any;

  schemaName: any = [];
  ngOnInit(): void {
    this.targetcurrentform = this.fb.group({
      runnumber: ['', [Validators.required]],
      contype: ['', [Validators.required]],
      tgtCon: ['', [Validators.required]],
      scheman: ['', [Validators.required]],
      sourcefile: ['', [Validators.required]],
      objectt: ['', [Validators.required]],
      sourcecon: ['', [Validators.required]],
    });
    this.targetFileUpload = this.fb.group({
      fileName: ['', [Validators.required]],
    });
    this.deltaform = this.fb.group({
      scrun: ['', [Validators.required]],
      scschema: ['', [Validators.required]],
    });
	
    this.GetprojectRunnumberinfo();
    this.getInfraSelect()
    // this.dropdownSettings = {
    //   singleSelection: false,
    //   idField: 'schemaname',
    //   textField: 'schemaname',
    //   selectAllText: 'Select All',
    //   unSelectAllText: 'UnSelect All',
    //   itemsShowLimit: 3,
    //   allowSearchFilter: true,
    // };
    this.getRunNumbers();
    this.GetRequestTableData();
    this.getPrjExeLogSelectTask("null");
    this.getRunNumber();
  }

  // select schema data
  selectschema(data: any) {
    this.schemaName = data
    //console.log(data)
  }
  selectExtraction: any;
  hide_files: any = true;
  hide_db: any = true
 
  selectedDropdown(data: any) {
    if (data == "1") {
      this.hide_files = true;
      this.hide_db = true;
      this.isGit = false;
    }
    if (data == "2") {
      this.hide_files = true;
      this.hide_db = false;
      this.isGit = true;
      this.targetcurrentform.controls['runnumber'].setValidators([Validators.required]);
      this.targetcurrentform.controls['contype'].setValidators([Validators.required]);
      this.targetcurrentform.controls['sourcecon'].setValidators([Validators.required]);
      this.targetcurrentform.controls['tgtCon'].setValidators([Validators.required]);
      this.targetcurrentform.controls['scheman'].setValidators([Validators.required]);
      this.targetcurrentform.controls['objectt'].setValidators([Validators.required]);
      this.targetcurrentform.controls['sourcefile'].clearValidators();
      this.GetDBConnections();
    }
    if (data == "3") {
      this.hide_files = false;
      this.hide_db = true;
      this.isGit = true;
      this.targetcurrentform.controls['runnumber'].setValidators([Validators.required]);
      this.targetcurrentform.controls['contype'].setValidators([Validators.required]);
      this.targetcurrentform.controls['sourcecon'].setValidators([Validators.required]);
      this.targetcurrentform.controls['tgtCon'].setValidators([Validators.required]);
      this.targetcurrentform.controls['scheman'].clearValidators();
      this.targetcurrentform.controls['sourcefile'].setValidators([Validators.required]);
      this.targetcurrentform.controls['objectt'].setValidators([Validators.required]);
      this.getFiles();
      this.GetDBConnections();
    }
    else {
      this.selectExtraction = data;
      this.targetcurrentform.controls['runnumber'].updateValueAndValidity();
      this.targetcurrentform.controls['contype'].updateValueAndValidity();
      this.targetcurrentform.controls['sourcecon'].updateValueAndValidity();
      this.targetcurrentform.controls['tgtCon'].updateValueAndValidity();
      this.targetcurrentform.controls['scheman'].updateValueAndValidity();
      this.targetcurrentform.controls['sourcefile'].updateValueAndValidity();
      this.targetcurrentform.controls['objectt'].updateValueAndValidity();

    }
  }
  get validate() {
    return this.targetcurrentform.controls;
  }
  get validates() {
    return this.targetFileUpload.controls;
  }
  TgtCodeShow() {
    this.TgtUpdateTgt = true;
  }
  TgtCodeShows() {
    this.TgtUpdateTgt = false;
  }

  TargetFileName: string = '';
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.TargetFileName = event.target.files[0].name;
  }
  
  onKey() {
    this.pageNumber = 1;
    this.page2=1;
    this.p = 1;
    this.p1 = 1;
    this.p2 = 1;
    this.page3=1;
  }
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all') {
      this.pi = this.uploadedData.length;
      this.p = 1;

    }
    else if (value == '20') {
      this.p = 1;
    }
    else if (value == '50') {
      this.p = 1;
    }
    else if (value == '100') {
      this.p = 1;
    }
  }
  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }
  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }
  uploadedData: any = [];
  uploadedData1: any = [];
 
  prjSrcTgtData: any
  prjSrcSrcData: any
  GetDBConnections() {
    const projectId = this.projectId.toString();
    this.deployment.getConList(projectId).subscribe((data:any) => {
      this.prjSrcTgtData = data['Table1'];
      this.TargetConn = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "T" && item.migsrctype == "D";
      })
      this.SourceCo = this.prjSrcTgtData.filter((item: any) => {
        return item.migsrctgt == "S" && item.migsrctype == "D";
      })
    })
  }
  prjRunnumbers: any
  GetprojectRunnumberinfo() {
    const projectId = this.projectId.toString();
    this.deployment.GetPrjRuninfoSelectAll(projectId).subscribe((data: any) => {
      this.prjRunnumbers = data['Table1'];
    })
  }
  selecttgtConnection(tgtcon: any) {
    this.srcConn = tgtcon
  }
  srcConn: any

  spin_process: any
  infraData: any
  filtered: any
  getInfraSelect() {
    let id = this.projectId;
    // this.project.InfraSelect(id).subscribe((data:any) => {
    //   this.infraData = data['jsonResponseData']['Table1'];
    //   this.filtered = this.infraData.filter((element: any) => {
    //     return element.active == 'True';
    //   });
    // });
  }
  iteration: any
  selectIteration(iter: any) {
    this.iteration = iter;
    this.getFiles();

  }
  spin_db: boolean = false;
  ExecuteCommandOnVM(filename: any, val: any) {

    let obj: any = {
      projectID: this.projectId.toString(),
      command: 'delta_target_current.sh',
      ObjectType: this.UserInfo.email,
      id: this.projectId.toString(),
      ConnectionName: this.iteration,//iteration or run no.
      Connection: this.srcConn,
      Schema: this.schemaName.toString()
    }
    if (val == 1) {
      this.spin_db = true;
      obj.Operation = "Database";
    }
    if (val == 2) {
      this.spin_process = true
      obj.Operation = "Fileshare";
      obj.filename = filename;
    }


    // this.project.ExecuteCommandOnVM(obj).subscribe(
    //   (data: any) => {
    //     this.spin_process = false;
    //     this.spin_db = false
    //     if ((data.message == 'Command Executed Successfully')) {
    //       this.alertService.success(data.message);
    //       localStorage.setItem('Targetfilename', filename);
    //     }
    //   },
    //   (error) => {
    //     this.spin_db = false
    //     this.spin_process = false;
    //     this.alertService.danger(
    //       'Something Went Wrong please try Again Later!'
    //     );
    //   }
    // );
  }

  isLoading: any = [];
  isload(value: any) {
    this.isLoading = []
    this.isLoading[value] = true
  }

  onItemSelect(item: any) {
    this.schemaName.push(item.schemaname);
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.schemaname);
    this.schemaName.splice(inde, 1);
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.schemaname);
    });
  }
  onDeSelectAll(items: any) {
    this.schemaName = [];
  }
  runNumbers: any;
  getRunNumber() {
    let obj = {
      projectId: this.projectId.toString(),
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbers = data['Table1'].filter((data: any) => {
        return data.iteration !== null && data.iteration !== '';
      })
    });
  }
  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }

  fileName: string = '';
  fileResult: any;
  Spin: any
  sendValue($event: any): void {
    this.readThis($event.target);
  }
  readThis(inputValue: any): void {
    let file: File = inputValue.files[0];
    this.fileName = inputValue.files[0].name;
    let myReader: FileReader = new FileReader();
    myReader.onloadend = (e) => {
      this.fileResult = myReader.result;
    };
    myReader.readAsDataURL(file);
  }

  UpLoadFilesToSubFolder() {
    let BinaryFile = {
      projectID: this.projectId.toString(),
      containerName: 'qmigratorfiles' + this.projectId,
      folderName: "Code",
      fileName: this.fileName,
      content: this.fileResult.split(',')[1],
      subFolder: "External_Source"
    };
    this.Spin = true;
    // this.project.UpLoadFilesToSubFolder(BinaryFile).subscribe(
    //   (data: any) => {
    //     this.Spin = false;
    //     if (data.message == "File uploaded Successfully") {
    //       this.alertService.success(data.message);
    //     }
    //   },
    //   (error) => {
    //     this.Spin = false;
    //     this.alertService.danger('Something went wrong!');
    //   }
    // );
  }

  run_no: any = ""
  spin_upload: boolean = false;
  runNumberIncrease() {
    this.spin_upload = true
    if (this.iteration == "New") {
      let obj = {
        projectId: this.projectId.toString(),
        migType: "postgres_Extraction",
        migSrcType: "F",
        fileName: this.fileName,
        dbconname: "null",
        operation: "null",
        schema: "ALL",
        objectType: "null",
        objectPattern: "null",
        acl: ""
      }
      // this.project.RunInfoInsert(obj).subscribe((data: any) => {
      //   var resp = data['jsonResponseData']['Table1']
      //   this.run_no = resp[0].run_no;
      //   if (this.run_no != "") {
      //     this.uploadFiles()
      //   }
      //   else {
      //     this.spin_upload = false
      //     this.alertService.danger("Failed to create Run Number");
      //   }
      // })
    }
    else {
      this.run_no = this.iteration
      this.uploadFile()
    }

  }

  getFiles() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iteration + "/Target_Current_Zip"
    };
    this.deployment.getFiles(requestObj).subscribe((data:any) => {
      this.uploadedData1 = data;
      this.uploadedData1 = this.uploadedData1.reverse();
    });
  }
  uploadResponse: any
  uploadfileSpin: boolean = false;
  selectFile: any;
  fileAdd: boolean = false;
  uploadFile() {
    this.uploadfileSpin = true
    const sourceFileUpload: FormData = new FormData();
    sourceFileUpload.append('file', this.selectFile, this.selectFile.name);
    sourceFileUpload.append('path', "PRJ1167SRC/Delta_process/"+ this.iteration + "/Target_Current_Zip/");
    this.deployment.UploadCloudFiles(sourceFileUpload).subscribe(
      (response: any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.getFiles();
        this.toast.success(response.message)
      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.toast.error('Something went wrong')
      }
    )
  }

  conid: any;
  SourceConn: any;
  schemaList: any = []
  prjSchemaLists(conid: any) {
    this.SourceConn = conid;
    let obj = {
      projectId: this.projectId.toString(),
      sourceConnection: conid.toString()
    }
    this.deployment.getschemaList(obj).subscribe((data:any) => {
      this.schemaList = data['Table1'];
    });
  }
  fileupload: any;
  fileuploadvalue(value: any) {
    this.fileupload = value.fileupload
  }

  Deltacurrent: any;
  Deltacurrentextarct:any;
  DeltaCommand(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      fileshareoption: value.contype,
      srcCon: value.sourcecon,
      tgtCon: value.tgtCon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      objecttype: value.objecttype,
      extraction_Category: "Target_Current",
      task: "Target_Current",
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      this.Deltacurrent = data
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data);
      }
    })
  }

  DeltaCommandExtract(value: any) {
    let obj = {
      projectId: this.projectId.toString(),
      iteration: value.runnumber,
      ileshareoption: value.contype,
      tgtCon: value.tgtCon,
      schema: value.scheman.toString(),
      ObjectCategory: value.objectt,
      extraction_Category: "Target_Current",
      task: "Target_Current",
      fileName: value.sourcefile,
    }
    this.deployment.DeltaCommand(obj).subscribe((data: any) => {
      debugger;
      this.Deltacurrentextarct = data;
      if (data.message == "Command Inserted") {
        this.toast.success("Command Inserted");
      }
      else {
        this.toast.error(data.message);
      }
    })
  }

  runNumbersData: any;
  getRunNumbers() {
    let obj = {
      projectId: this.projectId,
      migsrcType: 'Source_Current'
    }
    this.deployment.GetRunNoForstmts(obj).subscribe((data: any) => {
      this.runNumbersData = data['Table1'];
      for (const element of this.runNumbersData) {
        if (element.iteration == "") {
          element.iteration = "New"
          break;
        }
      }
    });
  }

  ExeLog: any = {};
  prjLogData: any;
  selectedrun: any;
  NoDataHide: boolean = false;
  getPrjExeLogSelectTask(value: any) {
    this.selectedrun = value;
    if (value == "") {
      value = "null";
    }
    this.prjLogData = [];
    this.ExeLog.projectId = this.projectId.toString();
    this.ExeLog.operationType = "Target_Current";
    this.ExeLog.action = "null";
    this.ExeLog.row_id = "null";
    this.ExeLog.runno = value;
    this.ExeLog.operationName = "null";
    this.deployment.PrjExelogSelectTask(this.ExeLog).subscribe((data: any) => {
      this.prjLogData = data['Table1'];
      this.NoDataHide = true;
    })
  }
  ref_spin: boolean = false
  refresh()
  {
    this.GetRequestTableData();
  }
  
  RequestTableData: any;
  GetRequestTableData() {
    let obj = {
      projectId: this.projectId,
      operationType: 'Target_Current'
    }
    this.ref_spin=true;
    this.deployment.GetRequestTableData(obj).subscribe((data: any) => {
      this.RequestTableData = data['Table1'];
      this.ref_spin=false;
    });
  }

  deleteResponse: any;
  // delete request table data
  deleteTableDatas(request_id: any) {
    const obj = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.deployment.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      if (data['Table1'][0].v_status) {
        this.toast.success(data['Table1'][0].v_status);
      }
      else {
        this.toast.error(data);
      }
	  this.GetRequestTableData();
    })
  }
  iterationForLogs: any;
  //filter logs
  selectIterForLogs(value: any) {
    this.iterationForLogs = value;
    this.GetFilesExecutionLogsFromDirectory();
  }
  LogsData: any;
  GetFilesExecutionLogsFromDirectory() {
    let requestObj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.iterationForLogs + "/Execution_Logs/Deployment/Target_Current"
    };
    this.LogsData=[];
    this.deployment.getFiles(requestObj).subscribe((data:any) => {
      this.LogsData = data;
      this.uploadedData = this.uploadedData.reverse();
    });
  }
  fileResponse: any;
  spin_dwld: any;
  //download files
  downloadFile(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  get validatess() {
    return this.deltaform.controls;
  }
	
	 GetSourceFilePathData:any;
  GetSourceFilePath()
  {
    const obj = {
      path: "PRJ" + this.projectId + "SRC/Delta_process/" + this.run + "/Target/Database_Current/"+this.deltaschema.toString()+"/Procedure/"
    }
    this.GetSourceFilePathData=[];
    this.deployment.getFiles(obj).subscribe((data: any) => {
      this.GetSourceFilePathData = data;
    });
  }
  downloadFiles(path:string,filename:string) {
    var pth=path.split("/mnt/pypod/")[1]
    pth="/mnt/eng/"+pth
    this.deployment.downloadLargeFiles(pth).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = filename;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
   // sc schemalist
   schemaNamess:any;
   GetSCSchemaList(runno: any) {
     this.schemaNamess = []
     let obj = {
       projectId: this.projectId,
       runno: runno
     }
     this.deployment.GetSchemaSelect(obj).subscribe((data: any) => {
       this.schemaNamess = data['Table1'];
     });
   }
   deltaschema:any;
  sourceSelected: boolean = false
  sourceSel(value: string) {
    value != '' ? this.sourceSelected = true : this.sourceSelected = false;
    this.deltaschema=value;
    this.GetSourceFilePath();
  }
  run:any;
  selectRunnumber(itera: any) {
    this.run = itera;
    this.GetSCSchemaList(itera);
  }

  downloadFiless(fileInfo: any) {
    this.deployment.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }

}

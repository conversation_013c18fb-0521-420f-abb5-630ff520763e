import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { AirflowComponent } from './common/airflow/airflow.component';
import { ConfigurationsComponent } from './common/configurations/configurations.component';
import { DataCompareComponent } from './common/data-compare/data-compare.component';
import { DataLoadStatusComponent } from './common/data-load-status/data-load-status.component';
import { ExecutionsComponent } from './common/executions/executions.component';
import { ReportsComponent } from './common/reports/reports.component';
import { UploadDagFileComponent } from './common/upload-dag-file/upload-dag-file.component';
import { ValidationsComponent } from './common/validations/validations.component';
import { PodManagementComponent } from './common/pod-management/pod-management.component';
import { DataValidationComponent } from './common/data-validation/data-validation.component';
import { CdcComponent } from './common/cdc/cdc.component';
import { NewconfigurationsComponent } from './common/newconfigurations/newconfigurations.component';
import { CutoverComponent } from './common/cutover/cutover.component';
import { JobscreationagentComponent } from './common/jobscreationagent/jobscreationagent.component';

const routes: Routes = [
  {path:'', component:LayoutComponent, children:[
    {path: '', redirectTo: 'Configurations', pathMatch: 'full' },
    {path:'airflow', component:AirflowComponent, data:{name:'Scheduler'}},
    {path:'configurations', component:ConfigurationsComponent, data:{name:'Configurations'}},
    {path:'dataCompare', component:DataCompareComponent, data:{name:'Data Compare'}},
    {path:'dataLoadStatus', component:DataLoadStatusComponent, data:{name:'Data Load Status'}},
    {path:'executions', component:ExecutionsComponent, data:{name:'Executions'}},
    {path:'reports', component:ReportsComponent, data:{name:'Reports'}},
    {path:'uploadDagFile', component:UploadDagFileComponent, data:{name:'Upload Dag File'}},
    {path:'validations', component:ValidationsComponent, data:{name:'Validations'}},
    {path:'cutover', component:CutoverComponent, data:{name:'Cut-Over'}},
    {path:'podManagement', component:PodManagementComponent, data:{name:'POD Management'}},
    {path:'dataValidation', component:DataValidationComponent, data:{name:'Data Validation'}},
    {path:'cdc', component:CdcComponent, data:{name:'CDC'}},
    {path:'newconfig', component:NewconfigurationsComponent, data:{name:'Configuration'}},
    {path:'jobsAgent', component:JobscreationagentComponent, data:{name:'Jobs Agent'}},


  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DataMigrationRoutingModule { }

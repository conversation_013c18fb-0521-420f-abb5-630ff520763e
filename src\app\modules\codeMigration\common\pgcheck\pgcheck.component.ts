import { Component } from '@angular/core';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CommonService } from '../../../../services/common.service';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { NgSelectModule } from '@ng-select/ng-select';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import * as XLSX from 'xlsx';


@Component({
  selector: 'app-pgcheck',
  standalone: true,
  imports: [NgxPaginationModule, SearchFilterPipe, SpinnerComponent, NgSelectModule, FormsModule, ReactiveFormsModule,],
  templateUrl: './pgcheck.component.html',
  styles: ``
})
export class PgcheckComponent {
  projectId: string = ""
  pageName: string = ''
  pageNumber: any
  constructor(private titleService: Title, private project: CommonService, private Codemigration: CodeMigrationService,
    private fb: FormBuilder, private toatst: HotToastService, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }


  p: number = 1;
  depLogs = [];
  executeForm: any;
  currentPage: any;
  track: any;
  paginate: any;
  itemsPerPage: any;
  datachangeLogs: any;


  targetList: any

  fileResponse: any
  spin_dwld: any;

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList();
    this.fetchAssessmentFiles()
    this.executeForm = this.fb.group({
      targetConnection1: ['', [Validators.required]],
      dbname1: ['', [Validators.required]],
    })
    this.pgcheckForm = this.fb.group({
      targetConnection2: ['', [Validators.required]],
      schema: ['', [Validators.required]],
      dbname2: ['', [Validators.required]],
      procName: ['', [Validators.required]]
    })

  }
  p2:any
  exe_spin:boolean=false
  executeExtension(formData: any) {
    this.exe_spin= true
    let obj = {
      ConId: formData.targetConnection1,
      dbname: formData.dbname1
    }
    this.Codemigration.executeExtension(obj).subscribe((data: any) => {
      this.exe_spin=false
      this.toatst.success("Extension Installed")
    })
  }

  dbList: any
  dbList2: any
  selectedTgtCon: any;
  fetchDbs(value: any, flag: any) {
    this.selectedTgtCon = value
    this.Codemigration.GetDbnames(value).subscribe((data: any) => {
      if (flag == 0) {
        this.dbList = data
      } else {
        this.dbList2 = data
      }
    })
  }

  pgcheckForm: any;
  getConList: any
  ConsList: any
  Conid: any;

  GetConsList() {
    this.Codemigration.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.targetList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.Conid = data['Table1'].filter((item: any) => {
        return item.Connection_ID
      })


    })
  }
  selectedSchemas: any
  selectedProcs: any
  selectedSchemaList: any
  procList: any = []
  getProcs(data: any) {
    var schemaslist: any = []
    var dt = data[0] == "all" ? "All" : data[0]
    if (data.includes('All')) {
      this.schemaList.filter((item: any) => {
        if (item.schemaname != "All") {
          schemaslist.push("'" + item.schemaname + "'")
        }
      })
    } else {
      data.filter((item: any) => {
        schemaslist.push("'" + item + "'")
      })
    }
    this.selectedSchemaList = schemaslist

    let obj = {
      conid: this.selectedTgtCon,
      dbname: this.selectedDbName,
      schema: schemaslist.toString()
    }
    this.Codemigration.getProcs(obj).subscribe((data: any) => {
      this.procList = data
      var ob = { procName: 'ALL' }
      this.procList.unshift(ob)
    })
  }
  selectedProcList: any
  GetProcs(data: any) {
    var selectedProcs: any = []
    if (data.includes('ALL')) {
      this.procList.filter((items: any) => {
        if (items.procName != "ALL") {
          selectedProcs.push("'" + items.procName + "'")
        }
      })
    }
    else {
      data.filter((item: any) => {
        selectedProcs.push("'" + item + "'")
      })
    }
    this.selectedProcList = selectedProcs;
  }

  p1: any
  page2: any
  selectedObjItems: any = [];
  selectedObjItems1: any = []
  onKey() {
    this.p1 = 1;
    this.page2 = 1;
  }
  schemaList: any

  selectedDbName: string = ""
  getSchemas(value: any) {
    this.selectedDbName = value.toLowerCase()
    let obj = {
      conid: this.selectedTgtCon,
      dbname: value.toLowerCase()
    }
    this.Codemigration.tgtSchemaSelectwithdb(obj).subscribe((data: any) => {
      this.schemaList = data
      this.schemaList.sort((a:any, b:any) => a.schemaname.localeCompare(b.schemaname));
      this.schemaList.unshift({ schemaname: 'All' })

    })
  }
  pgerror_spin: boolean = false
  pgwarning_spin: boolean = false
  pgCheckResponse: any
  executePgCheck(formdata: any, value: any) {
    this.p1 = 1
    if (value == "0") {
      this.pgerror_spin = true
      this.pgwarning_spin = false
    }
    else {
      this.pgerror_spin = false
      this.pgwarning_spin = true
    }
    var schemaslist: any = []
    var proclist: any = []
    this.selectedSchemaList.filter((item: any) => {
      schemaslist.push(item)
    })
    this.selectedProcList.filter((item1: any) => {
      proclist.push(item1.toLowerCase())
    })
    let obj = {
      Conid: formdata.targetConnection2,
      schema: schemaslist.toString(),
      dbname: formdata.dbname2,
      procs: proclist.toString(),
      type: value.toString()
    }
    this.Codemigration.executePgCheck(obj).subscribe((data: any) => {
      this.pgCheckResponse = data
      if (value == "0") {
        this.pgerror_spin = false
      }
      else {
        this.pgwarning_spin = false
      }
    })
  }
  get f(): any {
    return this.executeForm.controls;
  }
  get ffs(): any {
    return this.pgcheckForm.controls;
  }
  testing: any = []
  excelSpin: boolean = false
  fileName: string = ""
  exportexcel(): void {
    this.testing = []
    this.excelSpin = true
    this.fileName = "PGCheckData.xlsx"
    var test = this.pgCheckResponse
    for (var el of test) {
      var newEle: any = {};
      newEle.Schema = el.schema;
      newEle.Procedure = el.sp;
      newEle.ErrorMessage = el.error;
      newEle.ErrorQuery = el.error_query;
      newEle.ErrorType = el.error_type;
      this.testing.push(newEle);
    }
    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.testing);

    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, this.fileName);

  }
  assessmentFiles: any
  fetchAssessmentFiles() {
    const path = "AssessmentReports/PGCheckReports"
    this.Codemigration.GetFilesFromDir(path).subscribe((data: any) => {
      this.assessmentFiles = data
    })
  }
  downloadFile(fileInfo: any) {
    this.Codemigration.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  pi:any=10
  datachange:any
}



<!-- header -->
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Upload @if(migtypeid == '35'){Code}@else{DAL} File
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="getForm" (ngSubmit)="uploadFile()">
                        <div class="form-group">
                            <label for="formFile" class="form-label d-required">Upload  File </label>
                            <div class="custom-file-upload">
                                <input class="form-control" formControlName="document" type="file" id="formFile"
                                    (change)="onFileSelected($event)">
                                <div class="file-upload-mask">
                                    @if (fileName == '') {
                                    <img src="assets/images/fileUpload.png" alt="img" />
                                    <p>Drag and drop zip file here or click and add zip file</p>
                                    <button class="btn btn-upload"> Add @if(migtypeid == '35'){Code}@else{DAL} File </button>
                                    }@else{
                                    <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                        <p> {{ fileName }} </p>
                                    </div>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3 offset-md-9">
                                <div class="form-group">
                                    <button class="btn btn-upload w-100"> <span class="mdi mdi-file-plus"
                                            (click)="uploadFile()"></span>
                                        Upload@if(getSpin){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Target Connection -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="dalConversion">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#dalConversionTab" aria-expanded="false" aria-controls="flush-collapse">
                    @if(migtypeid == '35'){Code}@else{DAL} Conversion
                </button>
            </h2>
            <div id="dalConversionTab" class="accordion-collapse collapse" aria-labelledby="dalConversionTab"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="dalConvForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Migration Name</label>
                                    <select class="form-select" formControlName="migrationName">
                                        <option selected value="">Select Migration </option>
                                        @for(mig of migtype;track mig; ){
                                        <option value="{{mig.migtype}}">{{mig.migtype}}</option>
                                        }
                                    </select>
                                    <!-- @if ( f.migrationName.touched && f.migrationName.invalid) {
                                <p class="text-start text-danger mt-1">
                                    @if (f.migrationName.errors.required) { Migration Name is required }
                                </p>                                    
                            }    -->
                                </div>
                            </div>
                            <!-- Database -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="name">Iteration ID 
                                        <button class="btn btn-sync" (click)="getPrjRuninfoSelectAll()">
                                            @if(iteration_spin){
                                            <app-spinner />
                                            }@else{
                                                <span class="mdi mdi-refresh"></span>
                                            }
                                        </button></label>
                                    <select class="form-select" aria-label="Default select example"
                                        formControlName="iterationID">
                                        <option selected value="">Select Iterataion</option>
                                        @for(tgtsc of runnoForReports;track tgtsc; ){
                                        <option value="{{tgtsc.iteration}}">{{tgtsc.iteration_filename}}</option>
                                        }
                                    </select>
                                    @if(f.iterationID.touched && f.iterationID.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(f.iterationID.errors.required) {Iteration ID is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-md-3">
                                <div class="body-header-button">
                                    <button [disabled]="dalConvForm.invalid" class="btn btn-upload w-100 mt-3"
                                        (click)="triggerDal(dalConvForm.value)">
                                        <span class="mdi mdi-checkbox-marked-circle-outline"></span>
                                        Convert @if(dal_spin){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    @if(migtypeid == '35'){Code}@else{DAL} Status
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-6">
                            <h3 class="main_h py-4 ps-3">
                                @if(migtypeid == '35'){Code}@else{DAL} Migration Status
                                <button class="btn btn-sync" (click)="getreqTableData()">
                                    @if(ref_spin){
                                    <app-spinner />
                                    }@else{
                                        <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </h3>
                        </div>
                        <!-- search status of storage obj migration -->
                        <div class="col-12 col-sm-6 col-md-6">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table" id="example" style="width: 100%">
                            <thead>
                                <tr>
                                    <th>RunNo</th>
                                    <!-- <th>Connection</th> -->
                                    <th>Operation</th>
                                    <!-- <th>Schema Name</th> -->
                                    <th>Object Type</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (con of tabledata |searchFilter: datachange| paginate: { itemsPerPage: 10,
                                currentPage: page1 ,id:'second'} ;track con) {

                                <tr>
                                    <td>{{con.run_id}}</td>
                                    <!-- <td>{{ con.conname }}</td> -->
                                    <td>{{ con.operation_name }}</td>
                                    <!-- <td>{{ con.schemaname }}</td> -->
                                    <td>{{ con.objecttype }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        @switch (con.status) {
                                        @case ('I') {
                                        <span>Initialize</span>
                                        }
                                        @case ('P') {
                                        <span>Pending</span>
                                        }
                                        @default {
                                        <span>Completed</span>
                                        }
                                        }
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="page1 = $event" id="second"></pagination-controls>
                    </div>

                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    @if(migtypeid == '35'){Code}@else{DAL} Conversion Files
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <!-- select run no details -->
                        <div class="col-sm-6 col-md-3 col-xl-3">
                            <div class="row">
                                <div class="col-8 col-sm-8 col-md-8 col-xl-8 pe-1">
                                    <div class="form-group">
                                        <select class="form-select" #iter (change)="selectIteration(iter.value)">
                                            <option selected value="">
                                                SelectRunNumber
                                            </option>
                                            @for( list of runnoForReports;track list;){
                                            <option value="{{ list.iteration}}">
                                                {{list.iteration_filename}}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4 col-sm-4 col-md-4 col-xl-4 mt-2 ps-1">
                                    <button class="btn btn-sync">
                                        @if(getRunSpin){
                                        <app-spinner />
                                        }@else{
                                            <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- select hrs details -->
                        <!-- <div class="col-sm-6 col-md-3 col-xl-3">
                            <div class="form-group">
                                <select class="form-select">
                                    <option selected value="">Select Hours</option>
                                </select>
                            </div>
                        </div> -->
                        <div class="col-12 col-sm-6 col-md-6">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Reports" class="form-control"
                                    [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                </div>
                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(docs of ExecututionFiles |searchFilter:
                            datachange1|paginate:{
                            itemsPerPage: 10, currentPage: page2, id:'third'};
                            track docs){
                            <tr>
                                @if(docs.fileName.includes('.zip'))
                                {
                                <td>{{ page2*10+$index+1-10 }}</td>
                                <td>{{docs.fileName }}</td>
                                <td>{{docs.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(docs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            }
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page2 = $event" id="third"></pagination-controls>
                </div>
            </div>
        </div>

        <!---Page-Activity Log-->
        <!-- <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    DAL Logs
                </button>
            </h3>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <!-- select run no details 
                        <div class="col-6 col-sm-6 col-md-3 col-lg-3 col-xl-2">
                            <div class="form-group">
                                <select class="form-select">
                                    <option selected value="">
                                        Select Run Number
                                    </option>
                                </select>
                            </div>
                        </div>
                        <!-- select onj type details 
                        <div class="col-6 col-sm-6 col-md-3 col-lg-3 col-xl-2">
                            <div class="form-group">
                                <select class="form-select">
                                    <option selected value="">Select Obj Type</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-8">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Deployment Logs" aria-controls="example"
                                    class="form-control" [(ngModel)]="datachange2" (keyup)="onKey()" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- for download file 
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(logs of depLogs |searchFilter: datachange2|paginate:{
                            itemsPerPage: 10, currentPage: page3, id:'second'};
                            track logs){
                            <tr>
                                <td>{{ page3*10+$index+1-10 }}</td>
                                <td>{{logs.fileName }}</td>
                                <td>
                                    -
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <!-- pagination 
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page3 = $event"></pagination-controls>
                </div>
            </div>
        </div> -->
        @if(migtypeid != '35'){
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Execution Logs
                </button>
            </h3>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-sm-6 col-md-4 col-xl-2">
                            <div class="form-group">
                                <select class="form-select" #it (change)="selectIterationForLogs(it.value)">
                                    <option  disabled>
                                        SelectRunNumber
                                    </option>
                                    @for( list of runnoForReports;track list;){
                                    <option value="{{ list.iteration}}">
                                        {{list.iteration_filename}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-sm-6 col-md-8 col-xl-10">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachange3" (keyup)="onKey()" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(logs of depLogs |searchFilter:
                            datachange3|paginate:{
                            itemsPerPage: 10, currentPage: page4, id:'Four'};
                            track logs){
                            <tr>
                                <td>{{ page4*10+$index+1-10 }}</td>
                                <td>{{logs.fileName }}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(logs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page4 = $event" id="Four">
                    </pagination-controls>
                </div>
            </div>
        </div>
        }
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingFour">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseFour" aria-expanded="false" aria-controls="flush-collapseFour">
                    Page-Activity Log
                </button>
            </h3>
            <div id="flush-collapseFour" class="accordion-collapse collapse" aria-labelledby="flush-headingFour"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">                        
                        <div class="col-sm-6 col-md-3 col-xl-3">
                            <div class="row">
                                <div class="col-8 col-sm-8 col-md-8 col-xl-8 pe-1">
                                    <div class="form-group">
                                        <select class="form-select" #selRun (change)="GetIndividualLogs(selRun.value)">
                                            <option selected value="">
                                                SelectRunNumber
                                            </option>
                                            @for( list of runnoForReports;track list;){
                                            <option value="{{ list.iteration}}">
                                                {{list.iteration_filename}}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4 col-sm-4 col-md-4 col-xl-4 mt-2 ps-1">
                                    <button class="btn btn-sync"  (click)="getPrjRuninfoSelectAll()">
                                        @if(reports_spin){
                                        <app-spinner />
                                        }@else{
                                            <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-9 col-xl-9">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Activity Logs" aria-controls="example"
                                    class="form-select" [(ngModel)]="datachange4" (keyup)="onKey()" />
                            </div>
                        </div>
                    </div>
                </div>
                <!-- table details of activity log -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table" id="example" style="width: 100%">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>Run No</th>
                                <th>Date and Time</th>
                                <th>Task</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (documents of logdata |searchFilter: datachange4| paginate:
                            { itemsPerPage: 10,
                            currentPage: page5 , id: 'first'} ;track documents; ) {
                            <tr>
                                <td>{{page5*10+$index+1-10}}</td>
                                <td>{{ documents.iteration }}</td>
                                <td>{{ documents.activity_date }}</td>
                                <td>{{ documents.
                                    migtask}}</td>
                            </tr>
                            }

                        </tbody>
                    </table>
                </div>
                <!-- pagination status -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page5 = $event" id="first">
                    </pagination-controls>
                </div>
            </div>
        </div>
    </div>
<div class="v-pageName">{{pageName}}</div>
<!--- Performance optimisation List --->
<div class="qmig-card">
    <div class="row">
        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
            <h3 class="main_h px-3 pt-3"> Bloated Indexes </h3>
        </div>
        <div class="col-12 col-sm-6 col-md-9 col-lg-9 col-xl-9 text-end mt-3 pe-4" [hidden]="!RatioIndexesData">
            <p class="m-0"> Ratio Indexes : <b>{{RatioIndexesData }}</b></p>
        </div>
    </div>
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="BloatedForm">
            <div class="row">
                <div class="col-md-3 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="targtetConnection"> Connection
                            Name</label>
                        <select formControlName="tgtconn" #Myselect4 (change)="
                    selTgttId(Myselect4.value);GetperfSchemas()" class="form-select">
                            <option value="" disabled selected>Select Connection Name</option>
                            @for(list of tgtList;track list; ){
                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                            }
                        </select>
                        <div class="alert">
                            @if(f.tgtconn.touched && f.tgtconn.invalid) {
                            <p class="text-start text-danger mt-1">Connection Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                    <div class="form-group">
                        <label class="form-label d-required" for="targtetConnection"> Schema
                            Name</label>
                        <ng-select [placeholder]="'Select Schema'" formControlName="schemaName" [items]="perSchema"
                            [multiple]="true" (change)="selectSchema3(selectedObjItems3)" bindLabel="schemaname"
                            groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false" bindValue="schemaname"
                            [(ngModel)]="selectedObjItems3">
                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                            </ng-template>
                        </ng-select>
                        <div class="alert">
                            @if(f.schemaName.touched && f.schemaName.invalid) {
                            <p class="text-start text-danger mt-1">Connection Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 mt-4 pt-2">
                    <button class="btn btn-upload w-100"
                        (click)="GetBloatedIndexes(BloatedForm.value);GetBloatedIndexExcel(BloatedForm.value)"
                        [disabled]="BloatedForm.invalid">Fetch Data
                        @if(ref_spin3){<app-spinner />}
                    </button>
                </div>
            </div>
        </form>
    </div>
    <div  [hidden]="BloatIndexesData?.length <= 0">
        <hr class="dash-dotted" />
        <div class="qmig-card-body">
            <div class="ps-3 col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 offset-md-9">
                <button class="btn btn-sign w-100" (click)="exportexcelShowlogsBloat()">
                    <span class="mdi mdi-arrow-up-thin"></span>Export 
                    @if(excelSpinn3){<app-spinner />} </button>
            </div>
        </div>
        <div [hidden]="BloatIndexesData?.length <= 0">
        <div class="table-responsive">
            <table class="table table-hover qmig-table">
                <thead>
                    <tr>
                        <th>Schema Name</th>
                        <th>Table Name</th>
                        <th>Index Name</th>
                        <th>Index Size</th>
                        <th>IndexBloat</th>
                        <th>Vacum Table</th>
                        <!-- <th>Re Index</th> -->


                    </tr>
                </thead>
                <tbody>
                    @for (con of BloatIndexesData | searchFilter: searchText
                    | paginate: {
                    itemsPerPage: 10, currentPage: pageNumber } ; track con; ) {
                    <tr>
                        <td>{{con.schemaname}}</td>
                        <td>{{con.tablename}}</td>
                        <td>{{con.indexname}}</td>
                        <td>{{con.indexsize}}</td>
                        <td>{{con.indexBloat}}</td>
                        <td>{{con.vacumtable}}</td>
                        <!-- <td>{{con.reindex}}</td> -->
                    </tr>
                    <tr *ngIf="BloatIndexesData.length === 0">
                        <td colspan="7" class="text-center">Empty list of Documents</td>
                    </tr>

                    } @empty {
                    <tr>
                        <td colspan="4">
                            <p class="text-center m-0 w-100">Empty list of Documents
                            </p>
                        </td>
                    </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="custom_pagination">
            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
        </div>
    </div>
    </div>
</div>
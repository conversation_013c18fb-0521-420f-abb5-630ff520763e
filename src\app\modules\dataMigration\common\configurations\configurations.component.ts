import { Component } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { HotToastService } from '@ngxpert/hot-toast';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';


@Component({
  selector: 'app-configurations',
  standalone: true,
  imports: [NgSelectModule, CommonModule, FormsModule, ReactiveFormsModule, NgxPaginationModule, SearchFilterPipe, SpinnerComponent],
  templateUrl: './configurations.component.html',
  styles: ``
})
export class ConfigurationsComponent {
  getForm = this.formBuilder.group({
    operation: ['', [Validators.required]],
    smallTable: ['', [Validators.required]],
    largeTable: ['', [Validators.required]],
    chunkSize: ['', [Validators.required]],
    confiName: ['', [Validators.required, Validators.maxLength(15)]],
    sourceConnection: ['', [Validators.required]],
    targetConnection: ['', [Validators.required]],
    schemas: [''],
    tgtschemas: [''],
    tables: [''],
    topicName: [''],
    GroupId: [''],
    sleepTime: [''],
    serverIP: [''],
    request_CPU: [''],
    request_Memory: [''],
    limit_CPU: [''],
    limit_Memory: [''],
    request_CPU_measure: [''],
    request_Memory_measure: [''],
    limit_CPU_measure: [''],
    limit_Memory_measure: [''],
    auditLogs: [''],
    refFile: [''],
    level: [''],
    batch: [''],
    batchsize: [''],
    scn: [''],
    dagname: [''],
    TablesList: [''],
    shellScript: [''],
    ggHome: [''],
    podName: [''],
    trailPath: [''],
    dataLoad: [''],
    slotname: [''],
    readsize: [''],
    method: [''],
    rows: [''],
    podConfig: [''],
    data_LoadType: [''],
    dagExecute: [''],
    cdcType: [''],
    cdcLoadType: [''],
    replication:['']
  })


  CPUForm: any;
  getSpin: boolean = false;
  projectId: any;
  extractForm: any
  operations: any = [
    { option: "Initial load for GG", value: "GG_Initial_Data_Load" },
    { option: "Initial Data Load", value: "Initial_Data_Load" },
    { option: "Partition Data Load", value: "Partition_Initial_Data_Load" },
    { option: "Catch Up", value: "Incremental_Data_Load" },
    { option: "CDC Load", value: "CDC_Load" },
    { option: "CDC For Higher Volumes", value: "Cdc_config" },
    { option: "Reverse CDC", value: "Reverse_CDC" },
  ]

  dataLoadType: any = [{ option: "Initial load for GG", value: "GG_Initial_Data_Load" },
  { option: "Initial Data Load", value: "Initial_Data_Load" },
  { option: "Partition Data Load", value: "Partition_Initial_Data_Load" }]

  Level: any = [{ option: "Table List", value: "Table_List" },
  { option: "Schema List", value: "schema_List" },]

  cdcLoadData: any = [
    { name: "Database", value: "Database" },
    { name: "File", value: "File" },
  ]

  p: number = 1;
  pi: number = 10;
  page1: number = 1;

  datachange: any;
  Files: any;
  not_grid: boolean = true;
  grid_active: boolean = true;
  //executed: boolean = true

  /*--- multi drop down ---*/


  selectedItems = [];
  schemaName: any = [];
  schemaID: any = [];
  selectedTable: any = [];
  selectedTables: any = [];
  tablesList: any = [];
  migtypeid: string = ""
  sql_oracletype: boolean = false
  TableMetaFileName: string = ''
  pageName: string = ''
  constructor(private titleService: Title, private route: ActivatedRoute, private toast: HotToastService, private datamigration: DataMigrationService, private formBuilder: FormBuilder, private project: DataMigrationService) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    this.p = 1;
    this.page1 = 1;
    if (this.migtypeid == "30") {
      this.sql_oracletype = true
    }
    else {
      this.sql_oracletype = false
    }

  }

  /*--- multi drop down ---*/

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getConfigMenu()
    this.GetConsList()
    this.fetchConfigFiles()
    this.getPodList()
    this.ref_spin = false
    this.p = 1;
    this.page1 = 1;
    this.extractForm = this.formBuilder.group({
      sourceConnection: ['', Validators.required],
      schema: ['', Validators.required]
    })

    this.CPUForm = this.formBuilder.group({
      sourceConnection: ['', Validators.required]
    })
  }
  get f() {
    return this.getForm.controls;
  }

  podList: any;
  shellScriptList: any;
  get fs() {
    return this.extractForm.controls;
  }

  get cf() {
    return this.CPUForm.controls
  }
  getPodList() {
    this.datamigration.getGoldenGatePod().subscribe((data: any) => this.podList = data)
  }
  callShellScript(value: any) {
    this.datamigration.GetGGShellDirectoryEx(value + '/scripts').subscribe((data: any) => this.shellScriptList = data)
  }

  showschema: boolean = false
  showtable: boolean = false
  selectedLevel: any
  selectLevel(value: any) {
    this.selectedLevel = value
    this.schemaName = []
    this.schemaID = []
    if (value == "schema_List") {
      this.showschema = true
      this.showtable = false
    }
    else {
      this.showschema = false
      this.showtable = true
    }
  }
  clickEvent() {
    this.grid_active = !this.grid_active;
    this.not_grid = true;
  }
  gridEvent() {
    this.not_grid = !this.not_grid;
    this.grid_active = false;
    this.not_grid = false;
  }
  ConsList: any = [];
  schemaList: any = []
  tgtlist: any = []
  auditList: any = []
  GetConsList() {
    this.datamigration.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtlist = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      this.auditList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'A' && item.conname != '';
      });
      this.auditList = this.auditList[0]
    });
  }
  schemaList1: any = []
  getSchemasList1(ConnectionId: any) {
    // this.getTableSrcId = ConnectionId
    //console.log(ConnectionId)
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId
    }

    this.datamigration.SchemaListSelect(obj).subscribe((data: any) => {
      this.schemaList1 = data['Table1'];
    })


    // if (this.migtypeid == "35" || this.migtypeid == "34") {
    //   this.fetchDynamoTables(ConnectionId);
    // }
  }
  spin: boolean = false
  extractValue(value: any) {
    let obj = {
      operation: "Table_Extraction",
      projectId: this.projectId.toString(),
      schema: value.schema,
      srcId: value.sourceConnection
    }
    this.spin = true
    this.datamigration.insertTablesCommand(obj).subscribe((data: any) => {
      this.spin = false
      this.toast.success("Tables Extracted Successfully")
    },
      error => {
        this.spin = false
        this.toast.error("Failed to Tables Extracted")
      })
  }
  getAuditConnId: any
  getauditCon(value: any) {
    this.getAuditConnId = value
  }
  srcId: any
  getDb(value: any) {
    this.srcId = value
    //console.log(value)
  }
  tgtId: any
  getTgtId(value: any) {
    this.tgtId = value
  }
  tgtSchemaList: any = []
  selectedItemstgt: any
  getSchemasList(ConnectionId: any, value: string) {
    const obj = {
      projectid: this.projectId,
      connectioId: ConnectionId,
    };
    if (this.migtypeid == "31" && value == "S") {
      this.schemaList=[]
      var local = this.ConsList.filter((item: any) => {
        return item.Connection_ID == ConnectionId
      })
      let obj = {
        schemaname: local[0].dbschema
      }
      this.schemaList.push(obj)
    }
    else {
      this.datamigration.SchemaListSelect(obj).subscribe((data: any) => {
        if (value == "S") {
          this.schemaList = data['Table1'];
        } else {
          this.tgtSchemaList = data['Table1'];
          this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => {
            return item.schemaname != "ALL"
          });
        }
        if (this.migtypeid == "35" || this.migtypeid == "34") {
          this.fetchDynamoTables(ConnectionId);
        }
      });
    }
  }
  executed: boolean = false
  auditChecked: boolean = false
  getCheckValue($event: any): void {
    //console.log($event.target.checked)
    if ($event.target.checked == true) {
      this.auditChecked = true
    }
    else {
      this.auditChecked = false
    }
  }
  InserttablesCommand(formData: any) {
    let obj: any = {}
    this.executed = true
    if (formData.confiName.includes(".")) {
      formData.confiName = formData.confiName.split(".")[0]
    }
    let logs
    if (formData.auditLogs == true) {
      logs = 1
    }
    else {
      logs = 0
    }
    //console.log(formData.auditLogs)
    let reqMem = formData.request_Memory_measure == '' ? 'Mi' : formData.request_Memory_measure
    let limitMem = formData.limit_Memory_measure == '' ? 'Mi' : formData.limit_Memory_measure
    if (this.selectOperation == "Incremental_Data_Load") {
      obj = {
        projectId: this.projectId.toString(),
        srcId: this.srcId,//formData.conncetions,//
        // schema: this.schemaName.toString(),
        operation: this.selectOperation,
        fileName: "catch_" + formData.confiName,
        reffilename: formData.refFile,
        dataLoadType: formData.dataLoad
        //auditFlag: logs.toString()
      }
    }
    else if (this.selectOperation == "CDC_Load") {
      obj = {
        projectId: this.projectId.toString(),
        srcId: this.srcId,//formData.conncetions,//
        // schema: this.schemaName.toString(),
        operation: this.selectOperation,
        fileName: "cdc_" + formData.confiName,
        tgtId: this.tgtId,
        schema: this.selectedsrcschema.toString(),
        tgtschema: this.tgtschemaname,
        tableList: this.showschemaa ? '' : this.selectedTable.toString(),
        batchsize: formData.batchsize,
        cdcoption: formData.cdcLoadType,
        //reffilename: formData.refFile,
        // topicname:formData.topicName,
        //groupid: formData.GroupId,
        //serverip: formData.serverIP,
        //scn: formData.scn,
        //sleepTime: formData.sleepTime
        //auditFlag: logs.toString()
      }
    }
    else if (this.selectOperation == "GG_Initial_Data_Load") {

      obj = {
        projectId: this.projectId.toString(),
        srcId: this.srcId,//formData.conncetions,//
        schema: this.selectedsrcschema.toString(),//this.selectedItems.toString(),
        operation: this.selectOperation,
        smallTableConcurrency: formData.smallTable,
        largeTableConcurrency: formData.largeTable,
        chunkSize: formData.chunkSize,
        serverip: formData.serverIP,
        requestCPU: formData.request_CPU + formData.request_CPU_measure,
        requestMemory: formData.request_Memory + reqMem == 'Mi' ? " " : formData.request_Memory + reqMem,
        limitCPU: formData.limit_CPU + formData.limit_CPU_measure,
        limitMemory: formData.limit_Memory + limitMem == 'Mi' ? " " : formData.limit_Memory + limitMem,
        auditFlag: logs.toString(),
        fileName: "initgg_" + formData.confiName,
        tgtId: this.tgtId,
        podName: formData.podName,
        ggHome: formData.ggHome,
        shellScript: formData.shellScript,
        trailPath: formData.trailPath,
        schemaId: this.schemaID.toString(),
        tableList: this.showschemaa ? '' : this.selectedTable.toString(),
        podConfig: formData.podConfig,
        data_LoadType: formData.data_LoadType,
        dagExecute: formData.dagExecute,
        tgtschema: this.tgtschemaname,
        cdcType: formData.cdcType
      }
    }
    else if (this.selectOperation == "E2E_Data_Load") {
      obj = {
        projectId: this.projectId.toString(),
        srcId: this.srcId,//formData.conncetions,//
        schema: this.selectedsrcschema.toString(),//this.selectedItems.toString(),
        operation: this.selectOperation,
        smallTableConcurrency: formData.smallTable,
        largeTableConcurrency: formData.largeTable,
        chunkSize: formData.chunkSize,
        serverip: formData.serverIP,
        requestCPU: formData.request_CPU + formData.request_CPU_measure,
        requestMemory: formData.request_Memory + reqMem == 'Mi' ? " " : formData.request_Memory + reqMem,
        limitCPU: formData.limit_CPU + formData.limit_CPU_measure,
        limitMemory: formData.limit_Memory + limitMem == 'Mi' ? " " : formData.limit_Memory + limitMem,
        auditFlag: logs.toString(),
        fileName: "initE2E_" + formData.confiName,
        tgtId: this.tgtId,
        podName: formData.podName,
        ggHome: formData.ggHome,
        shellScript: formData.shellScript,
        trailPath: formData.trailPath,
        schemaId: this.schemaID.toString(),
        tableList: this.showschemaa ? '' : this.selectedTable.toString(),
        podConfig: formData.podConfig,
        data_LoadType: formData.data_LoadType,
        dagExecute: formData.dagExecute,
        tgtschema: this.tgtschemaname,
        cdcType: formData.cdcType
      }
    }
    else if (this.selectOperation == "Partition_Initial_Data_Load") {
      obj = {
        projectId: this.projectId.toString(),
        srcId: this.srcId,//formData.conncetions,//
        schema: this.selectedsrcschema.toString(),//this.selectedItems.toString(),
        operation: this.selectOperation,
        smallTableConcurrency: formData.smallTable,
        largeTableConcurrency: formData.largeTable,
        chunkSize: formData.chunkSize,
        serverip: formData.serverIP,
        requestCPU: formData.request_CPU + formData.request_CPU_measure,
        requestMemory: formData.request_Memory + reqMem == 'Mi' ? " " : formData.request_Memory + reqMem,
        limitCPU: formData.limit_CPU + formData.limit_CPU_measure,
        limitMemory: formData.limit_Memory + limitMem == 'Mi' ? " " : formData.limit_Memory + limitMem,
        auditFlag: logs.toString(),
        fileName: "initPD_" + formData.confiName,
        tgtId: this.tgtId,
        podName: formData.podName,
        ggHome: formData.ggHome,
        shellScript: formData.shellScript,
        trailPath: formData.trailPath,
        schemaId: this.schemaID.toString(),
        tableList: this.showschemaa ? '' : this.selectedTable.toString(),
        podConfig: formData.podConfig,
        data_LoadType: formData.data_LoadType,
        dagExecute: formData.dagExecute,
        tgtschema: this.tgtschemaname,
        cdcType: formData.cdcType
      }
    }
    else if (this.selectOperation == "Cdc_config") {
      var schemastring = "";
      if (this.schemaName.length > 0) {
        for (let k = 0; k < this.schemaName.length; k++) {
          if (schemastring.length == 0) {
            schemastring = "'" + this.schemaName[k] + "'"
          }
          else {
            schemastring = schemastring + ",'" + this.schemaName[k] + "'"
          }

        }
      }
      //console.log(schemastring)
      if (schemastring != "") {
        formData.TablesList = "null";
        // (<HTMLInputElement>document.getElementById("tbllist")).value=""
      }
      if (formData.TablesList != "null") {
        schemastring = "null"
      }
      //console.log(schemastring)
      obj = {
        projectId: this.projectId.toString(),
        srcId: this.srcId,//formData.conncetions,//
        tgtId: this.tgtId,
        schema: schemastring.toString(),
        operation: this.selectOperation,
        fileName: "cdchv_" + formData.confiName,
        reffilename: formData.refFile,
        batch: formData.batch,
        batchsize: formData.batchsize,
        scn: formData.scn,
        level: formData.level,
        tablename: formData.TablesList,
        dagname: formData.dagname

      }
    }
    else if (this.selectOperation == "Data_Validation") {
      // validaton script
    }
    else if (this.selectOperation == "Reverse_CDC") {
      var schemastring = "";
      if (this.schemaName.length > 0) {
        for (const element of this.schemaName) {
          if (schemastring.length == 0) {
            schemastring = "'" + element + "'"
          }
          else {
            schemastring = schemastring + ",'" + element + "'"
          }
        }
      }
      obj = {
        projectId: this.projectId.toString(),
        srcId: this.tgtId,//formData.conncetions,//
        tgtId: this.srcId,
        schema: '',//schemastring.toString(),
        operation: this.selectOperation,
        fileName: "rev_" + formData.confiName,
        scn: formData.scn,
        dagname: formData.dagname,
        slotname: formData.slotname,
        tablename: formData.TablesList

      }
    }
    else {
      obj = {
        projectId: this.projectId.toString(),
        srcId: this.srcId,//formData.conncetions,//
        schema: this.selectedsrcschema.toString(),
        operation: this.selectOperation,
        smallTableConcurrency: formData.smallTable,
        largeTableConcurrency: formData.largeTable,
        chunkSize: formData.chunkSize,
        fileName: "init_" + formData.confiName,
        tgtId: this.tgtId,
        // serverip: formData.serverIP,
        requestCPU: formData.request_CPU + formData.request_CPU_measure,
        requestMemory: formData.request_Memory + reqMem == 'Mi' ? " " : formData.request_Memory + reqMem,
        limitCPU: formData.limit_CPU + formData.limit_CPU_measure,
        limitMemory: formData.limit_Memory + limitMem == 'Mi' ? " " : formData.limit_Memory + limitMem,
        auditFlag: logs.toString(),
        tableList: this.showschemaa ? '' : this.selectedTable.toString(),
        podConfig: formData.podConfig,
        data_LoadType: formData.data_LoadType,
        dagExecute: formData.dagExecute,
        tgtschema: this.tgtschemaname
        // tablename: formData.tables
      }
      if (this.migtypeid == "30" || this.migtypeid == "20") {
        // obj['method'] = formData.method
        obj['readsize'] = formData.readsize
        obj['rows'] = formData.rows
        obj['schema'] = this.selectedsrcschema
        // obj['tgtschema'] = this.tgtschemaname
        //console.log(obj['tgtschema'])
      } else if (this.migtypeid == "31") {
        obj['readsize'] = formData.batchsize
        obj['cdcType']=formData.cdcType
      }
      else {
        obj['serverip'] = formData.serverIP
      }
    }
    //console.log(this.selectOperation)
    if (this.selectOperation == "CDC_Load") {
      //console.log(obj)
      this.datamigration.insertTablesCommand(obj).subscribe((data: any) => {
        this.executed = false
        this.toast.success("Config File Created")
      },
        error => {
          this.executed = false
          this.toast.error("Failed to Create Config File")
        })
    } else if (this.selectOperation == "GG_Initial_Data_Load" || this.selectOperation == "Initial_Data_Load" || this.selectOperation == "Partition_Initial_Data_Load" || this.selectOperation == "E2E_Data_Load") {
      this.executed = false
      if (this.migtypeid == "20" || this.migtypeid == "28" || this.migtypeid == "31" || this.migtypeid == "37" ) {
        this.datamigration.GGPDCrateConifg(obj).subscribe((data: any) => {
          this.toast.success("Executed")
        })
      }
      else {
        this.datamigration.insertTablesCommand(obj).subscribe((data: any) => {
          this.executed = false
          this.toast.success("Config File Created")
        },
          error => {
            this.executed = false
            this.toast.error("Failed to Create Config File")
          })
      }

    } else {
      //console.log(this.selectOperation)
      this.executed = false
      this.datamigration.insertTablesCommand(obj).subscribe((data: any) => {
        this.toast.success("Executed")
      })
    }

  }


  fileName: string = '';

  fileResult: any;
  upload_spin: boolean = false;

  sendValue($event: any): void {
    this.readThis($event.target);
  }
  readThis(inputValue: any): void {
    const file: File = inputValue.files[0];
    this.fileName = inputValue.files[0].name;
    const myReader: FileReader = new FileReader();
    myReader.onloadend = (e) => {
      this.fileResult = myReader.result;
    };
    myReader.readAsDataURL(file);
  }

  selFile: any
  onFileSelected1(event: any) {
    const file: File = event.target.files[0];
    this.selFile = file
    this.fileName = file.name;
    //console.log(this.selFile)
    //console.log(this.selFile.name)
    //this.uploadFile(file);
  }
  uploadSpin1: boolean = false
  uploadConfigTempSpin: boolean = false
  filePath: string = ''
  uploadFile(data: string) {

    const formData: FormData = new FormData();
    if (data == 'configFile') {
      this.uploadSpin1 = true
      this.filePath = "Config_Files/Data_Migration/"
      formData.append('file', this.selFile, this.selFile.name);
      formData.append('path', this.filePath);
    } else {
      this.uploadConfigTempSpin = true
      this.filePath = "Ora2Pg_Configuration_Template/"
      formData.append('file', this.selFile, 'ora2pg_configuration.txt');
      formData.append('path', this.filePath);
    }
    this.datamigration.UploadProjectDocs(formData).subscribe(
      response => {
        this.fileName = ''
        this.uploadSpin1 = false
        //this.ReplaceFileName()
        this.fetchConfigFiles()
        this.toast.success("File uploaded successfully")
      },
      error => {
        this.fileName = ''
        this.uploadSpin1 = false
        this.toast.warning("Error uploading file")
      }
    );
  }
  configFiles: any = []
  confFiles: any
  ref_spin: boolean = false
  fetchConfigFiles() {
    this.ref_spin = true
    const path = "Config_Files/Data_Migration"//"AIRFLOW_FILES/Data_Migration_Reports/"
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.confFiles = data
      this.fetchConfigFiles1()
      this.ref_spin = false
    })
  }
  fetchConfigFiles1() {
    const path = "Config_Files/CDC";//"AIRFLOW_FILES/Data_Migration_Reports/"
    var tempFiles: any = []
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      tempFiles = data
      tempFiles.filter((item: any) => {
        this.confFiles.push(item);
      })
      this.confFiles.sort((a: any, b: any) => {
        return new Date(b.created_dt).getTime() - new Date(a.created_dt).getTime();
      });
      //console.log(this.confFiles)
      //this.configFiles = data
    })
  }
  filterConfig(value: any) {
    this.configFiles = []
    this.confFiles.forEach((el: any) => {
      if (value == "Partition_Initial_Data_Load") {
        if (el.fileName.includes("initPD_")) {
          this.configFiles.push(el)
        }
      }
      else if (value == "GG_Initial_Data_Load") {
        if (el.fileName.includes("initgg_")) {
          this.configFiles.push(el)
        }
      }
      else if (value == "Initial_Data_Load") {
        if (el.fileName.includes("init_")) {
          this.configFiles.push(el)
        }
      }

    })
  }
  onKey() {
    this.p = 1;
    this.page1 = 1;
  }
  sortValue(value: any) {
    this.pi = value;
    if (value == 'all' || value == '20' || value == '50' || value == '100') {
      this.p = 1;
      this.page1 = 1;
    }
  }

  incrementSelected: boolean = false
  cdcSelected: boolean = false
  selectOperation: any
  logminer: boolean = false
  initialLoadGGSelected: boolean = false
  reverseCdc: boolean = false
  getOP(value: string) {
    this.incrementSelected = this.cdcSelected = this.logminer = this.initialLoadGGSelected = this.reverseCdc = false;
    this.clearAllValidators()
    switch (value) {
      case 'Incremental_Data_Load':
        this.incrementSelected = true;
        this.setRequiredValidators(['refFile', 'confiName', 'dataLoad']);
        if (['30', '20'].includes(this.migtypeid)) {
          this.getForm.controls['tgtschemas'].clearValidators();
        }
        break;

      case 'CDC_Load':
        this.cdcSelected = true;
        this.setRequiredValidators([
          'sourceConnection', 'targetConnection', 'schemas', 'tgtschemas',
          'confiName', 'batchsize'
        ]);
        if (this.migtypeid !== '31') {
          this.getForm.controls['cdcLoadType'].setValidators([Validators.required]);
        }
        break;

      case 'Cdc_config':
        this.incrementSelected = this.cdcSelected = this.logminer = true
        this.setRequiredValidators(['sourceConnection', 'targetConnection', 'confiName', 'refFile', 'scn', 'level', 'dagname']);
        if (this.migtypeid == "30" || this.migtypeid == "20") {
          this.getForm.controls['tgtschemas'].clearValidators()
        }
        break;

      case 'GG_Initial_Data_Load':
        this.initialLoadGGSelected = true;
        this.setRequiredValidators(['sourceConnection', 'targetConnection', 'smallTable', 'largeTable', 'chunkSize', 'confiName', 'schemas', 'podConfig', 'data_LoadType', 'dagExecute', 'cdcType']);
        if (this.migtypeid === "30" || this.migtypeid === "20") {
          this.setRequiredValidators(['tgtschemas'],);
        }
        break;

      case 'E2E_Data_Load':
      case 'Partition_Initial_Data_Load':
        this.initialLoadGGSelected = true;
        this.setRequiredValidators(['sourceConnection', 'targetConnection', 'smallTable', 'largeTable', 'chunkSize', 'confiName', 'schemas', 'podConfig', 'data_LoadType', 'dagExecute', 'cdcType', 'shellScript', 'trailPath', 'ggHome', 'podName']);
        if (this.migtypeid === "30" || this.migtypeid === "20") {
          this.setRequiredValidators(['tgtschemas'],);
        }
        break;

      case 'Reverse_CDC':
        this.incrementSelected = this.reverseCdc = true;
        this.setRequiredValidators(['sourceConnection', 'targetConnection', 'confiName', 'dagname', 'slotname', 'scn', 'TablesList']);
        break;

      default:
        this.setRequiredValidators(['sourceConnection', 'targetConnection', 'smallTable', 'largeTable', 'chunkSize', 'confiName', 'schemas', 'tables', 'podConfig', 'data_LoadType', 'dagExecute']);
        if (this.migtypeid === "30" || this.migtypeid === "20") {
          this.setRequiredValidators(['readsize', 'rows', 'tgtschemas'],);
        } else if (this.migtypeid === "31") {
          this.setRequiredValidators(['batchsize'],);
        }
        break;
    }
  
    // Update controls after modifying validators
    this.updateAllControls();
    this.selectOperation = value;
  }


  clearAllValidators() {
    Object.keys(this.getForm.controls).forEach(controlName => {
      const control = this.getForm.get(controlName);
      control?.clearValidators();
      control?.updateValueAndValidity();
    });
  }

  setRequiredValidators(controls: string[]) {
    controls.forEach(control => {
      (this.getForm.controls[control as keyof typeof this.getForm.controls] as FormControl).setValidators([Validators.required]);
    });
  }

  updateAllControls() {
    Object.keys(this.getForm.controls).forEach(control => {
      (this.getForm.controls[control as keyof typeof this.getForm.controls] as FormControl).updateValueAndValidity();
    });
  }
  
  cdcValidator(value: any) {
    if (value !== 'Golden_Gate') {
      this.getForm.controls['shellScript'].clearValidators()
      this.getForm.controls['trailPath'].clearValidators()
      this.getForm.controls['ggHome'].clearValidators()
      this.getForm.controls['podName'].clearValidators()
    } else {
      this.getForm.controls['shellScript'].setValidators([Validators.required])
      this.getForm.controls['trailPath'].setValidators([Validators.required])
      this.getForm.controls['ggHome'].setValidators([Validators.required])
      this.getForm.controls['podName'].setValidators([Validators.required])
    }
    this.getForm.controls['shellScript'].updateValueAndValidity()
    this.getForm.controls['trailPath'].updateValueAndValidity()
    this.getForm.controls['ggHome'].updateValueAndValidity()
    this.getForm.controls['podName'].updateValueAndValidity()
  }
  refFile: any
  getConfigFile(file: any) {
    this.refFile = file
    //console.log(this.refFile)
  }
  fileResponse: any
  spin_dwld: any;
  downloadFile(fileInfo: any) {
    this.datamigration.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(blob);
      document.body.appendChild(downloadLink);
      downloadLink.download = fileInfo.fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  ReplaceFileName() {
    this.datamigration.ReplaceFileName(this.selFile.name).subscribe((data: any) => {
      this.toast.success("Command Executed");
    })
  }

  hasMultipleSchemas: boolean = false

  schemasCheck() {
    if (this.schemaName.length > 1) {
      this.getForm.controls.tables.clearValidators()
      this.hasMultipleSchemas = false
    }
    else {
      if (this.migtypeid == "30" || this.migtypeid == "20") {
        this.getForm.controls.tables.clearValidators()
        this.hasMultipleSchemas = true
      }
      else {
        this.getForm.controls.tables.setValidators([Validators.required])
        this.hasMultipleSchemas = false;
      }
    }

    this.getForm.controls.tables.updateValueAndValidity()
  }
  tgtschemaname: string = ""
  selecttgtschema(value: string) {
    this.tgtschemaname = value
  }
  selectedsrcschema: string = ""
  showschemaa: boolean = false
  onItemSelect(item: any) {
    //console.log(item)
    this.selectedsrcschema = item
    // if (item.length == 1) {
    if (item != undefined) {
      this.showschemaa = false;
      const schemaId = this.schemaList.filter((el: any) => el.schemaname == item.toString())
      this.schemaName.push(item);
      //console.log(schemaId)
      let obj = {
        schema: item,
        srcId: this.srcId
      }
      if (this.migtypeid == "35" || this.migtypeid == "34") {

      }
      else {
        this.datamigration.GetTablesByschemaGG(obj).subscribe((data: any) => {
          this.tablesList = data['Table1']
          this.tablesList.forEach((item: any) => {
            item.type = "ALL"
          })
          //console.log("Tables", this.tablesList)

        })
      }

      this.schemaID.push(schemaId[0].schema_id)
      this.schemasCheck()
    }
    else {
      this.showschemaa = true;
    }
  }
  onItemDeSelect(item: any) {
    const inde = this.schemaName.indexOf(item.schemaname);
    this.schemaName.splice(inde, 1);
    this.schemaID.splice(inde, 1);
    this.schemasCheck()
  }
  onSelectAll(items: any) {
    items.forEach((dd: any) => {
      this.schemaName.push(dd.schemaname);
    });
    items.forEach((dd: any) => {
      const schemaId = this.schemaList.filter((el: any) => el.schemaname == dd.schemaname)
      this.schemaID.push(schemaId[0].schema_id);
    });
    this.schemasCheck()
  }

  tablesSelect(value: any) {
    if (value == "ALL") {
      this.tablesList.forEach((item: any) => {
        this.selectedTable.push(item.table_name)
      })
    }
  }

  onDeSelectAll(items: any) {
    this.schemaName = [];
    this.schemaID = [];
    this.schemasCheck()
  }
  onTableSelect(item: any) {
    this.selectedTables.push(item.table_name);
  }
  onTableSelectAll(item: any) {
    item.forEach((dd: any) => {
      this.selectedTables.push(dd.table_name);
    });
  }
  onTableDeSelect(item: any) {
    const index = this.selectedTables.indexOf(item.table_name);
    this.selectedTables.splice(index, 1);
  }
  onTableDeSelectAll(item: any) {
    this.selectedTables = [];
  }
  delete_file: boolean = false
  deleteAirflowFiles(value: any) {
    this.datamigration.deleteAirflowFiles(value).subscribe((data: any) => {
      this.fetchConfigFiles()
      this.toast.success(data.message)
    })
  }
  loadingSpinners: { [key: string]: boolean } = {};
  toggleSpinner(itemId: string): void {
    this.loadingSpinners[itemId] = !this.loadingSpinners[itemId];
  }

  selFile1: any
  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selFile1 = file
    this.TableMetaFileName = file.name
    //console.log(this.selFile1)
    //console.log(this.selFile1.name)
    //this.uploadFile(file);
  }
  uploadSpin: boolean = false
  uploadFile1() {
    this.uploadSpin = true
    const formData: FormData = new FormData();
    formData.append('file', this.selFile1, this.selFile1.name);
    formData.append('path', "");
    this.datamigration.UploadDagFiles(formData).subscribe(
      response => {
        this.TableMetaFileName = ''
        this.uploadSpin = false
        this.toast.success("File uploaded successfully")
        //console.log('File uploaded successfully', response);
      },
      error => {
        this.TableMetaFileName = ''
        this.uploadSpin = false
        this.toast.warning("Error uploading file")
        console.error('Error uploading file', error);
      }
    );
  }
  configData: any
  getConfigMenu() {
    this.datamigration.GetConfigMenu(this.migtypeid).subscribe((data: any) => {
      this.configData = data['Table1'].filter((item: any) => {
        if (this.migtypeid == "31") {
          return item.configtype != "Data Compare" && item.configtype != "Catch Up" && item.configtype != "Reverse CDC"
        }
        return item.configtype != "Data Compare"
      })
    })
  }

  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }

  CPUSchema: string = '';
  getCPUSchema(value: string) {
    this.CPUSchema = value
  }
  cpu_spin: boolean = false
  extractCPUValue() {
    let obj = {
      operation: "CPU_Memory_Utilization",
      projectId: this.projectId.toString(),
      srcId: this.CPUSchema
    }
    this.datamigration.insertTablesCommand(obj).subscribe((data: any) => {
      this.spin = false
      this.toast.success("Tables Extracted Successfully")
    },
      error => {
        this.spin = false
        this.toast.error("Failed to Tables Extracted")
      })
  }
  fetchDynamoTables(conId: any) {
    this.datamigration.fetchDynamoTables(conId).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }

}

<!--- Bread Crumb --->
<div class="v-pageName">{{pageName}}</div>

    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="body-main mt-4">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="form qmig-Form" [formGroup]="deltafilesform" >
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label">Select Run Number</label>
                                        <select formControlName="runnumber" class="form-select" #run
                                        (change)="selectRunnumber(run.value)">
                                        <option selected disabled>Select Run Number</option>
                                        @for(list of runNumbersData;track list;){
                                        <option value="{{ list.iteration }}">{{ list.iteration}}</option>
                                        }
                                    </select>
                                        <div class="alert">
                                            @if(validate.runnumber.touched && validate.runnumber.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.runnumber.errors?.['required']) { Run Number is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="name" class="form-label">Select Schema</label>
                                        <select class="form-select" aria-label="Default select example"
                                        aria-placeholder="Select Schema Name" #schemaa
                                        (change)="sourceSel(schemaa.value)" formControlName="scschema">
                                        <option selected>Select Schema</option>
                                        @for(list of schemaNamess;track list;){
                                    <option value="{{ list.schemaname }}">{{ list.schemaname}}</option>
                                    }
                                    </select>
                                        <div class="alert">
                                            @if(validate.scschema.touched && validate.scschema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if(validate.scschema.errors?.['required']) { Schema Name is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 offset-md-3">
                                    <div class="mt-3">
                                        
                                    <button class="btn btn-upload w-100" (click)="downloadExecel()" [disabled]="deltafilesform.invalid">
                                        <span class="mdi mdi-cloud-download-outline"></span> Download
                                    </button>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="" [hidden]="!schemaSel">
                        <div class="custom_search cs-r mb-3">
                            <span class="mdi mdi-magnify"></span>
                            <input type="text" placeholder="Search Status" class="form-control" >
                        </div>
                    </div> 
                        <div class="table-responsive" [hidden]="!schemaSel">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <!-- <th>Sno</th> -->
                                        <th>Object Type/Object Name </th>
                                        <th>File name</th>
                                        <th>S- BaseLine Code</th>
                                        <th>S- Current</th>
                                        <th>S- Code Differences</th>
                                        <th>T- BaseLine Code</th>
                                        <th>T- Current</th>
                                        <th>T- Code Differences</th>
                                        <th>S & T Diff</th>
                                        <th>Action</th>
                                        <th>Process</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (con of GetSourceFilePathData |searchFilter: datachange1| paginate: { itemsPerPage: 10,
                                    currentPage: pages ,id:'Third'} ;track con) {
                                    <tr>
<!--         
                                        <td>{{p*10+$index+1-10}}</td> -->
                                        <td>{{ con.schemaname }}/{{con.objecttype_name}}</td>
                                        <td>{{ con.basefilename }}</td>
                                        <td>
                                            <a data-placement="bottom"
                                                (click)="downloadFiles(con.baseline_filepath,con.baselineFile)">
                                                <span class="mdi mdi-file-arrow-up-down"></span></a>
                                        </td>
                                        <td>
                                            <a data-placement="bottom"
                                                (click)="downloadFiles(con.current_filepath,con.currentFile)">
                                                <span class="mdi mdi-file-arrow-up-down"></span></a>
                                        </td>
                                        <td>
                                            <a data-placement="bottom"
                                            (click)="downloadFiles(con.delta_filepath,con.htmlFile)">
                                            <span class="mdi mdi-file-arrow-up-down"></span></a>
                                        </td>
                                        <td>
                                            @if(con.target_baseline_filepath!='' && con.target_objectname!=undefined){
                                            <a data-placement="bottom"
                                            (click)="downloadFiles(con.target_baseline_filepath,con.target_baselineFile)">
                                            <span class="mdi mdi-file-arrow-up-down"></span></a>
                                    }
                                        </td>
                                        <td>
                                            @if(con.target_current_filepath!='' && con.target_objectname!=undefined){
                                               
                                            <a data-placement="bottom"
                                            (click)="downloadFiles(con.target_current_filepath,con.target_currentFile)">
                                            <span class="mdi mdi-file-arrow-up-down"></span></a>
                                            }
                                        </td>
                                        <td>
                                            @if(con.target_html_filepath!='' && con.target_objectname!=undefined){
                                                <a data-placement="bottom"
                                                (click)="downloadFiles(con.target_html_filepath,con.target_htmlFile)">
                                                <span class="mdi mdi-file-arrow-up-down"></span></a>
                                            }
                                            
                                        </td>
                                        <td>
                                            @if(con.src_tgt_diff_path!='' && con.target_objectname!=undefined){
                                                <a data-placement="bottom"
                                                (click)="downloadFiles(con.src_tgt_diff_path,con.src_tgt_diff_file)">
                                                <span class="mdi mdi-file-arrow-up-down"></span></a>
                                            }
                                        </td>
                                        <td>
                                            <button  class="btn btn-download"
                                            data-bs-toggle="offcanvas" data-bs-target="#demo">
                                                <span class="mdi mdi-pencil btn-icon-prepend"></span>
                                            </button>
                                        </td>
                                        <td>
                                            <button  class="btn btn-download">
                                                <span class="mdi mdi-reload btn-icon-prepend"></span>
                                            </button>
                                        </td>
                                        <td>
        
                                    </tr>
                                    } @empty {
                                    <!-- <tr>
                                        <td colspan="4">
                                            <p class="text-center m-0 w-100">Empty list of Documents</p>
                                        </td>
                                    </tr> -->
                                    }
                                </tbody>
                            </table>
                            <div class="custom_pagination">
                                <pagination-controls (pageChange)="pages = $event" id="Third">
                                </pagination-controls>
                            </div>
                        </div>
                       
                </div>
            </div>
        </div>
    </div>

    <div class="offcanvas offcanvas-end" [hidden]="!schemaSel" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Upload Document</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="uploadForm">
            <div class="form-group">
                <label for="formFile" class="form-label d-required">Upload Source File </label>
                <div class="custom-file-upload">
                    <input class="form-control" type="file" id="formFile" formControlName="file1"
                        (change)="onFileSelected($event)">
                    <div class="file-upload-mask">
                        @if (SourceFileName == '') {
                        <img src="assets/images/fileUpload.png" alt="img" />
                        <p>Drag and Source Source file here or click add deployment file  </p>
                        <button class="btn btn-upload"> Add File </button>
                        }
                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                            <p> {{ SourceFileName }} </p>
                        </div>
                    </div>
                </div>
                @if ( validates.file1.touched && validates.file1.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (validates.file1.errors.required) { File is required }
                </p>
                }
            </div>
            <div class="form-group">
                <button class="btn btn-upload w-100" (click)="uploadFile()" [disabled]="uploadForm.invalid"> <span
                        class="mdi mdi-file-plus"></span> Upload File @if(uploadfileSpin){<app-spinner />}</button>
            </div>
        </form>
        </div>

        <div class="offcanvas-body">
            <form class="form qmig-Form" [formGroup]="uploadsForm">
                <div class="form-group">
                    <label for="formFile" class="form-label d-required">Upload Target File </label>
                    <div class="custom-file-upload">
                        <input class="form-control" type="file" id="formFile" formControlName="file2"
                            (change)="onFileSelected1($event)">
                        <div class="file-upload-mask">
                            @if (TragetFileName == '') {
                            <img src="assets/images/fileUpload.png" alt="img" />
                            <p>Drag and Delta Target file here or click add deployment file </p>
                            <button class="btn btn-upload"> Add File </button>
                            }
                            <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                <p> {{ TragetFileName }} </p>
                            </div>
                        </div>
                    </div>
                    @if ( validatess.file2.touched && validatess.file2.invalid) {
                    <p class="text-start text-danger mt-1">
                        @if (validatess.file2.errors.required) { File is required }
                    </p>
                    }
                </div>
                <div class="form-group">
                    <button class="btn btn-upload w-100" (click)="uploadFile()" [disabled]="uploadsForm.invalid"> <span
                            class="mdi mdi-file-plus"></span> Upload File @if(uploadfileSpin){<app-spinner />}</button>
                </div>
            </form>
            </div>
            <!-- <div class="form-group" >
                <label for="formFile" class="form-label d-required">Upload Target File </label>
                <div class="custom-file-upload">
                    <input class="form-control" type="file" id="formFile" formControlName="file2"
                        (change)="onFileSelected1($event)">
                    <div class="file-upload-mask">
                        @if (TragetFileName == '') {
                        <img src="assets/images/fileUpload.png" alt="img" />
                        <p>Drag and Delta Target file here or click add deployment file </p>
                        <button class="btn btn-upload"> Add File </button>
                        }
                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                            <p> {{ TragetFileName }} </p>
                        </div>
                    </div>
                </div>
                @if ( validates.file2.touched && validates.file2.invalid) {
                <p class="text-start text-danger mt-1">
                    @if (validates.file2.errors.required) { File is required }
                </p>
                }
            </div>
            <div class="form-group">
                <button class="btn btn-upload w-100" (click)="uploadFile()" [disabled]="uploadForm.invalid"> <span
                        class="mdi mdi-file-plus"></span> Upload File @if(uploadfileSpin){<app-spinner />}</button>
            </div> -->
        
    </div>

<!-- <div class="offcanvas offcanvas-end" tabindex="-1"  id="demo">
    <div class="offcanvas-header">
      <h4 class="main_h">Source & Target Files Update</h4>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form" [formGroup]="uploadForm" (ngSubmit)="uploadFile()">
            <div class="form-group">
                <label for="formFile" class="form-label d-required">Source Code Current File  </label>
                <div class="custom-file-upload">
                    <input class="form-control" type="file" id="formFile" formControlName="document" (change)="sendValueSrc($event)" accept=".zip,.rar,.7zip">
                    <div class="file-upload-mask">
                        @if (fileName == '') { 
                            <img src="assets/images/fileUpload.png" alt="img" />                                            
                            <p>Drag and drop Source Code Current File  here or click add Source Code Current File  file  </p>
                            <button class="btn btn-upload"> Add File </button>
                         }
                        <div class="d-flex justify-content-center align-items-center h-100 w-100"><p> {{ fileName }} </p></div>
                    </div>                                        
                </div>                                
                @if ( f.file.touched && f.file.invalid) {
                    <p class="text-start text-danger mt-1">
                        @if (f.file.errors.required) { File is required }
                    </p>                                    
                }
            </div>
            <div class="form-group">
                <button class="btn btn-upload w-100"  [disabled]="uploadForm.invalid"> <span class="mdi mdi-file-plus"></span> Upload File @if(uploadfileSpin){<app-spinner/>}</button>
            </div>
        </form>
        <form class="form qmig-Form" [formGroup]="uploadForm" (ngSubmit)="uploadFile()">
            <div class="form-group">
                <label for="formFile" class="form-label d-required">Target Code Current File  </label>
                <div class="custom-file-upload">
                    <input class="form-control" type="file" id="formFile" formControlName="document" (change)="sendValuetgt($event)" accept=".zip,.rar,.7zip">
                    <div class="file-upload-mask">
                        @if (fileName == '') { 
                            <img src="assets/images/fileUpload.png" alt="img" />                                            
                            <p>Drag and drop Target Code Current File here or click add Target Code Current File  </p>
                            <button class="btn btn-upload"> Add File </button>
                         }
                        <div class="d-flex justify-content-center align-items-center h-100 w-100"><p> {{ fileName }} </p></div>
                    </div>                                        
                </div>                                
                @if ( f.file.touched && f.file.invalid) {
                    <p class="text-start text-danger mt-1">
                        @if (f.file.errors.required) { File is required }
                    </p>                                    
                }
            </div>
            <div class="form-group">
                <button class="btn btn-upload w-100"  [disabled]="uploadForm.invalid"> <span class="mdi mdi-file-plus"></span> Upload File @if(uploadfileSpin){<app-spinner/>}</button>
            </div>
        </form>
    </div>
  </div> -->

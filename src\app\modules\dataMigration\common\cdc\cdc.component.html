<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Create Configuration
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">

                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <div class="row">
                            <div class="col-12 col-md-6 col-xl-6 mt-4"></div>
                            <div class="col-12 col-md-3 col-xl-3 mt-4"></div>
                            <div class="col-12 col-md-3 col-xl-3 mt-4">
                                <button class="btn btn-upload w-100 " (click)="onEditConfigClick()"
                                    data-bs-toggle="offcanvas" data-bs-target="#demo"> <span
                                        class="mdi mdi-checkbox-marked-circle-outline" aria-hidden="true"></span>Edit
                                    Configuration</button>
                            </div>
                        </div>
                        <form class="form qmig-Form" [formGroup]="getForm">
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Operation </label>
                                        <select class="form-select" formControlName="operation"
                                            (change)="getOP(opvalue.value)" #opvalue>
                                            <option>Select Operation</option>
                                            @for(list of operations;track list; ){
                                            <option value="{{list.value}}">{{list.option}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.operation.touched && f.operation.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.operation.errors?.['required']) { operation is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- source connection list -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name"> @if(getForm.value.operation ==
                                            'Reverse_CDC'){ Target }@else{Source} Connection </label>
                                        <select class="form-select" #org1
                                            (change)="getDb(org1.value);getSchemasList(org1.value,'S');onSourceChange(org1.value)"
                                            formControlName="sourceConnection">
                                            <option>Select @if(getForm.value.operation == 'Reverse_CDC'){ Target
                                                }@else{Source} Connection</option>
                                            @for(list of ConsList;track list; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.sourceConnection.touched && f.sourceConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.sourceConnection.errors?.['required']) {
                                                @if(getForm.value.operation == 'Reverse_CDC'){ Target }@else{Source}
                                                Connection is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>


                                <!-- schema list -->
                                @if(getForm.value.operation != "Load_Files"){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Source {{schemalable}} </label>
                                        <ng-select [placeholder]="'Select Schema Name'" formControlName="schemas"
                                            groupBy="type" [selectableGroup]="true" [items]="schemaList"
                                            (change)="OnSchemasSelect()" [multiple]="true" bindLabel="schema_name"
                                            [closeOnSelect]="false" bindValue="schema_name" clearAllText="Clear"
                                            [clearSearchOnAdd]="true" [(ngModel)]="selectedSchemas">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }"
                                                    value={{item.schema_name}} />
                                                {{item.schema_name}}
                                            </ng-template>
                                            <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.schema_name}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if ( f.schemas.touched && f.schemas.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.schemas.errors?.['required']) { {{schemalable}} is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }

                                <!-- tableslist -->
                                @if(!tableHide && getForm.value.operation!= "Load_Files"){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">Tables </label>
                                        <ng-select [placeholder]="'Select Table Name'" formControlName="tables"
                                            groupBy="type" [selectableGroup]="true" [items]="tablesList"
                                            (change)="tablesSelect(selectedTable)" [multiple]="true"
                                            bindLabel="table_name" [closeOnSelect]="false" bindValue="table_name"
                                            clearAllText="Clear" [clearSearchOnAdd]="true" [(ngModel)]="selectedTable">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }"
                                                    value={{item.table_name}} />
                                                {{item.table_name}}
                                            </ng-template>
                                            <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.table_name}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if ( f.tables.touched && f.tables.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.tables.errors?.['required']) { Tables required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- cdc load type -->
                                @if(getForm.value.operation!= "Load_Files"){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">CDC Load Type
                                        </label>
                                        <select class="form-select" formControlName="cdcLoadType">
                                            <option selected value="">Select CDC Load Type </option>
                                            @for(cdcL of cdcLoadData;track cdcL; ){
                                            <option value="{{ cdcL.value }}">{{ cdcL.name}}</option>
                                            }

                                        </select>
                                        <div class="alert">
                                            @if ( f.cdcLoadType.touched && f.cdcLoadType.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.cdcLoadType.errors?.['required']) { CDC Load Type is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }
                                <!-- target list -->
                                @if( getForm.value.cdcLoadType == 'Database' || getForm.value.operation== "Load_Files"){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label" for="name">@if(getForm.value.operation ==
                                            'Reverse_CDC'){ Source }@else{Target} Connection </label>
                                        <select class="form-select" #tgtcon
                                            (change)="getTgtId(tgtcon.value);getSchemasList(tgtcon.value,'T');onTargetChange(tgtcon.value)"
                                            formControlName="targetConnection">
                                            <option>Select @if(getForm.value.operation == 'Reverse_CDC'){ Source
                                                }@else{Target} Connection</option>
                                            @for(list of tgtList;track list; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.targetConnection.touched && f.targetConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.targetConnection.errors?.['required']) {
                                                @if(getForm.value.operation == 'Reverse_CDC'){ Source }@else{Target}
                                                Connection is
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }


                                <!-- Target schema list -->
                                @if( getForm.value.cdcLoadType == 'Database' && getForm.value.operation!= "Load_Files"){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label " for="name">@if(getForm.value.operation ==
                                            'Reverse_CDC'){ Source }@else{Target} {{schemalable}} </label>
                                        <select class="form-select" #tgtsc
                                            (change)="selecttgtschema(tgtsc.value); getPGTables(tgtsc.value)"
                                            formControlName="tgtschemas">
                                            <option>Select @if(getForm.value.operation == 'Reverse_CDC'){ Source
                                                }@else{Target} Schema</option>
                                            @for(tgtsch of tgtSchemaList;track tgtsch; ){
                                            <option value="{{tgtsch.schema_name}}">{{tgtsch.schema_name}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.tgtschemas.touched && f.tgtschemas.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.tgtschemas.errors?.['required']) { Target {{schemalable}}
                                                required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }



                                <!-- configuration name  -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Configuration Name </label>
                                        <input type="text" class="form-control" formControlName="confiName"
                                            placeholder="CONFIG_FILE_DATE" maxlength="15" />
                                        <div class="alert">
                                            @if ( f.confiName.touched && f.confiName.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.confiName.errors?.['required']) { Configuration Name required }
                                                @if (f.confiName.errors?.['maxLength']) { Maximium Length is 15
                                                character's }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>



                                <!-- @if(getForm.value.operation == 'Reverse_CDC' && migtypeid!="41"){
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Replication Slot Name </label>
                                        <select class="form-select" formControlName="replicate">
                                            <option selected value="">Select Replication Slot Name </option>
                                            @for(repli of replicationList;track repli; ){
                                            <option value="{{ repli.slotname }}">{{ repli.slotname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( f.replicate.touched && f.replicate.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.replicate.errors?.['required']) { Relication Slot Name required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                } -->
                                <div class="col-4 col-sm-4 col-md-4 col-lg-4 col-xl-3">
                                    <div>
                                        <button class="btn btn-upload w-100 mt-4" [disabled]="!getForm.valid"
                                            (click)="DataMigrationNewCommand(getForm.value)"> <span
                                                class="mdi mdi-file-cog-outline btn-icon-prepend"></span>Create Config
                                            @if(executed){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @if(migtypeid=="31" || migtypeid=="41"){
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse1" aria-expanded="true" aria-controls="flush-collapse">
                    Create Load File Configuration
                </button>
            </h2>
            <div id="flush-collapse1" class="accordion-collapse collapse" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="cdcLoadForm">
                            <div class="row">

                                <!-- target list -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Target Connection </label>
                                        <select class="form-select" #tgtcon
                                            (change)="getTgtId(tgtcon.value);getSchemasList(tgtcon.value,'T')"
                                            formControlName="targetConnection">
                                            <option>Select Target Connection</option>
                                            @for(list of tgtList;track list; ){
                                            <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( e.targetConnection.touched && e.targetConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (e.targetConnection.errors?.['required']) {
                                                Target Connection is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <!-- schema list -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Schema </label>
                                        <select class="form-select" #sc (change)="onItemSelect(sc.value)"
                                            formControlName="schemas">
                                            <option>Select Schema</option>
                                            @for(sch of schemaListFiles;track sch; ){
                                            <option value="{{sch.schemaname}}">{{sch.schemaname}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( e.schemas.touched && e.schemas.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (e.schemas.errors?.['required']) { {{schemalable}} is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Tables </label>
                                        <ng-select [placeholder]="'Select Table Name'" formControlName="tables"
                                            groupBy="type" [selectableGroup]="true" [items]="tablesList"
                                            (change)="tablesSelect(selectedTable)" [multiple]="true"
                                            bindLabel="table_name" [closeOnSelect]="false" bindValue="table_name"
                                            clearAllText="Clear" [clearSearchOnAdd]="true" [(ngModel)]="selectedTable">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$"
                                                let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }"
                                                    value={{item.table_name}} />
                                                {{item.table_name}}
                                            </ng-template>
                                            <ng-template ng-multi-label-tmp let-items="items">
                                                <div class="ng-value" *ngFor="let item of slicedData(items)">
                                                    {{item.table_name}}
                                                </div>
                                                <div class="ng-value" *ngIf="items.length > 1">
                                                    <span class="ng-value-label">{{items.length - 1}} more...</span>
                                                </div>
                                            </ng-template>
                                        </ng-select>
                                        <div class="alert">
                                            @if ( e.tables.touched && e.tables.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (e.tables.errors?.['required']) { Tables required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>


                                @if(migtypeid=="31" ){
                                <!-- Operation-->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Operation </label>
                                        <select class="form-select" formControlName="operation"
                                            (change)="filterConfigFiles(op1value.value)" #op1value>
                                            <option>Select Operation</option>
                                            @for(list of configData;track list; ){
                                            <option value="{{list.configvalue}}">{{list.configtype}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( e.operation.touched && e.operation.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (e.operation.errors?.['required']) { operation is required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>


                                <!-- Reference File-->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Reference Config File</label>
                                        <select class="form-select" formControlName="refFile" #tgtcon>
                                            <option>Select Reference Config File</option>
                                            @for(file of filteredConfigFiles;track file; ){
                                            <option value="{{file.fileName }}">{{file.fileName}}</option>
                                            }
                                        </select>
                                        <div class="alert">
                                            @if ( e.refFile.touched && e.refFile.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (e.refFile.errors?.['required']) { Reference Config File is required
                                                }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                }

                                <!-- configuration name  -->
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="name">Configuration Name </label>
                                        <input type="text" class="form-control" formControlName="confiName"
                                            placeholder="CONFIG_FILE_DATE" maxlength="15" />
                                        <div class="alert">
                                            @if ( e.confiName.touched && e.confiName.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (e.confiName.errors?.['required']) { Configuration Name required }
                                                @if (e.confiName.errors?.['maxLength']) { Maximium Length is 15
                                                character's }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                </div>


                            </div>
                            <div class="row">
                                <div class="col-8 col-sm-8 col-md-8 col-lg-8 col-xl-9"></div>
                                <div class="col-4 col-sm-4 col-md-4 col-lg-4 col-xl-3">
                                    <div>
                                        <button class="btn btn-upload w-100 mt-4" [disabled]="!cdcLoadForm.valid"
                                            (click)="DataMigrationCommand(cdcLoadForm.value,2)"> <span
                                                class="mdi mdi-file-cog-outline btn-icon-prepend"></span>Create Load
                                            File Config
                                            @if(executed){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        }
        <!-- operation details -->

        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne1" aria-expanded="false" aria-controls="flush-collapseOne">
                    Configuration Files
                </button>
            </h3>
            <div id="flush-collapseOne1" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="body-header mt-4">
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <div class="row">
                                <div class="col-12 col-sm-4 col-md-3 col-lg-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source
                                            Connection Name</label>
                                        <select class="form-select" #srcFile
                                            (change)="fetchSourceConfigFiles(srcFile.value)">
                                            <option>Select a Source Connection</option>
                                            @for ( src of ConsList; track src) {
                                            <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-4 col-md-3 col-lg-3">
                                    <div class="form-group">
                                        <label class="form-label" for="targtetConnection">Target Connection Name</label>
                                        <select class="form-select" #tgtFile
                                            (change)="fetchTargetConfigFiles(tgtFile.value)">
                                            <option>Select Target Connection</option>
                                            @for(tgt of tgtList;track tgt; ){
                                            <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-4 col-md-6 col-lg-6 d-flex">
                                    <div class="custom_search cs-r me-3 my-3 ">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Config Files" class="form-control"
                                            [(ngModel)]="searchText" (keyup)="onKey()">
                                    </div>
                                    <button class="btn btn-sync" (click)="fetchNewValidationFiles1()">
                                        @if(getRunSpin11){
                                        <app-spinner />
                                        }@else{
                                        <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="body-main mt-4">
                            <div class="qmig-card">
                                <!-- <h3 class="main_h px-3 pb-1 pt-3">List of Reports</h3> -->
                                <div class="table-responsive">
                                    <table class="table table-hover qmig-table">
                                        <thead>
                                            <tr>
                                                <th>S.NO</th>
                                                <th>File Name</th>
                                                <th>Created Date</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for (Reports of cdcfiles | searchFilter: searchText | paginate: {
                                            itemsPerPage: pi,
                                            currentPage: p2 } ; track Reports; let i = $index) {
                                            <tr>
                                                <td>{{p2*pi+i+1-pi}}</td>
                                                <td>{{Reports.fileName }}</td>
                                                <td>{{Reports.created_dt}}</td>
                                                <td>
                                                    <button class="btn btn-download" (click)="downloadFile(Reports)">
                                                        <span
                                                            class="mdi mdi-cloud-download-outline"></span>@if(spinDownload){<app-spinner />}
                                                    </button>
                                                    <button class="btn btn-delete"
                                                        (click)="deleteFiles(Reports.filePath)">
                                                        <span class="mdi mdi-delete"></span>
                                                    </button>
                                                </td>
                                            </tr>
                                            } @empty {
                                            <tr>
                                                <td colspan="4">
                                                    <p class="text-center m-0 w-100">Empty list of Reports</p>
                                                </td>
                                            </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="custom_pagination">
                                    <pagination-controls (pageChange)="p2 = $event"></pagination-controls>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapsea" aria-expanded="true" aria-controls="flush-collapse">
                    Execution
                </button>
            </h2>
            <div id="flush-collapsea" class="accordion-collapse collapse " aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card">
                    <div class="qmig-card-body">
                        <form class="form qmig-Form" [formGroup]="ExecutionForm"
                            (ngSubmit)="getDagFormValue(ExecutionForm.value)">
                            <div class="row">
                                <!-- <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Operation</label>
                                        <select class="form-select" #oper
                                            (change)="selectOperation(oper.value);filterConfigFiles()"
                                            formControlName="operation">
                                            <option>Select a Operations</option>
                                            @for(dag of configData ;track dag; ){
                                            <option value="{{ dag.configvalue }}"> {{ dag.configtype }} </option>
                                            }
                                        </select>

                                        <!-- Validators 
                                        @if ( forms.operation.touched && forms.operation.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (forms.operation.errors.required) {operation is Required }
                                        </p>
                                        }
                                    </div>
                                </div> -->
                                <!-- config details -->
                                <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="targtetConnection">Source
                                            Connection Name</label>
                                        <select class="form-select" #srcFile
                                            (change)="fetchSourceConfigFiles1(srcFile.value)">
                                            <option>Select a Source Connection</option>
                                            @for ( src of ConsList; track src) {
                                            <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-3 col-md-3 col-lg-3 ">
                                    <div class="form-group">
                                        <label class="form-label" for="targtetConnection">Target Connection Name</label>
                                        <select class="form-select" #tgtFile
                                            (change)="fetchTargetConfigFiles1(tgtFile.value)">
                                            <option>Select Target Connection</option>
                                            @for(tgt of tgtList;track tgt; ){
                                            <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3">
                                    <div class="form-group">
                                        <label class="form-label d-required" for="Schema">Select Config</label>
                                        <select class="form-select" #config
                                            (change)="selectDag(config.value);getConfigDags(config.value)"
                                            formControlName="config">
                                            <option>Select a Config</option>
                                            @for(Config of cdcFileExe ;track Config; ){
                                            <option value="{{ Config.filePath }}"> {{ Config.fileName }} </option>
                                            }
                                        </select>

                                        <!-- Validators -->
                                        @if ( forms.config.touched && forms.config.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (forms.config.errors.required) {config is Required }
                                        </p>
                                        }
                                    </div>
                                </div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-1 d-flex">
                                    <button class="btn btn-sync" (click)="refreshdags()">
                                        @if(reff_spin){
                                        <app-spinner />
                                        }
                                        @else{
                                        <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </div>
                                @if(dagSelected)
                                {

                                <div class="col-6 col-md-3 col-xl-3 mt-4">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100" data-bs-toggle="offcanvas"
                                            data-bs-target="#updateOrgModal" (click)="openPopup();popup(1)"
                                            [disabled]="!isCheckBoxSel">
                                            <span class="mdi mdi-timer-play"></span>Schedule Dag
                                        </button>
                                    </div>
                                </div>
                                <!-- Trigger details-->
                                <div class="col-6 col-md-3 col-xl-3 mt-4">
                                    <div class="body-header-button">
                                        <button class="btn btn-sign w-100" (click)="getSCN();popup(0)"
                                            [disabled]="!isCheckBoxSel">
                                            <span class="mdi mdi-cog-play"></span>
                                            Trigger Dag @if(trigger_spin){<app-spinner />}</button>
                                    </div>
                                </div>
                                }
                            </div>
                        </form>
                    </div>
                </div>
                @if(dagSelected)
                {
                <div class="body-main mt-4">
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <div class="row">
                                <div class="col-12 col-md-7 offset-5 d-flex">
                                    <div class="custom_search cs-r me-3 my-3">
                                        <span class="mdi mdi-magnify"></span>
                                        <input type="text" placeholder="Search Dags" class="form-control"
                                            [(ngModel)]="searchText1" (keyup)="onKey()">
                                    </div>
                                    <button class="btn btn-sync" (click)="refreshdags()">
                                        @if(reff_spin){
                                        <app-spinner />
                                        }@else{
                                        <span class="mdi mdi-refresh"></span>
                                        }
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- Table details -->
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table" id="example" style="width:100%">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <div class="form-check m-0">
                                                <label class="form-check-label">
                                                    <input type="checkbox" (click)="selectAll($event)"
                                                        class="form-check-input" [checked]="showCheckStatus"
                                                        [disabled]="isDisableAllCheck"> <i class="input-helper"></i>
                                                </label>
                                            </div>
                                        </th>

                                        <th>Dag</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- For pagination -->
                                    @for(dag of dagsInfo | searchFilter: searchText1 |paginate:
                                    {itemsPerPage:10,currentPage:
                                    p,id:'First'};track dag){
                                    <tr>
                                        <td>
                                            <div class="form-check mt-33">
                                                <label class="form-check-label">
                                                    <input type="checkbox" class="form-check-input" #chck
                                                        (click)="checkboxselect($event, chck.value)"
                                                        value="{{dag.dag_id}}" [checked]='dag.isSelected'
                                                        [disabled]="dag.isDagAvailable==false">
                                                    <i class="input-helper"></i>
                                                </label>
                                            </div>
                                        </td>
                                        <td>{{dag.dag_id}}</td>
                                        @if(dag.isDagAvailable==true)
                                        {<td><i class="mdi mdi-check green"></i>
                                        </td>
                                        }
                                        @if(dag.isDagAvailable==false)
                                        {<td><i class="mdi mdi-close red"></i>
                                        </td>
                                        }
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p = $event" id="First"></pagination-controls>
                        </div>
                    </div>
                </div>
                }
            </div>
        </div>

        <!-- operation details -->
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    CDC Data Reports
                </button>
            </h3>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="body-main mt-3">
                    <div class="qmig-card">
                        <!-- <h3 class="main_h px-3 pt-3">CDC Reports</h3> -->
                        <div class="qmig-card-body">
                            <form class="form qmig-Form" [formGroup]="cdcForm">
                                <div class="row">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 col-cm1">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">Configuration File</label>
                                            <select formControlName="configFile" class="form-select">
                                                <option selected value="">Select Configuration File</option>
                                                @for ( list of cdcConfigFiles; track list) {
                                                <option value="{{ list.config_id }}">{{list.file_name }}</option>
                                                }
                                            </select>
                                            @if ( fs.configFile.touched && fs.configFile.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fs.configFile.errors.required) {configFile is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 col-cm1">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">From Date</label>
                                            <input type="datetime-local" class="form-control"
                                                formControlName="fromDate" />
                                            @if ( fs.fromDate.touched && fs.fromDate.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fs.fromDate.errors.required) {From Date is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 col-cm1">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="name">To Date</label>
                                            <input type="datetime-local" class="form-control"
                                                formControlName="toDate" />
                                            @if ( fs.toDate.touched && fs.toDate.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fs.toDate.errors.required) {To Date is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 col-cm1 mt-4 pt-1">
                                        <button class="btn btn-upload w-100" [disabled]="!cdcForm.valid"
                                            (click)="FetchCdcData(cdcForm.value)"> Show Summary
                                            @if(summarySpinner){<app-spinner />}</button>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 col-cm1  mt-4 pt-1">
                                        <button class="btn btn-upload w-100" [disabled]="!cdcForm.valid"
                                            (click)="DataMigrationCommand(cdcForm.value,0)"> Detailed Report
                                            @if(reportSpinner){<app-spinner />}</button>
                                    </div>

                                </div>
                            </form>
                        </div>

                        <!-- downloadFile -->
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Total Transactions</th>
                                        <th>Total Transactions Per Sec</th>
                                        <th>Batch Duration(Min)</th>
                                        <th>Batch Size(Kb)</th>


                                    </tr>
                                </thead>
                                <tbody>
                                    @for(validrepo of cdcResponse
                                    | searchFilter : datachange
                                    | paginate
                                    : {
                                    itemsPerPage: pi,
                                    currentPage: p1,
                                    id: 'third'
                                    };track validrepo
                                    )
                                    {
                                    <tr>
                                        <td>{{p1*pi+$index+1-pi}}</td>
                                        <td> {{validrepo.no_of_transactions }}</td>
                                        <td> {{validrepo.no_transactions_per_sec }}</td>
                                        <td>{{validrepo.batch_duration_min }}</td>
                                        <td> {{validrepo.batch_statements_size_kb }}</td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="5">
                                            <p class="text-center m-0 w-100">Empty list</p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="p1 = $event" id="third"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!--- Bread Crumb --->



        <!--- Reports List --->

    </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="updateOrgModal">
    <div class="offcanvas-header">
        <h4 class="card-title">
            <i class="fas fa-chalkboard-teacher"></i> Dag Execution
        </h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body">
        <form class="form qmig-Form">
            <div class="form-group">
                @if(scheduleSelect){
                <div>
                    <span>Selected Dags:{{count.length}}</span>
                    <div>

                        @if(initialselected){
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Type</label>
                            <select class="form-select" #init (change)="selecttype(init.value)">>
                                <option>Select a Types</option>
                                @for(Types of initTypes ;track Types; ){
                                <option value="{{ Types.value }}"> {{ Types.option }} </option>
                                }
                            </select>
                        </div>
                        }
                    </div>
                </div>
                <button class="btn btn-upload w-100" (click)="getSCN()">
                    Execute @if(executeSpin){<app-spinner />}</button>
                }
                @if(!scheduleSelect){
                <div>
                    <span>Selected Dags:{{count.length}}</span>
                    <div>
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Schedule Date</label>
                            <input type="datetime-local" class="form-control" id="schDate">
                        </div>
                        <button class="btn btn-upload w-100" (click)="scheduleDags()"> <span
                                class="mdi btn-icon-prepend"></span>
                            Schedule</button>
                    </div>
                </div>
                }

                <!-- Execute button -->

            </div>
        </form>
    </div>
</div>
<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
    <div class="offcanvas-header">
        <h4 class="main_h">Json Details</h4>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>
    <!--- Connection Name --->
    <div class="offcanvas-body">
        <div class="col-md-12">
            <div class="form-group">
                <label class="form-label d-required" for="name">Json Data </label>
                <textarea class="form-control" type="text" id="read" placeholder="data" rows="15" cols="30"
                    [(ngModel)]="readDataString"></textarea>
            </div>
        </div>
        <div class="body-header-button mt-1">
            <button (click)="Update()" class="btn btn-upload w-100 "> Save
                @if(uploadSpin){<app-spinner />}</button>
        </div>
    </div>
</div>
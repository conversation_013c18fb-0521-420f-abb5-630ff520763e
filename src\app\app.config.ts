import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';

import { routes } from './app.routes';
import { provideClientHydration } from '@angular/platform-browser';

import { provideHotToastConfig } from '@ngxpert/hot-toast';
import { HttpClientModule, provideHttpClient } from '@angular/common/http';
import { noopInterceptorProvider } from './core/authProvider';
import { MarkdownModule } from 'ngx-markdown';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes), 
    provideClientHydration(), 
    provideHotToastConfig(), 
    provideHttpClient(), 
    importProvidersFrom(HttpClientModule),
    noopInterceptorProvider,
    importProvidersFrom(MarkdownModule.forRoot()), // <- changed here
  ]
};

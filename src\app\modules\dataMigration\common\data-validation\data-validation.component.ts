import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgxPaginationModule } from 'ngx-pagination';

@Component({
  selector: 'app-data-validation',
  standalone: true,
  imports: [SpinnerComponent, SearchFilterPipe, CommonModule, FormsModule, ReactiveFormsModule, NgxPaginationModule],
  templateUrl: './data-validation.component.html',
  styles: ``
})
export class DataValidationComponent {
  //global variables
  projectId: any;
  pageName: string = ''
  ConsList: any;
  tgtlist: any;
  validationForm: any;

  datachange: string = ''
  FilesList: any;
  searchText: any;
  pageNumber: number = 1;

  constructor(private titleService: Title, private route: ActivatedRoute, public formBuilder: FormBuilder, private datamigration: DataMigrationService, private toast: HotToastService,) {
    this.pageName = this.route.snapshot.data['name'];
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList()
    this.validationForm = this.formBuilder.group({
      connectionType: ['', [Validators.required]],
      targetConnection: ['', [Validators.required]],
      fileName: ['', [Validators.required]],
      table: ['', [Validators.required]],
      filter_expression: ['', [Validators.required]],
      rows: ['', [Validators.required]],
      filter_value: [''],
    });
    this.fetchTablesfromDB()
    let obj={
      filename:"Music_Comparison.html",
      status:"Matched"
    }
    this.filesres.push(obj)
  }

  /*-get source and target connections-*/
  GetConsList() {
    this.datamigration.getConList(this.projectId?.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      //console.log(this.ConsList)
      this.tgtlist = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      //console.log(this.tgtlist)
    });
  }
  /*-Form Validation-*/
  get f() {
    return this.validationForm.controls;

  }

  onkey() {
    this.pageNumber = 1
  }
  TriggerValidation(formData: any) {
    let obj = {
      projectId: this.projectId,
      srcId: formData.connectionType,
      tgtId: formData.targetConnection,
      tablename: formData.table,
      filterExpression: formData.filter_expression,
      filterValue: formData.filter_value,
      rows: formData.rows,
      fileName: formData.fileName
    }
    this.datamigration.triggerDataValidation(obj).subscribe((data: any) => {
      this.toast.success(data.message);
    })
  }
  tablesList: any = []
  fetchDynamoTables(conId: any) {
    this.datamigration.fetchDynamoTables(conId).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  fileRes: any = []
  fetchValidationFiles(table: string, filename: string) {
    var path = "Data_Compare/Python_Data_Files/" + table + "/_~_source_/" + filename + ".json"
    this.datamigration.GetFilesFromExpath(path).subscribe((data: any) => {
      this.fileRes = data
    })
  }
  fileResponse: any
  spin_dwld: boolean = false
  downloadFile(filename:any) {
    this.spin_dwld = true
    // var path = "Data_Compare/Python_Data_Files/" + table + "/_~_" + type + "_/" + filename + ".json"
    var path = "/mnt/extra/Data_Compare/Python_Data_Files/" + filename

    this.datamigration.downloadLargeFiles(path).subscribe((blob: any) => {
      this.fileResponse = blob;
      const downloadLink = document.createElement('a');
      downloadLink.href = window.URL.createObjectURL(blob);
      document.body.appendChild(downloadLink);
      downloadLink.download = filename;
      downloadLink.click();
      this.spin_dwld = false
    })
  }
  tablesres: any
  fetchTablesfromDB() {
    this.datamigration.fetchTablesFromDB().subscribe((data: any) => {
      this.tablesres = data["Table1"]
    })
  }
  filesres: any=[]
  fetchfilesfromDB(table:string) {
    this.datamigration.fetchFilesFromDB(table).subscribe((data: any) => {
      this.filesres = data["Table1"]
    })
  }

}

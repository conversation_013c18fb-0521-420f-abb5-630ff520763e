import { Component, NgModule } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { AssessmentService } from '../../../../services/assessment.service';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { conlist, deleteFile, documentsList, fileStatus, redisCommand } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';

@Component({
  selector: 'app-extract-schemas',
  standalone: true,
  imports: [BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, NgSelectModule],
  templateUrl: './extract-schemas.component.html',
  styles: ``
})
export class ExtractSchemasComponent {
  /*-- Global Variable--*/
  projectId: string;

  extractschemaForm = this.formBuilder.group({
    dataBase: ['', [Validators.required]],
    tgtdatabase:['',[Validators.required]],
    Category: ['']
  })
  extracttgtschemaForm = this.formBuilder.group({
    dataBase: ['', [Validators.required]],
  })
  ConsList: any = [];
  srcId: any;

  /*-- for spinners --*/
  getSpin_S: boolean = false;
  getSpin_T: boolean = false;


  /*-- Redis Command--*/
  redisResp: any;
  targetList: any
  schemaName: any = [];
  selectedItems: any = [];
  pageName: string = ''
  dropschema: string = ""
  schemalable: string = ""
  constructor(private titleService: Title, private toast: HotToastService, private assessmentService: AssessmentService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    this.pageName = this.route.snapshot.data['name'];
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }
    if (this.migtypeid == "30" || this.migtypeid == "39" || this.migtypeid == "28" || this.migtypeid == "31") {
      this.dropschema = "Drop Target Schema Objects"
    }
    else {
      this.dropschema = "Drop Target Schema"
    }
  }
  createTargetForm: any
  migtypeid: string = ""
  deleteForm: any
  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.createTargetForm = this.formBuilder.group({
      ctTargetConnection: ['', [Validators.required]],
      ctSchema: ['', [Validators.required]],
    })
    this.GetConsList()
    this.getreqTableData()
    this.filterExecutionReports()
    if (this.migtypeid == "30" || this.migtypeid == "39" || this.migtypeid == "28") {
      this.deleteForm = this.formBuilder.group({
        targetConnection: ['', [Validators.required]],
        schema: ['', [Validators.required]],
        objTye: ['', [Validators.required]],
        objnames: ['', [Validators.required]],
      });
    } else {
      this.deleteForm = this.formBuilder.group({
        targetConnection: ['', [Validators.required]],
        schema: ['', [Validators.required]],
        objnames: [''],
        objTye: ['']
      });
    }
    
  }

  /*-- for Form Validation --*/
  get fs(): any {
    return this.extractschemaForm.controls;
  }
  get ftgt(): any {
    return this.extracttgtschemaForm.controls;
  }
  get f() {
    return this.deleteForm.controls;
  }
  /*-- for Source Connection --*/
  refreshform() {
    this.createTargetForm.reset()
  }
  GetConsList() {
    this.assessmentService.getConList(this.projectId).subscribe((data: conlist) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';

      });
      this.targetList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
    });
  }
  getDb(value: string) {
    this.srcId = value
  }
  tgtId: any
  getgtId(value: string) {
    this.tgtId = value
  }

  /*-- for Execute button --*/

  RedisCommand(value: any) {

    const obj: redisCommand = {
      projectId: this.projectId.toString(),
      conId: this.srcId,
      contype: value
    }
    if (value == 'S') {
      this.getSpin_S = true;
    }
    else {
      this.getSpin_T = true;
    }

    this.assessmentService.insertSchema(obj).subscribe(
      (data: fileStatus) => {
        this.redisResp = data;
        this.toast.success("Schemas Executed")
        if (value == 'S') {
          this.getSpin_S = false;
        }
        else {
          this.getSpin_T = false;
        }
      },
      error => {
        if (value == 'S') {
          this.getSpin_S = false;
        }
        else {
          this.getSpin_T = false;
        }
        this.toast.error('Something went wrong')
      })
  }
  get fc() {
    return this.createTargetForm.controls;
  }

  createschemaresponse: string = ""
  spin_createschema: boolean = false
  createSchema(formData: any) {
    this.spin_createschema = true
    let obj = {
      Conid: formData.ctTargetConnection,
      schemaname: formData.ctSchema
    }
    if (this.migtypeid == "30" || this.migtypeid == "39") {
      this.assessmentService.createschema(obj).subscribe((data: any) => {
        this.createschemaresponse = data.message;
        this.spin_createschema = false
        //this.createTargetForm.reset();
        this.toast.success(this.createschemaresponse)
      })
    }
    else if (this.migtypeid == "28") {
      this.assessmentService.CreateSqlSchema(obj).subscribe((data: any) => {
        this.createschemaresponse = data.message;
        this.spin_createschema = false
        //this.createTargetForm.reset();
        this.toast.success(this.createschemaresponse)
      })
    }
    else {
      this.assessmentService.CreatePGSchema(obj).subscribe((data: any) => {
        this.createschemaresponse = data.message;
        this.spin_createschema = false
        // this.createTargetForm.reset();
        this.toast.success(this.createschemaresponse)
      })
    }
  }
  tgtSchemaList: any;
  selectedsrcschema: any = [];
  currentTargetConnection: string = '';
  getTgtSchema(item: string) {
    this.currentTargetConnection = item;
    const addAllOption = () => {
      this.tgtSchemaList = this.tgtSchemaList.filter((item: any) => item.schema_name !== 'ALL');
      this.tgtSchemaList.forEach((item: any) => {
        item.type = "ALL";
      });
    };

    if (this.migtypeid == "30" || this.migtypeid == "39") {
      this.assessmentService.OraSchemas(item).subscribe((data: any) => {
        this.tgtSchemaList = data;
        addAllOption();
      });
    } else if (this.migtypeid == "28") {
      this.assessmentService.getSqlSchema(item).subscribe((data: any) => {
        this.tgtSchemaList = data;
        addAllOption();
      });
    } else if (this.migtypeid == "31") {
      this.tgtSchemaList = [];
      const schData = this.targetList.find((el: any) => el.Connection_ID === this.currentTargetConnection);
      if (schData) {
        this.tgtSchemaList.push({ schemaname: schData.dbname });
      }
      addAllOption();
    } else {
      this.assessmentService.tgtSchemaSelect(item).subscribe((data: any) => {
        this.tgtSchemaList = data;
        addAllOption();
      });
    }
  }

  selectedtgtObjType: string = ""
  selectObjectType(value: any) {
    this.selectedtgtObjType = value
  }
  targetschema: any
  gettgtSchema(data: any) {
    this.targetschema = data

  }
  selectedobjctName: any
  selectObjNames(value: any) {
    this.selectedobjctName = value
  }
  dropresponse: string = ""
  spin_drop: boolean = false
  DropObjects() {
    this.spin_drop = true
    var selobjnames: any = []
    var obj: any = []
    this.selectedobjctName = this.selectedobjctName.forEach((item: any) => {
      if (item == "ALL") {
        if (this.migtypeid == "31") {
          this.ObjectNamesList.forEach((item2: any) => {
            selobjnames.push(this.dbname + "." + item2.objectName)
          })

        }
        else {
          selobjnames.push(item)
        }
      }
      else {
        if (this.migtypeid == "28") {
          selobjnames.push("'" + item.split('.')[1] + "'")
        }
        else if (this.migtypeid == "31") {
          selobjnames.push(this.dbname + "." + item)
        } else {
          selobjnames.push("'" + item + "'")

        }
      }
    })
    if (this.migtypeid == "31") {
      obj = {
        conid: this.currentTargetConnection,
        tableLis: selobjnames.toString()
      }
    }
    else {

      obj = {
        Conid: this.currentTargetConnection,
        schemaname: this.targetschema,
        objectType: this.selectedtgtObjType,
        objectName: selobjnames.toString()
      }
    }

    if (this.migtypeid == "28") {
      this.assessmentService.dropSqlSchema(obj).subscribe((data: any) => {
        this.dropresponse = data.message
        this.spin_drop = false
        this.deleteForm.reset()
        this.toast.success(this.dropresponse)
      })
    }
    else if (this.migtypeid == "31") {
      this.assessmentService.DropMySqlTables(obj).subscribe((data: any) => {
        this.dropresponse = data.message
        this.spin_drop = false
        this.deleteForm.reset()
        this.toast.success(this.dropresponse)
      })
    }
    else if (this.migtypeid == "30" || this.migtypeid == "39") {
      this.assessmentService.dropsql2oraObjects(obj).subscribe((data: any) => {
        this.dropresponse = data.message
        this.spin_drop = false
        this.deleteForm.reset()
        this.toast.success(this.dropresponse)
      })
    }
    else {
      this.assessmentService.dropschemaobjs(obj).subscribe((data: any) => {
        this.dropresponse = data.message
        this.spin_drop = false
        this.deleteForm.reset()
        this.toast.success(this.dropresponse)
      })
    }

  }
  spin_delete: boolean = false
  DeleteSchema() {
    this.spin_delete = true
    if (this.migtypeid == "30" || this.migtypeid == "39" || this.migtypeid == "28" || this.migtypeid == "31") {
      this.DropObjects()
    }
    else {
      let obj = {
        schemaname: this.targetschema === "ALL"
          ? (this.selectedsrcschema || []).join(', ')
          : this.schemaName.toString(),
        conId: this.currentTargetConnection
      }
      console.log(obj)
      this.assessmentService.DeleteSchema(obj).subscribe((data: any) => {
        if (data.message.includes("does not exist")) {
          this.spin_delete = false
          this.toast.error("DataBase/Schema Not Found")
        }
        else {
          this.spin_delete = false
          this.toast.success(data.message)
        }
      },
        (error) => {
          this.spin_delete = false
          this.toast.error(error.message);
        })
      this.deleteForm.reset();
    }
  }
  isAll: any
  objectTypesList: any;
  getObjecttypes(item: any) {
    this.selectedsrcschema = [];
    this.schemaName = [];

    const selectedSchemas = Array.isArray(item) ? item : [item];
    const allSelected = selectedSchemas.includes("ALL");

    if (allSelected) {
      this.selectedsrcschema = this.tgtSchemaList
        .filter((schemaItem: any) => schemaItem.schemaname !== "ALL")
        .map((schemaItem: any) => schemaItem.schemaname);

      this.schemaName = [...this.selectedsrcschema];
    } else {
      this.selectedsrcschema = [...selectedSchemas];
      this.schemaName = [...selectedSchemas];
    }

    this.schemaName.forEach((schema: any) => {
      let obj = {
        Conid: this.currentTargetConnection,
        schemaname: schema
      };
      if (this.migtypeid == "28") {
        this.assessmentService.getSqlObjecttypes(obj).subscribe((data: any) => {
          this.objectTypesList = data;
        });
      } else {
        this.assessmentService.getObjTypes(obj).subscribe((data: any) => {
          this.objectTypesList = data;
        });
      }
    });
  }

  dbname: any
  ObjectNamesList: any
  getObjectNames() {
    var obj: any = []
    if (this.migtypeid == "31") {
      obj = {
        conid: this.currentTargetConnection,
        dbname: this.dbname
      }
    }
    else {
      obj = {
        Conid: this.currentTargetConnection,
        schemaname: this.targetschema,
        objectType: this.selectedtgtObjType
      }
    }
    if (this.migtypeid == "28") {
      this.assessmentService.getSqlObjects(obj).subscribe((data: any) => {
        this.ObjectNamesList = data
        this.ObjectNamesList.filter((item: any) => {
          item.type = "ALL"
        })
      })
    }
    else if (this.migtypeid == "31") {
      this.assessmentService.GetMySqlTables(obj).subscribe((data: any) => {
        this.ObjectNamesList = data
        this.ObjectNamesList.filter((item: any) => {
          item.type = "ALL"
        })
      })
    }
    else {
      this.assessmentService.getObjNames(obj).subscribe((data: any) => {
        this.ObjectNamesList = data
        this.ObjectNamesList.filter((item: any) => {
          item.type = "ALL"
        })
      })
    }

  }
  getMySqlTables(value: any) {
    this.dbname = value
    var obj = {
      conid: this.currentTargetConnection,
      dbname: this.dbname
    }
    this.assessmentService.GetMySqlTables(obj).subscribe((data: any) => {
      this.ObjectNamesList = data
      this.ObjectNamesList.filter((item: any) => {
        item.type = "ALL"
        item.objectName = item.tableName
      })
    })
  }
  tabledata: any = []
  ref_spin: boolean = false
  z: any
  p1: number = 1
  piA: number = 10

  datachange1: any
  deleteResponse: any;
  onKey() {
    this.p1 = 1;
  }
  deleteTableDatas(request_id: any) {
    const obj: any = {
      projectId: this.projectId,
      requestId: request_id,
    };
    this.assessmentService.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.getreqTableData();
    });
  }
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: 'Extract_Schemas',
    };
    this.ref_spin = true
    this.assessmentService.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        for (let k = 0; k < this.tabledata.length; k++) {
          if (this.tabledata[k].status == "C") {
            this.tabledata[k].statusfull = "Completed"
          }
          else if (this.tabledata[k].status == "I") {
            this.tabledata[k].statusfull = "Initialize"
          }
          else if (this.tabledata[k].status == "P") {
            this.tabledata[k].statusfull = "Pending"
          }
          else {

          }

          if (this.tabledata[k].objecttype == "ALL") {
            this.tabledata[k].objecttype = ""
          }
        }
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList?.length; i++) {
              if (
                this.tabledata[this.z].connection_id ==
                this.ConsList[i].Connection_ID
              ) {
                this.tabledata[this.z].conname = this.ConsList[i].conname;
              }
            }
          }
        }
        else {
          this.tabledata = []
        }
      }
    });
  }
  AssessmentCommand(value: any) {
    var dbflag = ""
    this.ConsList.filter((item: any) => {
      if (item.Connection_ID == this.srcId) {
        if (item.conname.toLowerCase().includes("dump")) {
          dbflag = "False"
        }
        else {
          dbflag = "True"
        }
      }
    })
    let obj: any = {
      sourceConnectionId: this.srcId,
      projectId: this.projectId.toString(),
      dbFlag: dbflag,
      task: this.migtypeid == '48' ? "Extract_Pipelines" : "Extract_Schemas",
      jobName: "qmig-asses"

    }
  
    if (value == 'S') {
      this.getSpin_S = true;
    }
    else {
      this.getSpin_T = true;
    }
    if (value == "S") {
      this.assessmentService.AssessmentCommad(obj).subscribe((data: any) => {
        this.redisResp = data;
        this.toast.success("Schemas Extract Command Executed")
        if (value == 'S') {
          this.getSpin_S = false;
        }
        else {
          this.getSpin_T = false;
        }
      },
        error => {
          if (value == 'S') {
            this.getSpin_S = false;
          }
          else {
            this.getSpin_T = false;
          }
          this.toast.error('Something went wrong')
        })
      console.log(obj)
    }
    else {
      obj = {
        targetConnectionId: this.tgtId,
        projectId: this.projectId.toString(),
        task: "Extract_Schemas",
        jobName: "qmig-convs"
      }
      console.log(obj)
      this.assessmentService.ConversionCommad(obj).subscribe((data: any) => {
        this.redisResp = data;
        this.toast.success("Schemas Extract Command Executed")
        if (value == 'S') {
          this.getSpin_S = false;
        }
        else {
          this.getSpin_T = false;
        }
      },
        error => {
          if (value == 'S') {
            this.getSpin_S = false;
          }
          else {
            this.getSpin_T = false;
          }
          this.toast.error('Something went wrong')
        })
    }
  }
  fileResponse: any
  spin_dwld: boolean = false
  datachangeLogs: any
  page3: number = 1
  downloadFile(fileInfo: any) {
    this.assessmentService.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false

    })
  }
  iterationForLogs: any
  ExecututionFiles: any = []
  ExecututionLogs: any = []
  ref_spin_repo: boolean = false
  page4: number = 1
  //get execution reports
  filterExecutionReports() {
    this.ExecututionFiles = []
    this.ExecututionLogs = []
    this.ref_spin_repo = true
    var path = "PRJ" + this.projectId + "SRC/"
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      data.forEach((item: any) => {
        if (item.fileName.includes('_Inventory.xlsx')) {
          this.ExecututionFiles.push(item)
        }
        if (item.fileName.includes('schemas_extraction')) {
          this.ExecututionLogs.push(item)
        }
        if(item.fileName.includes('target_schema_extraction')){
          this.ExecututionLogs.push(item)
        }
      })
      this.ExecututionFiles.sort((a: any, b: any) => {
        const dateA = new Date(a.created_dt).getTime();
        const dateB = new Date(b.created_dt).getTime();
        return dateB - dateA; // For descending order
      });

      this.ExecututionLogs.sort((a: any, b: any) => {
        const dateA = new Date(a.created_dt).getTime();
        const dateB = new Date(b.created_dt).getTime();
        return dateB - dateA; // For descending order
      });
      this.ref_spin_repo = false
    })
  }
  databases: any = [
    { value: 'S', option: 'Source' },
    { value: 'T', option: 'Target' }
  ];
  migsrcTypevalue: any;
  sourcedata: boolean = false;
  targetdata:boolean = false;
  selectFilesAndDatabase(value: any) {
    this.migsrcTypevalue = value;
    console.log(value)
    if (this.migsrcTypevalue == "S") {
      this.sourcedata = true;
      this.targetdata = false;
    }else{
      this.sourcedata = false;
      this.targetdata = true;
    }

    if(this.migsrcTypevalue=='S'){
      this.clearValidators(['tgtdatabase'])
      this.setRequiredValidators(['dataBase'])
      this.updateAllControls()
 
     }else{
       this.clearValidators(['dataBase']);
       this.setRequiredValidators(['tgtdatabase'])
      this.updateAllControls();
 
     }
  }
  setRequiredValidators(controls: string[]) {
    controls.forEach(control => {
      (this.extractschemaForm.controls[control as keyof typeof this.extractschemaForm.controls] as FormControl).setValidators([Validators.required]);
    });
  }
  clearValidators(controls: string[]) {
    controls.forEach(control => {
      (this.extractschemaForm.controls[control as keyof typeof this.extractschemaForm.controls] as FormControl).clearValidators();
    });
  }
  clearAllValidators() {
    Object.keys(this.extractschemaForm.controls).forEach(controlName => {
      const control = this.extractschemaForm.get(controlName);
      control?.clearValidators();
      control?.updateValueAndValidity();
    });
  }
  updateAllControls() {
    Object.keys(this.extractschemaForm.controls).forEach(control => {
      (this.extractschemaForm.controls[control as keyof typeof this.extractschemaForm.controls] as FormControl).updateValueAndValidity();
    });
  }
}


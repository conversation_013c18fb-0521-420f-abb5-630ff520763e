import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';

@Component({
  selector: 'app-e2e-migration',
  standalone: true,
  imports: [FormsModule, ReactiveFormsModule,],
  templateUrl: './e2e-migration.component.html',
  styles: ``
})
export class E2eMigrationComponent {  

  execProjectForm: any;
  pageName:string = ''
  constructor(private FormBuilder: FormBuilder,  private toast: HotToastService, private route:ActivatedRoute) {
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit() {
    this.execProjectForm = this.FormBuilder.group({
      connectionName: ['', Validators.required],
      schema: ['', Validators.required],
      targetconnection:['',[Validators.required]],
      objectType: ['', Validators.required],
    });
  }

    //for validations
    get f() {
      return this.execProjectForm.controls;  
    }

}

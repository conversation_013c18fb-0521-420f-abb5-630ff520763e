<!-- PgCheck -->
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Execute Extension
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="executeForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Target Connection</label>
                                    <select class="form-select" formControlName="targetConnection1" #tgtCon
                                        (change)="fetchDbs(tgtCon.value,0)">
                                        <option selected value="">Select Target </option>
                                        @for(list of targetList;track list;){
                                        <option value="{{list.Connection_ID}}">{{list.conname}}</option>
                                        }
                                    </select>
                                    @if(f.targetConnection1.touched && f.targetConnection1.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(f.targetConnection1.errors.required) {Target Connection is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- Database -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Database</label>
                                    <select class="form-select" aria-label="Default select example"
                                        formControlName="dbname1">
                                        <option selected value="">Select Database</option>
                                        @for(dbs of dbList;track dbs){
                                        <option value="{{dbs.dbname}}">{{dbs.dbname}}</option>
                                        }
                                    </select>
                                    @if(f.dbname1.touched && f.dbname1.invalid){
                                    <p class="text-start text-danger mt-1">
                                        @if(f.dbname1.errors.required) {Database is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="body-header-button">
                                    <button (click)="executeExtension(executeForm.value)"
                                        [disabled]="this.executeForm.invalid" class="btn btn-upload w-100 mt-4">
                                        <span class="mdi mdi-checkbox-marked-circle-outline"></span>
                                        Execute @if(exe_spin){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Target Connection -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    PG Check
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="pgcheckForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Target Connection</label>
                                    <select class="form-select" formControlName="targetConnection2" #tgtCon1
                                        (change)="fetchDbs(tgtCon1.value,1)">
                                        <option selected value="">Select Target </option>
                                        @for(list of targetList;track list;) {
                                        <option value="{{ list.Connection_ID }}">{{list.conname }}</option>
                                        }
                                    </select>
                                    @if(ffs.targetConnection2.touched && ffs.targetConnection2.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(ffs.targetConnection2.errors.required) {targetConnection is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- Database -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Database</label>
                                    <select class="form-select" aria-label="Default select example"
                                        formControlName="dbname2" #db (change)="getSchemas(db.value)">
                                        <option selected value="">Select Database</option>
                                        @for(list of dbList2;track list;) {
                                        <option value="{{list.dbname }}">{{list.dbname}}</option>
                                        }
                                    </select>
                                    @if(ffs.dbname2.touched && ffs.dbname2.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(ffs.dbname2.errors.required) {dbname is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- Schema -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Schema</label>

                                    <ng-select [placeholder]="'Select Schema'" [items]="schemaList" [multiple]="true"
                                        (change)="getProcs(selectedObjItems)" bindLabel="schemaname"
                                        formControlName="schema" [closeOnSelect]="false" bindValue="schemaname"
                                        [(ngModel)]="selectedObjItems">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                        </ng-template>
                                    </ng-select>
                                    @if(ffs.schema.touched && ffs.schema.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(ffs.schema.errors.required) {schema is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- Procedures -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Procedures</label>
                                    <ng-select [placeholder]="'Select Procedure'" [items]="procList" [multiple]="true"
                                        bindLabel="procName" formControlName="procName" [closeOnSelect]="false"
                                        bindValue="procName" [(ngModel)]="selectedObjItems1"
                                        (change)="GetProcs(selectedObjItems1)">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" /> {{item.procName}}
                                        </ng-template>
                                    </ng-select>
                                    @if(ffs.procName.touched && ffs.procName.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if(ffs.procName.errors.required) {procName is required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 offset-md-6">
                                <div class="body-header-button">
                                    <button (click)="executePgCheck(pgcheckForm.value,0)"
                                        [disabled]="pgcheckForm.invalid" class="btn btn-upload w-100">
                                        <span class="mdi mdi-checkbox-marked-circle-outline"></span>
                                        PG Check Error @if(pgerror_spin){<app-spinner />}</button>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="body-header-button">
                                    <button (click)="executePgCheck(pgcheckForm.value,1)"
                                        [disabled]="pgcheckForm.invalid" class="btn btn-upload w-100">
                                        <span class="mdi mdi-checkbox-marked-circle-outline"></span>
                                        PG Check Warning @if(pgwarning_spin){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    PG Check Result
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="row">
                    <div class="col-sm-6 col-md-6 col-xl-6 mt-2 mb-2">
                        <div class="custom_search cs-r">
                            <span class="mdi mdi-magnify"></span>
                            <input type="text" placeholder="Search PG Check Data " aria-controls="example"
                                class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                        </div>
                    </div>
                    <div class="col-sm-2 col-md-2 col-xl-2 mt-2 mb-2 offset-md-4">
                        <button class="btn btn-sign w-100" (click)="exportexcel()">
                            <i class="mdi mdi-download "></i> Download  @if(excelSpin){<i class="fa fa-spinner fa-spin"></i>} </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>schema</th>
                                <th>Procedure</th>
                                <th>Error Message</th>
                                <th>Error Query</th>
                                <th>Error Type</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for ( pg of pgCheckResponse | searchFilter: datachangeLogs | paginate: {
                            itemsPerPage: 10, currentPage: p,id:'two' } ; track pg; let i
                            = $index) { <tr>
                                <td>{{(p*10+i+1-10)}}</td>
                                <td>{{ pg.schema }}</td>
                                <td>{{ pg.sp }}</td>
                                <td>{{ pg.error }}</td>
                                <td>{{ pg.error_query }}</td>
                                <td>{{ pg.error_type }}</td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty list of Documents</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="p = $event" id="two"></pagination-controls>
                </div>
            </div>
        </div>

        <!-- PG Check Result -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    PG Check Report
                </button>
            </h2>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <!-- <h3 class="main_h px-3 pt-3">Reports & Logs</h3> -->
                <!-- Assessment Logs -->
                <div class="qmig-card-body">
                    <div class="row">
                        <!-- Run Number -->
                        <!-- <div class="col-sm-6 col-md-3 col-xl-3">
                            <div class="row">
                                <div class="col-8 col-sm-8 col-md-8 col-xl-8 pe-1">
                                    <div class="form-group">
                                        <select class="form-select" (change)="selectIteration(iter.value)" #iter>
                                            <option selected value="">Select Run Number</option>
                                            @for( list of runnoForReports;track list;){
                                            <option value="{{ list.iteration }}">
                                                {{ list.dbschema }}
                                            </option>
                                            }
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4 col-sm-4 col-md-4 col-xl-4 mt-2 ps-1">
                                    <button class="btn btn-sync" (click)="getUpdatedRunNumber()">
                                        Refresh
                                        @if(getRunSpin){
                                        <app-spinner />
                                        }
                                    </button>
                                </div>
                            </div>
                        </div> -->
                        <!-- Hours -->
                       
                        <div class="col-md-6 col-xl-6">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search PG Check Reports" class="form-control"
                                    [(ngModel)]="datachange" (keyup)="onKey()">
                            </div>
                        </div>
                    </div>
                </div>
                <!-- downloadFile -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>File Name</th>
                                <th>Folder Name</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(validrepo of assessmentFiles |searchFilter: datachange|paginate:{
                            itemsPerPage: pi, currentPage: p2, id:'third'};
                            track validrepo;){
                            <tr>
                                <td>{{p*pi+$index+1-pi}}</td>
                                <td>{{validrepo.fileName }}</td>
                                <td>PG Check</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(validrepo)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty list of Reports</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                </div>
            </div>
        </div>

    </div>
</div>
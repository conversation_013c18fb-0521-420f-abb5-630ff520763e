import { Component } from '@angular/core';
import { TestingService } from '../../../../services/testing.service';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { SpinnerWhiteComponent } from '../../../../shared/components/spinner-white/spinner-white.component';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { ActivatedRoute } from '@angular/router';

declare let $: any;

@Component({
  selector: 'app-tc-datamigration',
  standalone: true,
  imports: [SpinnerWhiteComponent, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './tc-datamigration.component.html',
  styles: ``
})
export class TcDatamigrationComponent {

  sourcecon: any;
  connectionFlag: boolean = false;
  Selection: string = '';
  btndis: boolean = true;
  sourceData: any = [];
  targetData: any = [];
  schemaList: any;
  projectId: any;
  tgtconnection: any;
  selectedConname: any;
  conId: any;
  conName: any;
  userData: any;
  infraSelectData: any;
  tabledata: any
  z: any;
  prjSrcTgt: any = {};
  selectcheck: boolean = false;
  schemaCheck: any;
  fileResponse: any;
  spinner: boolean = false;
  searchText: string = '';
  FunctionalTestingSrcTableData: any = [];
  p: number = 1;
  isLoading: any = []
  datachange: any;
  filesData: any;
  spin_dwld: any;
  ExecututionFiles: any
  datachange4: any
  plog: any
  iterationselected: any
  connection: any;
  TestCaseForm:any;


  /*--- Project Documents Pagination   ---*/
  pageNumber: number = 1;
  pageName: string = ''

  constructor(private toast: HotToastService, private testingService: TestingService, public formBuilder: FormBuilder,
    private route: ActivatedRoute
  ) 
  {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];

  }

  threads: number = 4;

  executeschemaForm = this.formBuilder.group({
    dataBase: ['', [Validators.required]],
  })

  ngOnInit(): void {
    this.schemaCheck = "break";
    this.TestCaseForm = this.formBuilder.group({
      connectionName:['',[Validators.required]],
      testCase:['',[Validators.required]],
      targetConnection:['',[Validators.required]],
      Threads:['']
    })
    // this.TestCaseForm = this.fb.group({
    //    code_object_name: ['', [Validators.required]],
    //    tgt_test_id: ['', [Validators.required]],
    //    test_id: ['', [Validators.required]],
    //    schema: ['', [Validators.required]],
    //  });
    this.GetConsList();
    this.filterExecutionReports();
  }
  get f():any{
    return this.TestCaseForm.controls
   }

  // onKey() {
  //   this.p = 1;
  // }

  /*  Source Connection Dropdown*/
  testCaseSelection(data: any) {
    const selectedconname = this.sourceData.filter((item: any) => {
      return item.Connection_ID === data;
    });
    this.userData = [];
    this.selectedConname = data;
    this.conId = selectedconname[0]?.Connection_ID;
    this.conName = selectedconname[0]?.conname;
    this.prjSchemaList();
  }


  testCaseSelections(data: any) {
    this.tgtconnection = data;
  }


  prjSchemaList() {
    const obj = {
      projectId: this.projectId.toString(),
      ConId: this.conId,
    };
    this.testingService.PrjSchemasListSelectData(obj).subscribe((data) => {
      this.schemaList = data.Table1;
    });
  }

  /* Connection List*/
  GetConsList() {
    this.testingService.getConList(this.projectId.toString()).subscribe((data: any) => {
      const condata = data['Table1'];
      this.sourceData = condata.filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.targetData = condata.filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }

  // getProjectSrctgtconfSelect() {
  //   this.prjSrcTgt.projectId = this.projectId.toString();
  //   this.prjSrcTgt.migsrcType = 'D';
  //   this.prjSrcTgt.connectionName = 'null';
  //   this.testingService.ProjectSrctgtconfSelect(this.prjSrcTgt).subscribe((data) => {
  //     const obj = data.Table1;
  //     this.sourceData = obj.filter((item: any) => {
  //       return item.migsrctgt == 'S' && item.migsrctype == 'D';
  //     });
  //     this.targetData = obj.filter((item: any) => {
  //       return item.migsrctgt == 'T' && item.migsrctype == 'D';
  //     });
  //   });
  // }

  checkboxselect($event: any): void {
    this.selectcheck = $event.target.checked
    if (this.selectcheck == true) {
      this.schemaCheck = 'continue'
    }
    if (this.selectcheck == false) {
      this.schemaCheck = 'break'
    }
  }

  /*--- Download file   ---*/

  downloadFile(title: any) {
    this.testingService.downloadFiles(title).subscribe((blob: any) => {
      this.fileResponse = blob;
      //console.log(blob)
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = title.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
    })
  }

  /* Source Connection dropdown*/
  selectsourceconnection(value: any) {
    this.sourcecon = value
    if (this.tgtconnection != undefined) {
      if (this.sourcecon != this.tgtconnection) {
        this.connectionFlag = false
      }
      if (this.sourcecon == this.tgtconnection) {
        this.toast.error('Please Select another Connection')
      }
    }
  }

  /* File  */

  createFile(title: any) {
    let obj = {
      projectId: this.projectId.toString(),
      containerName: "qmigratorfiles" + this.projectId,
      folderName: this.projectId + "_TcDataMigration",
      filename: title
    }
    this.spin_dwld = true
    this.testingService.GetfileContent(obj).subscribe((data: any) => {
      this.fileResponse = data.message;

      const downloadLink = document.createElement('a');
      const fileName = title;

      downloadLink.href = 'data:application/octet-stream;base64,' + this.fileResponse;
      downloadLink.download = fileName;
      downloadLink.click();
      this.spin_dwld = false
    })
  }

  isload(value: any) {
    this.isLoading = []
    this.isLoading[value] = true
  }

  /* Report data*/
  filterExecutionReports() {
    var path = 'PRJ' + this.projectId + 'SRC'
    this.testingService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
      //console.log(this.ExecututionFiles)
    })
  }

}


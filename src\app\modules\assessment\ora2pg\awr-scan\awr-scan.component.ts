import { Component } from '@angular/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { AssessmentService } from '../../../../services/assessment.service';
import { deleteTableData, deleteTabledata, GetExceution } from '../../../../models/interfaces/types';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';

declare let $: any;

@Component({
  selector: 'app-awr-scan',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './awr-scan.component.html',
  styles: ``
})
export class AwrScanComponent {
   // All Variables
   i: string = '';
   z: number = 0;
  deploymentForm: any
  upload_spin: boolean = false
  fileName: string = '';
  connectionsForm: any;
  processBtn: boolean = false;
  ExecuteBtn: boolean = false;
  datachange: any;
  page2: number = 1;
  p1: number = 1;
  p3:number=1;
  piA:number=10;
  piB:number=10;
  piC:number=10;
  logfile: any = [];
  projectId: any;
  exelist: any = [];
  buttonDisable: boolean = true;
  pageNumber: number = 1;
  p: number = 1;
  
  isCheckBoxSel: boolean = false;
  getIPfromCheck: any;
  showCheckStatus: boolean = false
  allIPs: any = []
  ExecutionFiles: any=[];
  datachange1: any;
  p2: number = 1;
  ref_spin: boolean = false;
  status_spin: boolean = false;
  pageName: string = ''
  exedisable:boolean=true;
  user: any
  AwrReports:any=[];
  tabledata: any;
  //GetConList variables
  ConsList: any;
  fileResponse: any;
  downloadFiles: any;
  

  //for upload
  uploadfileSpin: boolean = false;
  selectFile: any;
  fileAdd: boolean = false;
  uploadForm = this.fb.group({
    file: ['', [Validators.required]],


  })

  
  getRunSpin: boolean = false;

  constructor(public fb: FormBuilder,
    private formBuider: FormBuilder, private assessmentService: AssessmentService, private toast: HotToastService,
    private route: ActivatedRoute) {
      this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
  }

  ngOnInit(): void {
    this.filterExecutionReports()
    this.filterExecutionReports1()
   this.getreqTableData()
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.selectFile = file
    this.fileName = event.target.files[0].name;
  }
  currentlyCheked(isChecked: any) {
    if (isChecked.target.checked) {
      this.processBtn = true;
    } else {
      this.processBtn = false;
    }
  }
  checkboxselect($event: any, value: any): void {
    this.exedisable=false;
    this.getIPfromCheck = value;
    if ($event.target.checked) {
      this.isCheckBoxSel = true
      this.allIPs.push(value)
      //console.log(this.allIPs)
    } else {
      const index = this.allIPs.indexOf(value);
      const rem = this.allIPs.splice(index, 1);
      //console.log(rem)
      //console.log(this.allIPs)
      this.isCheckBoxSel = false
    }
  }
  selectAll($event: any) {
    this.showCheckStatus = $event.target.checked
    if ($event.target.checked) {
      this.allIPs = []
      // this.dagsInfo.filter((item: any) => {
      //   if (item.isDagAvailable !== false) {
      //     item.isSelected = true
      //     this.allIPs.push(item)
      //   }
      // })
      this.isCheckBoxSel = true
    } else {
      this.allIPs = []
      // for (const element of this.dagsInfo) {
      //   element.isSelected = false
      // }
      this.isCheckBoxSel = false
    }
    //console.log(this.allIPs)
  }
  Executebtn() {
    const obj: GetExceution = {
      projectId: this.projectId.toString(),
      fileName:"'"+this.allIPs.toString()+"'",
      jobName: "qmig-asses"
    };
    this.assessmentService.GetExceution(obj).subscribe((data: any) => {
      this.exelist = data;
      this.isCheckBoxSel = false
      this.toast.success("Executed Successfully ")
      this.filterExecutionReports()
    },
    error => {
      this.filterExecutionReports()
      this.isCheckBoxSel = false
      this.toast.error('Something went wrong')
    });
  }

  filterExecutionReports() {
    this.getRunSpin = true
    var path = "PRJ" + this.projectId + "SRC/AWR_Reports/Zip_Files"
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecutionFiles = data
      this.getRunSpin = false
    })
  }

  uploadFile() {
    //console.log("uploaded")
    this.uploadfileSpin = true
    var filepath = "PRJ" + this.projectId + "SRC/AWR_Reports/Zip_Files"
    const formData: FormData = new FormData();
    formData.append('file', this.selectFile, this.selectFile.name);
    formData.append('path', filepath);
    this.assessmentService.uploadDocument(formData).subscribe(
      (response: any) => {
        this.uploadfileSpin = false
        this.fileAdd = false
        // this.getDocuments();
        this.uploadForm.controls.file.reset()
        this.fileName = ''
        this.toast.success(response.message)

        this.buttonDisable = false;

      },
      error => {
        this.uploadfileSpin = false
        this.fileAdd = false
        this.uploadForm.controls.file.reset()
        this.fileName = ''
        this.toast.error('Something went wrong')
        //this.openPopup()
        this.buttonDisable = true;

      }

    )
  }
  onKey() {
    this.p1 = 1;
    this.p = 1;
    this.p2 = 1;
  }
  filterExecutionReports1() {
    this.ref_spin = true
    var path = "PRJ" + this.projectId + "SRC/AWR_Reports/Output_Files"
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.AwrReports = data
      this.ref_spin = false
    })
  }
  getreqTableData() {
    this.status_spin = true
    const obj = {
      projectId: this.projectId,
      operationType: 'AWR_Report',
    };
   
    this.assessmentService.GetReqData(obj).subscribe((data: any) => {
      this.tabledata = data['Table1'];
      this.status_spin = false
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
       
        for (let k = 0; k < this.tabledata.length; k++) {
          if (this.tabledata[k].status == "C") {
            this.tabledata[k].statusfull = "Completed"
          }
          else if (this.tabledata[k].status == "I") {
            this.tabledata[k].statusfull = "Initialize"
          }
          else if (this.tabledata[k].status == "P") {
            this.tabledata[k].statusfull = "Pending"
          }
          else {

          }
          
          if (this.tabledata[k].objecttype == "ALL") {
            this.tabledata[k].objecttype = ""
          }
        }
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList?.length; i++) {
              if (
                this.tabledata[this.z].connection_id ==
                this.ConsList[i].Connection_ID
              ) {
                this.tabledata[this.z].conname = this.ConsList[i].conname;
              }
            }
          }
        }
        else {
          this.tabledata = []
        }
      }
    });
  }

  /*--- Download file ---*/
  
  downloadFile(title: any) {
    this.assessmentService.downloadFiles(title.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      //console.log(blob)
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = title.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
    })
  }
//Delete Table Data
deleteResponse: any;
deleteTableDatas(request_id: any) {
  const obj:deleteTableData = {
    projectId: this.projectId,
    requestId: request_id,
  };
  this.assessmentService.deleteTableData(obj).subscribe((data: deleteTabledata) => {
    //this.deleteResponse = data['Table1'];
    this.getreqTableData();
  });
}

  /*--- Delete Project Reports   ---*/

  deleteFiles(path: string) {
    this.assessmentService.deleteFile(path).subscribe({
      next: (data: any) => {
        this.filterExecutionReports();
        this.toast.success("File deleted Successfully")
      },
      error: (error) => {
        this.toast.error(error.message)
      },
    });
  }

  AWRResponseData: any = []
  AssessmentCommand() {
    let obj: any = {
      projectId: this.projectId.toString(),
      task: "AWR_Report",
      fileName:"'"+this.allIPs.toString()+"'",
      jobName:'qmig-asses'
    }
    this.assessmentService.AssessmentCommad(obj).subscribe((data: any) => {
      this.AWRResponseData = data;
      this.toast.success("AWR Command Executed")
    },
      error => {
        this.toast.error('Something went wrong')
      })
  }

}

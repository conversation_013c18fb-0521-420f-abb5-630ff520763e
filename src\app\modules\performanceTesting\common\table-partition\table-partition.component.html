<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <h3 class="main_h px-3 pt-3"> Table Partitioning </h3>
    <div class="qmig-card-body">
        <form class="form qmig-Form" [formGroup]="perfForm">
            <div class="row">
                <!--- Category  --->
                <div class="col-md-3 col-xl-3">
                    <div class="">
                        <label class="form-label d-required" for="targtetConnection"> Connection
                            Name</label>
                        <select formControlName="tgtconnection" #Myselect2 (change)="
                                    selTgtId(Myselect2.value);GetperfSchemas() " class="form-select">
                            <option value="" disabled selected>Select Connection Name</option>
                            @for(list of tgtList;track list; ){
                            <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                            }
                        </select>
                        <div class="alert">
                            @if(validate.tgtconnection.touched && validate.tgtconnection.invalid) {
                            <p class="text-start text-danger mt-1">Connection Name required
                            </p>
                            }
                        </div>
                    </div>
                </div>

            </div>
        </form>
    </div>
</div>
<div class="qmig-card mt-3 pt-3">
    <!--- Table Partition --->

    <div class="accordion qmig-accordion accordion-flush" id="accordionPanelsStayOpenExample ">
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingFour">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                    Partitioning Strategy
                </button>
            </h2>
            <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="headingFour"
                data-bs-parent="#accordionExample">

                <div class="body-main" [hidden]="!hideD">
                    <div class="row">
                        <!-- Export Button -->
                        <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3">
                            <h3 class="main_h pt-3 mt-3 ps-3">
                                Refresh
                                <button class="btn btn-sync " (click)="Refresh()">
                                    @if(PartitonTableDatass){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </h3>
                        </div>
                        <div class="col-12 col-sm-6 col-md-6">
                            <div class="custom_search mt-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange" (keyup)="onKey()">
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-3 col-lg-2 col-xl-3 pe-3 mt-3 mb-3">
                            <button class="btn btn-upload w-100" (click)="exportexcelTablePartion()">
                                <span class="mdi mdi-arrow-up-thin"></span>Export
                                @if(excelspind){<app-spinner />}
                            </button>
                        </div>

                    </div>
                    <div [hidden]="false">
                        <div class="table-responsive">
                            <table class="table table-hover qmig-table">
                                <thead>
                                    <tr>
                                        <th>S.No</th>
                                        <th>Partiton Id</th>
                                        <th>TableName</th>
                                        <th>Key Column Name</th>
                                        <th>TableSize</th>
                                        <th>RowCount</th>
                                        <th>Status</th>
                                        <th>Occurrence</th>
                                        <th>Execute</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (con of PartitonTableData |searchFilter: datachange| paginate: { itemsPerPage:
                                    10,
                                    currentPage: pages ,id:'Third'} ;track con;let i=$index)
                                    {
                                    <tr>
                                        <td>{{10*pages+i+1-10}}</td>
                                        <td>{{con.id}}</td>
                                        <td>{{con.tablename}}</td>
                                        <td [ngSwitch]="true">
                                            <div *ngSwitchCase="con.datatype === 'None' && con.rowcount < 100000"></div>
                                            <div *ngSwitchCase="con.datatype !== 'None' && con.rowcount > 100000">
                                                {{con.columnname}}</div>
                                            <div *ngSwitchDefault></div>
                                        </td>
                                        <td>{{con.tablesize}}</td>
                                        <td>{{con.rowcount}}</td>
                                        <td>
                                            <!-- <div *ngSwitchCase="con.datatype === 'None' && con.rowcount > 100000">Not Suitable For
                                    Partitions</div>
                                <div *ngSwitchCase="con.datatype !== 'None' && con.rowcount > 100000">Suitable For
                                    Partitions</div>
                                <div *ngSwitchDefault>Not Suitable For Partitions</div> -->
                                            {{con.status}}
                                        </td>
                                        <td>{{con.procedurecount}}</td>
                                        <td>
                                            <!-- *ngIf="con.datatype !== 'None' && con.rowcount > 100000 && !con.clicked && (con.status === 'Partition Already Exists' || con.status === 'Partition Created')" -->
                                            <div class="form-check">
                                                <!-- Button will be visible and clickable only if it's not clicked before and status is not "Already partition created" -->
                                                <!-- Button that triggers partition action -->
                                                @if(con.status === 'Partition Created' || con.status === 'Partition Already Exists') {
                                                <p>Completed</p>
                                                }
                                                @else if(con.status === 'Not suitable for partition') {
                                                <p></p>
                                                }

                                                @else{
                                                <button (click)="onActionClick(con.id); onActionClicks(con)"
                                                    class="btn btn-download">
                                                    <span class="mdi mdi-cloud-arrow-up" data-bs-toggle="offcanvas"
                                                        data-bs-target="#demo"></span>
                                                </button>
                                                }
                                                <!-- Message that shows after partition is completed -->

                                            </div>
                                        </td>
                                    </tr>
                                    } @empty {
                                    <tr>
                                        <td colspan="8">
                                            <p class="text-center m-0 w-100">Empty list of Partitions
                                            </p>
                                        </td>
                                    </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pages = $event" id="Third">
                            </pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingFour">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                    Partition Report Status
                </button>
            </h2>
            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour"
                data-bs-parent="#accordionExample">
                <div class="row">
                    <!-- Export Button -->
                    <div class="ps-3 col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 offset-md-6 mt-3">
                        <button class="btn btn-upload w-100" (click)="partionReportstatus()">
                            <span class="mdi mdi-arrow-up-thin"></span>Export
                            @if(excelspin5){<app-spinner />}
                        </button>
                    </div>
                    <div class="ps-3 col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 mt-3">
                        <button class="btn btn-sign w-100" (click)="getlogs()">
                            <span class="mdi mdi-arrow-up-thin"></span>Export Logs
                            @if(log_spin){<app-spinner />}
                        </button>
                    </div>
                </div>
                <div class="table-responsive mt-3" [hidden]="PerfLogPartition?.length <= 0">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>Sno</th>
                                <th>Partiton Id</th>
                                <th>Table Name</th>
                                <th>Created Date</th>
                                <th>Status</th>
                                <th> Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (con of PerfLogPartition | paginate: { itemsPerPage: 10,
                            currentPage: pageNumbers } ; track con;let i=$index) {
                            <tr>
                                <td>{{10*pages+i+1-10}}</td>
                                <td>{{con.sno}}</td>
                                <td>{{con.tablename}}</td>
                                <td>{{con.createddate}}</td>
                                <td>
                                    <div role="button">
                                        <span [ngClass]="{
                                              'text-success': con.status.toLowerCase().includes('success'),
                                              'text-danger': con.status.toLowerCase().includes('failed'),
                                              'text-dark': con.status.toLowerCase().includes('inprogress') || con.status.toLowerCase().includes('started')
                                            }">
                                            {{ con.status | titlecase }}
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <button class="btn btn-download" (click)="onClickChange(con)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="8">
                                    <p class="text-center m-0 w-100">Empty list </p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="pageNumbers = $event"></pagination-controls>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                    Partition Strategy Input
                </button>
            </h2>
            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree"
                data-bs-parent="#accordionExample">
                <div class="qmig-card-body">
                    <form [formGroup]="partitionForm">
                        <div class="row">
                            <div class="col-12 col-sm-12 col-md-4">
                                <div class="form-group d-flex">
                                    <div class="form-check me-4">
                                        <input class="form-check-input" formControlName="partitionType" type="radio"
                                            value="fullDB" id="flexRadioDefault2">
                                        <label class="form-check-label form-label" for="flexRadioDefault2">
                                            Full DB
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" formControlName="partitionType" type="radio"
                                            value="uploadFile" id="flexRadioDefault3">
                                        <label class="form-check-label form-label" for="flexRadioDefault3">
                                            File Upload
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if(partitionForm.value.partitionType === 'fullDB') {
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-3 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection"> Connection
                                        Name</label>
                                    <select formControlName="tgtcon" #Mycon (change)="
                                selectTgtForFullDB(Mycon.value) " class="form-select">
                                        <option value="" disabled selected>Select Connection Name</option>
                                        @for(list of tgtList;track list; ){
                                        <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                        }
                                    </select>
                                    <div class="alert">
                                        @if(upload.tgtcon.touched && upload.tgtcon.invalid) {
                                        <p class="text-start text-danger mt-1">Connection Name required
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-9 col-lg-4 col-xl-3 mt-4 ">
                                <div class="body-header-button">
                                    <button class="btn btn-upload w-100 "
                                        (click)="fetchAllProcs(partitionForm.value.tgtcon)"> <span
                                            class="mdi mdi-cog-play"></span> Execute Full DB
                                    </button>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-9 col-lg-4 col-xl-3 mt-4 ">
                                <span class="mt-3">
                                    <h6>@if(AllProcStatus_spin){<app-spinner />} {{AllProcStatus}}</h6>
                                </span>
                            </div>
                        </div>
                        }
                    </form>
                    @if(partitionForm.value.partitionType === 'uploadFile') {
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                            <div class="form-group" [formGroup]="uploadForm">
                                <label class="form-label d-required" for="upload">Upload File</label>
                                <div class="custom-file-upload">
                                    <input class="form-control" type="file" id="formFile" formControlName="file1"
                                        (change)="onFileSelected($event)">
                                    <div class="file-upload-mask">
                                        @if (FileNameUpload == '') {
                                        <img src="assets/images/fileUpload.png" alt="img" />
                                        <p>Drop the file here or click add file </p>
                                        <button class="btn btn-upload"> Add File </button>
                                        }
                                        <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                            <p> {{ FileNameUpload }} </p>
                                        </div>
                                    </div>
                                    @if ( validates.file1.touched && validates.file1.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (validates.file1.errors.required) { File is required }
                                    </p>
                                    }

                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 offset-md-9">
                            <button class="btn btn-upload w-100" (click)="uploadFile()"
                                [disabled]="uploadForm.invalid"><span class="mdi mdi-file-plus"></span> Execution
                                @if(uploadfileSpin){<app-spinner />}</button>
                        </div>
                    </div>
                    }
                </div>
                @if(partitionForm.value.partitionType === 'uploadFile') {
                <hr class="dash-dotted" />
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="Executionform">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                                <div class="">
                                    <label class="form-label d-required" for="targtetConnection">Upload
                                        Files</label>
                                    <select formControlName="filenamee" #ex
                                        (click)="selectFilenames(ex.value);GetPerflogFilestatus(1)" class="form-select">
                                        <option value="" selected disabled>Select File</option>
                                        @for(list of uploadedData;track list;){
                                        <option value="{{ list.fileName }}">{{ list.fileName}}</option>
                                        }
                                    </select>
                                    <div class="alert">
                                        @if(validatee.filenamee.touched && validatee.filenamee.invalid) {
                                        <p class="text-start text-danger mt-1">Connection Name required
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3">
                                <div class="">
                                    <label class="form-label d-required" for="targtetConnection"> Connection
                                        Name</label>
                                    <select formControlName="tgtconn" #Myselect3 (change)="
                                    selTgtIds(Myselect3.value)" class="form-select">
                                        <option value="" disabled selected>Select Connection Name</option>
                                        @for(list of tgtList;track list; ){
                                        <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                        }
                                    </select>
                                    <div class="alert">
                                        @if(validatee.tgtconn.touched && validatee.tgtconn.invalid) {
                                        <p class="text-start text-danger mt-1">Connection Name required
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-3 mt-4 pt-2">
                                <button type="button" #inert class="btn btn-upload w-100"
                                    (click)="TablePartitionInsert(Executionform.value)"
                                    [disabled]="Executionform.invalid"><span class="mdi mdi-cloud-sync"></span>
                                    insertion
                                    @if(commandspin){<app-spinner />}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                }
                <!-- @if(fileStatus){ -->
                <hr class="dash-dotted" />
                <div class="row">
                    <div class="col-12 col-sm-6 col-md-6">
                        <h3 class="main_h pb-3 ps-3">
                            File Status
                            <button class="btn btn-sync " (click)="RefreshFileStatus()">
                                @if(ref_spin3){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </h3>
                    </div>
                </div>
                <!-- } -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>Id</th>
                                <th>Status</th>
                                <th>File Name</th>
                                <th>Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (datas of PerflogstatusData | paginate: { itemsPerPage: 10,
                            currentPage: pageNumber } ; track datas) {
                            <tr>
                                <td>{{datas.file_id}}</td>
                                <td>{{datas.status}}</td>
                                <td>{{datas.file_name}}</td>
                                <td>{{datas.remarks}}</td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="8">
                                    <p class="text-center m-0 w-100">Empty list </p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
    <div class="offcanvas-header">
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
    </div>

    <div class="offcanvas-body" [hidden]="buttonsp">
        <!-- Confirmation message -->
        <p>Would you like to execute the PostgreSQL configuration queries before creating the partition?</p>
    </div>
    <!-- Modal Footer with Yes and No Buttons -->
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary me-1" data-bs-dismiss="offcanvas" [hidden]="buttonsp"
            (click)="SkipMethod()">Skip</button>
        <button type="button" class="btn btn-primary me-1" [hidden]="buttonsp" (click)="onYesClick()">Yes</button>
    </div>

    <!-- Form with the textarea (hidden initially) and Execute button -->
    <div class="offcanvas-body">
        <form class="form qmig-Form">
            <div class="form-group" *ngIf="showForms" [formGroup]="showForm">
                <label for="formFile" class="form-label d-required">Queries</label>
                <textarea class="form-control" formControlName="queries" id="queries" rows="10" name="queries"
                    placeholder="Queries" rows="15" cols="30"></textarea>
            </div>
            <!-- Execute Button - visible only after confirmation -->
            <div class="mt-3" *ngIf="showExecuteButton">
                <button class="btn btn-primary w-40" data-bs-dismiss="offcanvas"
                    (click)="executeQueries(showForm.value)">
                    <span class="mdi mdi-arrow-up-thin"></span>Execute
                    <!-- @if(excelspin5){<app-spinner />} -->
                </button>
            </div>
            <!-- <div class="form-group" *ngIf="showExecuteButton">
                <button class="btn btn-primary w-50" data-bs-dismiss="offcanvas" (click)="executeQueries()">Execute</button>
            </div> -->
        </form>
    </div>
</div>
<!-- <header -->
<div class="v-pageName">{{pageName}}</div>
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <!-- @if(migtypeid != "38"){
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingTest">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseTest1" aria-expanded="false" aria-controls="flush-collapse">
                        Extract Tables
                    </button>
                </h2>
                <div id="flush-collapseTest1" class="accordion-collapse collapse show" aria-labelledby="flush-headingTest"
                    data-bs-parent="#accordionFlushExample">
                    <div class="qmig-card">
                        <div class="qmig-card-body">
                            <form class="form qmig-Form" [formGroup]="extractForm">
                                <div class="row">
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="targtetConnection">Source Connection
                                                Name</label>
                                            <select class="form-select" #org (change)="getSchemasList1(org.value)"
                                                formControlName="sourceConnection">
                                                <option disabled>Select a Source Connection</option>
                                                @for ( list of srcConsList; track list) {
                                                <option value="{{ list.Connection_ID }}">{{ list.conname }}</option>
                                                }
                                            </select>
                                            @if ( fs.sourceConnection.touched && fs.sourceConnection.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fs.sourceConnection.errors.required) {Source Connection is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                        <div class="form-group">
                                            <label class="form-label d-required" for="targtetConnection"> {{schemalable}}
                                                Name</label>
                                            <select class="form-select" #org (change)="getSchemasList1(org.value)"
                                                formControlName="schema">
                                                <option disabled>Select {{schemalable}} </option>
                                                @for ( list of schemaList1; track list) {
                                                <option value="{{ list.schema_name }}">{{ list.schema_name }}</option>
                                                }
                                            </select>
                                            @if ( fs.schema.touched && fs.schema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (fs.schema.errors.required) {{{schemalable}} is Required }
                                            </p>
                                            }
                                        </div>
                                    </div>
                                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-4 ">
                                        <div class="body-header-button">
                                            <button class="btn btn-upload w-100 " (click)="extractValue(extractForm.value)"
                                                [disabled]="extractForm.invalid"> <span class="mdi mdi-cog-play"></span>
                                                Extract
                                                Tables
                                                @if(spin){<app-spinner />}</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            } -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    E2E Migration
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="execProjectForm">
                        <div class="row">
                            <!-- source connection list -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group" form>
                                    <label class="form-label d-required" for="Schema">Source Connection</label>
                                    <select class="form-select" formControlName="connectionName" #srccon
                                        (change)="getSchemasList(srccon.value,'0');selectedfiletype(srccon.value)">
                                        <option>Select Connection Name</option>
                                        @for(ConList of srcConsList;track ConList; ){
                                        <option value="{{ConList.Connection_ID}}">{{ConList.conname}}</option>
                                        }
                                    </select>
                                    @if ( f.connectionName.touched && f.connectionName.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.connectionName.errors.required) {Source Connection is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- Source Schema list -->

                            @if(this.migtypeid == '49'){

                            }@else{
                            @if(dataloadtype!='PG_Dump'){
                            @if(this.migtypeid == '31'|| this.migtypeid == '28'){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Source {{schemalable}}</label>
                                    <ng-select groupBy="type" [selectableGroup]="true"
                                        (change)="onItemSelect(selectedItems);validationstable()"
                                        formControlName="schema" [items]="srcschemaList" [multiple]="true"
                                        bindLabel="schemaname" [closeOnSelect]="false" bindValue="schemaname"
                                        [(ngModel)]="selectedItems">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                        </ng-template>
                                    </ng-select>
                                    @if ( f.schema.touched && f.schema.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.schema.errors.required) {Source {{schemalable}} is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            }@else {
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Source {{schemalable}}</label>
                                    <ng-select groupBy="type" [selectableGroup]="true"
                                        (change)="onItemSelect(selectedItems);validationstable()"
                                        formControlName="schema" [items]="srcschemaList" [multiple]="true"
                                        bindLabel="schema_name" [closeOnSelect]="false" bindValue="schema_name"
                                        [(ngModel)]="selectedItems">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" value={{item.schema_name}} />
                                            {{item.schema_name}}
                                        </ng-template>
                                    </ng-select>
                                    @if ( f.schema.touched && f.schema.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.schema.errors.required) {Source {{schemalable}} is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            }
                            }
                            }
                            <!-- Data Load Type list -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Data Load Type</label>
                                    <select class="form-select" formControlName="dataload" #dl
                                        (change)="fetch(dl.value)">
                                        <option>Select Data Load Type</option>
                                        @if(migtypeid == '31'){
                                        <option value="Database">ETL</option>
                                        }@else{
                                        <option value="Database">Database</option>
                                        }
                                        @if(migtypeid == '49'){

                                        }@else{
                                        <option value="File">File</option>
                                        }
                                        @if(migtypeid=="38")
                                        {
                                        <option value="PG_Dump">PG Dump</option>
                                        }
                                        @else if(migtypeid=="30"){
                                        <option value="Sql_Loader">Sql Loader</option>
                                        }
                                        @else if(migtypeid=="31"){
                                        <option value="Data_Dumper">Data Dumper</option>
                                        }
                                        @else if(migtypeid=="49"){
                                        <option value="Default">Default</option>
                                        }
                                    </select>
                                    <!-- <select class="form-select" formControlName="dataload" #dl
                                            (change)="fetch(dl.value)">
                                            <option>Select Data Load Type</option>
                                            <option value="Default">Default</option>
                                            @if(migtypeid=="31"){
                                            <option value="Mydumper">My Dumper</option>
                                            <option value="ETL">ETL</option>
                                            }
                                            @else if(migtypeid=="38")
                                            {
                                            <option value="pg_dump">PG Dump</option>
                                            }
                                            @else if(this.migtypeid=="30"){
                                            <option value="Sql_Loader">
                                                Sql Loader
                                            </option>
                                            }
                                            @else if(this.migtypeid=="28"){
                                            <option value="Bulk_Copy">Bulk Copy</option>
                                            }
                                            @else if(this.migtypeid=="20"){
                                            <option value="Ora2PG">Ora2PG</option>
                                            }
                                        </select> -->
                                    @if ( f.dataload.touched && f.dataload.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.dataload.errors.required) {Data Load is Required }
                                    </p>
                                    }
                                </div>
                            </div>


                            <!-- Dr Connection List -->
                            @if(this.migtypeid == '49'){

                            }@else{
                            @if (dataloadtype!='PG_Dump' && dataloadtype!='Data_Dumper') {
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group" form>
                                    <label class="form-label d-required" for="Schema">Dr Connection</label>
                                    <select class="form-select" formControlName="drconnectionName" #srccon1 (change)="selectedtgttype1(srccon1.value)">
                                        <option>Select Dr Connection Name</option>
                                        @for(ConList of tgtConsList;track ConList; ){
                                        <option value="{{ConList.Connection_ID}}">{{ConList.conname}}</option>
                                        }
                                    </select>
                                    @if ( f.drconnectionName.touched && f.drconnectionName.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.drconnectionName.errors.required) {Dr Connection is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            }
                            }
                            <!-- target connection list -->
                            @if( dataloadtype !='File'){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="Schema">Target Connection</label>
                                    <select class="form-select" formControlName="targetconnection" #tgtcon
                                        (change)="getSchemasList(tgtcon.value,'1');selectedtgttype(tgtcon.value)">
                                        <option>Select Target Connection Name</option>
                                        @for(tgtList of tgtConsList;track tgtList; ){
                                        <option value="{{tgtList.Connection_ID}}">{{tgtList.conname}}</option>
                                        }
                                    </select>
                                    @if ( f.targetconnection.touched && f.targetconnection.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.targetconnection.errors.required) {Target Connection is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            }

                            <!-- target schemalist -->
                            @if(this.migtypeid == '49'){

                            }@else{
                            @if(isAll!="ALL"){
                            @if(showTargetSchemaAndTables){
                            @if(dataloadtype!='PG_Dump' && dataloadtype!="File"){
                            <!-- @if(migtypeid == '31'|| migtypeid == '28'){ -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="Schema">Target {{schemalable}}</label>
                                    <select class="form-select" formControlName="tgtschema">
                                        <option>Select Target {{schemalable}}</option>
                                        @for(tgtsch of tgtschemaList;track tgtsch; ){
                                        @if(migtypeid == '31'|| migtypeid == '28'){
                                        <option value="{{tgtsch.schemaname}}">{{tgtsch.schemaname}}</option>
                                        }@else {
                                        <option value="{{tgtsch.schema_name}}">{{tgtsch.schema_name}}</option>
                                        }
                                        }
                                    </select>
                                    <!-- @if ( f.tgtschema.touched && f.tgtschema.invalid) {
                                            <p class="text-start text-danger mt-1">
                                                @if (f.tgtschema.errors.required) {Target {{schemalable}} is Required }
                                            </p>
                                            } -->
                                </div>
                            </div>
                            }
                            }
                            }
                            }

                           
                            <!-- Table List -->
                            @if(this.migtypeid == '49'){

                            }@else{
                            @if(isAll!="ALL"){
                            @if(showTargetSchemaAndTables){
                            @if(dataloadtype!='Data_Dumper'){
                            @if(this.migtypeid=='31'){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Tables </label>
                                    <ng-select [placeholder]="'Select Tables For Data Migration'"
                                        formControlName="tables" groupBy="type" [selectableGroup]="true"
                                        [items]="tablesList" [multiple]="true" bindLabel="tableName"
                                        [closeOnSelect]="false" bindValue="tableName"
                                        (change)="tablesSelect(selectedTable)" [(ngModel)]="selectedTable">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" value={{item.tableName}} />
                                            {{item.tableName}}
                                        </ng-template>
                                        <ng-template ng-multi-label-tmp let-items="items">
                                            @for(item of slicedData(items);track item; ){
                                            <div class="ng-value">
                                                {{item.tableName}}
                                            </div>
                                            }
                                            @if(items.length > 1)
                                            {
                                            <div class="ng-value">
                                                <span class="ng-value-label">{{items.length - 1}} more...</span>
                                            </div>
                                            }
                                        </ng-template>
                                    </ng-select>
                                    <div class="alert">
                                        @if ( f.tables.touched && f.tables.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.tables.errors?.['required']) { Tables required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            }
                            @else{
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Tables </label>
                                    <ng-select [placeholder]="'Select Tables For Data Migration'"
                                        formControlName="tables" groupBy="type" [selectableGroup]="true"
                                        [items]="tablesList" [multiple]="true" bindLabel="table_name"
                                        [closeOnSelect]="false" bindValue="table_name"
                                        (change)="tablesSelect(selectedTable)" [(ngModel)]="selectedTable">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" value={{item.table_name}} />
                                            {{item.table_name}}
                                        </ng-template>
                                        <ng-template ng-multi-label-tmp let-items="items">
                                            @for(item of slicedData(items);track item; ){
                                            <div class="ng-value">
                                                {{item.table_name}}
                                            </div>
                                            }
                                            @if(items.length > 1)
                                            {
                                            <div class="ng-value">
                                                <span class="ng-value-label">{{items.length - 1}} more...</span>
                                            </div>
                                            }
                                        </ng-template>
                                    </ng-select>
                                    <div class="alert">
                                        @if ( f.tables.touched && f.tables.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.tables.errors?.['required']) { Tables required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>}
                            }
                            }
                            }
                            }

                            <!-- operation -->
                            @if( dataloadtype!="Mydumper" ){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="name">Operation </label>
                                    <select class="form-select" formControlName="operation">
                                        <option>Select Load Operation</option>
                                        @if(this.migtypeid== '49'){
                                        <option value="scan">Scan</option>
                                        <option value="live">Live</option>
                                        <option value="liveonly">Live Only</option>
                                        }@else{
                                        @for(list of configData;track list; ){
                                        <option value="{{list.configvalue}}">{{list.configtype}}</option>
                                        }
                                        }
                                    </select>
                                    <div class="alert">
                                        @if ( f.operation.touched && f.operation.invalid) {
                                        <p class="text-start text-danger mt-1">
                                            @if (f.operation.errors?.['required']) { operation is required }
                                        </p>
                                        }
                                    </div>
                                </div>
                            </div>
                            }
                            @if(this.migtypeid == '49'){

                            }@else{
                            @if( dataloadtype!="Mydumper" && dataloadtype!='PG_Dump'){
                            @if(execProjectForm.value.operation == "E2E_Data_Load"){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">CDC Load Type</label>
                                    <select class="form-select" formControlName="cdcdataload">
                                        <option>Select CDC Load Type</option>
                                        <option value="Database">Database</option>
                                        <option value="File">File</option>

                                    </select>

                                    @if ( f.cdcdataload.touched && f.cdcdataload.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.cdcdataload.errors.required) {CDC Load is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            }
                            }
                            }
                            <!-- FileName -->
                            @if(this.migtypeid == '49'){

                            }@else{
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Config File Name</label>
                                    <input type="text" class="form-control" formControlName="confiName"
                                        maxlength="15" />
                                    @if ( f.confiName.touched && f.confiName.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.confiName.errors.required) {Filename is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            }
                            <!-- check details button -->

                        </div>
                        <div class="row">
                            <div class="col-12 col-sm-8 col-md-8 col-lg-8 col-xl-9">
                            </div>
                             <!-- Check Details -->
                             <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3 mt-cmd-3">
                                <div class="form-group mt-1">
                                    <div class="text-right">
                                        <button class="btn btn-upload w-100"
                                            (click)="openModal(this.execProjectForm.value)"
                                            data-bs-toggle="offcanvas" data-bs-target="#demo"
                                           > <span
                                                class="mdi mdi-checkbox-marked-circle-outline"
                                                aria-hidden="true"></span>Check
                                            Details</button>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="col-12 col-sm-4 col-md-4 col-lg-4 col-xl-3 offset-md-6">
                                <button [disabled]="execProjectForm.invalid" class="btn btn-upload w-100 mt-3"
                                    (click)="TriggerE2EMigration(execProjectForm.value)">
                                    Execute </button>
                            </div> -->
                        </div>
                    </form>
                </div>
            </div>
        </div>



        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest_2" aria-expanded="false" aria-controls="flush-collapse">
                    E2E Migration Status
                </button>
            </h2>
            <div id="flush-collapseTest_2" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <!--- codeexcration List --->
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-3 col-md-2 col-lg-2 mt-3 ">
                            <div class="form-group">
                                <select class="form-select" #dc (change)="selectOp(dc.value)">
                                    <option>Select Operation</option>
                                    <option value="Extraction">Extraction</option>
                                    <option value="Conversion">Conversion</option>
                                    <option value="Datamigration">Data Migration</option>

                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-3 col-md-2 mt-3 " >
                            <div class="form-group" [hidden]="!dm_show">
                                <!-- <label class="form-label d-required" for="targtetConnection">Source
                                    Connection Name</label> -->
                                <select class="form-select" #srcFile (change)="fetchSourceConfigFiles(srcFile.value)">
                                    <option>Select a Source Connection</option>
                                    @for ( src of srcConsList; track src) {
                                    <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-3 col-md-2 mt-3" >
                            <div class="form-group" [hidden]="!dm_show">
                                <!-- <label class="form-label" for="targtetConnection">Target Connection Name</label> -->
                                <select class="form-select" #tgtFile (change)="fetchTargetConfigFiles(tgtFile.value)">
                                    <option>Select Target Connection</option>
                                    @for(tgt of tgtConsList;track tgt; ){
                                    <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-3 col-md-6 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                            <button class="btn btn-sync" (click)="getreqTableData();fetchConfigFiles1()">
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                        <!-- <div class="col-md-2"></div>
                        <div class="col-md-1">
                            <h3 class="d-flex justify-content-end align-items-center gap-2 my-3 me-3">
                                <button class="btn btn-syncc" (click)="getreqTableData();fetchConfigFiles1();">
                                    @if(ref_spin){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
                                </button>
                            </h3>
                        </div> -->
                        <!-- <div class="col-md-6">
                            <div class="custom_searchh cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                        </div> -->
                    </div>

                    <!-- Code Extraction Status Table -->
                    <div class="table-responsive" [hidden]="dm_show">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>Run No</th>
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>{{schemalable}} Name</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(con of tabledata| searchFilter: datachange1| paginate:{ itemsPerPage: piA,
                                currentPage: page2,
                                id:'first'};
                                track con;) {
                                <tr>
                                    <!-- <td>{{page2*piA+$index+1-piA}}</td> -->
                                    <td>{{con.iteration_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_name }}</td>
                                    <td>{{ con.schema_name }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        {{con.status =='Error'?con.error:con.status}}
                                    </td>

                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>

                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="12">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination" [hidden]="dm_show">
                        <pagination-controls (pageChange)="page2 = $event" id="first"></pagination-controls>
                    </div>

                    <!-- for download file -->
                    <div class="table-responsive" [hidden]="!dm_show">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>File Name</th>
                                    <th>Created Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for(docs of confFiles |searchFilter: datachange1|paginate:{
                                itemsPerPage: 10, currentPage: p, id:'third'};
                                track docs){
                                <tr>
                                    <td>{{ p*10+$index+1-10 }}</td>
                                    <td>{{docs.fileName }}</td>
                                    <td>{{docs.created_dt}}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(docs)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                        <!-- <button (click)="toggleSpinner(docs.fileName);deleteAirflowFiles(docs.fileName)"
                                                class="btn btn-delete">
                                                <span class="mdi mdi-delete btn-icon-prepend"></span>
                                            </button> -->
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <!-- pagination -->
                    <div class="custom_pagination" [hidden]="!dm_show">
                        <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h3 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false" aria-controls="flush-collapseThree">
                    Execution Logs
                </button>
            </h3>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        @if(this.migtypeid=='49'){
                        <div class="col-12 col-sm-3 col-md-3 "></div>
                        }@else{
                        <div class="col-12 col-sm-3 col-md-3 mt-3">
                            <div class="form-group">
                                <select class="form-select" #code (change)="selectIterForLogs(code.value)">
                                    <option selected value="">
                                        Select Run Number
                                    </option>
                                    @for( list of runnoForReports;track list;){
                                    <option value="{{ list.iteration}}">{{list.iteration}}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        }
                        @if(this.migtypeid=='49'){
                        <div class="col-12 col-sm-3 col-md-3"></div>
                        }@else{
                        <div class="col-12 col-sm-3 col-md-3 mt-3">
                            <div class="form-group">
                                <select class="form-select" #exelog (change)="exeOp(exelog.value)">
                                    <option>Select Operation</option>
                                    @for( list of exeOperations;track list;){
                                    <option value="{{ list.value}}">{{list.option}}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        }
                        <div class="col-12 col-sm-6 col-md-6 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="filterExecutionReports();filterExecutionReports1()">
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                        <!-- <div class="col-sm-6 col-md-6 col-xl-8">
                            <div class="custom_search cs-r">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                        </div> -->
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(logs of ExecututionFiles |searchFilter:
                            datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: page3, id:'Four'};
                            track logs){
                            <tr>
                                <td>{{ page3*10+$index+1-10 }}</td>
                                <td>{{logs.fileName }}</td>
                                <td>{{logs.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(logs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100"> {{ operationSelected ? 'No logs for the selected operation' :'Please select an run number & operation.' }}</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page3 = $event" id="Four">
                    </pagination-controls>
                </div>
            </div>
        </div>
        <!-- Reports -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne1" aria-expanded="false" aria-controls="flush-collapseOne">
                    Reports
                </button>
            </h2>
            <div id="flush-collapseOne1" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <!--- Reports List --->
                <div class="accordion-body">
                    <div class="row">
                        <div class="row">
                            <div class="col-12 col-sm-2 col-md-2 col-lg-2 col-xl-2"
                                [hidden]="this.repoOper =='Data_Migration'">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Run
                                        No</label>
                                    <select class="form-select" #iter (change)="SelectIteration(iter.value)">
                                        <option selected value="">Select Run No</option>
                                        @for( list of runnoForReports;track list;){
                                        <option value="{{ list.iteration}}">{{list.iteration}}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-2 col-md-2 col-lg-2 col-xl-2">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Operation</label>
                                    <select class="form-select" #oprep (change)="selectReportOperation(oprep.value)">
                                        <option selected value="">Select Operation</option>
                                        @for(operation of reportOperations;track operation; ){
                                        <option value="{{ operation.value }}"> {{ operation.option }}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>
                           
                            @if(this.repoOper == "Data_Migration"){
                            <div class="col-12 col-sm-2 col-md-2 col-lg-2 col-xl-2">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Source
                                        Connection</label>
                                    <select class="form-select" #srcFile21 (change)="selectSrc(srcFile21.value)">
                                        <option>Select a Source Connection</option>
                                        @for ( srccf of srcConsList; track srccf) {
                                        <option value="{{ srccf.Connection_ID }}">{{ srccf.conname }}
                                        </option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-2 col-md-2 col-lg-2 col-xl-2">
                                <div class="form-group">
                                    <label class="form-label d-required" for="targtetConnection">Target Connection
                                    </label>
                                    <select class="form-select" #tgtFile22 (change)="selectTgt(tgtFile22.value)">
                                        <option>Select Target Connection</option>
                                        @for(tgt of tgtConsList;track tgt; ){
                                        <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            }
                            <div class="col-12 col-sm-4 col-md-4 d-flex">
                                <div class="custom_search cs-r my-3 me-3">
                                    <span class="mdi mdi-magnify"></span>
                                    <input type="text" placeholder="Search  Reports" class="form-control"
                                    [(ngModel)]="searchText1" (keyup)="onKey()">
                                </div>
                                <button class="btn btn-sync" (click)="fetchExtractionFiles();fetchStorageObjectsFiles()">
                                    @if(ref_spin){
                                    <app-spinner />
                                    }@else{
                                    <span class="mdi mdi-refresh"></span>
                                    }
    
                                </button>
                            </div>
                            <!-- <div class="col-12 col-sm-2 col-md-2 col-lg-2 col-xl-2 custom_search  mt-4">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search  Reports" class="form-control"
                                    [(ngModel)]="searchText1" (keyup)="onKey()">
                            </div> -->
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.NO</th>
                                    <th>File Name</th>
                                    <th>Created Date</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (Reports1 of reportFiles | searchFilter: searchText1 | paginate: {
                                itemsPerPage: pi1,
                                currentPage: pageNumber1 , id:'second'} ; track Reports1; let i = $index) {
                                <tr>
                                    <td>{{pageNumber1*pi+i+1-pi}}</td>
                                    <td>{{Reports1.fileName }}</td>
                                    <td>{{Reports1.created_dt}}</td>
                                    <td>
                                        <button class="btn btn-download" (click)="downloadFile(Reports1)">
                                            <span
                                                class="mdi mdi-cloud-download-outline"></span>@if(spinDownload){<app-spinner />}
                                        </button>
                                        <button class="btn btn-delete" (click)="deleteFiles(Reports1.filePath)">
                                            <span class="mdi mdi-delete"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100"> {{ operationSelectedforreports ? 'No files for the selected operation' :'Please select an run number & operation.' }}</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber1 = $event" id="second"></pagination-controls>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h"> E2E Migration Details</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <!--Connection Name--->
        <div class="offcanvas-body">
            <form class="form qmig-Form checkForm">
                <div class="form-group">
                    <label class="form-label d-required" for="name">Source Connection Name</label>
                    : &nbsp;{{ conName }}
                </div>
                 <!--Schema Name-->
                 <div class="form-group">
                    <label class="form-label d-required" for="name">Source Schema Name</label>
                    : &nbsp;{{ schemaName.toString() || selectedsrcschema.toString() }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Data Load Type</label>
                    : &nbsp;{{ dataloadtype }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Dr Connection Name</label>
                    : &nbsp;{{ conName2 }}
                </div>
                <div class="form-group">
                    <label class="form-label" for="name"> Target Connection Name</label>
                    : &nbsp;{{ conName1 }}
                </div>
                 <!--Schema Name-->
                 <div class="form-group">
                    <label class="form-label " for="name">Target Schema Name</label>
                    : &nbsp;{{ execProjectForm.value.tgtschema }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Tables</label>
                    : &nbsp;{{ selectedTable }}
                </div>
                 <!--Operation-->
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Operation</label>
                    : &nbsp;{{ execProjectForm.value.operation }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Config File Name</label>
                    : &nbsp;{{ execProjectForm.value.confiName }}
                </div>
                <!--Execute-->
                <div class="form-group">
                    <div class="body-header-button">
                        <!-- projectConRunTblInserts(codeextractionForm.value, false)" -->
                        <button class="btn btn-upload w-100 mt-3"
                        (click)="TriggerE2EMigration(execProjectForm.value)">
                        Execute </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { Title } from '@angular/platform-browser';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { CodeMigrationService } from '../../../../services/codeMigration.service';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgxPaginationModule } from 'ngx-pagination';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-module-agent',
  standalone: true,
  imports: [ConfirmDialogComponent,ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, SearchFilterPipe, NgxPaginationModule],
  templateUrl: './module-agent.component.html',
  styles: ``
})
export class ModuleAgentComponent {
  
  @ViewChild(ConfirmDialogComponent) confirmDialog: ConfirmDialogComponent | undefined;
  objectValues:any = Object.values;

  pageName: string = '';
  projectId: string = '';
  agentForm = this.formBuilder.group({
    schema: ['', [Validators.required]],
    objectType: ['', [Validators.required]],
    objectName: ['', [Validators.required]]
  });
  schemaList: any[] = [];
  objectTypeList: any[] = [];
  objectNamesList: any[] = [];

  triggerSpin: boolean = false;
  migrationName: any;
  stage1MetaData: any;

  stage1Schema: string = '';
  stage1ObjectType: string = '';
  stage1ObjectName: string = '';

  constructor(
    private readonly titleService: Title,
    private readonly toast: HotToastService,
    private readonly route: ActivatedRoute,
    public readonly formBuilder: FormBuilder,
    private readonly APIService: CodeMigrationService,
    private http: HttpClient,
  ) {
    this.pageName = this.route.snapshot.data['name'];
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.migrationName = localStorage.getItem("migrationType");
    this.stage1MetaData = JSON.parse((localStorage.getItem('stage1MetaData') as string)) || [];
    this.agentForm.patchValue({
      schema: this.stage1MetaData[0] || '',
      objectType: this.stage1MetaData[2] || '',
      objectName: this.stage1MetaData[1] || ''
    });
    this.getAgentTargetStatments(this.stage1MetaData[0] || '', this.stage1MetaData[2] || '', this.stage1MetaData[1] || '');
    this.stage1ObjectType = this.stage1MetaData[2] || '';
    this.stage1ObjectName = this.stage1MetaData[1] || '';
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.getSchemaList();
    this.getModuleStatus();
    if ( this.f.schema.value ) {
      this.getObjectTypes(this.f.schema.value);
    }

    if ( this.stage1ObjectType ) {
      this.getObjectNames(this.f.schema.value, this.stage1ObjectType);
    }
  }

  get f(): any {
    return this.agentForm.controls;
  }

  getSchemaList() {
    const body = {
      process_type: 'qmigrator',
      migration_name: this.migrationName
    };

    this.APIService.getModuleSchemas(body).subscribe(
      (data) => {
        this.schemaList = data.schemas;
      },
      (error) => {
        this.toast.error('Failed to fetch schema list');
      }
    );
  }

  getObjectTypes(schema: string) {
    const body = {
      process_type: 'qmigrator',
      migration_name: this.migrationName,
      schema_name : schema
    };

    this.APIService.getModuleObjectTypes(body).subscribe(
      (data) => {
        this.objectTypeList = data.object_types;
      },
      (error) => {
        this.toast.error('Failed to fetch object types');
      }
    );
  }

  getObjectNames(schema: string, objectType: string) {
    const body = {
      process_type: 'qmigrator',
      migration_name: this.migrationName,
      schema_name : schema,
      object_type : objectType
    };

    this.APIService.getModuleObjectNames(body).subscribe(
      (data) => {
        this.objectNamesList = data.object_names;
      },
      (error) => {
        this.toast.error('Failed to fetch object names');
      }
    );
  }

  triggerAgent() {
    if (this.agentForm.invalid) {
      this.toast.error('Please fill all required fields');
      return;
    }

    this.triggerSpin = true;

    const formData:any = {
      process_type : 'qmigrator',
      migration_name : this.migrationName,
      schema_name : this.f.schema.value,
      objecttype : this.f.objectType.value,
      object_name : this.f.objectName.value,
      cloud_category : 'local'
    };

    const formDataObj = new FormData();
    for (const key in formData) {
      formDataObj.append(key, formData[key]);
    }

    this.APIService.triggerAgent(formDataObj).subscribe(
      (response) => {
        this.toast.success('Agent triggered successfully');
        this.triggerSpin = false;
        this.agentForm.reset();
        localStorage.removeItem('stage1MetaData');
      },
      (error) => {
        this.toast.error('Failed to trigger agent');
        this.triggerSpin = false;
        this.agentForm.reset();
      }
    );
  }

  // Agent Status 
  reloadSpin: boolean = false;
  dataChange: any
  page: number = 1
  p: number = 1

  onKey() {
    this.p = 1;
  }
  executionLogs: any[] = [];
  getModuleStatus() {
    this.reloadSpin = true;
    this.APIService.getModuleStatus().subscribe(
      (data) => {
        // Handle the status data as needed
        this.executionLogs = data.requests;
        this.reloadSpin = false;
      },
      (error) => {
        this.toast.error('Failed to fetch module status');
        this.reloadSpin = false;
      }
    );
  }


  downloadFile() {      
    const formData = {
      migration_name : this.migrationName,
      schema_name : this.f.schema.value,
      object_type : this.f.objectType.value,
      object_name : this.f.objectName.value,
      cloud_category : 'Cloud'
    };

    this.APIService.downloadFile(formData).subscribe((response: HttpResponse<Blob>) => {
      const contentDisposition = response.headers.get('Content-Disposition');
      console.log('Content-Disposition:', contentDisposition);

      let filename = 'downloaded-file';
      const match = contentDisposition?.match(/filename="?([^"]+)"?/);
      if (match) {
        filename = match[1];
      }

      const url = window.URL.createObjectURL(response.body!);
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = filename;
      downloadLink.click();
      window.URL.revokeObjectURL(url);
    });

  }

  
  TargetStatmentList: any;
  getRunSpin: boolean = false
  getAgentTargetStatments(ss:string, ot:string, on:string) {
    if (!ss || !ot || !on) {
      return;
    }
    const body = {
      process_type: 'qmigrator',
      migration_name: this.migrationName,
      schema_name: this.f.schema.value,
      object_type: this.f.objectType.value,
      object_name: this.f.objectName.value
    }
    this.getRunSpin = true;

    this.APIService.getTargetStatements(body).subscribe((data: any) => {
      this.TargetStatmentList = data.target_statements;
      this.getRunSpin = false;      
    });
  }

  selectedAgentTGTID: string = ''
  agentLogs: any[] = [];
  agentRawLogs:any
  getAgentDeployFullData(ID: string) {
   const body = {
    target_statement_number : ID,
    process_type: 'qmigrator',
    migration_name: this.migrationName,
    schema_name: this.f.schema.value,
    object_type: this.f.objectType.value,
    object_name: this.f.objectName.value
   }
    this.selectedAgentTGTID = ID
    this.APIService.getStatements(body).subscribe((data: any) => {
      this.agentRawLogs = data
      this.agentLogs = Object.values(data.attempts) || data.attempts
      console.log('Agent Logs:', this.agentLogs);
    })
  }


  saveAgentLogs(mi:string, rc:string, rn:string, um:string) {
    const body = {
      module_id: mi,
      is_reviewed: true,
      is_merged: true,
      reviewer_comments: rc || '',
      reviewer_name: rn || '',
      updated_module: um ,
    }
    console.log('Saving Agent Logs:', body);
    if (!body.module_id || !body.updated_module) {
      this.toast.error('Please fill all required fields');
      return;
    }

    this.APIService.saveAgentLogs(body).subscribe((response:any) => {
      if (response.status === 'success') {
        this.toast.success('Agent logs saved successfully');
      } else {
        this.toast.error('Failed to save agent logs');
      }
    }, (error) => {
      this.toast.error('Error saving agent logs');
    });

  }

     // Called once confirmation is done
  onCheckboxChange(event: Event) {
  const input = event.target as HTMLInputElement;
  const isChecked = input.checked;
  
    if (isChecked) {
      // Ask for confirmation
      if (this.confirmDialog) {
        this.confirmDialog.openDialog();
        this.isManualDeployed = true;
      }
    } else {
      // Allow unchecking directly
      this.isManualDeployed = false;

    }
  }

  isManualDeployed:boolean = false
  
  handleConfirm(isConfirmed: boolean) {
    if (isConfirmed) {
      this.isManualDeployed = true
    } else {      
      this.isManualDeployed = false
      this.agentLogs.forEach((module:any) => {
        console.log(module.modules)
        //module.is_reviewed = false;
      });
    }
  }

  // Move to Registry
  moveToRegistry(id: number, rn:string, cm:string) {   
    const body = {
      module_id: id,
      source_selection: 'your_source_selection',
      cloud_category: 'Cloud',
      reviewer_name: rn,
      comments: 'Moved to registry' + '\n' + 'Below are the reviewer comments:' + cm,
    };

    this.APIService.moveToRegistry(body).subscribe(
      (response) => {
        this.toast.success('Module moved to registry successfully');
        this.getModuleStatus(); // Refresh the module status after moving to registry
      },
      (error) => {
        this.toast.error('Failed to move module to registry');
      }
    );
  }

  // Merge to QBook
  mergeToQBook(id: number, rn:string, cm:string) {
    const body = {
      module_id: id,
      cloud_category: 'Cloud',
      reviewer_name: rn,
      comments: 'Merged to QBook' + '\n' + 'Below are the reviewer comments:' + cm,
    };
    this.APIService.moveToRegistry(body).subscribe(
      (response) => {
        this.toast.success('Module merged to QBook successfully');
        this.getModuleStatus(); // Refresh the module status after merging
      },
      (error) => {
        this.toast.error('Failed to merge module to QBook');
      }
    );
  }
}

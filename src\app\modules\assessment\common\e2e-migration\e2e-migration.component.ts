import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { AssessmentService } from '../../../../services/assessment.service';
import { DataMigrationService } from '../../../../services/dataMigration.service';
import { Title } from '@angular/platform-browser';
import { NgSelectModule } from '@ng-select/ng-select';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { GetReqData } from '../../../../models/interfaces/types';
import { CommonModule } from '@angular/common';

declare let $: any;

@Component({
  selector: 'app-e2e-migration',
  standalone: true,
  imports: [FormsModule, ReactiveFormsModule, NgSelectModule, SpinnerComponent, CommonModule, NgSelectModule, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './e2e-migration.component.html',
  styles: ``
})
export class E2eMigrationComponent {

  execProjectForm: any;
  extractForm: any
  spin: boolean = false;
  pageName: string = ''
  projectId: string = ""
  migtypeid: string = ""
  tables: any
  tablesList: any = []
  schemalable: any = ''
  constructor(private titleService: Title, private FormBuilder: FormBuilder, private toast: HotToastService, private route: ActivatedRoute,
    private assessmentService: AssessmentService, private dataMigrationService: DataMigrationService
  ) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
    if (this.migtypeid == "31") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }


  }
  selectedItems: any
  reportOperations: any = []
  ngOnInit() {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
    this.GetConsList()
    this.getObjectTypes()
    this.getConfigMenu()
    this.getPrjRuninfoSelectAll();
    if (this.migtypeid == '49') {
      this.filterExecutionReports1()
    }
    this.execProjectForm = this.FormBuilder.group({
      connectionName: ['', Validators.required],
      schema: ['', Validators.required],
      drconnectionName: ['', Validators.required],
      tgtschema: [''],
      targetconnection: [''],
      //objectType: [''],
      dataload: [''],
      confiName: ['', Validators.required],
      tables: [''],
      //chunksize: [''],
      operation: [''],
      limit_CPU: ['', [Validators.required]],
      limit_CPU_measure: [''],
      request_CPU: ['', [Validators.required]],
      request_CPU_measure: [''],
      // replicate: [''],
      cdcdataload: ['']
    });
    this.extractForm = this.FormBuilder.group({
      sourceConnection: ['', Validators.required],
      schema: ['', Validators.required],
    })
    if (this.migtypeid == "31") {
      this.execProjectForm.get('dataload').setValidators([Validators.required])
      this.execProjectForm.controls['dataload'].updateValueAndValidity()
      this.execProjectForm.get('drconnectionName').clearValidators()
      this.execProjectForm.controls['drconnectionName'].updateValueAndValidity()
      this.execProjectForm.get('limit_CPU').clearValidators()
      this.execProjectForm.controls['limit_CPU'].updateValueAndValidity()
      this.execProjectForm.get('request_CPU').clearValidators()
      this.execProjectForm.controls['request_CPU'].updateValueAndValidity()
    } else if (this.migtypeid == '49') {
      this.execProjectForm.get('drconnectionName').clearValidators()
      this.execProjectForm.controls['drconnectionName'].updateValueAndValidity()
      this.execProjectForm.get('schema').clearValidators()
      this.execProjectForm.controls['schema'].updateValueAndValidity()
      this.execProjectForm.get('confiName').clearValidators()
      this.execProjectForm.controls['confiName'].updateValueAndValidity()
      this.execProjectForm.get('limit_CPU').clearValidators()
      this.execProjectForm.controls['limit_CPU'].updateValueAndValidity()
      this.execProjectForm.get('request_CPU').clearValidators()
      this.execProjectForm.controls['request_CPU'].updateValueAndValidity()
    }
    else {
      this.execProjectForm.get('limit_CPU').clearValidators()
      this.execProjectForm.controls['limit_CPU'].updateValueAndValidity()
      this.execProjectForm.get('request_CPU').clearValidators()
      this.execProjectForm.controls['request_CPU'].updateValueAndValidity()
      this.execProjectForm.get('tables').setValidators([Validators.required])
      this.execProjectForm.controls['tables'].updateValueAndValidity()

    }
    this.execProjectForm.updateValueAndValidity();

    if (this.migtypeid == "38") {
      this.fetchAirflowFiles()
    }
    this.reportOperations = [{ option: "Extraction", value: "Extraction" },
    { option: "conversion", value: "Storage_Objects" },
    { option: "Data Migration", value: "Data_Migration" },
    ]
  }
  get f() {
    return this.execProjectForm.controls;
  }
  get fs() {
    return this.extractForm.controls;
  }
  slicedData(data: any[]): any[] {
    return data.slice(0, 1)
  }

  selectedTable: any = []
  srcConsList: any
  tgtConsList: any
  srcConId: any
  tgtConid: any
  ConsList: any = []
  GetConsList() {
    this.assessmentService.getConList(this.projectId.toString()).subscribe((data: any) => {
      this.ConsList = data['Table1'];
      this.srcConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != '';
      });
      this.tgtConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
    });
  }
  srcschemaList: any = []
  tgtschemaList: any = []
  replicationList: any = []
  getSchemasList(ConnectionId: any, value: any) {
    const obj: any = {
      projectid: this.projectId,
      connectioId: ConnectionId,
    };
    if (value == "0") {
      this.srcConId = ConnectionId
    } else {
      this.tgtConid = ConnectionId
    }

    if (this.migtypeid == "31" && value == "0") {
      this.srcschemaList = []
      this.srcConsList.filter((item: any) => {
        if (item.Connection_ID == ConnectionId) {
          let obj = {
            schemaname: item.dbname
          }
          this.srcschemaList.push(obj)
        }
      })
    }
    else if (this.migtypeid == "31" && value == "1") {
      this.tgtschemaList = []
      this.tgtConsList.filter((item: any) => {
        if (item.Connection_ID == ConnectionId) {
          let obj = {
            schemaname: item.dbname
          }
          this.tgtschemaList.push(obj)
        }
      })

    }
    else if (this.migtypeid == "38" && value == "0") {
      this.GetPgSchemas(ConnectionId, 0);
      this.dataMigrationService.getRelication(ConnectionId).subscribe((data: any) => {
        this.replicationList = data
      })
    }
    else if (this.migtypeid == "38" && value == "1") {
      this.GetPgSchemas(ConnectionId, 1);
    }
    else {
      this.assessmentService.SchemaListSelect(obj).subscribe((data: any) => {
        if (value == "0") {
          this.srcschemaList = data['Table1'];
          this.srcschemaList = this.srcschemaList.filter((item: any) => item.schema_name !== 'ALL');
          this.srcschemaList.forEach((item: any) => {
            item.type = "ALL"
          })

        } else {
          this.tgtschemaList = data['Table1'];
        }
      });
    }

  }
  objectType: any
  getObjectTypes() {
    const obj = {
      projectId: this.projectId,
      objectgroupname: "Storage_Objects"
    }
    this.assessmentService.getObjectTypes(obj).subscribe((data: any) => {
      this.objectType = data['Table1'];
    })
  }
  ObjectNamesList: any
  getObjectNames(dbname: any) {
    var obj: any = []
    if (this.migtypeid == "31") {
      obj = {
        conid: this.srcConId,
        dbname: dbname
      }
    }
    if (this.migtypeid == "31") {
      this.assessmentService.GetMySqlTables(obj).subscribe((data: any) => {
        this.tablesList = data
        this.tablesList.filter((item: any) => {
          item.type = "ALL"
        })
      })
    }
    else {
      this.assessmentService.getObjNames(obj).subscribe((data: any) => {
        this.tablesList = data
        this.tablesList.filter((item: any) => {
          item.type = "ALL"
        })
      })
    }

  }
  triggerresponse: any
  triggere2eCOmmand(formdata: any) {
    if (formdata.confiName.includes(" ")) {
      let reqMem = formdata.request_Memory_measure == '' ? 'Mi' : formdata.request_Memory_measure
      let limitMem = formdata.limit_Memory_measure == '' ? 'Mi' : formdata.limit_Memory_measure
      this.toast.error("Please Remove Space From Filename")
    }
    else {
      let obj: any = {
        srcConId: this.srcConId,
        tgtConId: this.tgtConid,
        projectId: this.projectId.toString(),
        operation: "Extraction",
        schema: formdata.schema,
        targetschema: formdata.tgtschema,
        Object: "Storage_Objects",
        filename: formdata.confiName,
        dataload: formdata.dataload,
        chunksize: formdata.chunksize,
        operationload: formdata.operation,
        // requestCPU: formdata.request_CPU + formdata.request_CPU_measure,
        //limitCPU: formdata.limit_CPU + formdata.limit_CPU_measure,
      }
      if (this.migtypeid == "31" && formdata.dataload == "Mydumper") {

      }
      else if (this.migtypeid == "38") {
        obj['operation'] = "E2E_Migration";
        obj['replicationSlotName'] = formdata.replicate;
      }
      else {
        obj['table_name'] = this.selectedTable.length > 0 ? this.selectedTable.toString() : ""
      }
      if (this.migtypeid == "31") {
        this.assessmentService.TriggerMariaE2eCommand(obj).subscribe((data: any) => {
          this.triggerresponse = data;
          this.toast.success(this.triggerresponse.message)
          this.execProjectForm.reset()
        })
      }
      else {
        this.assessmentService.TriggerE2eCommand(obj).subscribe((data: any) => {
          this.triggerresponse = data;
          this.toast.success(this.triggerresponse.message)
          this.execProjectForm.reset()
        })
      }


    }



  }
  extractValue(value: any) {
    let obj = {
      task: "Extract_Tables",
      projectId: this.projectId.toString(),
      schema: value.schema,
      srcId: value.sourceConnection
    }
    this.spin = true
    this.assessmentService.DataMigrationCommand(obj).subscribe((data: any) => {
      this.spin = false
      this.toast.success("Tables Extracted Successfully")
    },
      error => {
        this.spin = false
        this.toast.error("Failed to Tables Extracted")
      })
  }

  schemaList1: any = []
  getSchemasList1(ConnectionId: any) {
    this.schemaList1 = []
    // this.getTableSrcId = ConnectionId
    if (this.migtypeid == "31") {
      this.srcConsList.filter((item: any) => {
        if (item.Connection_ID == ConnectionId) {
          let obj = {
            schemaname: item.dbname
          }
          this.schemaList1.push(obj)
        }
      })
    }
    else {
      const obj = {
        projectid: this.projectId,
        connectioId: ConnectionId
      }
      this.assessmentService.SchemaListSelect(obj).subscribe((data: any) => {
        this.schemaList1 = data['Table1'];
      })
    }
    // if (this.migtypeid == "35" || this.migtypeid == "34") {
    //   this.fetchDynamoTables(ConnectionId);
    // }
  }
  selectedsrcschema: any = [];
  selectedtgtschema: any
  schemavalue: any
  showschemaa: boolean = false
  schemaName: any = []
  isAll: any;
  showTargetSchemaAndTables: boolean = true;
  onItemSelect(item: any) {
    this.schemavalue = item.toString();
    this.showTargetSchemaAndTables = item.length === 1;
    this.isAll = item.toString();
    this.selectedsrcschema = [];
    const currentTgt = this.execProjectForm.get('tgtschema')?.value;
    if (item == "ALL") {
      this.srcschemaList.forEach((item: any) => {
        if (this.migtypeid == "31") {
          this.selectedsrcschema.push(item.schemaname)
        }
        else {
          this.selectedsrcschema.push(item.schema_name);
        }
        this.selectedsrcschema = this.selectedsrcschema.filter((item: any) => item !== 'ALL');
      })
      if (!currentTgt || currentTgt.length === 0 || currentTgt === 'Select Target Schema') {
        this.execProjectForm.get('tgtschema')?.setValue(this.selectedsrcschema);
      }
      console.log("selectedsrcschema", this.selectedsrcschema);
    }
    // if (item.length == 1) {
    else if (item != undefined) {
      this.showschemaa = false;
      const schemaId = this.srcschemaList.filter((el: any) => el.schema_name == item.toString())
      this.schemaName.push(item);
      if (!currentTgt || currentTgt.length === 0 || currentTgt === 'Select Target Schema') {
        this.execProjectForm.get('tgtschema')?.setValue(item);
      }
      let obj = {
        schema: item,
        srcId: this.srcConId
      }
      if (this.migtypeid == "35" || this.migtypeid == "34") {
      }
      else if (this.migtypeid == "20" || this.migtypeid == "40") {
        this.getOracleTables(item)
      }
      else if (this.migtypeid == "38") {
        this.GetPostgresTables(item)
      }
      else if (this.migtypeid == "46") {
        this.getDB2Tables(item)
      }
      else if (this.migtypeid == "31") {
        this.getObjectNames(item);
      }
      else if (this.migtypeid == "30") {
        this.getSqlTables(item);
      }
      else {
        this.assessmentService.GetTablesByschemaGG(obj).subscribe((data: any) => {
          this.tablesList = data['Table1']
          this.tablesList.forEach((item: any) => {
            item.type = "ALL"
          })

        })
      }

      // this.schemaID.push(schemaId[0].schema_id)
      // this.schemasCheck()
    }
    else {
      this.showschemaa = true;
    }
  }
  getSqlTables(value: any) {
    let obj = {
      conid: this.srcConId,
      schemaname: value
    }
    if (this.migtypeid == "30") {
      this.dataMigrationService.getsqlServerTables(obj).subscribe((data: any) => {
        this.tablesList = data
        this.tablesList.forEach((item: any) => {
          item.type = "ALL"
        })
      })
    }
    else {
      this.dataMigrationService.getsqlTables(obj).subscribe((data: any) => {
        this.tablesList = data
        this.tablesList.forEach((item: any) => {
          item.type = "ALL"
        })
      })
    }

  }
  tablesSelect(value: any) {
    if (value == "ALL") {
      this.tablesList.forEach((item: any) => {
        if (this.migtypeid == "31") {
          this.selectedTable.push(item.tableName)
        }
        else {
          this.selectedTable.push(item.table_name)
        }
        this.selectedTable = this.selectedTable.filter((item: any) => item !== 'ALL');
      })
    }
  }
  getTableDetails(sch: any) {
    let obj = {
      Conid: this.tgtConid,
      objectType: "TABLE",
      schemaname: sch
    }
    this.assessmentService.getObjNames(obj).subscribe((data: any) => {
      this.tablesList = data
    })
  }
  dataloadtype: any
  hidetgtschema: boolean = false
  fetch(value: any) {
    this.dataloadtype = value;
    if (value == "PG_Dump") {
      this.execProjectForm.controls['tables'].clearValidators();
      this.execProjectForm.controls['tables'].updateValueAndValidity()
      this.execProjectForm.controls['drconnectionName'].clearValidators();
      this.execProjectForm.controls['drconnectionName'].updateValueAndValidity()
      this.execProjectForm.controls['tgtschema'].clearValidators();
      this.execProjectForm.controls['tgtschema'].updateValueAndValidity()
      this.execProjectForm.controls['schema'].clearValidators();
      this.execProjectForm.controls['schema'].updateValueAndValidity()
    }

    if (this.migtypeid == "38" && value == "PG_Dump") {
      this.schemalable = "Database"
    } else {
      this.schemalable = "Schema"
    }

    if (this.migtypeid == "40" || this.migtypeid == "46") {
      this.hidetgtschema = true
    }

  }
  // validationstable() {
  //   if (this.isAll == "ALL") {
  //     this.execProjectForm.get('tables')?.clearValidators()
  //     this.execProjectForm.controls['tables']?.updateValueAndValidity()
  //   }
  // }
  validationstable() {
    const tablesControl = this.execProjectForm.get('tables');
    const tgtschemaControl = this.execProjectForm.get('tgtschema');
    const requestcpu = this.execProjectForm.get('request_CPU');
    const limitcpu = this.execProjectForm.get('limit_CPU');
    if (this.isAll === "ALL") {
      tablesControl?.clearValidators();
      tablesControl?.setValue(null);

      tgtschemaControl?.clearValidators();
      tgtschemaControl?.setValue(null);
      limitcpu?.clearValidators();
      limitcpu?.setValue(null);
      requestcpu?.clearValidators();
      requestcpu?.setValue(null);
    }
    else if (Array.isArray(this.selectedItems) && this.selectedItems?.length === 1) {
      tablesControl?.setValidators([Validators.required]);
      tgtschemaControl?.setValidators([Validators.required]);
      limitcpu?.clearValidators();
      limitcpu?.setValue(null);
      requestcpu?.clearValidators();
      requestcpu?.setValue(null);
    }
    else {
      tablesControl?.clearValidators();
      tablesControl?.setValue(null);

      tgtschemaControl?.clearValidators();
      tgtschemaControl?.setValue(null);

      limitcpu?.clearValidators();
      limitcpu?.setValue(null);
      requestcpu?.clearValidators();
      requestcpu?.setValue(null);
    }
    tablesControl?.updateValueAndValidity();
    tgtschemaControl?.updateValueAndValidity();
    limitcpu?.updateValueAndValidity();
    requestcpu?.updateValueAndValidity();
  }

  oper: any = ""
  dm_show: boolean = false
  selectOp(value: any) {
    this.oper = value
    if (value == "Datamigration") {
      this.dm_show = true
      // this.fetchConfigFiles()
    }
    else if (value == "Extraction") {
      this.getreqTableDataForEcxtractions()
      this.dm_show = false
    }
    else {
      this.dm_show = false
      this.getreqTableData()
    }

  }
  ref_spin: boolean = false
  tabledata: any = []
  z: any;
  page2: number = 1;
  piA: number = 10;
  //getreqTableData
  getreqTableData() {
    this.tabledata = []
    if (this.oper == "") {
      this.toast.error("Please Select Operation")
    }
    else if (this.oper == "Datamigration") {

    }
    else if (this.oper == "Conversion") {
      const obj: any = {
        projectId: this.projectId,
        operationType: this.oper//"Extraction"
      }
      // this.ref_spin = true
      this.assessmentService.GetReqData(obj).subscribe((data: any) => {
        this.tabledata = data['Table1'];
        if (this.tabledata == undefined) {
          this.tabledata = []
        }
        else {
          // this.ref_spin = false
          for (let k = 0; k < this.tabledata.length; k++) {
            if (this.tabledata[k].objecttype == "ALL") {
              this.tabledata[k].objecttype = ""
            }
          }
          if (this.tabledata != undefined) {
            for (this.z = 0; this.z < this.tabledata.length; this.z++) {
              for (let i = 0; i < this.ConsList?.length; i++) {
                if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                  this.tabledata[this.z].conname = this.ConsList[i].conname
                }
              }
            }
          }
          else {
            this.tabledata = []
          }
        }
      })
    }
    else if (this.oper == "Extraction") {
      this.getreqTableDataForEcxtractions()
    }
    else {

    }
  }
  getreqTableDataForEcxtractions() {
    this.tabledata = []
    const obj1: GetReqData = {
      projectId: this.projectId,
      operationType: "Pre_Extraction"
    }
    const obj2: GetReqData = {
      projectId: this.projectId,
      operationType: "Code_Extraction"
    }
    const obj3: GetReqData = {
      projectId: this.projectId,
      operationType: "Split_Package"
    }
    const obj4: GetReqData = {
      projectId: this.projectId,
      operationType: "Conversion_Dependent_Files"
    }
    var obj = [obj1, obj2, obj3, obj4]
    this.ref_spin = true
    obj.forEach((el: GetReqData) => {
      this.assessmentService.GetReqData(el).subscribe((data: any) => {
        var temp = data['Table1']
        if (temp != undefined) {
          if (temp.length > 0) {
            temp.forEach((item: any) => {
              this.tabledata.push(item)
            })
          }

          this.tabledata = this.tabledata.sort((a: any, b: any) => {
            return b.request_id - a.request_id; // For descending order
          });
        }
      })
      this.ref_spin = false
    })
  }
  deleteResponse: any
  datachange1: any
  //deleteTableDatas
  deleteTableDatas(request_id: any) {
    const obj: any = {
      projectId: this.projectId,
      requestId: request_id
    }
    this.assessmentService.deleteTableData(obj).subscribe((data: any) => {
      this.deleteResponse = data['Table1'];
      this.toast.success("Deleted Successfully");
      this.getreqTableData()
    })
  }
  onKey() {
    this.page2 = 1;
  }
  valFiles: any = []
  pageNumber1: number = 1
  fetchAirflowFiles() {
    const path = "Validation_Reports";// 
    this.assessmentService.GetFilesFromExpath(path).subscribe((data: any) => {
      this.valFiles = data
    })
  }
  fileStatus: any
  spinDownload: boolean = false
  pi: number = 10
  pi1: number = 10
  searchText1: any
  deleteFiles(path: string) {
    this.assessmentService.deleteFile(path).subscribe({
      next: (data: any) => {
        this.fileStatus = data.message
        // this.fetchAirflowFiles();
        this.selectTgt(this.tgtConId1);
        this.toast.success(this.fileStatus)
      },
      error: (error) => {
        this.toast.error(error.message)
      },
    });
  }
  fileResponse: any
  downloadFile(fileInfo: any) {
    this.assessmentService.downloadLargeFiles(fileInfo.filePath).subscribe((blob: any) => {
      this.fileResponse = blob;
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      const fileName = fileInfo.fileName;
      downloadLink.href = url;
      document.body.appendChild(downloadLink);
      downloadLink.download = fileName;
      downloadLink.click();
      this.spinDownload = false

    })
  }
  configData: any
  getConfigMenu() {
    this.assessmentService.GetConfigMenu(this.migtypeid).subscribe((data: any) => {
      this.configData = data['Table1']
      this.configData = this.configData.filter((item: any) => {
        return item.configtype != "Catchup"
      })

    })
  }
  confFiles: any
  fetchConfigFiles() {
    const path = "Config_Files/Data_Migration"//"AIRFLOW_FILES/Data_Migration_Reports/"
    this.assessmentService.GetFilesFromExpath(path).subscribe((data: any) => {
      this.confFiles = data
    })
  }
  fetchConfigFiles1() {
    this.fetchSourceConfigFiles(this.fileSrc);
    this.fetchTargetConfigFiles(this.tgtconn);
    //this.getreqTableDataForEcxtractions()
  }
  p: number = 1
  datachange: any
  delete_file: boolean = false
  // deleteAirflowFiles(value: any) {
  //   this.assessmentService.delete(value).subscribe((data: any) => {
  //     this.fetchConfigFiles()
  //     this.toast.success(data.message)
  //   })
  // }
  iterationForLogs: any
  exeOperations: any = []
  selectedExeOp: any
  datachangeLogs: any
  selectIterForLogs(value: any) {
    this.iterationForLogs = value
    this.exeOperations = [];
    this.ExecututionFiles = []
    this.operations()
  }
  operations() {
    this.exeOperations = [{
      option: "conversion", value: "Storage_Objects"
    },
   
    ]
  }

  page3: number = 1;
  operationSelected: boolean = false;

  exeOp(value: any) {
    this.selectedExeOp = value;
    this.operationSelected = true;
    this.filterExecutionReports()
  }
  ExecututionFiles: any = []
  filterExecutionReports() {
    var path = "PRJ" + this.projectId + "SRC/" + this.iterationForLogs + "/Execution_Logs/Conversion/"
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
      this.ExecututionFiles = this.ExecututionFiles.filter((item: any) => {
        return item.fileName.includes(this.selectedExeOp) //Code_Objects  Storage_Objects
      })
    })
  }
  filterExecutionReports1() {
    var path = ""
    this.assessmentService.GetFilesFromDir(path).subscribe((data: any) => {
      this.ExecututionFiles = data
      // this.ExecututionFiles = this.ExecututionFiles.filter((item: any) => {
      //   return item.fileName.includes(this.selectedExeOp) //Code_Objects  Storage_Objects
      // })
    })
  }
  runnoForReports: any
  getPrjRuninfoSelectAll() {
    this.assessmentService.GetRunno(this.projectId).subscribe((data: any) => {
      // this.getRunSpin = false
      this.runnoForReports = JSON.parse(JSON.stringify(data['Table1']));
      this.runnoForReports = this.runnoForReports.filter((data: any) => { return ((data.operation_name == "Extraction") && data.iteration != '') })
      this.runnoForReports = this.filterList(this.runnoForReports)
    });
  }
  filterList(listData: any) {
    let uniqueNames: any = []
    for (const element of listData) {
      if (uniqueNames.length == 0) {
        uniqueNames.push(element)
      }
      else {
        var abc = uniqueNames.filter((item: any) => {
          return item.iteration === element.iteration
        })
        if (abc.length == 0) {
          uniqueNames.push(element)
        }
      }
    }
    return uniqueNames;
  }
  iterationselected: any
  SelectIteration(value: any) {
    this.iterationselected = value;
    this.reportFiles = []
    this.repoOper = []
   //this.getPrjRuninfoSelectAll()
   
  }
  repoOper: any;
  operationSelectedforreports: boolean = false;
  selectReportOperation(value: any) {
    this.repoOper = value
    this.operationSelectedforreports=true;
    if (this.repoOper == "Extraction") {
      this.fetchExtractionFiles()
    } else if (this.repoOper == "Storage_Objects") {
      this.fetchStorageObjectsFiles()
    }
    else if (this.repoOper == "Code_Objects") {
      this.fetchCodeObjectsFiles()
    }
   // this.iterationselected = []
    this.reportFiles = []
    // if (this.repoOper == "Data_Migration") {
    //   this.fetchDMFiles()
    // }
  }

  reportFiles: any = []
  fetchExtractionFiles() {
    var basePath = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/"
    const paths = ['Pre_Extraction', 'Code_Extraction', 'Split_Package', 'Conversion_Dependent_Files']
    paths.forEach((el: string) => {
      this.assessmentService.GetFilesFromDir(basePath + el).subscribe((data: any) => {
        data.forEach((item: any) => {
          this.reportFiles.push(item)
        })
      })
    })
  }
  fetchCodeObjectsFiles() {
    const path1 = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Validation/"
    this.assessmentService.GetFilesFromDir(path1).subscribe((data: any) => {
      this.reportFiles = data.filter((item: any) => {
        return item.fileName.includes("_Code_Objects_Validation")
      })
    })
  }

  GetPgDbs(conid: any, value: any) {
    this.assessmentService.GetPgDatabase(conid).subscribe((data: any) => {
      if (value == 0) {
        data.forEach((item: any) => {
          item.schema_name = item.dbname
        })
        this.srcschemaList = data
      }
      else {
        data.forEach((item: any) => {
          item.schema_name = item.dbname
        })
        this.tgtschemaList = data
      }
    })
  }

  GetPgSchemas(conid: any, value: any) {
    this.assessmentService.tgtSchemaSelect(conid).subscribe((data: any) => {
      if (value == 0) {
        data.forEach((item: any) => {
          item.schema_name = item.schemaname
        })
        this.srcschemaList = data
      }
      else {
        data.forEach((item: any) => {
          item.schema_name = item.schemaname
        })
        this.tgtschemaList = data
      }
    })
  }

  fetchStorageObjectsFiles() {
    const path2 = "PRJ" + this.projectId + "SRC/" + this.iterationselected + "/Reports/Validation/"
    this.assessmentService.GetFilesFromDir(path2).subscribe((data: any) => {
      this.reportFiles = data.filter((item: any) => {
        return item.fileName.includes("_Storage_Objects_Validation")
      })
    })
  }
  fetchDMFiles() {
    const path = "Validation_Reports";// 
    this.assessmentService.GetFilesFromExpath(path).subscribe((data: any) => {
      this.reportFiles = data
      this.fetchDMiles1()
    })
  }
  fetchDMiles1() {
    var tempfiles: any = []
    const path = "Summary_Validation_Reports";// "Summary_validation/"
    this.assessmentService.GetFilesFromExpath(path).subscribe((data: any) => {
      tempfiles = data;
      if (tempfiles.length > 0) {
        tempfiles.forEach((itemk: any) => {
          this.reportFiles.push(itemk)
        })
      }
      this.reportFiles.sort((a: any, b: any) => {
        const dateA = new Date(a.created_dt).getTime();
        const dateB = new Date(b.created_dt).getTime();
        return dateB - dateA; // For descending order
      });
    })
  }
  getOracleTables(value: any) {
    let obj = {
      conid: this.srcConId,
      schemaname: value
    }
    this.assessmentService.getOracleTables(obj).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.table_name = item.tablename
      })

      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }

  getDB2Tables(value: any) {
    let obj = {
      conid: this.srcConId,
      schemaname: value
    }
    this.assessmentService.getDb2Tables(obj).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.table_name = item.tablename
      })
      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  TriggerE2EMigration(formdata: any) {
    let obj: any = {}
    if (formdata.dataload == "PG_Dump" || formdata.dataload == "Data_Dumper") {
      obj = {
        task: formdata.operation,
        projectId: this.projectId.toString(),
        source_connection_id: this.srcConId,
        target_connection_id: this.tgtConid == undefined ? "" : this.tgtConid,
        dr_connection_id: formdata.drconnectionName,
        mig_operation: formdata.operation,
        schema: formdata.schema,
        target_schema: formdata.tgtschema,
        table_name: this.selectedTable.length > 0 ? this.selectedTable.toString() : "",
        data_load_type: formdata.dataload,
        cdc_load_type: formdata.cdcdataload,
        // request_cpu: formdata.request_CPU + formdata.request_CPU_measure,
        // limit_cpu: formdata.limit_CPU + formdata.limit_CPU_measure,
        process_type: "E2E",
        file_name: formdata.confiName
      }
    }
    else {
      obj = {
        task: "Extraction",
        projectId: this.projectId.toString(),
        source_connection_id: this.srcConId,
        target_connection_id: this.tgtConid == undefined ? "" : this.tgtConid,
        dr_connection_id: this.migtypeid == '49' ? "null" : formdata.drconnectionName,
        mig_operation: formdata.operation,
        schema: this.migtypeid == '49' ? "null" : this.schemavalue === "ALL" ? this.selectedsrcschema.toString() : this.selectedItems.toString(),
        target_schema: formdata.tgtschema,
        table_name: this.migtypeid == '49' ? "null" : Array.isArray(this.selectedTable) && this.selectedTable.length > 0
          ? this.selectedTable.toString()
          : "",
        data_load_type: formdata.dataload,
        cdc_load_type: formdata.cdcdataload,
        // request_cpu: formdata.request_CPU + formdata.request_CPU_measure,
        // limit_cpu: formdata.limit_CPU + formdata.limit_CPU_measure,
        process_type: "E2E",
        iteration_id: "New",
        file_name: formdata.confiName
      }
    }
    console.log(obj)
    this.assessmentService.TriggerE2eCommandNew(obj).subscribe((data: any) => {
      this.triggerresponse = data;
      this.toast.success(this.triggerresponse.message)
      this.execProjectForm.reset()
    })
  }
  GetPostgresTables(value: any) {
    this.tablesList = []
    let obj = {
      conid: this.srcConId,
      schema: value
    }
    this.assessmentService.GetPostgresTablesBySchema(obj).subscribe((data: any) => {
      this.tablesList = data
      this.tablesList.forEach((item: any) => {
        item.type = "ALL"
      })
    })
  }
  fileSrc: any
  fetchSourceConfigFiles(conn: any) {
    this.ref_spin = true
    this.fileSrc = conn
    const path = conn + "/Config_Files/Data_Migration";
    this.assessmentService.GetFilesFromExpath(path).subscribe((data: any) => {
      this.confFiles = data
      this.ref_spin = false
    })
  }
  tgtconn: any
  fetchTargetConfigFiles(conn: any) {
    this.tgtconn = conn;
    this.ref_spin = true
    const path = this.fileSrc + "/" + conn + "/Config_Files/Data_Migration";
    this.assessmentService.GetFilesFromExpath(path).subscribe((data: any) => {
      this.confFiles = data
      this.ref_spin = false
    })
  }
  srcConId1: any
  tgtConId1: any
  selectSrc(value: any) {
    this.srcConId1 = value
  }
  tgtPath: any
  foldersList: any = []
  selectTgt(value: any) {
    this.tgtConId1 = value
    this.reportFiles = []
    this.tgtPath = this.srcConId1 + "/" + this.tgtConId1 + "/Validation_Reports"
    this.assessmentService.GetFilesFromExpath(this.tgtPath).subscribe((data: any) => {
      this.reportFiles = data
    })

  }
  openModal(value: any){
    $('#demo').offcanvas('show');
  }
  openPopup() {
    this.schemaName = []
    this.execProjectForm.reset();
  }
  // updateExecForm() {
  //   this.execProjectForm.patchValue({
  //     srcConId: this.execProjectForm.get('connectionName')?.value,
  //     schema: this.selectedsrcschema,
  //     dataLoadType: this.dataloadtype,
  //     drConnection: this.execProjectForm.get('drconnectionName')?.value,
  //     tgtConId: this.execProjectForm.get('targetconnection')?.value,
  //     targetschema: this.execProjectForm.get('tgtschema')?.value,
  //     operation: this.execProjectForm.get('operation')?.value,
  //     filename: this.execProjectForm.get('confiName')?.value,
  //   });
  // }
  selectedConname:any;
  conId:any;
  conName:any
  selectedfiletype(value: any) {
    const selectedconname = this.ConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.selectedConname = value;
    this.conId = selectedconname[0].Connection_ID;
    this.conName = selectedconname[0].conname;

  }
  selectedtgtname:any;
  conId1:any;
  conName1:any
  selectedtgttype(value: any) {
    const selectedtgtname = this.tgtConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.selectedtgtname = value;
    this.conId1 = selectedtgtname[0].Connection_ID;
    this.conName1 = selectedtgtname[0].conname;

  }
  selectedtgtname2:any;
  conId2:any;
  conName2:any
  selectedtgttype1(value: any) {
    const selectedtgtname2 = this.tgtConsList.filter((item: any) => {
      return item.Connection_ID === value;
    });
    this.selectedtgtname2 = value;
    this.conId2 = selectedtgtname2[0].Connection_ID;
    this.conName2 = selectedtgtname2[0].conname;

  }
}

import { Component } from '@angular/core';
import { BreadCrumbComponent } from '../../../../shared/components/bread-crumb/bread-crumb.component';
import { HotToastService } from '@ngxpert/hot-toast';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { NgSelectModule } from '@ng-select/ng-select';
import { PerofmanceTestingService } from '../../../../services/performanceTesting.service';
import { ActivatedRoute } from '@angular/router';
import * as XLSX from 'xlsx';


declare let $: any;
@Component({
  selector: 'app-log-explain',
  standalone: true,
  imports: [NgSelectModule, BreadCrumbComponent, ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe],

  templateUrl: './log-explain.component.html',
  styles: ``
})

export class LogExplainComponent {
  perfCallForm:any = this.formBuilder.group({
    tgtconnection: ['', Validators.required],
    fromDate:[null],
    connectionType: [null],
    ProcudureName:[null],
    callstatement:['']
  });
  hidedata: boolean = true;
  data: boolean = false;
  projectId: string;
  ref_spin: boolean = true;
  tabledata: any;
  userData: any = [];
  schemaList: any = [];
  schemaName: any = [];
  selectedschemas: any = [];
  execProjectForm: any;
  selectFile: any;
  fileName: string = '';
  uploadfileSpin: boolean = false;
  fileAdd: boolean = false;
  projectDocuments: any;
  projectDocumentFilter: any;
  pageNumber: number = 1;
  searchText: string = '';
  exe: any;
  contype: string = ""
  dup: string = ""
  mon: string = ""
  tgtId: any
  tgtValue: string = ''
  z: any;
  i: any;
  selectedConname: any
  conName: any
  conId: any;
  ConsList: any;
  tgtList: any
  selectedCategory: string = ""
  hideDrpdwn: boolean = false
  hideD: boolean = false
  value: any;
  selectedDuplicate: string = "";
  selectedMonitor: string = "";
  P2: number = 1;
  P3: number = 1;
  perSchema: any;
  selectedItems = [];
  selectedObjItems1 = [];
  perSchema1: any;
  exe1: any;
  TableData: boolean = false;
  analyzesearchlist: any;
  Exespinner: boolean = false;
  Monspinner: boolean = false;
  excelSpin: boolean = false;
  hideMon: boolean = false;

  pageName: string = ''
  indexMonitor: any;
  Monitor: boolean = false;
  testing: any;
  hideLog: boolean = false;
  uploadForm: any;
  hideTable: boolean = false;
  Callstm: any;
  task: any;
  ParTable: any;
  ConId: any;
  stat: boolean = false;
  PartitonTableData: any;
  hideQuery: boolean = false;
  selectedLog: string = "";
  hideL: boolean = false;
  hideS: boolean = false;
  hidedate: boolean = false;
  hideTab: boolean = false;




  constructor(private toast: HotToastService, private performanceservices: PerofmanceTestingService, public formBuilder: FormBuilder, private route: ActivatedRoute) {
    const getJson = localStorage.getItem('project_id') as string;
    this.projectId = JSON.parse(getJson);
    
  }

  /*--- Page Title ---*/
  pageTitle: string = "Documents"
  pageIcon: string = "assets/images/documents.svg"


  ngOnInit(): void {
    this.GetConsList();
    this.pageName = this.route.snapshot.data['name'];
  }
  

  
  /*--- Validation ---*/
  get validate() {
    return this.perfCallForm.controls;
  }

  /*--- Schema   ---*/
  GetperfSchemas() {
    const obj = {
      conId: this.tgtId,
    }
    this.performanceservices.GetperfSchemas(obj).subscribe((data: any) => {
      this.perSchema = data;
    }) 
  }

  /*--- SelectContype   ---*/
  selectContype(value: string) {
    this.contype = value;
    if (value == "0") {
      this.hidedata = true;
      this.data = false;
    }
    else {
      this.hidedata = false;
      this.data = true;
    }
  }

  

  

  /*--- Get Operation List   ---*/
  selTgtId(value: any) {
    this.tgtId = value
    this.tgtList.filter((el: any) => { return el.Connection_ID == value ? this.tgtValue = el.conname : '' })
  }

  

  /*--- GetReqTableData   ---*/
  getreqTableData() {
    const obj = {
      projectId: this.projectId,
      operationType: "Conversion"
    }
    this.ref_spin = true
    this.performanceservices.GetReqData(obj)?.subscribe((data: any) => {
      this.tabledata = data['Table1'];
      if (this.tabledata == undefined) {
        this.tabledata = []
      }
      else {
        this.ref_spin = false
        if (this.tabledata != undefined) {
          for (this.z = 0; this.z < this.tabledata.length; this.z++) {
            for (let i = 0; i < this.ConsList.length; i++) {
              if (this.tabledata[this.z].connection_id == this.ConsList[i].Connection_ID) {
                this.tabledata[this.z].conname = this.ConsList[i].conname
              }
            }
          }
        } else {
          this.tabledata = []
        }
        this.tabledata = this.tabledata.filter((item: any) => {
          return item.operation_name == "Storage_Objects"
        })
        //console.log(this.tabledata)
      }
    })
  }
  
  ftsFiles: any
  /*--- GetConsList ---*/
  GetConsList() {
    this.performanceservices.getConList(this.projectId.toString())?.subscribe((data: any) => {
      this.ConsList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'S' && item.conname != "";
      })
      this.tgtList = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != "";
      })
      this.getreqTableData()
      this.ref_spin = false
    })
  }

  /*--- Select Log ---*/
  selectLog(value:string){
    this.selectedLog = value
    if(value == "0")
      {
        this.hideS= true;
      }
      else {
        this.hideS= false;
      }
      if(value == "1")
        {
          this.hideL= true;
        }
        else {
          this.hideL= false;
        }
  }

  Showlogsdata:any
  selecteddate:any;
  selectschema:any;
  selectedprocedurename:any;
  SubmitShowLogs(value:any) {
     this.hideTab=true;
     if (value.fromDate ===null) {
      this.selecteddate= "null";
    }
    else
    {
      this.selecteddate = value.fromDate;
    }
    if (value.ProcudureName ===null) {
      this.selectedprocedurename= "null";
    }
    else
    {
      this.selectedprocedurename = value.ProcudureName;
    }
    if (value.connectionType ===null) {
      this.selectschema= "null";
    }
    else
    {
      this.selectschema = value.connectionType;
    }
    this.Monspinner=true;
     const obj = {
       Conid:this.tgtId,
       schema:this.selectschema,
       procedure:this.selectedprocedurename,
       exec_date: this.selecteddate,
     }
    this.performanceservices.SubmitShowLogs(obj).subscribe((data: any) => {
      this.Showlogsdata = data;
      this.Monspinner=false;
   
    })
  }

  
  formatDate(date: string): string{
    debugger;
    let m = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
      , o = new Date(date)
      , S = o.getDate()
      , x = m[o.getMonth()]
      , u = o.getFullYear();

      if(S<10)
        {
          S=parseInt("0"+S)
        }
      (`${u}-${x}-${S}`)
    return `${u}-${x}-${S}`
  }
  
  
  /*--- Log explain method ---*/
  insertspin:boolean=false;
  InputCall(value:any) {
    this.insertspin=true;
    const obj = {
      task:"Log_Explain_Plan",
      projectId: this.projectId.toString(),
      operation:"Database",
      option: 0,
      target_connection_id: this.tgtId,
      callStatement: value.callstatement,
      tgtId: this.tgtId,
    }
    this.performanceservices.Execute(obj).subscribe((data: any) => {
      this.Callstm = data;
      this.insertspin=false;
      this.toast.success(data.message)
    })

  }

  refresh()
  {
    this.Showlogsdata=[];
    this.SubmitShowLogs("null");
  }

   /*--- exportExcelAnalyze ---*/
   fileNamee = 'LogexplainPlan.xlsx';
   logexplan: any = []
   excelSpinn: any = false;
   exportexcelShowlogs(): void {
     this.logexplan = [];
     this.excelSpinn = true;
     var test = this.Showlogsdata;
     for (var el of test) {
       var newEle: any = {};
       newEle.Schema = el.schema;
       newEle.Procedure = el.procedure;
       newEle.SqlStatement = el.sql_statement;
       newEle.Plan = el.plan;
       newEle.Exec_time = el.exec_time;
       newEle.procexectime = el.procexectime;
       newEle.queryexectime = el.queryexectime;
       this.logexplan.push(newEle);
     }
     const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.logexplan);
     const wb: XLSX.WorkBook = XLSX.utils.book_new();
     XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
     XLSX.writeFile(wb, this.fileNamee);
     this.excelSpinn = false;
   }

//  /*--- exportExcelAnalyze ---*/
//  fileNamees = 'CallStatement.xlsx';
//  callstatement: any = [];
//  callSpinn: any = false;
//  exportexcelstatement(): void {
//    this.callstatement = [];
//    this.callSpinn = true;
//    var tests = this.Callstm;
//    for (var el of tests) {
//      var newEle: any = {};
//      newEle.Procedure = el.procedure;
//      newEle.Schema = el.schema;
//      newEle.Plan = el.plan;
//      newEle.Exec_time = el.exec_time;
//      this.callstatement.push(newEle);
//    }
//    const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(this.callstatement);
//    const wb: XLSX.WorkBook = XLSX.utils.book_new();
//    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
//    XLSX.writeFile(wb, this.fileNamees);
//  }

}

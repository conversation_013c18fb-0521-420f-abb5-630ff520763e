import { Component} from '@angular/core';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { HotToastService } from '@ngxpert/hot-toast';
import { ActivatedRoute } from '@angular/router';
import { DeploymentService } from '../../../../services/deployment.service';
import { NgSelectModule } from '@ng-select/ng-select';
import { CommonModule } from '@angular/common';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';
import { CommonService } from '../../../../services/common.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { conList, Table } from '../../../../models/interfaces/types';

interface RunNumbers{
  iteration:string
}



interface ObjectNames{
  objectname:string,
  objecttype_name:string
}

@Component({
  selector: 'app-delta-control',
  standalone: true,
  imports: [SpinnerComponent,NgSelectModule, ReactiveFormsModule, FormsModule, CommonModule, NgxPaginationModule, SearchFilterPipe],
  templateUrl: './delta-control.component.html',
  styles: ``
})
export class DeltaControlComponent  {


  // Initial Setup
  pageName:string = '';
  project_name: any
  projectId: any  
  runNumbers:RunNumbers[] = [];
  schemaList:any;
  objectNames:ObjectNames[] = [];

  selectedRunNumber:string = ''
  selectedSchema:string = ''
  selectedObject:string = ''
  sourceObject:string = ''
  targetObject:string = ''
  highlightedText1: SafeHtml | null = null;
  highlightedText2: SafeHtml | null = null;
    
  // Variables to store counts
  totalLinesText1: number = 0;
  totalLinesText2: number = 0;
  removalCount: number = 0;
  additionCount: number = 0;

  
  totalLinesText1Array: number[] = [];
  totalLinesText2Array: number[] = [];
  
  
  saveForm = this.formBuilder.group({
    runnumber: ['', [Validators.required]],
    schema:['', [Validators.required]],
    object: ['', [Validators.required]],
    viewType: ['', [Validators.required]],
  })

  deployForm = this.formBuilder.group({
    targetConnection: ['', [Validators.required]],
    deployTime:['', [Validators.required]],
    observation: ['', [Validators.required]],
  })
  
  //Delta Control
  statementList:any;
  viewData:any;

  
  user: any  
  tgtlist: Table[] = [];

  constructor(
    private toast: HotToastService, 
    public formBuilder: FormBuilder,
    private route: ActivatedRoute,
    public deploymentService:DeploymentService,
    public commonService:CommonService,
    private sanitizer: DomSanitizer
  ){
      this.pageName = this.route.snapshot.data['name'];
      this.project_name = localStorage.getItem('project_name');
      let getJson = localStorage.getItem('project_id') as string;
      this.projectId = JSON.parse(getJson);
      let userJson = localStorage.getItem('userData') as string;
      this.user = JSON.parse(userJson);
  }

  
  /*--- Form controls ---*/
  get f(): any { return this.saveForm.controls; }
  get d(): any { return this.deployForm.controls; }

  ngOnInit(): void{
    // Initial Fetch 
    this.getRunNumbers()
    this.GetConsList()
  }

  // Get Run Numbers By Project ID
  getRunNumbers(){
    let newObj = {
      projectId: this.projectId,
      migsrcType:'Source_Current'
    }
    this.deploymentService.GetRunno(newObj).subscribe((data: any) => {
      this.runNumbers = data['Table1']
    })
  }


  // Get Schema By Run ID
  connectionName:string | undefined = ''
  getSchemas(value: string) {
    this.selectedRunNumber = value;
    this.deploymentService.GetSchemasByRunId(value).subscribe((data: any) => {
      this.schemaList = data['Table1']
    })
  }

  selectedSchemaID:string | undefined = ''
  // Get Object By Run Number and Schema Name
  getObjectNames(value: string) {
    this.selectedSchema = value
    let object = {
      RunNo :this.selectedRunNumber,
      schemaName: value
    }
    this.connectionName = this.schemaList.find( (item:any) => item.schemaname === value )?.connection_id
    this.selectedSchemaID = this.schemaList.find( (item:any) => item.schemaname === value )?.schema_id
    //console.log(this.connectionID)
    this.deploymentService.getObjectNames(object).subscribe((data: any) => {
      this.objectNames = data['Table1']
    })
  }
  
  // Get Object By Run Number and Schema Name
  
  firstStmt: any;
  lastStmt: any;
  selectedObjectType:string = ''
  getObjects(value: string) {
    this.selectedObject = value    
    const objet = this.objectNames.filter( (oT:any) => { return this.selectedObject === oT.objectname })
    this.selectedObjectType = objet[0].objecttype_name
    this.sourceObject = ''
    this.targetObject = ''
    let object = {
      RunNo :this.selectedRunNumber,
      objName: value
    }
    this.viewData = [{ value: 'Grid_View', view: 'Grid View' }, { value: 'Format_View', view: 'Format View' }]
    this.deploymentService.getObjects(object).subscribe((data: any) => {
      this.statementList = data['Table1']
      this.firstStmt = this.statementList[0].target_statemetnumber
      this.lastStmt = this.statementList[this.statementList.length - 1].target_statemetnumber
      for (const element of this.statementList) {
        element.old_deltaId = element.target_deltacodedetailid
        element.IsCopywithId = false
        element.IsMovedDownCopy = false
        element.targetstatement_current = [{
          "tgtSmt": element.targetstatement_current,
          "textchanged": "",
          "target_deltacodedetailid": element.target_deltacodedetailid,
          "tgtStatus": element.target_status
        }]
        delete element.target_deltacodedetailid
      }
      data['Table1'].forEach( (el:any)=>{
        const srcCurrent = el.sourcestatement_current.replace(/\n/g, ' ')
        const tgtCurrent = el.targetstatement_current[0].tgtSmt.replace(/\n/g, ' ')
        this.sourceObject = this.sourceObject + "\n" + srcCurrent + "\n"
        this.targetObject = this.targetObject + "\n" + tgtCurrent + "\n"
        this.compareTexts()
      })
    },(error) => {
      this.toast.error(error);
    })
    this.getreqTableData()
  }

  onInput(event: any, field: 'sourceObject' | 'targetObject') {
    const value = event.target.innerText;
    if (field === 'sourceObject') {
      this.sourceObject = value;
    } else {
      this.targetObject = value;
    }
  }

  compareTexts() {
    // Reset counts
    this.removalCount = 0;
    this.additionCount = 0;
    
    const lines1 = this.sourceObject.split('\n');
    const lines2 = this.targetObject.split('\n');

    this.totalLinesText1 = lines1.length;
    this.totalLinesText2 = lines2.length;

    const diff1 = this.highlightDifferences(lines1, lines2, 'removed');
    const diff2 = this.highlightDifferences(lines2, lines1, 'added');

    this.highlightedText1 = this.sanitizer.bypassSecurityTrustHtml(diff1.join(''));
    this.highlightedText2 = this.sanitizer.bypassSecurityTrustHtml(diff2.join(''));
    
    this.totalLinesText1Array = Array.from({ length: this.totalLinesText1 }, (_, i) => i + 1);
    this.totalLinesText2Array = Array.from({ length: this.totalLinesText2 }, (_, i) => i + 1);
  }

  highlightDifferences(lines1: string[], lines2: string[], type: 'added' | 'removed'): string[] {
    return lines1.map((line, index) => {
      if (index >= lines2.length) {
        if (type === 'removed') {
          this.removalCount++;
        } else {
          this.additionCount++;
        }
        return this.wrapSentence(line, type);
      } else if (line !== lines2[index]) {
        // Only count removals/additions if lines are actually different
        if (type === 'removed') {
          this.removalCount++;
        } else {
          this.additionCount++;
        }
        return this.wrapSentence(this.wrapCharacterDifferences(line, lines2[index], type), type);
      } else {
        // If lines are the same, no need to increment the counts
        return this.wrapSentence(line, 'same');
      }
    });
  }

  wrapSentence(sentence: string, type: 'added' | 'removed' | 'same'): string {
    let backgroundColor = '';
    switch (type) {
      case 'added':
        backgroundColor = '#00c2811a'; // Light green background for the sentence
        break;
      case 'removed':
        backgroundColor = '#f53d3d1a'; // Light coral background for the sentence
        break;
      case 'same':
        backgroundColor = 'white';
        break;
    }
    return `<div style="background-color: ${backgroundColor}; padding: 2px;border-radius: 0.4rem;min-height:15px;margin: 5px 0px;">${sentence}</div>`;
  }

  wrapCharacterDifferences(line1: string, line2: string, type: 'added' | 'removed'): string {
    let result = '';
    for (let i = 0; i < line1.length; i++) {
      if (i < line2.length && line1[i] === line2[i]) {
        result += `<span>${line1[i]}</span>`;
      } else {
        const color = type === 'added' ? '#00c28166' : '#f53d3d66'; // Slightly darker green or coral for words
        result += `<span style="background-color: ${color};">${line1[i]}</span>`;
      }
    }
    return result;
  }

  statmentValue:string = ''
  statmentShow:boolean = false;
  stmtTV: number = 0;
  statmentClick(value:any){
    this.statmentShow = true
    this.statmentValue = value
    if (value != this.stmtTV) {
      this.stmtTV = value
    } else {
      this.statmentShow = false;
      this.stmtTV = 0
    }
  }

  
  /*--- Move Up Method ---*/
  moveUpCurrentStmtObj: any;
  moveUpCurrentStmt: any;
  moveUpGetCurrentStmtArray: any = [];
  isMerge: boolean = false
  temp_detailId: any

  stmtMoveSingleUp(data:string){
    this.isMerge = true
    this.moveUpCurrentStmtObj = []
    this.moveUpGetCurrentStmtArray = []
    this.temp_detailId = ""
    const inde = this.statementList.findIndex((x: any) => x.target_statemetnumber === data)
    this.statementList.forEach((ab: any) => {
      if (data == ab.target_statemetnumber) {
        this.moveUpCurrentStmtObj = ab.targetstatement_current
        ab.grid_changed = "yes"
      }
      if (ab.target_statemetnumber != "") {
        if (parseInt(data) > parseInt(ab.target_statemetnumber)) {
          this.moveUpGetCurrentStmtArray.push(ab)
        }
      }
    });
    this.moveUpCurrentStmt = this.moveUpGetCurrentStmtArray[this.moveUpGetCurrentStmtArray.length - 1].target_statemetnumber
    this.statementList.filter((el: any) => {
      if (this.moveUpCurrentStmt == el.target_statemetnumber) {
        //console.log(el.targetstatement_current)
        if (this.moveUpCurrentStmtObj.length >= 1) {
          if (this.moveUpCurrentStmtObj[0].tgtSmt == '') {
            this.moveUpCurrentStmtObj.splice(0, 1)
          }
          if (el.targetstatement_current.length > 0) {
            if (el.targetstatement_current[0].tgtSmt == "") {
              el.targetstatement_current.splice(0, 1)
            }
          }
        }

        if (this.moveUpCurrentStmtObj[0].tgtSmt == '') {
        } else {
          el.grid_changed = 'Yes'
          el.targetstatement_current.push(this.moveUpCurrentStmtObj[0])
        }
      }
      var tempdetailid = ""
      if (data == el.target_statemetnumber) {

        if (el.targetstatement_current.length > 0) {
          tempdetailid = el.targetstatement_current[0].target_deltacodedetailid
          el.targetstatement_current.splice(0, 1)
        }
        if (el.targetstatement_current.length == 0) {
          el.targetstatement_current = []

        }
      }
    })
  }


  isMove: boolean = false
  tempArrayTgt: any = []
  isEmptyStmtFlag: boolean = false;
  isMoveDownEmptyStmtFlag: boolean = false;
  emptyStmtCount: any;
  flag: any = 0
  TargetIds: any = []
  stmtMoveUp(data:any){
    this.isMerge = true
    this.tempArrayTgt = []
    this.emptyStmtCount = 0
    this.isEmptyStmtFlag = false
    const indexis = this.statementList.findIndex((x: any) => x.target_statemetnumber === data)
    for (let ind = parseInt(indexis); ind <= parseInt(indexis) && ind >= 0; ind--) {

      if (this.statementList[ind].targetstatement_current.length > 1) {
        if (this.isEmptyStmtFlag == false) {
          this.tempArrayTgt.push(parseInt(this.statementList[ind].target_statemetnumber))
          //console.log(this.statementList[ind].target_statemetnumber)
        }
        else {
          break;
        }
      }
      else {

        if (this.statementList[ind].targetstatement_current.length == 0) {
          this.tempArrayTgt.push(parseInt(this.statementList[ind].target_statemetnumber))
          this.emptyStmtCount++
          this.isEmptyStmtFlag = true
        }
        if (this.statementList[ind].targetstatement_current.length == 1) {
          if (this.statementList[ind].targetstatement_current[0].tgtSmt == "") {
            this.tempArrayTgt.push(parseInt(this.statementList[ind].target_statemetnumber))
            this.emptyStmtCount++
            this.isEmptyStmtFlag = true
          }

          else {
            if (this.isEmptyStmtFlag == false) {
              this.tempArrayTgt.push(parseInt(this.statementList[ind].target_statemetnumber))
              //console.log(this.statementList[ind].target_statemetnumber)
            }
            else {
              break;
            }
          }
        }
      }
    }
    this.tempArrayTgt.sort((a: any, b: any) => a - b);
    this.tempArrayTgt = this.tempArrayTgt.slice(this.emptyStmtCount, this.tempArrayTgt.length)
    for (let a = 0; a < this.tempArrayTgt.length; a++) {
      //console.log(this.tempArrayTgt[a])
    }
    if (this.isEmptyStmtFlag == true) {
      this.flag = 1
      if (this.flag == 1) {
        this.tempMethod(data);
      }
      this.flag = 1
    }
    else {
      this.toast.error("No empty grid exists for moving up.")
    }
  }

  tempMethod(data: any) {
    if (this.flag == 1) {
      for (let v = 0; v < this.tempArrayTgt.length; v++) {
        data = this.tempArrayTgt[v]
        this.moveUpGetCurrentStmtArray = []
        this.moveUpCurrentStmtObj = []
        this.statementList.forEach((ab: any) => {
          if (data == ab.target_statemetnumber) {
            this.moveUpCurrentStmtObj = ab.targetstatement_current
            ab.targetstatement_current = []
            ab.grid_changed = "yes"
          }
          if (ab.target_statemetnumber != "") {
            if (parseInt(data) > parseInt(ab.target_statemetnumber)) {
              this.moveUpGetCurrentStmtArray.push(ab)
            }
          }
        });
        const currentindex = this.statementList.findIndex((x: any) => x.target_statemetnumber == data)
        this.moveUpCurrentStmt = this.statementList[currentindex - this.emptyStmtCount].target_statemetnumber

        this.TargetIds.push(data)
        this.statementList.filter((el: any) => {
          //console.log(this.moveUpCurrentStmt)
          if (this.moveUpCurrentStmt == el.target_statemetnumber) {
            //console.log(el)
            //console.log(this.moveUpCurrentStmtObj)
            this.moveUpCurrentStmtObj.forEach((ba: any) => {
              if (ba.tgtSmt != '') {

                el.grid_changed = 'yes'
                if (el.targetstatement_current.length > 0) {

                  if (el.targetstatement_current[0].tgtSmt == "") {
                    el.targetstatement_current.splice(0, 1);
                  }
                }
                el.targetstatement_current.push(ba)
              }
            })
            el.targetstatement_current.push()
          }
        })
      }

    }
  }
  

  movedownEmptyStmtCount: any
  moveDownTempArray: any = []
  stmtMoveDown(data: any){
    this.isMerge = true
    this.tempArrayTgt = []
    this.movedownEmptyStmtCount = 0
    this.isMoveDownEmptyStmtFlag = false
    this.moveDownTempArray = []
    //console.log(this.statementList)
    const moveDoiwnIndex = this.statementList.findIndex((x: any) => x.target_statemetnumber === data)
    for (let ab = parseInt(moveDoiwnIndex); ab < this.statementList.length; ab++) {
      if (this.statementList[ab].targetstatement_current.length > 1) {
        if (this.isMoveDownEmptyStmtFlag == false) {
          this.moveDownTempArray.push(parseInt(this.statementList[ab].target_statemetnumber))
        }
        else {
          break;
        }
      }
      else {
        if (this.statementList[ab].targetstatement_current.length == 0) {
          this.moveDownTempArray.push(parseInt(this.statementList[ab].target_statemetnumber))
          this.movedownEmptyStmtCount++
          this.isMoveDownEmptyStmtFlag = true
        }
        if (this.statementList[ab].targetstatement_current.length == 1) {
          if (this.statementList[ab].targetstatement_current[0].tgtSmt == "") {
            this.moveDownTempArray.push(parseInt(this.statementList[ab].target_statemetnumber))
            this.movedownEmptyStmtCount++
            this.isMoveDownEmptyStmtFlag = true
          }

          else {
            if (this.isMoveDownEmptyStmtFlag == false) {
              this.moveDownTempArray.push(parseInt(this.statementList[ab].target_statemetnumber))
            }
            else {
              break;
            }
          }
        }
      }
    }
    this.moveDownTempArray.sort((a: any, b: any) => b - a);
    //console.log(this.moveDownTempArray)
    if (this.isMoveDownEmptyStmtFlag == true) {
      this.moveUpGetCurrentStmtArray = []
      this.moveUpCurrentStmtObj = []
      for (let z = 0; z < this.moveDownTempArray.length; z++) {
        data = this.moveDownTempArray[z]
        this.statementList.forEach((ab: any) => {
          if (data == ab.target_statemetnumber) {
            this.moveUpCurrentStmtObj = ab.targetstatement_current
            ab.targetstatement_current = []
            ab.grid_changed = "yes"
          }
          if (ab.target_statemetnumber != "") {
            if (parseInt(data) <= parseInt(ab.target_statemetnumber)) {
              this.moveUpGetCurrentStmtArray.push(ab)
            }
          }
        })
        // this.moveUpCurrentStmt = this.moveUpGetCurrentStmtArray[0].target_statemetnumber
        const currentindex = this.statementList.findIndex((x: any) => x.target_statemetnumber == data)
        if (this.statementList.length == currentindex + 1) {
          var indexes = currentindex - 1
        }
        else {
          indexes = currentindex + this.movedownEmptyStmtCount
        }
        if (indexes > this.statementList.length) {

          indexes = this.statementList.length - 1

        }
        if (indexes < this.statementList.length - 1) {
          this.moveUpCurrentStmt = this.statementList[indexes].target_statemetnumber
          //console.log(this.moveUpCurrentStmt)
          this.statementList.filter((el: any) => {
            if (this.moveUpCurrentStmt == el.target_statemetnumber) {
              if (this.moveUpCurrentStmtObj.length >= 2) {
                this.moveUpCurrentStmtObj.reverse();
              }
              this.moveUpCurrentStmtObj.forEach((ba: any) => {
                //console.log(ba)
                if (ba.tgtSmt != '') {
                  el.grid_changed = 'yes'
                  el.targetstatement_current.unshift(ba)
                }
              })
            }
          })
        }
      }
    }
    else {
      this.toast.error("No empty grid exists for moving down.")
    }
  }

  stmtMoveSingleDown(data: any){
    this.isMerge = true
    this.moveUpGetCurrentStmtArray = []
    this.moveUpCurrentStmtObj = []
    const indexis = this.statementList.findIndex((x: any) => x.target_statemetnumber === data)
    var nextno = parseInt(indexis) + 1
    if (this.statementList[nextno].target_statemetnumber == undefined || this.statementList[indexis + 1].target_statemetnumber == "") {
      this.statementList[nextno].IsMovedDownCopy = true
      this.statementList[nextno].status = "New"
      this.statementList[nextno].target_compareid = this.statementList[indexis].target_compareid,
        this.statementList[nextno].target_ids = "",
        this.statementList[nextno].target_statemetnumber = this.statementList[nextno].source_statemetnumber
      this.statementList[nextno].targetstatement_current = [
        {
          "tgtSmt": "",
          "textchanged": "",
          "target_deltacodedetailid": ""
        }
      ]
    }
    this.statementList.forEach((ab: any) => {
      if (data == ab.target_statemetnumber) {
        this.moveUpCurrentStmtObj = ab.targetstatement_current
        ab.grid_changed = "yes"
      }
      if (ab.target_statemetnumber != "") {
        if (parseInt(data) < parseInt(ab.target_statemetnumber)) {
          this.moveUpGetCurrentStmtArray.push(ab)
        }
      }
    })
    this.moveUpCurrentStmt = this.moveUpGetCurrentStmtArray[0].target_statemetnumber
    for (let rm = 0; rm < this.moveUpCurrentStmtObj.length; rm++) {
      if (this.moveUpCurrentStmtObj[rm].tgtSmt == "") {
        this.moveUpCurrentStmtObj.splice(rm, 1)
      }
    }
    this.statementList.filter((el: any) => {
      if (this.moveUpCurrentStmt == el.target_statemetnumber) {
        el.grid_changed = 'yes'
        if (this.moveUpCurrentStmtObj.length > 1) {
          if (this.moveUpCurrentStmtObj[0].tgtSmt == '') {
            this.moveUpCurrentStmtObj.splice(0, 1)
          }
        }
        if (this.moveUpCurrentStmtObj.length == 1) {
          if (this.moveUpCurrentStmtObj[0].tgtSmt == '') {
          }
          else {

            const indexOfMoveUpobj = 0
            el.targetstatement_current.unshift(this.moveUpCurrentStmtObj[indexOfMoveUpobj])
          }
        } else {
          if (this.moveUpCurrentStmtObj.length > 0) {
            if (this.moveUpCurrentStmtObj[0].tgtSmt == '') {
              const indexOfMoveUpobj = this.moveUpCurrentStmtObj.length - 1
              el.targetstatement_current.unshift(this.moveUpCurrentStmtObj[indexOfMoveUpobj])
            }
            else {
              const indexOfMoveUpobj = this.moveUpCurrentStmtObj.length - 1
              el.targetstatement_current.unshift(this.moveUpCurrentStmtObj[indexOfMoveUpobj])
            }
          }
        }
      }
    })
    this.statementList.forEach((el: any) => {
      if (data == el.target_statemetnumber) {
        if (this.moveUpCurrentStmtObj.length > 0) {
          const indexOfMoveUpob = this.moveUpCurrentStmtObj.length - 1
          el.targetstatement_current.splice(indexOfMoveUpob, 1)
        }
        if (el.targetstatement_current.length == 0) {
          el.targetstatement_current = []
        }
        else {
        }
      }
    })
  }

  addElement(data: any) {
    //console.log(data)
    this.moveUpGetCurrentStmtArray = []
    const indexis = this.statementList.findIndex((x: any) => x.target_statemetnumber === data)
    let getNewStmtNumber = data - 1
    let firstNumber = 0;
    if (getNewStmtNumber <= firstNumber) {
      alert("Maximum Statments Limit excuted")
    } else {
      this.statementList[indexis].isDisabled = true
      this.statementList.forEach((ab: any) => {
        if (getNewStmtNumber == ab.target_statemetnumber) {
          ////console.log(ab.target_statemetnumber)
          getNewStmtNumber = getNewStmtNumber - 1
        }
      });
      const addObject = {
        "grid_no": getNewStmtNumber,
        "grid_changed": 'new_grid',
        "source_compareid": this.statementList[indexis].source_compareid,
        "source_statemetnumber": getNewStmtNumber,
        "target_statemetnumber": getNewStmtNumber,
        "sourcestatement_current": "",
        "target_compareid": this.statementList[indexis].target_compareid,
        "created_by": '',
        "status": "New",
        "NewGrid": "yes",
        "targetstatement_current": [
          {
            "tgtSmt": "",
            "textchanged": "",
            "target_deltacodedetailid": ""
          }
        ]
      }
      this.statementList.splice((indexis), 0, addObject)
    }
    //console.log(this.statementList)

  }

  textareaChanged(data: any, event: any, i: any) {
    this.statementList.filter((el: any) => {
      if (el.target_statemetnumber == data) {
        if (el.targetstatement_current[0].tgtSmt == "") {
          el.targetstatement_current.splice(0, 1)
          i--;
        }
        if (el.NewGrid != 'yes' || el.NewGrid != undefined) {
          if (el.IsMovedDownCopy == false) {
            el.targetstatement_current[i].textchanged = "Yes"
            el.grid_changed = "yes"
          }
        }
        el.targetstatement_current[i].tgtSmt = event.target.innerText
      }
      const element = document.getElementById('tgstmt' + data);
      if (element) {
        const range = document.createRange();
        const selection = window.getSelection();
  
        // Cast to Node before using selectNodeContents
        range.selectNodeContents(element as Node);
        range.collapse(false);  // Collapse the range to the end (false = end of the range)
  
        // Clear previous selections and add the new one
        selection?.removeAllRanges();
        selection?.addRange(range);
      }
    })

    //console.log(this.statementList)
  }
  save_spin:boolean = false
  saveData() {
    //console.log(this.statementList)
    const temp_finalStmtList = this.statementList.filter((el: any) => {
      return el.grid_changed != ''
    })
    const finalStmtList: any = []
    //console.log(finalStmtList)
    for (let a = 0; a < temp_finalStmtList.length; a++) {
      temp_finalStmtList[a].projectId = this.projectId.toString()
      temp_finalStmtList[a].grid_no = temp_finalStmtList[a].grid_no.toString()
      temp_finalStmtList[a].source_statemetnumber = temp_finalStmtList[a].source_statemetnumber.toString()
      temp_finalStmtList[a].target_statemetnumber = temp_finalStmtList[a].target_statemetnumber.toString()
    }
    var moretextareas = temp_finalStmtList.filter((item: any) => {
      //having more than one one text ares
      if (item.targetstatement_current.length > 1) {
        item.grid_changed = "stmt_changed"
        finalStmtList.push(item)
      }
      //When text area is edited
      if (item.targetstatement_current.length > 0) {
        if (item.targetstatement_current[0].textchanged == "Yes" && item.IsCopywithId == false) {
          item.grid_changed = "stmt_changed"
          finalStmtList.push(item)
        }
      }
      if (item.targetstatement_current.length > 0) {
        if (item.old_deltaId != item.targetstatement_current[0].target_deltacodedetailid) {
          var compared = false
          var dt = item.targetstatement_current.filter((data: any) => {
            if (item.old_deltaId == data.target_deltacodedetailid) {
              compared = true
            }
          })
          if (compared != false) {
            item.targetstatement_current[0].target_deltacodedetailid = item.old_deltaId
          }
          if (item.IsMovedDownCopy == false) {
            var pushItem = finalStmtList.filter((it: any) => {
              return it.grid_no == item.grid_no
            })
            if (pushItem.length == 0) {
              item.old_target_detailid = "changed"
              finalStmtList.push(item);
            }
            if (pushItem.length > 0 && compared == false) {
              item.old_target_detailid = "changed"
            }
          }
        }
        if (item.targetstatement_current.length > 0) {
          if (item.IsCopywithId == true) {
            finalStmtList.push(item)
          }
        }
      }
      if (item.NewGrid == "yes") {
        finalStmtList.push(item)
      }
      if (item.targetstatement_current.length == 0) {
        if (item.old_deltaId != "") {
          item.old_target_detailid = "changed"
          item.targetstatement_current = [{
            "target_deltacodedetailid": "null"
          }]
          finalStmtList.push(item);
        }
      }
    })
    temp_finalStmtList.forEach((item: any) => {
      if (item.IsMovedDownCopy == true) {
        finalStmtList.push(item)
      }
    })
    let check = 0;
    temp_finalStmtList.forEach((item1: any) => {
      if (item1.IsMovedDownCopy == true) {
        if (item1.targetstatement_current.length == 1) {
          if (item1.targetstatement_current[0].tgtSmt == "") {
            finalStmtList.splice(check, 1)
          }
        }
      }
      check++
    })

    temp_finalStmtList.forEach((item2: any) => {
      if (item2.targetstatement_current.length > 0) {
        let inId = item2.targetstatement_current.findIndex((x: any) => x.tgtStatus === 'matching_with_source_deleted')
        if (inId >= 0) {
          item2.targetstatement_current.splice(inId, 1);
        }
      }
    })
    this.save_spin = true
    this.deploymentService.deltaUpdate(finalStmtList).subscribe((data: any) => {
      //this.saveGridData = data['jsonResponseData']['Table1']
      this.save_spin = false
      this.toast.success("Statements Updated Successfully")
      this.getObjects(this.selectedObject)
    },(error:any) => {
      this.save_spin = false
      this.toast.error(error)
    })
  }

  selectedView:string = ''
  getView(view:string){
    this.selectedView = view
  }
  
  generate_spin:boolean = false
  GenerateFile() {
    let obj = {
      task:'GENERATE_FILE',
      projectId:this.projectId.toString(),
      schema:this.selectedSchema,
      srcCon:this.connectionName,
      iteration:this.selectedRunNumber,
      objectName:this.selectedObject,
      viewOption: this.selectedView,
      ObjectCategory:this.selectedObjectType
    }
    this.generate_spin = true
    this.deploymentService.deltaGenerate(obj).subscribe( (data:any) => {
      this.generate_spin = false
      this.toast.success("File generated successfully")
    },(error:any) => {
      this.generate_spin = false
      this.toast.error(error)
    })
  }
  deploy_spin:boolean = false
  DeployFile() {
    let obj = {
      task:'DEPLOY',
      projectId:this.projectId.toString(),
      schema:this.selectedSchema,
      tgtCon:this.deployForm.value.targetConnection,
      srcCon:this.connectionName,
      iteration:this.selectedRunNumber,
      objectName:this.selectedObject,
      viewOption:this.selectedView,
      ObjectCategory:this.selectedObjectType,
      minutes:this.deployForm.value.deployTime?.toString(),
      observationText:"'" + this.deployForm.value.observation + "'"
    }
    this.deploy_spin = true

    this.deploymentService.deltaGenerate(obj).subscribe( (data:any) =>{
      this.deploy_spin = false
      this.toast.success("File deployed successfully")
      this.deployForm.reset()
    }, (error:any) => {
      this.deploy_spin = false
      this.toast.error(error)
      this.deployForm.reset()
    })
  }

  /*-get source and target connections-*/
  GetConsList() {
    this.deploymentService.getConList(this.projectId?.toString()).subscribe((data: conList) => {
      //console.log(this.ConsList)
      this.tgtlist = data['Table1'].filter((item: any) => {
        return item.migsrctgt == 'T' && item.conname != '';
      });
      //console.log(this.tgtlist)
    });
  }

  moveRight(data: any, data1: any) {
    //console.log(data, data1)
    const indexis = this.statementList.findIndex((x: any) => x.source_statemetnumber === data)
    if (this.statementList[indexis].targetstatement_current.length == 0) {
      this.statementList[indexis].IsMovedDownCopy = true
      this.statementList[indexis].status = "New"
      this.statementList[indexis].target_compareid = this.statementList[indexis].target_compareid,
        this.statementList[indexis].target_ids = "",
        this.statementList[indexis].target_statemetnumber = this.statementList[indexis].source_statemetnumber
      this.statementList[indexis].targetstatement_current = [
        {
          "tgtSmt": "",
          "textchanged": "",
          "target_deltacodedetailid": ""
        }
      ]
      data1 = this.statementList[indexis].target_statemetnumber
    }
    else {
      if (this.statementList[indexis].targetstatement_current[0].target_deltacodedetailid == undefined || this.statementList[indexis].targetstatement_current[0].target_deltacodedetailid == "") {
        this.statementList[indexis].IsMovedDownCopy = true
        this.statementList[indexis].status = "New"
        this.statementList[indexis].target_compareid = this.statementList[indexis].target_compareid,
          this.statementList[indexis].target_ids = "",
          this.statementList[indexis].target_statemetnumber = this.statementList[indexis].source_statemetnumber
        this.statementList[indexis].targetstatement_current = [
          {
            "tgtSmt": "",
            "textchanged": "",
            "target_deltacodedetailid": ""
          }
        ]
        data1 = this.statementList[indexis].target_statemetnumber
      }
      else {
        this.statementList[indexis].IsCopywithId = true
      }
    }
    this.statementList.filter((el: any) => {
      if (el.source_statemetnumber == data && el.target_statemetnumber == data1) {
        if (el.sourcestatement_current != "") {

          el.grid_changed = 'yes'
          el.targetstatement_current[0] = {
            "tgtSmt": el.sourcestatement_current,
          }

        }
        else {
          this.toast.error("Source Statment is Empty")
        }
        //console.log(el)
      }
    })
    //console.log(this.statementList)
  }
  //formatValue:any ;
  fileResult:any
  fileName:any
  format_spin:boolean = false
  saveOpData() {
    this.format_spin = true
    var el = <HTMLInputElement>document.getElementById('save_id')
    el.disabled = true;
    //this.formatValue = ""
    //this.formatValue = (<HTMLInputElement>document.getElementById('tgttxt_area')).value
    //console.log(this.targetObject)
    const currentindex = this.objectNames.findIndex((x: any) => x.objectname == this.selectedObject)
    this.objectNames.splice(currentindex, 1);
    this.changeObjectStatus();
    const blob = new Blob([this.targetObject], { type: 'text/plain' });
    const fileName = this.selectedObject;
    this.fileResult = blob
    this.fileName = fileName + ".sql";
    this.UploadBinaryFileToShare();
  }

  changeStatus: any
  changeObjectStatus() {
    let obj = {
      projectId: this.projectId.toString(),
      schemaName: this.selectedSchemaID,
      RunNo: this.selectedRunNumber,
      ObjStatus: "Inactive",
      objectName: this.selectedObject,
      acl: ''
    }
    this.deploymentService.changeObjStatus(obj).subscribe((data: any) => {
      this.changeStatus = data['jsonResponseData']['Table1']
      this.format_spin = false
      this.toast.success("Status " + this.changeStatus[0].v_status)
    },
    (error:any) => {
      this.format_spin = false
      this.toast.error('Status not updated!');
    })
  }

  UploadBinaryFileToShare() {
    const sourceFileUpload: FormData = new FormData();
    sourceFileUpload.append('file', this.fileResult, this.fileName);
    sourceFileUpload.append('path', this.selectedRunNumber + "/Delta_process/Target/Database_current/" + this.selectedSchema + "/" + this.selectedObjectType + "/" + this.fileName);
    const filePath = this.selectedRunNumber + "/Delta_process/Target/Database_current/" + this.selectedSchema + "/" + this.selectedObjectType + "/" + this.fileName
    this.deploymentService.UploadCloudFiles(sourceFileUpload).subscribe( (ba:any) => console.log(ba))
  }
  deployTime_spin:boolean = false
  deployStatusList:any;
  getreqTableData(){
    let obj = {
      projectId: this.projectId.toString(),
      iteration: this.selectedRunNumber,
      objectName: this.selectedObject,
      schemaId: this.selectedSchemaID,
    };
    this.deployTime_spin = true
    this.deploymentService.getDeploymentStatus(obj).subscribe( (data:any) => {
      this.deployStatusList = data['Table1']
      this.deployTime_spin = false
    })
  }
  observations: any;
  statusMessages: any;
  deploystatus: any;
  deployTime: any

  selectTimestamp(value: any) {
    let TgtDeployStatusSelect = this.deployStatusList?.filter((item: any) => {
      return item.activitytime == value;
    });
    this.deployTime = TgtDeployStatusSelect[0]?.time_taken_min;
    this.statusMessages = TgtDeployStatusSelect[0]?.statusmessage;
    this.observations = TgtDeployStatusSelect[0]?.observations;
    this.deploystatus = TgtDeployStatusSelect[0]?.deploystatus;
  }

}
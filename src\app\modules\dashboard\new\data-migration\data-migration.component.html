<section class="dashboard_reports">
    <div class="row">
        <div class="col-md-12">
            <dash-tabs></dash-tabs>
        </div>
    </div>
    <!--- Main Content --->
    <div class="qmigTabs mt-3">
        <div class="row">
            <div class="col-md-2 pe-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #sc
                        (change)="onSourceConnectionChange(sc.value);getCPUData(sc.value)">
                        <option selected disabled>Select Source Connection</option>
                        @for ( src of ConsList; track src) {
                        <option value="{{ src.Connection_ID }}">{{ src.conname }}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #schemaSelect
                        (change)="onSchemaChange(schemaSelect.value)">
                        <option selected disabled>Select Source Schema Name</option>
                        @for(sc of schemaList; track sc;){
                        <option value="{{sc.schemaname}}">{{sc.schemaname}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #tgtsc
                        (change)="ontargetConnectionChange(tgtsc.value)">
                        <option selected disabled>Select Target Connection</option>
                        @for(tgt of tgtList;track tgt; ){
                        <option value="{{tgt.Connection_ID}}">{{tgt.conname}}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #tgtsch (change)="ontgtSchemaChange(tgtsch.value)">
                        <option selected disabled>Select Target Schema Name</option>
                        @for ( sche of tgtSchemaList; track sche) {
                        <option value="{{ sche.schemaname }}"> {{sche.schemaname }}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <select class="form-select form-small m-w100" #srctab (change)="onTableChange(srctab.value)">
                        <option selected disabled>Select Tables</option>
                        @for (obj of ObjectNamesList; track obj) {
                        <option [value]="obj.objectName">{{ obj.objectName }}</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-2 px-1 mt-1">
                <div class="form-group mb-0">
                    <button class="btn btn-upload w-100 " (click)="openModal()" data-bs-toggle="offcanvas"
                        data-bs-target="#demo"> <span class="mdi mdi-checkbox-marked-circle-outline"
                            aria-hidden="true"></span>Check Details</button>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3">
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Total Table Size in Source</p>
                            <h4 class="mt-1">{{ gb }} GB</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Total Table Size in Target</p>
                            <h4 class="mt-1">{{Tgtgb}} GB</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Source Row Count</p>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/chart.svg" alt="db" />
                        </div>
                    </div>
                    <h4 class="mt-1">{{ numRows }}</h4>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Target Row Count</p>
                            <h4 class="mt-1">{{TGtnumRows}}</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Total tables Count</p>
                            <h4 class="mt-1">{{totalTablesCount}}</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Migrated Tables Count</p>
                            <h4 class="mt-1">{{extractedTablesCount}}</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-3">
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Source Total CPU </p>
                            <h4 class="mt-1">{{totalCpuCores}}</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Source CPU Utilization</p>
                            <h4 class="mt-1">{{utilizedCpu}}</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Sorce Total Memory </p>
                            <h4 class="mt-1">{{totalMemoryGb}}</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Source Memory Utilization</p>
                            <h4 class="mt-1">{{memoryUtilizationGb}}</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/database.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-3">

    </div>
    <div class="row mt-3">
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <div class="row">
                        <div class="col-9">
                            <p>Through Put</p>
                            <h4 class="mt-1">{{troughput}}Mbps</h4>
                        </div>
                        <div class="col-3">
                            <img src="../../../../../assets/images/dashboard/chart1.svg" alt="db" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-2 col-lg-2 col-xl-2">
            <div class="qmig-card h-100">
                <div class="qmig-card-body d-flex flex-column justify-content-between h-100">
                    <h5>Total Progress</h5>
                    <h3>
                        @if(TGtnumRows){
                        {{ TGtnumRows / numRows | percent : "1.0-2" }}
                        }@else{
                        0
                        }
                    </h3>
                    <!-- <div style="display: block;">
                        <canvas id="myDoughnutChart" baseChart width="100" height="120" #chart3="base-chart"
                            [type]="'doughnut'" [data]="lineChartData" [options]="lineChartOptions"
                            [legend]="lineChartLegend" [plugins]="lineChartPlugins">
                        </canvas>
                    </div> -->
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-3 dashCH">
        <div class="col-md-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Table size: Source vs Target </h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart [datasets]="sourceVSChartData"
                            [labels]="sourceVSChartLabels" [options]="sourceVSChartOptions"
                            [legend]="sourceVSChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>CPU Utilization: Source CPU Precentage </h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart [datasets]="CPUsourceVSChartData"
                            [labels]="CPUsourceVSChartLabels" [options]="CPUsourceVSChartOptions"
                            [legend]="CPUsourceVSChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="qmig-card">
                <div class="qmig-card-body">
                    <h5>Memory Percentage: Source </h5>
                    <div style="display: block;">
                        <canvas width="400" height="200" baseChart #chart1="base-chart" [datasets]="sObjectChartData"
                            [labels]="sObjectChartLabels" [options]="sObjectChartOptions" [legend]="sObjectChartLegend">
                        </canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Code Scan</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <!--- Connection Name --->
        <div class="offcanvas-body">
            <form class="form qmig-Form checkForm">
                <div class="form-group mb-0">
                    <label class="form-label d-required" for="targtetConnection">Source Connection</label>
                    <select class="form-select form-small m-w100" (change)="onSourceChange($event)">
                        <option selected disabled>Select Source Connection</option>
                        @for ( dtsrc of ConsList; track dtsrc) {
                        <option value="{{ dtsrc.Connection_ID }}">{{ dtsrc.conname }}</option>
                        }
                    </select>
                </div>
                <!--- Operation--->
                <!-- <div class="form-group mb-0">
                    <label class="form-label d-required" for="targtetConnection">Target Connection</label>
                    <select class="form-select form-small m-w100" (change)="onTargetChange($event)">
                        <option selected disabled>Select Target Connection</option>
                        @for(dttgt of tgtList;track dttgt; ){
                        <option value="{{dttgt.Connection_ID}}">{{dttgt.conname}}</option>
                        }
                    </select>
                </div> -->
                <!--- Execute--->
                <!-- projectConRunTblInserts(AssessmentForm.value, false)" -->
                <div class="form-group">
                    <div class="body-header-button">
                        <button type="button" class="btn btn-upload w-100 me-1" (click)="DatamigrationCommand()">
                            <span></span> Execute
                        </button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</section>
<div class="v-pageName">{{pageName}}</div>
<!--- Performance optimisation List --->
<div class="body-main mt-4" [hidden]="Monitdate">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form" [formGroup]="perfForm">
                <div class="row">
                    <!--- Category  --->
                    <div class="col-md-3 col-xl-3"  >
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Category</label>
                            <select class="form-select" #cat (change)="selectCategory(cat.value)"
                                formControlName="category">
                                <option>Select Category</option>
                                @for(cat of categoryList;track cat; ){
                                <option value="{{ cat.values }}"> {{ cat.option }} </option>
                                }
                            </select>
                            <div class="alert">
                                @if(validate.category.touched && validate.category.invalid) {
                                <p class="text-start text-danger mt-1">Category is required
                                </p>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-xl-3" >
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Connection Type</label>
                            <select class="form-select" #conty (change)="selectContype(conty.value)"
                                formControlName="connectionType">
                                <option>Select Connection Type</option>
                                <option value="0">Database</option>
                                <option value="1" [hidden]="hideDrpdwn || hideMon || hideLog || Monitdate">File</option>
                            </select>
                            <div class="alert">
                                @if(validate.connectionType.touched && validate.connectionType.invalid) {
                                <p class="text-start text-danger mt-1">Connection Type is required
                                </p>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-xl-3"
                        [hidden]="sourceHide || hideMon || hideLog || hideTable || hideQuery">
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">Source Connection
                                Name</label>
                            <select #Myselect1 (change)="
                            selectedfiletype(Myselect1.value);
                            getSchemasList(Myselect1.value)
                          " class="form-select" formControlName="connection">
                                <option>Select Connection Name</option>
                                @for(list of ConsList;track list; ){
                                <option value="{{ list.Connection_ID }}"> {{list.conname }} </option>
                                }
                            </select>
                            <div class="alert">
                                @if(validate.connection.touched && validate.connection.invalid) {
                                <p class="text-start text-danger mt-1">Source Connection required
                                </p>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection"> Connection
                                Name</label>
                            <select formControlName="tgtconnection" #Myselect2 (change)="
                            selTgtId(Myselect2.value);GetperfSchemas() " class="form-select">
                                <option>Select Connection Name</option>
                                @for(list of tgtList;track list; ){
                                <option value="{{ list.Connection_ID }}"> {{ list.conname }} </option>
                                }
                            </select>
                            <div class="alert">
                                @if(validate.tgtconnection.touched && validate.tgtconnection.invalid) {
                                <p class="text-start text-danger mt-1">Connection Name required
                                </p>
                                }
                            </div>
                        </div>
                    </div>
                    @if(conty.value==="1"){
                    <div class="col-md-3 col-xl-3">
                        <div class="form-group">
                            <label class="form-label d-required" for="targtetConnection">File Name</label>
                            <select class="form-select" #Myselect3 (change)="
                                    selectedfileName(Myselect3.value);
                                    " class="form-select" formControlName="fileName">
                                <option>Select File Name</option>
                                @for(list of ftsFiles;track list; ){
                                <option value="{{list.fileName}}">{{list.fileName}}</option>
                                }
                            </select>
                        </div>
                    </div>
                    }
                    <div class="col-md-3 col-xl-3 "
                        [hidden]="hideDrpdwn || hideMon || hideLog || hideTable || hideQuery"
                        [ngClass]="cat.value == 'FTS'? 'mt-4':'offset-md-9'  ">
                        <div class="body-header-button">
                            <button class="btn btn-upload w-100" data-bs-toggle="offcanvas" data-bs-target="#demo"
                                (click)="Execute(perfForm.value)"> <span class="mdi mdi-cog-play"></span>
                                Execute</button>
                        </div>
                    </div>
                    <div class="row" [hidden]="hidedata">
                        <div class="col-md-3 col-xl-3">
                            <div class="body-header-button">
                                <input class="form-control" type="file" id="formFile">
                            </div>
                        </div>
                        <div class="col-md-3 col-xl-3">
                            <div class="body-header-button">
                                <button class="btn btn-upload" data-bs-toggle="offcanvas"
                                    data-bs-target="#demo">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>

<!--- Duplicate index validation Title --->
<div class="body-main mt-4" [hidden]="!hideDrpdwn">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form">
                <div class="body-header">
                    <h3 class="main_h">Duplicate index Validation</h3>
                </div>
                <div class="row">
                    <!--- Duplicate index Validation --->
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <select class="form-select" #dup (change)="selectDuplicate(dup.value)"
                                formControlName="duplicateIndex">
                                <option>Select duplicate indexes</option>
                                <option value="0">Select All schemas</option>
                                <option value="1">Select schema wise</option>
                            </select>

                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group" [hidden]="!hideD">
                            <ng-select [placeholder]="'Select Schema'" [items]="perSchema" [multiple]="true"
                                (change)="selectSchema(selectedObjItems1)" bindLabel="schemaname" groupBy="gender"
                                [selectableGroup]="true" [closeOnSelect]="false" bindValue="schemaname"
                                [(ngModel)]="selectedObjItems1" [ngModelOptions]="{standalone: true}">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                        [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <div class="body-header-button">
                                <button class="btn btn-upload w-100" data-bs-toggle="offcanvas" data-bs-target="#demo"
                                    (click)="GetDuplicateIndexData()">Submit
                                    @if(Exespinner){<app-spinner />}</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <button class="btn btn-sign w-100" (click)="exportexcelAnalyze()">
                            <i class="mdi mdi-download "></i> Download
                            @if(Exespinner){<app-spinner />}</button>
                    </div>
                </div>
            </form>
        </div>
        <div [hidden]="!TableData">
            <!-- <h3 class="main_h px-3 pb-1 pt-3">Roles</h3> -->
            <div class="table-responsive">
                <table class="table table-hover qmig-table">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>SchemaName</th>
                            <th>TableName</th>
                            <th>IndexColumns</th>
                            <th>IndexName</th>
                            <th>IndexType</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (documents of duplicateIndexData | paginate: { itemsPerPage: 10,
                        currentPage: pageNumber } ; track documents) {
                        <tr>
                            <td>{{pageNumber*10+$index+1-10}}</td>
                            <td>{{documents.schemaname}}</td>
                            <td>{{documents.tablename}}</td>
                            <td>{{documents.indexColumns}}</td>
                            <td>{{documents.indexname}}</td>
                            <td>{{documents.indexType}}</td>

                        </tr>
                        } @empty {
                        <tr>
                            <td colspan="4">
                                <p class="text-center m-0 w-100">No Results</p>
                            </td>
                        </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="custom_pagination">
                <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
            </div>
        </div>
    </div>
</div>
<!--- Database Index Monitor --->
<div class="body-main mt-4" [hidden]="!hideMon">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form">
                <div class="body-header">
                    <h3 class="main_h">Database Index Monitoring</h3>
                </div>
                <div class="row">
                    <!--- Database index Validation --->
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">

                            <select class="form-select" #mon (change)="selectOption(mon.value)">
                                <option>Select</option>
                                <!-- <option value="0">Created Indexes</option>
                                <option value="1">Dropped Indexes</option> -->
                                <option value="2">Full Index Report</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group" [hidden]="!hidedate">
                            <label class="d-block" for="name"></label>
                            <input type="date" class="form-control" formControlName="fromDate" id="selectedFromDate">
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group" [hidden]="!hidedate">
                            <label class="d-block" for="name"></label>
                            <input type="date" class="form-control" formControlName="toDate" id="selectedToDate">
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <div class="body-header-button">
                                <button class="btn btn-upload w-100" data-bs-toggle="offcanvas"
                                    (click)="GetPerfIndexMonitorData()">Submit
                                    @if(Monspinner){<app-spinner />}</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <button class="btn btn-sign w-100" (click)="exportexcelMonitor()">
                            <i class="mdi mdi-download "></i> Download
                            @if(Monspinner){<app-spinner />}</button>
                    </div>
                </div>
            </form>
        </div>
        <div [hidden]="!Monitdate">
            <div class="table-responsive">
                <table class="table table-hover qmig-table">
                    <thead>
                        <tr>
                            <th>S.No</th>
                            <th>Type</th>
                            <th>SchemaName</th>
                            <th>TableName</th>
                            <th>IndexName</th>
                            <th>LogDate</th>
                        </tr>
                    </thead>
                    <tbody>
                        @for (documents of indexMonitor | paginate: { itemsPerPage: 10, currentPage:
                        pageNumber } ; track documents;) {
                        <tr>
                            <td>{{pageNumber*10+$index+1-10}}</td>
                            <td>{{documents.type}}</td>
                            <td>{{documents.schemaname}}</td>
                            <td>{{documents.tablename}}</td>
                            <td>{{documents.indexname}}</td>
                            <td>{{documents.logdate}}</td>
                        </tr>
                        } @empty {
                        <tr>
                            <td colspan="4">
                                <p class="text-center m-0 w-100">No Results</p>
                            </td>
                        </tr>
                        }
                    </tbody>
                </table>
            </div>
            <div class="custom_pagination">
                <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
            </div>
        </div>
    </div>
</div>
<!--- Log Explain Plan --->
<div class="body-main mt-4" [hidden]="!hideLog">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form">
                <div class="body-header">
                    <h3 class="main_h">Call Statement</h3>
                </div>
                <div class="row">
                    <!--- Log Explain  --->
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group">
                            <select class="form-select" #plan (change)="selectLog(plan.value)">
                                <option>Select duplicate indexes</option>
                                <option value="0">Show Logs</option>
                                <option value="1">CallStatement</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group" [hidden]="!hideS">
                            <label class="d-block" for="name"></label>
                            <input type="date" class="form-control" id="date" formControlName="Date">
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group" [hidden]="!hideS">
                            <input type="text" class="form-control" formControlName="connectionType"
                                placeholder="SchemaName" id="sch_name">
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group" [hidden]="!hideS">
                            <input type="text" class="form-control" formControlName="connectionType"
                                placeholder="ProcudureName" id="Prodc_name">
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="body-header-button" [hidden]="!hideS">
                            <button class="btn btn-upload w-100" data-bs-toggle="sch_pro"
                                (click)="SubmitShowLogs()">Submit
                                @if(Monspinner){<app-spinner />}</button>
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="form-group" [hidden]="!hideL">
                            <input type="text" class="form-control" formControlName="connectionType"
                                placeholder="Call Statement" id="call_stmt">
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                        <div class="body-header-button" [hidden]="!hideL">
                            <button class="btn btn-upload w-100" data-bs-toggle="offcanvas" data-bs-target="#callst"
                                (click)="InputCall()">Submit
                                @if(Monspinner){<app-spinner />}</button>
                        </div>
                    </div>
                </div>
                <div>
                    <!-- <h3 class="main_h px-3 pb-1 pt-3">Roles</h3> -->
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>SchemaName</th>
                                    <th>Procedure</th>
                                    <th>Date</th>
                                  
                                </tr>
                            </thead>
                            <tbody>
                                @for (documents of Showlogsdata | paginate: { itemsPerPage: 10,
                                currentPage: pageNumber } ; track documents) {
                                <tr>
                                    <td>{{pageNumber*10+$index+1-10}}</td>
                                    <td>{{documents.schema}}</td>
                                    <td>{{documents.procedure}}</td>
                                    <td>{{documents.exec_date}}</td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">No Results</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!--- Table Partition Table --->
<div class="body-main mt-4" [hidden]="!hideTable">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form">
                <div class="body-header">
                    <h3 class="main_h">Table Partion Startegy</h3>
                </div>
                <div class="row">
                    <!--- Table Partition --->
                    <div class="body-main mt-4">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <select class="form-select" #dup (change)="selectDuplicate(dup.value)"
                                        formControlName="duplicateIndex">
                                        <option>Select Partition Report</option>
                                        <option value="0">Full Report</option>
                                        <option value="1">Partial Report</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <div class="body-header-button">
                                        <button class="btn btn-upload w-100" data-bs-toggle="offcanvas"
                                            (click)="GetTablePartition()">Submit
                                            @if(Monspinner){<app-spinner />}</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <button class="btn btn-sign w-100" (click)="exportexcelTablePartition()">
                                    <i class="mdi mdi-download "></i> Download
                                    @if(Monspinner){<app-spinner />}</button>
                            </div>
                        </div>
                        <div [hidden]="false">
                            <div class="table-responsive">
                                <table class="table table-hover qmig-table">

                                    <thead>
                                        <tr>
                                            <th>S.No</th>
                                            <th>ProcudureName</th>
                                            <th>RowCount</th>
                                            <th>TableName</th>
                                            <th>TableSize</th>
                                            <th>ColumnDetails</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for (documents of PartitonTableData | searchFilter: searchText
                                        | paginate: {
                                        itemsPerPage: 10, currentPage: pageNumber } ; track documents; )
                                        {
                                        <tr>
                                            <td>{{pageNumber*10+$index+1-10}}</td>
                                            <td>{{documents.procudurename}}</td>
                                            <td>{{documents.rowcount}}</td>
                                            <td>{{documents.tablename}}</td>
                                            <td>{{documents.tablesize}}</td>
                                            <td>{{documents.columndetails}}</td>
                                        </tr>
                                        } @empty {
                                        <tr>
                                            <td colspan="4">
                                                <p class="text-center m-0 w-100">Empty list of Documents
                                                </p>
                                            </td>
                                        </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            <div class="custom_pagination">
                                <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4"></div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label d-required">Upload File</label>
                                    <div class="custom-file-upload">
                                        <input class="form-control" type="file" id="formFile" />
                                        <div class="file-upload-mask">
                                            @if (fileName == '') {
                                            <img src="assets/images/fileUpload.png" alt="img" />
                                            <p>Drop the file here or click add file </p>
                                            <button class="btn btn-upload"> Add File </button>
                                            }
                                            <div class="d-flex justify-content-center align-items-center h-100 w-100">
                                                <p> {{ fileName }} </p>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <button class="btn btn-upload w-100" (click)="uploadFile()">
                                                <span class="mdi mdi-file-plus"></span>
                                                Upload File
                                                @if(uploadfileSpin){<app-spinner />}</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-9"></div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <button class="btn btn-upload w-100" (click)="uploadFile()"><span
                                            class="mdi mdi-file-plus"></span>Upload
                                        File
                                        @if(uploadfileSpin){<app-spinner />}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<!--- Long Run Queries --->
<div class="body-main mt-4" [hidden]="!hideQuery">
    <div class="qmig-card">
        <div class="qmig-card-body">
            <form class="form qmig-Form">
                <div class="body-header">
                    <h3 class="main_h">Long Run Queries</h3>
                </div>
                <div class="row">
                    <!--- Long run query --->
                    <div class="body-main mt-4">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="d-block" for="name"></label>
                                    <input type="datetime-local" class="form-control" formControlName="fromDate" id="selectedFromDate">
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="d-block" for="name"></label>
                                    <input type="datetime-local" class="form-control" formControlName="toDate" id="selectedToDate">
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">

                                    <button class="btn btn-upload w-100" data-bs-toggle="offcanvas"
                                    (click)="getLongRunningQueries()">Submit
                                        @if(Monspinner){<app-spinner />}</button>

                                </div>
                            </div>
                        </div>
                        <div [hidden]="false">
                            <div class="table-responsive">
                                <table class="table table-hover qmig-table">
                                    <thead>
                                        <tr>
                                            <th>S.No</th>
                                            <th>CallStatement Name</th>

                                        </tr>
                                    </thead>
                                    <!-- <tbody>
                                        @for (documents of PartitonTableData | searchFilter: searchText
                                        | paginate: {
                                        itemsPerPage: 10, currentPage: pageNumber } ; track documents; )
                                        {
                                        <tr>
                                            <td>{{pageNumber*10+$index+1-10}}</td>
                                            <td>{{documents.procudurename}}</td>
                                            <td>{{documents.rowcount}}</td>
                                            <td>{{documents.tablename}}</td>
                                            <td>{{documents.tablesize}}</td>
                                            <td>{{documents.columndetails}}</td>
                                        </tr>
                                        } @empty {
                                        <tr>
                                            <td colspan="4">
                                                <p class="text-center m-0 w-100">Empty list of Documents
                                                </p>
                                            </td>
                                        </tr>
                                        }
                                    </tbody> -->
                                </table>
                            </div>
                            <div class="custom_pagination">
                                <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!--- Reports and Logs --->
<!-- <div class="qmig-card mt-3" [hidden]="true">
    <div class="qmig-card-body">
        <h3 class="main_h">Reports</h3>
        <div class="accordion accordion-flush qmig-accordion" id="accordionFlushExample">
            <div class="accordion-item">
                <h2 class="accordion-header" id="flush-headingOne">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                        data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                        Execution Reports
                    </button>
                </h2>
                <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                    data-bs-parent="#accordionFlushExample">
                    <div class="row">


                        <div class="form-group">
                            <label class="form-label " for="targtetConnection"><var></var></label>
                            <select class="form-select">
                                <option>Select Dags</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2 col-xl-2">
                        <div class="form-group">
                            <label class="form-label" for="targtetConnection"><var></var></label>
                            <select class="form-select">
                                <option>Select Run Id</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2 col-xl-2">
                        <div class="form-group">
                            <label class="form-label" for="targtetConnection"><var></var></label>
                            <select class="form-select">
                                <option>Select Task Id</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6 col-xl-6 mt-4">
                        <div class="custom_search mb-3">
                            <span class="mdi mdi-magnify"></span>
                            <input type="text" placeholder="Search Execution Reports" class="form-control"
                                [(ngModel)]="searchText">
                        </div>
                    </div>
                </div>

                <div class="body-main mt-4">
                    <div class="qmig-card">
                        <h3 class="main_h px-3 pb-1 pt-3">Reports List</h3>
                        <table class="table table-hover qmig-table">
                            <thead>
                                <tr>
                                    <th>S.no</th>
                                    <th>File Name</th>
                                    <th>Folder Name</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (documents of ExecututionFiles | searchFilter: searchText | paginate: {
                                itemsPerPage: 10, currentPage: pageNumber } ; track documents; ) {
                                <tr>
                                    <td>{{pageNumber*10+$index+1-10}}</td>
                                    <td>{{documents.fileName }}</td>
                                    <td>Project Docs</td>
                                    <td>
                                        <button class="btn btn-delete" (click)="deleteFiles(documents.filePath)">
                                            <span class="mdi mdi-trash-can-outline"></span>
                                        </button>
                                        <button class="btn btn-download" (click)="downloadFile(documents)">
                                            <span class="mdi mdi-cloud-download-outline"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>
                        </table>
                        <div class="custom_pagination">
                            <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div> -->
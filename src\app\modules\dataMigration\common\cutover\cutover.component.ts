import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, RouterOutlet } from '@angular/router';
import { HotToastService } from '@ngxpert/hot-toast';
import { SpinnerComponent } from '../../../../shared/components/spinner/spinner.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SearchFilterPipe } from '../../../../shared/pipes/search-filter.pipe';

@Component({
  selector: 'app-cutover',
  standalone: true,
    imports: [ReactiveFormsModule, FormsModule, CommonModule, SpinnerComponent, NgxPaginationModule, SearchFilterPipe, RouterOutlet],
  templateUrl: './cutover.component.html',
  styles: ``
})
export class CutoverComponent {

  pageName:string = ''
  projectId: any;
  migtypeid: any;

  // Add User Form
  addUserForm = this.formBuilder.group({
    userName: ['', [Validators.required]],
    userEmail: ['', [Validators.required]]
  })
  adduserSpin:boolean = false;

  // Add Process Form
  addProcessForm = this.formBuilder.group({
    processName: ['', [Validators.required]]
  })
  addprocessSpin:boolean = false;

  // Add Process Table Form
  processTableForm = this.formBuilder.group({
    processStep: ['', [Validators.required]],
    owner: ['', [Validators.required]],
    status: ['', [Validators.required]],
    timetaken: ['', [Validators.required]],
    parallel: ['', [Validators.required]],
    remarks: ['', [Validators.required]]
  })
  addprocessTableSpin:boolean = false;

  constructor(
     private readonly toast: HotToastService,
     public formBuilder: FormBuilder , 
     private readonly route: ActivatedRoute, 
     private readonly titleService: Title
    ) {
    this.projectId = JSON.parse((localStorage.getItem('project_id') as string));
    this.pageName = this.route.snapshot.data['name'];
    this.migtypeid = JSON.parse((localStorage.getItem('migtypeid') as string));
  }

  ngOnInit(): void {
    this.titleService.setTitle(`QMigrator - ${this.pageName}`);
  }

  get f():any { return this.addUserForm.controls; }
  get p():any { return this.addProcessForm.controls; }
  get pt():any { return this.processTableForm.controls; }
}

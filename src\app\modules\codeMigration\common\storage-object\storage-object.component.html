<!-- <header -->
<div class="v-pageName">{{pageName}}</div>

<!-- get run number list -->
<div class="qmig-card">
    <div class="accordion accordion-flush qmig-accordion" id="accordionPanelsStayOpenExample">
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-heading">
                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapse" aria-expanded="true" aria-controls="flush-collapse">
                    Storage Objects Conversion
                </button>
            </h2>
            <div id="flush-collapse" class="accordion-collapse collapse show" aria-labelledby="flush-heading"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <form class="form qmig-Form" [formGroup]="execProjectForm">
                        <div class="row">
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Run No</label>
                                    <select class="form-select" #run (change)="getRunNumber(run.value)"
                                        formControlName="runNo">
                                        <option selected value="">Select Run No</option>
                                        @for( list of runnoForReports;track list;){
                                        <option value="{{ list.iteration}}">{{list.iteration}}</option>
                                        }
                                    </select>
                                    @if ( f.runNo.touched && f.runNo.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.runNo.errors.required) {Run No is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- source connection list -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group" form>
                                    <label class="form-label d-required" for="Schema">Source Connection Name</label>
                                    <select class="form-select" #Myselect1 (change)="
                                            
                                selectedfiletype(Myselect1.value);
                                " formControlName="connectionName">
                                        <option selected value="">Select Connection Name</option>
                                        @for(conlist of conList ;track conlist; ){
                                        <option value="{{conlist.Connection_ID}}">{{conlist.conname}}</option>
                                        }
                                    </select>
                                    @if ( f.connectionName.touched && f.connectionName.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.connectionName.errors.required) {ConnectionName is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Source {{schemalable}}
                                        Name</label>
                                    <!-- <select class="form-select" #sch (change)="selectschema(sch.value)"
                                        formControlName="schema">
                                        <option selected value="">Select {{schemalable}} </option>
                                        @for(sc of schemaList;track sc; ){
                                        <option value="{{sc.schema_name}}">{{sc.schema_name}}</option>
                                        }
                                    </select> -->

                                    <ng-select groupBy="type" [selectableGroup]="true"
                                        (change)="selectschema(selectedItems);validationstable()"
                                        formControlName="schema" [items]="schemaList" [multiple]="true"
                                        bindLabel="schema_name" [closeOnSelect]="false" bindValue="schema_name"
                                        [(ngModel)]="selectedItems">
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                [ngModelOptions]="{ standalone : true }" value={{item.schema_name}} />
                                            {{item.schema_name}}
                                        </ng-template>
                                    </ng-select>
                                    @if ( f.schema.touched && f.schema.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.schema.errors.required) {Source {{schemalable}} is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <!-- target connection list -->
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="Schema">Target Connection Name</label>

                                    <select class="form-select" #Myselect2 (change)="
                                        selTgtId(Myselect2.value);getSchemasList(Myselect2.value)"
                                        formControlName="targetconnection">
                                        <option selected value="">Select Target Connection Name</option>
                                        @for(tgtList of tgtList;track tgtList; ){
                                        <option value="{{tgtList.Connection_ID}}">{{tgtList.conname}}</option>
                                        }
                                    </select>
                                    @if ( f.targetconnection.touched && f.targetconnection.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.targetconnection.errors.required) {Target Connection is Required }
                                    </p>
                                    }
                                </div>
                            </div>
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Deploy Connection Name</label>
                                    <select class="form-select" #dep (change)="selectDeployCon(dep.value);"
                                        formControlName=" deployConnection">
                                        <option selected value="">Select Deploy Connection Name</option>
                                        @for(tgtList of tgtList;track tgtList; ){
                                        <option value="{{tgtList.Connection_ID}}">{{tgtList.conname}}</option>
                                        }
                                    </select>
                                    @if ( f.deployConnection.touched && f.deployConnection.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.deployConnection.errors.required) {Deploy Connection is Required }
                                    </p>
                                    }
                                </div>
                            </div>

                            <!-- get object type list -->
                            @if(isAll!="ALL"){
                            @if(showTargetSchemaAndTables){
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label" for="Schema">Deploy {{schemalable}}
                                        Name</label>
                                    <select class="form-select" #tgtsch (change)="selectTgtSchema(tgtsch.value)"
                                        formControlName="targetSchema">
                                        <option selected value="">Select Deploy {{schemalable}}</option>
                                        @for(tgtsc of tgtSchemaList;track tgtsc; ){
                                        <option value="{{tgtsc.schema_name}}">{{tgtsc.schema_name}}</option>
                                        }
                                    </select>
                                    <!-- @if ( f.targetSchema.touched && f.targetSchema.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.targetSchema.errors.required) {Target {{schemalable}} is Required }
                                    </p>
                                    } -->
                                    <!-- <ng-select [placeholder]="'Select Schema Name'" [items]="tgtSchemaList"
                                            (change)="selectTgtSchema(selectedItems)" [multiple]="true" bindLabel="schemaname"
                                            groupBy="gender" [selectableGroup]="true" [closeOnSelect]="false"
                                            bindValue="schemaname" [(ngModel)]="selectedItemstgt"
                                            [ngModelOptions]="{standalone: true}">
                                            <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                                <input id="item-{{index}}" type="checkbox" [(ngModel)]="item$.selected"
                                                    [ngModelOptions]="{ standalone : true }" /> {{item.schemaname}}
                                            </ng-template>
                                        </ng-select> -->
                                </div>
                            </div>
                            }
                            }
                            <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                <div class="form-group">
                                    <label class="form-label d-required" for="Schema">Object Type</label>
                                    <select class="form-select" #obj (change)="selectObj(obj.value)"
                                        formControlName="objectType">
                                        <option selected value="">Select Object type</option>
                                        @for(list of objectType;track list; ){
                                        <option value="{{list.object_type}}">{{list.object_type}}</option>
                                        }
                                    </select>
                                    @if ( f.objectType.touched && f.objectType.invalid) {
                                    <p class="text-start text-danger mt-1">
                                        @if (f.objectType.errors.required) {ObjectType is Required }
                                    </p>
                                    }
                                </div>
                            </div>

                            <!-- check details button -->
                            <div class="row">
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3"></div>
                                <div class="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                                    <div>
                                        <button (click)="openModal(this.execProjectForm.value)"
                                            [disabled]="execProjectForm.invalid" class="btn btn-upload w-100 mt-4"
                                            data-bs-toggle="offcanvas" data-bs-target="#demo">
                                            <span class="mdi mdi-checkbox-marked-circle-outline"
                                                aria-hidden="true"></span>
                                            Check Details</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- storage obj details based on reqtabledata    -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTest">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTest" aria-expanded="false" aria-controls="flush-collapse">
                    Execution Status
                </button>
            </h2>
            <div id="flush-collapseTest" class="accordion-collapse collapse" aria-labelledby="flush-headingTest"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-7 offset-5 d-flex">
                            <div class="custom_search cs-r my-3 me-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Status" class="form-control"
                                    [(ngModel)]="datachange1" (keyup)="onKey()">
                            </div>
                            <button class="btn btn-sync" (click)="getreqTableData()">
                                @if(ref_spin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover qmig-table" id="example" style="width: 100%">
                            <thead>
                                <tr>
                                    <th>RunNo</th>
                                    <th>Connection</th>
                                    <th>Operation</th>
                                    <th>Operation Category</th>
                                    <th>{{schemalable}} Name</th>
                                    <th>Object Type</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (con of tabledata |searchFilter: datachange1| paginate: { itemsPerPage: 10,
                                currentPage: page2 ,id:'second'} ;track con) {

                                <tr>
                                    <td>{{con.iteration_id}}</td>
                                    <td>{{ con.conname }}</td>
                                    <td>{{ con.operation_name }}</td>
                                    <td>{{ con.operation_category }}</td>
                                    <td>{{ con.schema_name }}</td>
                                    <td>{{ con.object_type }}</td>
                                    <td>{{con.created_dt}}</td>
                                    <td>{{con.updated_dt}}</td>
                                    <td>
                                        {{con.status}}
                                    </td>
                                    <td>
                                        <button (click)="deleteTableDatas(con.request_id)" class="btn btn-delete">
                                            <span class="mdi mdi-delete btn-icon-prepend"></span>
                                        </button>
                                    </td>
                                </tr>
                                } @empty {
                                <tr>
                                    <td colspan="4">
                                        <p class="text-center m-0 w-100">Empty list of Documents</p>
                                    </td>
                                </tr>
                                }
                            </tbody>

                        </table>
                    </div>
                    <!-- pagination -->
                    <div class="custom_pagination">
                        <pagination-controls (pageChange)="page2 = $event" id="second"></pagination-controls>
                    </div>

                </div>
            </div>
        </div>

        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingThree">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseThree" aria-expanded="false"
                    aria-controls="flush-collapseThree">Execution Logs
                </button>
            </h2>
            <div id="flush-collapseThree" class="accordion-collapse collapse" aria-labelledby="flush-headingThree"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-3 col-xl-4 mt-3">
                            <div class="form-group">
                                <select class="form-select" #storage (change)="selectIterforlogs(storage.value)">
                                    <option selected value="">
                                        Select Run Number
                                    </option>
                                    @for( list of runnoForReports;track list;){
                                    <option value="{{ list.iteration}}">{{list.iteration}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-3 col-xl-2"></div>
                        <div class="col-12 col-sm-6 col-md-6 col-xl-6 d-flex">
                            <div class="custom_search cs-r me-3 my-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Execution Logs " aria-controls="example"
                                    class="form-select" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="filterExecutionReports()">
                                @if(ref_spin1){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                    </div>
                </div>


                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(logs of ExecututionFiles |searchFilter: datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: page3, id:'Four'};
                            track logs){
                            <tr>
                                <td>{{ page3*10+$index+1-10 }}</td>
                                <td>{{logs.fileName }}</td>
                                <td>{{logs.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(logs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="page3 = $event" id="Four">
                    </pagination-controls>
                </div>
            </div>
        </div>

        <!-- deployment reports list -->
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTwo">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo" aria-expanded="false" aria-controls="flush-collapseTwo">
                    Deployment Logs
                </button>
            </h2>
            <div id="flush-collapseTwo" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 mt-3">
                            <div class="form-group">
                                <select class="form-select" #iter1 (change)="selectIterForLogs(iter1.value)">
                                    <option selected value="">
                                        Select Run Number
                                    </option>
                                    @for( list of runnoForReports;track list ;){
                                    <option value="{{ list.iteration}}">{{list.iteration}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <!-- select obj type -->
                        <div class="col-12 col-sm-6 col-md-3 col-lg-3 col-xl-2 mt-3">
                            <div class="form-group">
                                <select class="form-select" #obj1 (change)="selectObjType(obj1.value)"
                                    aria-label="Default select example">
                                    <option selected value="">Select Obj Type</option>
                                    @for( list of depObjTypes;track objectType;){
                                    <option value="{{ list.object_type}}">{{list.object_type}}
                                    </option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-md-6 col-lg-6 col-xl-8 d-flex">
                            <div class="custom_search cs-r me-3 my-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Deployment Logs" aria-controls="example"
                                    class="form-control" [(ngModel)]="datachangeLogs" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="fetchLogs()">
                                @if(ref_spin2){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(logs of depLogs |searchFilter: datachangeLogs|paginate:{
                            itemsPerPage: 10, currentPage: pageNumber, id:'second'};
                            track logs){
                            <tr>
                                <td>{{ pageNumber*10+$index+1-10 }}</td>
                                <td>{{logs.fileName }}</td>
                                <td>{{logs.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(logs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="pageNumber = $event"></pagination-controls>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingTwo2">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseTwo2" aria-expanded="false" aria-controls="flush-collapseTwo2">
                    Failed Objects
                </button>
            </h2>
            <div id="flush-collapseTwo2" class="accordion-collapse collapse" aria-labelledby="flush-headingTwo2"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 mt-3">
                            <div class="form-group">
                                <select class="form-select" #iter12 (change)="selectIterForLogs1(iter12.value)">
                                    <option selected value="">
                                        Select Run Number
                                    </option>
                                    @for( list1 of runnoForReports;track list1 ;){
                                    <option [value]="list1.iteration">{{list1.iteration}}</option>
                                    }
                                </select>
                                <!-- @if(ref_spin){
                                <i class="mdi mdi-sync mdi-spin"></i>
                                }@else{
                                <i class="mdi mdi-sync" (click)="getUpdatedRunNumber()"></i>
                                } -->
                            </div>
                        </div>
                        <div class="col-12 col-sm-3 col-md-3 col-lg-3 col-xl-3 mt-3">
                            <!-- <div class="form-group">
                                <select class="form-select" #obj12 (change)="selectObjType1(obj12.value)"
                                    aria-label="Default select example">
                                    <option selected value="">Select Obj Type</option>
                                    @for( list2 of depObjTypes;track list2;){
                                    <option value="{{ list2.object_type}}">{{list2.object_type}}
                                    </option>
                                    }
                                </select>
                            </div> -->
                        </div>
                        <div class="col-12 col-sm-12 col-md-6 col-lg-6 col-xl-6 d-flex">
                            <div class="custom_search cs-r me-3 my-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Deployment Logs" aria-controls="example"
                                    class="form-control" [(ngModel)]="datachangeLogs1" (keyup)="onKey()" />
                            </div>
                            <button class="btn btn-sync" (click)="fetchLogs1()">
                                @if(ref_spin2){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }

                            </button>
                        </div>
                    </div>
                </div>

                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(logss of depLogs1 |searchFilter: datachangeLogs1|paginate:{
                            itemsPerPage: 10, currentPage: pageNumber1, id:'third'};
                            track logss){
                            <tr>
                                <td>{{ pageNumber1*10+$index+1-10 }}</td>
                                <td>{{logss.fileName }}</td>
                                <td>{{logss.created_dt}}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(logss)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="pageNumber1 = $event"></pagination-controls>
                </div>
            </div>
        </div>
        <div class="accordion-item">
            <h2 class="accordion-header" id="flush-headingOne">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                    data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                    Reports
                </button>
            </h2>
            <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                data-bs-parent="#accordionFlushExample">
                <div class="qmig-card-body">
                    <div class="row">
                        <div class="col-12 col-sm-3 col-md-3 col-xl-3 mt-3">
                            <div class="form-group">
                                <select class="form-select" #iter (change)="selectIteration(iter.value)">
                                    <option selected value="">
                                        Select Run Number
                                    </option>
                                    @for( list of runnoForReports;track list;){
                                    <option value="{{ list.iteration}}">{{list.iteration}}</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-12 col-sm-3 col-md-3 col-xl-3 mt-3"></div>
                        <div class="col-12 col-sm-6 col-md-9 col-xl-6 d-flex">
                            <div class="custom_search cs-r me-3 my-3">
                                <span class="mdi mdi-magnify"></span>
                                <input type="text" placeholder="Search Storage Reports" class="form-control"
                                    [(ngModel)]="datachange" (keyup)="onKey()">
                            </div>
                            <button class="btn btn-sync" (click)="getUpdatedRunNumber()">
                                @if(getRunSpin){
                                <app-spinner />
                                }@else{
                                <span class="mdi mdi-refresh"></span>
                                }
                            </button>
                        </div>
                    </div>
                </div>
                <!-- for download file -->
                <div class="table-responsive">
                    <table class="table table-hover qmig-table">
                        <thead>
                            <tr>
                                <th>S.No</th>
                                <th>File Name</th>
                                <th>Created Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @for(docs of assessmentFiles |searchFilter: datachange|paginate:{
                            itemsPerPage: 10, currentPage: p, id:'third'};
                            track docs){
                            <tr>
                                <td>{{ p*10+$index+1-10 }}</td>
                                <td>{{docs.fileName }}</td>
                                <td>{{docs.created_dt }}</td>
                                <td>
                                    <button class="btn btn-download" (click)="downloadFile(docs)">
                                        <span class="mdi mdi-cloud-download-outline"></span>
                                    </button>
                                </td>
                            </tr>
                            } @empty {
                            <tr>
                                <td colspan="4">
                                    <p class="text-center m-0 w-100">Empty</p>
                                </td>
                            </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <!-- pagination -->
                <div class="custom_pagination">
                    <pagination-controls (pageChange)="p = $event" id="third"></pagination-controls>
                </div>
            </div>
        </div>
    </div>
    <!-- execute button details -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="demo">
        <div class="offcanvas-header">
            <h4 class="main_h">Storage Object Migration</h4>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" (click)="openPopup()"></button>
        </div>
        <div class="offcanvas-body">
            <form class="form qmig-Form checkForm">
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Run Number</label>
                    : &nbsp;{{ currentRunNumber }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Source Connection</label>
                    : &nbsp;{{ conName }}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Source Schema</label>
                    : &nbsp;{{ schemaName.toString() }}
                </div>
                <div class="form-group">
                    <label class="form-label " for="name">Target Connection</label>
                    : &nbsp;{{tgtValue}}
                </div>
                <div class="form-group">
                    <label class="form-label d-required" for="name">Deploy Connection</label>
                    : &nbsp;{{drconName}}
                </div>
                <!-- @if(migtypeid=="30")
                { -->
                @if(isAll!="ALL"){
                @if(showTargetSchemaAndTables){
                <div class="form-group">
                    <label class="form-label" for="name"> Target Schema</label>
                    : &nbsp; {{ selectedtgtScema || selectedsrcschema.toString() }}
                </div>
                }
                }
                <div class="form-group">
                    <label class="form-label d-required" for="name"> Object Type</label>
                    : &nbsp;{{objtype}}
                </div>
                <div class="form-group">
                    <div class="form-check form-check-flat form-check-primary mt-3">
                        <label class="form-check-label form-label">
                            <input type="checkbox" class="form-check-input" (click)="getCheckValue($event)"
                                formControlName="refreshFlag">
                            <i class="input-helper"></i> Restart
                        </label>
                        <label class="form-check-label form-label">
                            <input type="checkbox" class="form-check-input" 
                                formControlName="refreshFlag">
                            <i class="input-helper"></i> Drop Schema
                        </label>
                    </div>
                </div>
                <div class="form-group">
                    <div class="body-header-button">
                        <button class="btn btn-upload w-100 me-1" (click)="ConversionCommand()">
                            <span></span> Execute</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
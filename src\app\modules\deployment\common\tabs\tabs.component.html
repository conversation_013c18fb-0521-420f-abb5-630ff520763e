
<div class="body-header">
    <div class="row">
        <div class="col-md-11 pt-2">
            <div class="qmigTabs">
                <!-- Nav pills -->
                <ul class="nav nav-pills">
                    @if(selectedscreen=="FS"){
                        <li class="nav-item">
                            <a class="nav-link q-tooltip"  routerLinkActive="active"   routerLink="../targetCurrent">T-Current
                                <i>Target modified code</i>
                            </a>
                        </li>
                }
                    <li class="nav-items">
                        <a class="nav-link q-tooltip"  routerLinkActive="active"  routerLink="../sourceUpload">S-Current
                            <i>Source changed code extraction</i> </a>
                    </li>
                    @if(selectedscreen=="TI"){
                    <li class="nav-item">
                        <a class="nav-link q-tooltip"    routerLinkActive="active"  routerLink="../sourceBaseline">S-Baseline
                            <i>Source original code extraction</i>
                        </a>
                    </li>
                }
                @if(selectedscreen=="ST"){
                    <li class="nav-item">
                        <a class="nav-link q-tooltip"   routerLinkActive="active"  routerLink="../sourceBaseline">S-Baseline
                            <i>Source original code extraction</i>
                        </a>
                    </li>
                }
                @if(selectedscreen=="BC"){
                    <li class="nav-item">
                        <a class="nav-link q-tooltip"    routerLinkActive="active"  routerLink="../sourceBaseline">S-Baseline
                            <i>Source original code extraction</i>
                        </a>
                    </li>
                }
                    @if(selectedscreen=="TI"){
                    <li class="nav-item">
                        <a class="nav-link q-tooltip"   routerLinkActive="active"  routerLink="../sourceCompare">S-Compare
                            <i>Difference between the source code</i>
                        </a>
                    </li>
                }
                @if(selectedscreen=="BC"){
                    <li class="nav-item">
                        <a class="nav-link q-tooltip"  routerLinkActive="active"   routerLink="../sourceCompare">S-Compare
                            <i>Difference between the source code</i>
                        </a>
                    </li>
                }
                    <!-- <li class="nav-item">
                        <a class="nav-link q-tooltip"   routerLinkActive="active" routerLink="../targetConversion">T-Conversion
                            <i>Converted new target code</i>
                        </a>
                    </li> -->
                    @if(selectedscreen=="TI"){
                    <li class="nav-item">
                        <a class="nav-link q-tooltip"  routerLinkActive="active"   routerLink="../targetBaseline">T-Baseline
                            <i>Target original code extraction</i>
                        </a>
                    </li>
                }

                @if(selectedscreen=="ST"){
                <li class="nav-item">
                    <a class="nav-link  q-tooltip active"  routerLinkActive="active"     routerLink="../sourceCompare">S-Compare
                        <i>Target modified code</i>
                    </a>
                </li>
        } 

                @if(selectedscreen=="ST"){
                    <li class="nav-item">
                        <a class="nav-link q-tooltip"  routerLinkActive="active"   routerLink="../targetBaseline">T-Baseline
                            <i>Target original code extraction</i>
                        </a>
                    </li>
                }
                @if(selectedscreen=="BC"){
                    <li class="nav-item">
                        <a class="nav-link q-tooltip"  routerLinkActive="active"  routerLink="../targetBaseline">T-Baseline
                            <i>Target original code extraction</i>
                        </a>
                    </li>
                }
                
                    @if(selectedscreen=="BC"){
                        <li class="nav-item">
                            <a class="nav-link q-tooltip"  routerLinkActive="active"   routerLink="../targetConversion">T-Conversion
                                <i>Converted new target code</i>
                            </a>
                        </li>
                }
               
            <!-- @if(selectedscreen=="ST"){
                <li class="nav-item">
                    <a class="nav-link  q-tooltip active"  routerLinkActive="active"     routerLink="../targetCurrent">T-Current
                        <i>Target modified code</i>
                    </a>
                </li>
        } -->
                    <!-- <li class="nav-item">
                        <a class="nav-link q-tooltip"   routerLinkActive="active" routerLink="../targetCurrent">T-Current
                            <i>Target modified code</i>
                        </a>
                    </li> -->
                    <!-- <li class="nav-item">
                        <a class="nav-link q-tooltip"   routerLinkActive="active" routerLink="../targetCompare">T-Compare</a>
                    </li> -->
                    @if(selectedscreen=="ST"){
                        <li class="nav-item">
                            <a class="nav-link q-tooltip " routerLinkActive="active"  routerLink="../targetintergate">Target Integrate
                                <i>Process Delta Changes</i>
                            </a>
                        </li>  
                }
                
                 
                    @if(selectedscreen=="ST"){
                        <li class="nav-item">
                            <a class="nav-link  q-tooltip active"  routerLinkActive="active"   routerLink="../SandTCompare">S & T Compare
                                <i>Source and Target Compare</i>
                            </a>
                        </li> 
                }
                @if(selectedscreen=="TI"){
                    <li class="nav-item">
                        <a class="nav-link  q-tooltip active"   routerLinkActive="active"  routerLink="../targetintergate">Target Integrate
                            <i>Process Delta Changes</i>
                        </a>
                    </li>  
            }
            @if(selectedscreen=="TI"){
                <li class="nav-item">
                    <a class="nav-link q-tooltip " routerLinkActive="active"  routerLink="../targetCompare">T-Compare
                        <i>Difference between the source code</i>
                    </a>
                </li>  
        }

            @if(selectedscreen=="BC"){
                <li class="nav-item">
                    <a class="nav-link  q-tooltip active"  routerLinkActive="active"  routerLink="../BandCCompare">B & C Compare
                        <i>T-Baseline and T-Current Compare</i>
                    </a>
                </li> 
        }
        @if(selectedscreen=="FS"){
            <li class="nav-item">
                <a class="nav-link  q-tooltip active"  routerLinkActive="active"  routerLink="../SandTCompare">S & T Compare
                    <i>Source and Target Compare</i>
                </a>
            </li> 
    }
                </ul>
            </div>                
        </div>
    </div>
</div>
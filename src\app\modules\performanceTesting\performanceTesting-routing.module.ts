import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '../../shared/components/layout/layout.component';
import { PerformanceTestingComponent } from './common/performance-testing/performance-testing.component';
import { DuplicateIndexComponent } from './common/duplicate-index/duplicate-index.component';
import { DatabaseIndexComponent } from './common/database-index/database-index.component';
import { TablePartitionComponent } from './common/table-partition/table-partition.component';
import { LogExplainComponent } from './common/log-explain/log-explain.component';
import { LogRunningQueriesComponent } from './common/log-running-queries/log-running-queries.component';
import { MonitorTableDateComponent } from './common/monitor-table-date/monitor-table-date.component';
import { PerformanceTuningInsightsComponent } from './common/performance-tuning-insights/performance-tuning-insights.component';
import { PerformanceIndextuningComponent } from './common/performance-indextuning/performance-indextuning.component';
import { QueryInsightsComponent } from './common/query-insights/query-insights.component';
import { ReportsComponent } from './common/reports/reports.component';
import { AgentComponent } from './common/agent/agent.component';


const routes: Routes = [
  {path:'', component:LayoutComponent, children:[
    {path:'performanceTesting', component:PerformanceTestingComponent,data: { name: 'Perf Optimization'}},
    {path:'duplicateIndex', component:DuplicateIndexComponent, data: { name:'Duplicate Index'}},
    {path:'databaseIndex', component:DatabaseIndexComponent, data: {name:'Database Index'}},
    {path:'tablePartition', component:TablePartitionComponent, data: {name:'Table Partition'}},
    {path:'logExplain', component:LogExplainComponent, data: {name: 'Log Explain'}},
    {path:'logRunningQueries', component:LogRunningQueriesComponent, data: {name: 'Log Running Queries'}},
    {path:'monitorTableData', component:MonitorTableDateComponent, data: {name: 'Monitor Table Data'}},
    {path:'PerformanceTuningInsights', component:PerformanceTuningInsightsComponent, data: {name: 'Performance-Insights'}},
    {path:'QueryInsights', component:QueryInsightsComponent, data: {name: 'Query-Insights'}},
    {path:'PerformanceIndexTuning', component:PerformanceIndextuningComponent, data: {name: 'Performance-IndexTuning'}},
    {path:'perf-reports', component:ReportsComponent, data: {name: 'Performance-Reports'}},
    {path:'agent', component:AgentComponent, data:{name:'Agent'}}
    
  ]}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PerformanceTestingRoutingModule { }
